const { initializeDatabase, closeDatabase } = require('../config/database');

async function setupDatabase() {
  try {
    console.log('Setting up database...');
    await initializeDatabase();
    console.log('Database setup completed successfully!');
    
    // Insert sample data
    console.log('Inserting sample data...');
    await insertSampleData();
    console.log('Sample data inserted successfully!');
    
  } catch (error) {
    console.error('Error setting up database:', error);
    process.exit(1);
  } finally {
    await closeDatabase();
  }
}

async function insertSampleData() {
  const { getDatabase } = require('../config/database');
  const db = getDatabase();
  
  return new Promise((resolve, reject) => {
    // Sample students data
    const sampleStudents = [
      {
        student_id: 'STU001',
        name: '<PERSON>',
        course: 'Computer Science',
        email: '<EMAIL>',
        address: '123 Main St, City, State 12345'
      },
      {
        student_id: 'STU002',
        name: '<PERSON>',
        course: 'Business Administration',
        email: '<EMAIL>',
        address: '456 Oak Ave, City, State 12345'
      },
      {
        student_id: 'STU003',
        name: '<PERSON>',
        course: 'Engineering',
        email: '<EMAIL>',
        address: '789 Pine St, City, State 12345'
      }
    ];
    
    // Insert students
    const insertStudent = `
      INSERT INTO students (
        student_id, name, course, email, address
      ) VALUES (?, ?, ?, ?, ?)
    `;

    let completed = 0;
    const total = sampleStudents.length;

    sampleStudents.forEach(student => {
      db.run(insertStudent, [
        student.student_id,
        student.name,
        student.course,
        student.email,
        student.address
      ], function(err) {
        if (err && err.code !== 'SQLITE_CONSTRAINT_UNIQUE') {
          console.error('Error inserting student:', err);
        } else if (err && err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
          console.log(`Student ${student.student_id} already exists, skipping...`);
        } else {
          console.log(`Inserted student: ${student.name}`);
        }
        
        completed++;
        if (completed === total) {
          resolve();
        }
      });
    });
  });
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
