const { initializeDatabase, closeDatabase } = require('../config/database');

async function setupDatabase() {
  try {
    console.log('Setting up database...');
    await initializeDatabase();
    console.log('Database setup completed successfully!');
    
    // Insert sample data
    console.log('Inserting sample data...');
    await insertSampleData();
    console.log('Sample data inserted successfully!');
    
  } catch (error) {
    console.error('Error setting up database:', error);
    process.exit(1);
  } finally {
    await closeDatabase();
  }
}

async function insertSampleData() {
  const { getDatabase } = require('../config/database');
  const db = getDatabase();
  
  return new Promise((resolve, reject) => {
    // Sample students data
    const sampleStudents = [
      {
        student_id: 'STU001',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        phone: '+1234567890',
        date_of_birth: '2000-05-15',
        address: '123 Main St, City, State 12345',
        course: 'Computer Science',
        year_of_study: 3,
        status: 'active'
      },
      {
        student_id: 'STU002',
        first_name: '<PERSON>',
        last_name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+1234567891',
        date_of_birth: '1999-08-22',
        address: '456 Oak Ave, City, State 12345',
        course: 'Business Administration',
        year_of_study: 2,
        status: 'active'
      },
      {
        student_id: 'STU003',
        first_name: 'Mike',
        last_name: 'Johnson',
        email: '<EMAIL>',
        phone: '+1234567892',
        date_of_birth: '2001-03-10',
        address: '789 Pine St, City, State 12345',
        course: 'Engineering',
        year_of_study: 1,
        status: 'active'
      }
    ];
    
    // Insert students
    const insertStudent = `
      INSERT INTO students (
        student_id, first_name, last_name, email, phone, 
        date_of_birth, address, course, year_of_study, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    let completed = 0;
    const total = sampleStudents.length;
    
    sampleStudents.forEach(student => {
      db.run(insertStudent, [
        student.student_id,
        student.first_name,
        student.last_name,
        student.email,
        student.phone,
        student.date_of_birth,
        student.address,
        student.course,
        student.year_of_study,
        student.status
      ], function(err) {
        if (err && err.code !== 'SQLITE_CONSTRAINT_UNIQUE') {
          console.error('Error inserting student:', err);
        } else if (err && err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
          console.log(`Student ${student.student_id} already exists, skipping...`);
        } else {
          console.log(`Inserted student: ${student.first_name} ${student.last_name}`);
        }
        
        completed++;
        if (completed === total) {
          resolve();
        }
      });
    });
  });
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
