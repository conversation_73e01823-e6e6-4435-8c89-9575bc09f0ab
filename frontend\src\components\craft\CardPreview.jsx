import React from 'react'
import { Editor, Frame } from '@craftjs/core'
import { Text } from './Text'
import { ImagePlaceholder } from './ImagePlaceholder'
import { Container } from './Container'

export const CardPreview = ({ templateData, studentData, className = '' }) => {
  // Handle template data (could be string or object)
  let craftData
  try {
    let parsed
    if (typeof templateData === 'string') {
      parsed = JSON.parse(templateData)
    } else {
      parsed = templateData
    }
    craftData = parsed.craftjs || parsed
  } catch (error) {
    console.error('Error parsing template data:', error)
    return <div className="text-red-500">Error loading template</div>
  }

  // Function to replace placeholders in text
  const replacePlaceholders = (text) => {
    if (!text || !studentData) return text
    
    return text
      .replace(/{name}/g, studentData.name || '')
      .replace(/{student_id}/g, studentData.student_id || '')
      .replace(/{course}/g, studentData.course || '')
      .replace(/{email}/g, studentData.email || '')
      .replace(/{address}/g, studentData.address || '')
  }

  // Custom Text component for preview that replaces placeholders
  const PreviewText = ({ text, ...props }) => {
    const processedText = replacePlaceholders(text)
    return <Text text={processedText} {...props} />
  }

  // Custom ImagePlaceholder for preview that shows actual student photo
  const PreviewImagePlaceholder = ({ width, height, ...props }) => {
    if (studentData?.photo_url) {
      return (
        <img
          src={`http://localhost:5001${studentData.photo_url}`}
          alt={studentData.name}
          style={{
            width: `${width}px`,
            height: `${height}px`,
            objectFit: 'cover',
            border: '1px solid #cccccc'
          }}
        />
      )
    }
    return <ImagePlaceholder width={width} height={height} {...props} />
  }

  return (
    <div className={className}>
      <Editor
        resolver={{
          Text: PreviewText,
          ImagePlaceholder: PreviewImagePlaceholder,
          Container
        }}
        enabled={false} // Disable editing in preview mode
      >
        <Frame data={craftData}>
          <div>Preview not available</div>
        </Frame>
      </Editor>
    </div>
  )
}
