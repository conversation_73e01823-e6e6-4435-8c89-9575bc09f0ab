import React from 'react'

export const CardPreview = ({ templateData, studentData, className = '' }) => {
  // Handle template data (could be string or object)
  let templateInfo
  try {
    let parsed
    if (typeof templateData === 'string') {
      parsed = JSON.parse(templateData)
    } else {
      parsed = templateData
    }
    templateInfo = parsed
  } catch (error) {
    console.error('Error parsing template data:', error)
    return <div className="text-red-500">Error loading template</div>
  }

  // Function to replace placeholders in text
  const replacePlaceholders = (text) => {
    if (!text || !studentData) return text

    return text
      .replace(/{name}/g, studentData.name || '')
      .replace(/{student_id}/g, studentData.student_id || '')
      .replace(/{course}/g, studentData.course || '')
      .replace(/{email}/g, studentData.email || '')
      .replace(/{address}/g, studentData.address || '')
  }

  // Get template dimensions
  const width = templateInfo?.width || 400
  const height = templateInfo?.height || 250
  const backgroundColor = templateInfo?.backgroundColor || '#ffffff'

  return (
    <div className={className}>
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
          backgroundColor,
          border: '2px solid #ddd',
          position: 'relative',
          boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      >
        {/* Simple preview layout */}
        <div style={{
          position: 'absolute',
          top: '20px',
          left: '20px',
          right: '20px'
        }}>
          <div style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '8px',
            textAlign: 'center'
          }}>
            STUDENT ID CARD
          </div>

          <div style={{
            display: 'flex',
            gap: '15px',
            marginTop: '15px'
          }}>
            {/* Photo placeholder */}
            <div style={{
              width: '80px',
              height: '100px',
              border: '2px dashed #ccc',
              background: '#f9f9f9',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              color: '#999',
              flexShrink: 0
            }}>
              {studentData?.photo_url ? (
                <img
                  src={`http://localhost:5001${studentData.photo_url}`}
                  alt={studentData.name}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />
              ) : (
                'Photo'
              )}
            </div>

            {/* Student info */}
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '16px',
                fontWeight: 'bold',
                color: '#333',
                marginBottom: '5px'
              }}>
                {replacePlaceholders('{name}')}
              </div>
              <div style={{
                fontSize: '14px',
                color: '#666',
                lineHeight: '1.4'
              }}>
                <div>ID: {replacePlaceholders('{student_id}')}</div>
                <div>Course: {replacePlaceholders('{course}')}</div>
                <div>Email: {replacePlaceholders('{email}')}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
