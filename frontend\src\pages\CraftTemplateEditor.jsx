import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Editor, Frame, Element, useEditor } from '@craftjs/core'
import { ArrowLeft, Save, Eye } from 'lucide-react'
import toast from 'react-hot-toast'

// Craft components
import { Text } from '../components/craft/Text'
import { ImagePlaceholder } from '../components/craft/ImagePlaceholder'
import { Container } from '../components/craft/Container'
import { Toolbox } from '../components/craft/Toolbox'
import { SettingsPanel } from '../components/craft/SettingsPanel'

// API
import { templateAPI } from '../services/api'

export default function CraftTemplateEditor() {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEdit = Boolean(id)
  
  const [templateName, setTemplateName] = useState(isEdit ? 'Loading...' : 'New Template')
  const [loading, setLoading] = useState(false)
  const [initialData, setInitialData] = useState(null)

  useEffect(() => {
    if (isEdit) {
      fetchTemplate()
    }
  }, [isEdit, id])

  const fetchTemplate = async () => {
    try {
      setLoading(true)
      const response = await templateAPI.getById(id)
      const template = response.data

      setTemplateName(template.name)

      // Parse template data
      let templateData
      try {
        templateData = JSON.parse(template.template_data)
        if (templateData.craftjs) {
          setInitialData(templateData.craftjs)
        }
      } catch (error) {
        console.error('Error parsing template data:', error)
        toast.error('Error loading template data')
      }
    } catch (error) {
      console.error('Error fetching template:', error)
      toast.error('Failed to load template')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (query) => {
    try {
      setLoading(true)

      const json = query.serialize()
      const templateData = {
        name: templateName,
        template_data: {
          craftjs: json,
          width: 400,
          height: 250,
          backgroundColor: '#ffffff'
        }
      }

      if (isEdit) {
        await templateAPI.update(id, templateData)
        toast.success('Template updated successfully')
      } else {
        await templateAPI.create(templateData)
        toast.success('Template created successfully')
      }
      
      navigate('/templates')
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Failed to save template')
    } finally {
      setLoading(false)
    }
  }

  const handlePreview = (query) => {
    const json = query.serialize()
    console.log('Template JSON:', json)
    toast.success('Check console for template data')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/templates')}
                className="btn-secondary mr-4"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </button>
              <div>
                <input
                  type="text"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  className="text-xl font-semibold bg-transparent border-none outline-none"
                  placeholder="Template Name"
                />
                <p className="text-sm text-gray-600">
                  {isEdit ? 'Edit Template' : 'Create New Template'}
                </p>
              </div>
            </div>
            
            <Editor
              resolver={{
                Text,
                ImagePlaceholder,
                Container
              }}
            >
              <div className="flex items-center space-x-3">
                <EditorButtons onSave={handleSave} onPreview={handlePreview} loading={loading} />
              </div>
            </Editor>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <Editor
          resolver={{
            Text,
            ImagePlaceholder,
            Container
          }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Toolbox */}
            <div>
              <Toolbox />
            </div>

            {/* Canvas */}
            <div className="lg:col-span-2">
              <div className="card p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Canvas</h3>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 min-h-[500px] bg-white relative overflow-hidden">
                  {isEdit && initialData ? (
                    <Frame data={initialData}>
                      <Element is={Container} canvas />
                    </Frame>
                  ) : (
                    <Frame>
                      <Element
                        is={Container}
                        backgroundColor="#ffffff"
                        padding={20}
                        width={400}
                        height={250}
                        x={50}
                        y={50}
                        canvas
                      >
                        <Text text="STUDENT ID CARD" fontSize={18} fontWeight="bold" textAlign="center" x={100} y={10} />
                        <ImagePlaceholder width={80} height={100} x={20} y={50} />
                        <Text text="{name}" fontSize={16} fontWeight="bold" x={120} y={60} />
                        <Text text="ID: {student_id}" fontSize={12} color="#666666" x={120} y={85} />
                        <Text text="Course: {course}" fontSize={12} color="#666666" x={120} y={105} />
                      </Element>
                    </Frame>
                  )}
                </div>
              </div>
            </div>

            {/* Settings Panel */}
            <div>
              <SettingsPanel />
            </div>
          </div>
        </Editor>
      </div>
    </div>
  )
}

// Separate component for editor buttons to access useEditor hook
const EditorButtons = ({ onSave, onPreview, loading }) => {
  const { query } = useEditor()

  return (
    <>
      <button
        onClick={() => onPreview(query)}
        className="btn-secondary"
        disabled={loading}
        style={{
          minWidth: '120px',
          whiteSpace: 'nowrap',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '0.75rem 1.5rem',
          gap: '0.5rem'
        }}
      >
        <Eye className="h-4 w-4" />
        Preview
      </button>
      <button
        onClick={() => onSave(query)}
        className="btn-primary"
        disabled={loading}
        style={{
          minWidth: '160px',
          whiteSpace: 'nowrap',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '0.75rem 1.5rem',
          gap: '0.5rem'
        }}
      >
        <Save className="h-4 w-4" />
        {loading ? 'Saving...' : 'Save Template'}
      </button>
    </>
  )
}
