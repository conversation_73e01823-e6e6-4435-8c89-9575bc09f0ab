import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Editor, Frame, Element, useEditor } from '@craftjs/core'
import { ArrowLeft, Save, Eye } from 'lucide-react'
import toast from 'react-hot-toast'

// Craft components
import { Text } from '../components/craft/Text'
import { ImagePlaceholder } from '../components/craft/ImagePlaceholder'
import { Container } from '../components/craft/Container'
import { Toolbox } from '../components/craft/Toolbox'
import { SettingsPanel } from '../components/craft/SettingsPanel'

// API
import { templateAPI } from '../services/api'

export default function CraftTemplateEditor() {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEdit = Boolean(id)
  
  const [templateName, setTemplateName] = useState(isEdit ? 'Loading...' : 'New Template')
  const [loading, setLoading] = useState(false)
  const [initialData, setInitialData] = useState(null)

  useEffect(() => {
    if (isEdit) {
      fetchTemplate()
    }
  }, [isEdit, id])

  const fetchTemplate = async () => {
    try {
      setLoading(true)
      const response = await templateAPI.getById(id)
      const template = response.data

      setTemplateName(template.name)

      // Parse template data
      let templateData
      try {
        templateData = JSON.parse(template.template_data)
        if (templateData.craftjs) {
          setInitialData(templateData.craftjs)
        }
      } catch (error) {
        console.error('Error parsing template data:', error)
        toast.error('Error loading template data')
      }
    } catch (error) {
      console.error('Error fetching template:', error)
      toast.error('Failed to load template')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (query) => {
    try {
      setLoading(true)

      const json = query.serialize()
      const templateData = {
        name: templateName,
        template_data: {
          craftjs: json,
          width: 400,
          height: 250,
          backgroundColor: '#ffffff'
        }
      }

      if (isEdit) {
        await templateAPI.update(id, templateData)
        toast.success('Template updated successfully')
      } else {
        await templateAPI.create(templateData)
        toast.success('Template created successfully')
      }
      
      navigate('/templates')
    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Failed to save template')
    } finally {
      setLoading(false)
    }
  }

  const handlePreview = (query) => {
    try {
      const json = query.serialize()
      const templateData = {
        craftjs: json,
        width: 400,
        height: 250,
        backgroundColor: '#ffffff'
      }

      // Create sample student data for preview
      const sampleStudent = {
        name: 'John Doe',
        student_id: 'STU001',
        course: 'Computer Science',
        email: '<EMAIL>',
        address: '123 University Ave',
        photo_url: null
      }

      // Open preview in a new window
      const previewWindow = window.open('', '_blank', 'width=600,height=400')
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Template Preview</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: Arial, sans-serif;
              background: #f5f5f5;
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
            }
            .preview-container {
              background: white;
              padding: 20px;
              border-radius: 8px;
              box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .preview-title {
              text-align: center;
              margin-bottom: 20px;
              color: #333;
            }
            .card-preview {
              border: 2px solid #ddd;
              background: ${templateData.backgroundColor || '#ffffff'};
              width: ${templateData.width}px;
              height: ${templateData.height}px;
              position: relative;
              margin: 0 auto;
            }
          </style>
        </head>
        <body>
          <div class="preview-container">
            <h2 class="preview-title">Template Preview</h2>
            <div class="card-preview">
              <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: #666;">
                <p><strong>Template Preview</strong></p>
                <p>Sample: ${sampleStudent.name}</p>
                <p>ID: ${sampleStudent.student_id}</p>
                <p>Course: ${sampleStudent.course}</p>
              </div>
            </div>
            <p style="text-align: center; margin-top: 20px; color: #666; font-size: 14px;">
              This is a basic preview. The actual card will render with full styling and student data.
            </p>
          </div>
        </body>
        </html>
      `)
      previewWindow.document.close()

      toast.success('Preview opened in new window')
    } catch (error) {
      console.error('Error generating preview:', error)
      toast.error('Failed to generate preview')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/templates')}
                className="btn-secondary mr-4"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </button>
              <div>
                <input
                  type="text"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  className="text-xl font-semibold bg-transparent border-none outline-none"
                  placeholder="Template Name"
                />
                <p className="text-sm text-gray-600">
                  {isEdit ? 'Edit Template' : 'Create New Template'}
                </p>
              </div>
            </div>
            
            <Editor
              resolver={{
                Text,
                ImagePlaceholder,
                Container
              }}
            >
              <div className="flex items-center space-x-3">
                <EditorButtons onSave={handleSave} onPreview={handlePreview} loading={loading} />
              </div>
            </Editor>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <Editor
          resolver={{
            Text,
            ImagePlaceholder,
            Container
          }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Toolbox */}
            <div>
              <Toolbox />
            </div>

            {/* Canvas */}
            <div className="lg:col-span-2">
              <div className="card p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Canvas</h3>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 min-h-[600px] bg-white relative" style={{ overflow: 'visible', minWidth: '600px' }}>
                  {isEdit && initialData ? (
                    <Frame data={initialData}>
                      <Element is={Container} canvas />
                    </Frame>
                  ) : (
                    <Frame>
                      <Element
                        is={Container}
                        backgroundColor="#ffffff"
                        padding={20}
                        width={400}
                        height={250}
                        canvas
                      >
                        <Text text="STUDENT ID CARD" fontSize={18} fontWeight="bold" textAlign="center" />
                        <ImagePlaceholder width={80} height={100} />
                        <Text text="{name}" fontSize={16} fontWeight="bold" />
                        <Text text="ID: {student_id}" fontSize={12} color="#666666" />
                        <Text text="Course: {course}" fontSize={12} color="#666666" />
                      </Element>
                    </Frame>
                  )}
                </div>
              </div>
            </div>

            {/* Settings Panel */}
            <div>
              <SettingsPanel />
            </div>
          </div>
        </Editor>
      </div>
    </div>
  )
}

// Separate component for editor buttons to access useEditor hook
const EditorButtons = ({ onSave, onPreview, loading }) => {
  const { query } = useEditor()

  return (
    <>
      <button
        onClick={() => onPreview(query)}
        className="btn-secondary"
        disabled={loading}
        style={{
          minWidth: '120px',
          whiteSpace: 'nowrap',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '0.75rem 1.5rem',
          gap: '0.5rem'
        }}
      >
        <Eye className="h-4 w-4" />
        Preview
      </button>
      <button
        onClick={() => onSave(query)}
        className="btn-primary"
        disabled={loading}
        style={{
          minWidth: '160px',
          whiteSpace: 'nowrap',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '0.75rem 1.5rem',
          gap: '0.5rem'
        }}
      >
        <Save className="h-4 w-4" />
        {loading ? 'Saving...' : 'Save Template'}
      </button>
    </>
  )
}
