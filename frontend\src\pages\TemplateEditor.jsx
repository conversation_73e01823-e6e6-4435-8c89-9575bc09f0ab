import { useState, useEffect, useRef } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { ArrowLeft, Save, Eye, Type, Image, Square } from 'lucide-react'
import { fabric } from 'fabric'
import { templateAPI, cardAPI } from '../services/api'
import toast from 'react-hot-toast'

export default function TemplateEditor() {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEdit = Boolean(id)
  const canvasRef = useRef(null)
  const fabricCanvasRef = useRef(null)
  
  const [templateName, setTemplateName] = useState('')
  const [canvasSize, setCanvasSize] = useState({ width: 400, height: 250 })
  const [backgroundColor, setBackgroundColor] = useState('#ffffff')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [selectedTool, setSelectedTool] = useState('select')

  useEffect(() => {
    initializeCanvas()
    if (isEdit) {
      fetchTemplate()
    }
    
    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose()
      }
    }
  }, [])

  const initializeCanvas = () => {
    if (canvasRef.current && !fabricCanvasRef.current) {
      fabricCanvasRef.current = new fabric.Canvas(canvasRef.current, {
        width: canvasSize.width,
        height: canvasSize.height,
        backgroundColor: backgroundColor
      })
      
      // Add default elements for new templates
      if (!isEdit) {
        addDefaultElements()
      }
    }
  }

  const addDefaultElements = () => {
    const canvas = fabricCanvasRef.current
    if (!canvas) return

    // Add title
    const title = new fabric.Text('STUDENT ID CARD', {
      left: canvasSize.width / 2,
      top: 30,
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
      originX: 'center',
      fill: '#333333'
    })
    canvas.add(title)

    // Add photo placeholder
    const photoRect = new fabric.Rect({
      left: 30,
      top: 60,
      width: 80,
      height: 100,
      fill: '#f0f0f0',
      stroke: '#cccccc',
      strokeWidth: 2
    })
    canvas.add(photoRect)

    // Add student name placeholder
    const nameText = new fabric.Text('{first_name} {last_name}', {
      left: 130,
      top: 80,
      fontSize: 16,
      fontWeight: 'bold',
      fill: '#333333'
    })
    canvas.add(nameText)

    // Add student ID placeholder
    const idText = new fabric.Text('ID: {student_id}', {
      left: 130,
      top: 105,
      fontSize: 12,
      fill: '#666666'
    })
    canvas.add(idText)

    // Add course placeholder
    const courseText = new fabric.Text('Course: {course}', {
      left: 130,
      top: 125,
      fontSize: 12,
      fill: '#666666'
    })
    canvas.add(courseText)

    canvas.renderAll()
  }

  const fetchTemplate = async () => {
    try {
      setLoading(true)
      const response = await templateAPI.getById(id)
      const template = response.data
      
      setTemplateName(template.name)
      setCanvasSize({
        width: template.template_data.width,
        height: template.template_data.height
      })
      setBackgroundColor(template.template_data.backgroundColor)
      
      // Load template elements into canvas
      loadTemplateElements(template.template_data)
      
    } catch (error) {
      console.error('Error fetching template:', error)
      toast.error('Failed to load template')
      navigate('/templates')
    } finally {
      setLoading(false)
    }
  }

  const loadTemplateElements = (templateData) => {
    const canvas = fabricCanvasRef.current
    if (!canvas) return

    canvas.setWidth(templateData.width)
    canvas.setHeight(templateData.height)
    canvas.setBackgroundColor(templateData.backgroundColor)

    // Clear existing objects
    canvas.clear()

    // Add elements from template data
    templateData.elements.forEach(element => {
      if (element.type === 'text') {
        const text = new fabric.Text(element.content, {
          left: element.x,
          top: element.y,
          fontSize: element.fontSize || 14,
          fontWeight: element.fontWeight || 'normal',
          fill: element.color || '#000000',
          textAlign: element.textAlign || 'left'
        })
        canvas.add(text)
      } else if (element.type === 'image') {
        const rect = new fabric.Rect({
          left: element.x,
          top: element.y,
          width: element.width,
          height: element.height,
          fill: '#f0f0f0',
          stroke: '#cccccc',
          strokeWidth: 2
        })
        canvas.add(rect)
      } else if (element.type === 'shape') {
        if (element.shapeType === 'rectangle') {
          const rect = new fabric.Rect({
            left: element.x,
            top: element.y,
            width: element.width,
            height: element.height,
            fill: element.fillColor || 'transparent',
            stroke: element.strokeColor || '#000000',
            strokeWidth: element.strokeWidth || 1
          })
          canvas.add(rect)
        }
      }
    })

    canvas.renderAll()
  }

  const addTextElement = () => {
    const canvas = fabricCanvasRef.current
    if (!canvas) return

    const text = new fabric.Text('Sample Text', {
      left: 50,
      top: 50,
      fontSize: 14,
      fill: '#000000'
    })
    canvas.add(text)
    canvas.setActiveObject(text)
    canvas.renderAll()
  }

  const addImagePlaceholder = () => {
    const canvas = fabricCanvasRef.current
    if (!canvas) return

    const rect = new fabric.Rect({
      left: 50,
      top: 50,
      width: 80,
      height: 100,
      fill: '#f0f0f0',
      stroke: '#cccccc',
      strokeWidth: 2
    })
    canvas.add(rect)
    canvas.setActiveObject(rect)
    canvas.renderAll()
  }

  const addShapeElement = () => {
    const canvas = fabricCanvasRef.current
    if (!canvas) return

    const rect = new fabric.Rect({
      left: 50,
      top: 50,
      width: 100,
      height: 50,
      fill: 'transparent',
      stroke: '#000000',
      strokeWidth: 2
    })
    canvas.add(rect)
    canvas.setActiveObject(rect)
    canvas.renderAll()
  }

  const updateCanvasSize = (newSize) => {
    setCanvasSize(newSize)
    const canvas = fabricCanvasRef.current
    if (canvas) {
      canvas.setWidth(newSize.width)
      canvas.setHeight(newSize.height)
      canvas.renderAll()
    }
  }

  const updateBackgroundColor = (color) => {
    setBackgroundColor(color)
    const canvas = fabricCanvasRef.current
    if (canvas) {
      canvas.setBackgroundColor(color)
      canvas.renderAll()
    }
  }

  const convertCanvasToTemplateData = () => {
    const canvas = fabricCanvasRef.current
    if (!canvas) return null

    const elements = []
    
    canvas.getObjects().forEach(obj => {
      if (obj.type === 'text') {
        elements.push({
          type: 'text',
          content: obj.text,
          x: obj.left,
          y: obj.top,
          fontSize: obj.fontSize,
          fontWeight: obj.fontWeight,
          color: obj.fill,
          textAlign: obj.textAlign
        })
      } else if (obj.type === 'rect') {
        // Determine if it's an image placeholder or shape
        if (obj.stroke && obj.strokeWidth > 0 && obj.fill === '#f0f0f0') {
          elements.push({
            type: 'image',
            x: obj.left,
            y: obj.top,
            width: obj.width,
            height: obj.height,
            placeholder: 'student_photo'
          })
        } else {
          elements.push({
            type: 'shape',
            shapeType: 'rectangle',
            x: obj.left,
            y: obj.top,
            width: obj.width,
            height: obj.height,
            fillColor: obj.fill,
            strokeColor: obj.stroke,
            strokeWidth: obj.strokeWidth
          })
        }
      }
    })

    return {
      width: canvasSize.width,
      height: canvasSize.height,
      backgroundColor: backgroundColor,
      elements: elements
    }
  }

  const handleSave = async () => {
    if (!templateName.trim()) {
      toast.error('Please enter a template name')
      return
    }

    try {
      setSaving(true)
      
      const templateData = convertCanvasToTemplateData()
      if (!templateData) {
        toast.error('Failed to convert canvas data')
        return
      }

      const payload = {
        name: templateName,
        template_data: templateData,
        is_default: false
      }

      if (isEdit) {
        await templateAPI.update(id, payload)
        toast.success('Template updated successfully')
      } else {
        await templateAPI.create(payload)
        toast.success('Template created successfully')
      }

      navigate('/templates')

    } catch (error) {
      console.error('Error saving template:', error)
      toast.error('Failed to save template')
    } finally {
      setSaving(false)
    }
  }

  const handlePreview = async () => {
    const templateData = convertCanvasToTemplateData()
    if (!templateData) {
      toast.error('Failed to generate preview')
      return
    }

    try {
      const sampleStudent = {
        first_name: 'John',
        last_name: 'Doe',
        student_id: 'STU001',
        course: 'Computer Science',
        year_of_study: 3,
        email: '<EMAIL>'
      }

      const response = await cardAPI.preview({
        studentData: sampleStudent,
        templateData: templateData
      })

      // Open preview in new window
      window.open(`http://localhost:5001${response.data.previewUrl}`, '_blank')

    } catch (error) {
      console.error('Error generating preview:', error)
      toast.error('Failed to generate preview')
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="card p-6">
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/templates')}
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEdit ? 'Edit Template' : 'Create Template'}
            </h1>
            <p className="text-gray-600">Design your ID card template</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handlePreview}
            className="btn-secondary"
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Template'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Tools Panel */}
        <div className="space-y-6">
          {/* Template Settings */}
          <div className="card p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Template Settings</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Template Name
                </label>
                <input
                  type="text"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  className="input"
                  placeholder="Enter template name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Canvas Size
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={canvasSize.width}
                    onChange={(e) => updateCanvasSize({ ...canvasSize, width: parseInt(e.target.value) || 400 })}
                    className="input"
                    placeholder="Width"
                  />
                  <input
                    type="number"
                    value={canvasSize.height}
                    onChange={(e) => updateCanvasSize({ ...canvasSize, height: parseInt(e.target.value) || 250 })}
                    className="input"
                    placeholder="Height"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Background Color
                </label>
                <input
                  type="color"
                  value={backgroundColor}
                  onChange={(e) => updateBackgroundColor(e.target.value)}
                  className="w-full h-10 rounded border border-gray-300"
                />
              </div>
            </div>
          </div>

          {/* Tools */}
          <div className="card p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Tools</h3>
            <div className="space-y-2">
              <button
                onClick={addTextElement}
                className="w-full btn-secondary justify-start"
              >
                <Type className="h-4 w-4 mr-2" />
                Add Text
              </button>
              <button
                onClick={addImagePlaceholder}
                className="w-full btn-secondary justify-start"
              >
                <Image className="h-4 w-4 mr-2" />
                Add Image
              </button>
              <button
                onClick={addShapeElement}
                className="w-full btn-secondary justify-start"
              >
                <Square className="h-4 w-4 mr-2" />
                Add Shape
              </button>
            </div>
          </div>

          {/* Placeholders */}
          <div className="card p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Available Placeholders</h3>
            <div className="space-y-2 text-sm">
              <div className="p-2 bg-gray-50 rounded font-mono">{'{name}'}</div>
              <div className="p-2 bg-gray-50 rounded font-mono">{'{student_id}'}</div>
              <div className="p-2 bg-gray-50 rounded font-mono">{'{course}'}</div>
              <div className="p-2 bg-gray-50 rounded font-mono">{'{email}'}</div>
              <div className="p-2 bg-gray-50 rounded font-mono">{'{address}'}</div>
            </div>
          </div>
        </div>

        {/* Canvas Area */}
        <div className="lg:col-span-3">
          <div className="card p-6">
            <div className="flex justify-center">
              <div className="border border-gray-300 shadow-lg">
                <canvas
                  ref={canvasRef}
                  width={canvasSize.width}
                  height={canvasSize.height}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
