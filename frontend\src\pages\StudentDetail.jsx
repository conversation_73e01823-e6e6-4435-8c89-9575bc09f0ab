import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom'
import { ArrowLeft, Edit, CreditCard, Mail, Phone, MapPin, Calendar, GraduationCap } from 'lucide-react'
import { studentAPI, cardAPI } from '../services/api'
import toast from 'react-hot-toast'

export default function StudentDetail() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [student, setStudent] = useState(null)
  const [cardHistory, setCardHistory] = useState([])
  const [loading, setLoading] = useState(true)
  const [generatingCard, setGeneratingCard] = useState(false)

  useEffect(() => {
    fetchStudentData()
  }, [id])

  const fetchStudentData = async () => {
    try {
      setLoading(true)
      
      // Fetch student details
      const studentResponse = await studentAPI.getById(id)
      setStudent(studentResponse.data)
      
      // Fetch card history
      const cardResponse = await studentAPI.getCardHistory(id)
      setCardHistory(cardResponse.data)
      
    } catch (error) {
      console.error('Error fetching student data:', error)
      toast.error('Failed to load student data')
      navigate('/students')
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateCard = async () => {
    try {
      setGeneratingCard(true)
      const response = await cardAPI.generate(id)
      toast.success('ID card generated successfully!')
      
      // Refresh card history
      const cardResponse = await studentAPI.getCardHistory(id)
      setCardHistory(cardResponse.data)
      
    } catch (error) {
      console.error('Error generating card:', error)
      toast.error('Failed to generate ID card')
    } finally {
      setGeneratingCard(false)
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'inactive':
        return 'bg-gray-100 text-gray-800'
      case 'graduated':
        return 'bg-blue-100 text-blue-800'
      case 'suspended':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="card p-6">
            <div className="flex items-start gap-6">
              <div className="h-32 w-32 bg-gray-200 rounded-lg"></div>
              <div className="flex-1 space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!student) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Student not found</h3>
        <p className="text-gray-500 mt-2">The student you're looking for doesn't exist.</p>
        <Link to="/students" className="btn-primary mt-4">
          Back to Students
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/students')}
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {student.name}
            </h1>
            <p className="text-gray-600">Student ID: {student.student_id}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Link
            to={`/students/${id}/edit`}
            className="btn-secondary"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Link>
          <button
            onClick={handleGenerateCard}
            disabled={generatingCard}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <CreditCard className="h-4 w-4 mr-2" />
            {generatingCard ? 'Generating...' : 'Generate ID Card'}
          </button>
        </div>
      </div>

      {/* Student Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2">
          <div className="card p-6">
            <div className="flex items-start gap-6">
              {/* Student Icon */}
              <div className="flex-shrink-0">
                <div className="h-32 w-32 rounded-lg bg-blue-100 flex items-center justify-center border-2 border-blue-200">
                  <span className="text-3xl font-bold text-blue-600">
                    {student.name?.[0] || 'S'}
                  </span>
                </div>
              </div>

              {/* Details */}
              <div className="flex-1 space-y-4">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    {student.name}
                  </h2>
                  <p className="text-gray-600 mt-1">ID: {student.student_id}</p>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{student.email}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <GraduationCap className="h-4 w-4" />
                    <span>{student.course}</span>
                  </div>
                </div>

                {student.address && (
                  <div className="flex items-start gap-2 text-gray-600">
                    <MapPin className="h-4 w-4 mt-0.5" />
                    <span>{student.address}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="space-y-6">
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Student Information</h3>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Student ID</span>
                <p className="text-gray-900">{student.student_id}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Course</span>
                <p className="text-gray-900">{student.course}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Email</span>
                <p className="text-gray-900">{student.email}</p>
              </div>
              {student.address && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Address</span>
                  <p className="text-gray-900">{student.address}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Card History */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">ID Card History</h3>
        </div>
        <div className="card-content">
          {cardHistory.length > 0 ? (
            <div className="space-y-4">
              {cardHistory.map((card) => (
                <div key={card.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">
                      Generated using {card.template_name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatDate(card.generated_at)}
                    </p>
                  </div>
                  {card.card_url && (
                    <a
                      href={`http://localhost:5001${card.card_url}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn-secondary btn-sm"
                    >
                      View Card
                    </a>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No ID cards generated</h3>
              <p className="mt-1 text-sm text-gray-500">
                Generate the first ID card for this student.
              </p>
              <div className="mt-6">
                <button
                  onClick={handleGenerateCard}
                  disabled={generatingCard}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  {generatingCard ? 'Generating...' : 'Generate ID Card'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
