const { initializeDatabase, getDatabase, closeDatabase } = require('../config/database');
const predefinedTemplates = require('../data/predefinedTemplates');

async function populateTemplates() {
  try {
    console.log('Populating predefined templates...');
    await initializeDatabase();
    
    const db = getDatabase();
    
    // Clear existing templates except default
    await new Promise((resolve, reject) => {
      db.run('DELETE FROM card_templates WHERE is_default = 0', (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    // Insert predefined templates
    for (const template of predefinedTemplates) {
      await new Promise((resolve, reject) => {
        const query = `
          INSERT INTO card_templates (name, template_data, is_default)
          VALUES (?, ?, ?)
        `;
        
        db.run(query, [
          template.name,
          JSON.stringify(template.template_data),
          0 // Not default, these are selectable templates
        ], function(err) {
          if (err) {
            console.error(`Error inserting template ${template.name}:`, err);
            reject(err);
          } else {
            console.log(`✓ Inserted template: ${template.name}`);
            resolve();
          }
        });
      });
    }
    
    console.log(`\n🎉 Successfully populated ${predefinedTemplates.length} predefined templates!`);
    console.log('\nAvailable templates:');
    predefinedTemplates.forEach((template, index) => {
      console.log(`${index + 1}. ${template.name} (${template.category})`);
    });
    
  } catch (error) {
    console.error('Error populating templates:', error);
    process.exit(1);
  } finally {
    await closeDatabase();
  }
}

// Run the script if executed directly
if (require.main === module) {
  populateTemplates();
}

module.exports = { populateTemplates };
