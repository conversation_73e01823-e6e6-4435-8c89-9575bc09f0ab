import { useState, useEffect } from 'react'
import { Link, useSearchParams } from 'react-router-dom'
import { Plus, Search, Filter, Edit, Trash2, Eye, CreditCard } from 'lucide-react'
import { studentAPI } from '../services/api'
import toast from 'react-hot-toast'

export default function Students() {
  const [students, setStudents] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    course: '',
    year: '',
    status: ''
  })
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalStudents: 0
  })
  const [searchParams, setSearchParams] = useSearchParams()

  useEffect(() => {
    // Get initial filters from URL params
    const status = searchParams.get('status')
    if (status) {
      setFilters(prev => ({ ...prev, status }))
    }
    fetchStudents()
  }, [searchParams])

  const fetchStudents = async (page = 1, search = '', filterParams = {}) => {
    try {
      setLoading(true)
      
      let response
      if (search) {
        response = await studentAPI.search(search)
        setStudents(response.data)
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalStudents: response.data.length
        })
      } else if (filterParams.course) {
        response = await studentAPI.filterByCourse(filterParams.course)
        setStudents(response.data)
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalStudents: response.data.length
        })
      } else if (filterParams.year) {
        response = await studentAPI.filterByYear(filterParams.year)
        setStudents(response.data)
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalStudents: response.data.length
        })
      } else if (filterParams.status) {
        response = await studentAPI.filterByStatus(filterParams.status)
        setStudents(response.data)
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalStudents: response.data.length
        })
      } else {
        response = await studentAPI.getAll({ page, limit: 10 })
        setStudents(response.data.students || [])
        setPagination(response.data.pagination || {})
      }
    } catch (error) {
      console.error('Error fetching students:', error)
      toast.error('Failed to load students')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e) => {
    e.preventDefault()
    fetchStudents(1, searchTerm)
  }

  const handleFilterChange = (filterType, value) => {
    const newFilters = { ...filters, [filterType]: value }
    setFilters(newFilters)
    
    // Clear other filters when one is selected
    const activeFilter = { [filterType]: value }
    fetchStudents(1, '', activeFilter)
  }

  const clearFilters = () => {
    setFilters({ course: '', year: '', status: '' })
    setSearchTerm('')
    setSearchParams({})
    fetchStudents()
  }

  const handleDelete = async (id, studentName) => {
    if (!window.confirm(`Are you sure you want to delete ${studentName}?`)) {
      return
    }

    try {
      await studentAPI.delete(id)
      toast.success('Student deleted successfully')
      fetchStudents(pagination.currentPage)
    } catch (error) {
      console.error('Error deleting student:', error)
      toast.error('Failed to delete student')
    }
  }

  const handlePageChange = (page) => {
    fetchStudents(page, searchTerm, filters)
  }

  // Get unique values for filter dropdowns
  const uniqueCourses = [...new Set(students.map(s => s.course).filter(Boolean))]
  const uniqueYears = [...new Set(students.map(s => s.year_of_study).filter(Boolean))]

  if (loading && students.length === 0) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="card p-6">
            <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Students</h1>
          <p className="text-gray-600">Manage student records and information</p>
        </div>
        <Link to="/students/new" className="btn-primary">
          <Plus className="h-4 w-4 mr-2" />
          Add Student
        </Link>
      </div>

      {/* Search and Filters */}
      <div className="card p-6">
        <div className="space-y-4">
          {/* Search */}
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search students by name, ID, or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input pl-10"
                />
              </div>
            </div>
            <button type="submit" className="btn-primary">
              Search
            </button>
          </form>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filters:</span>
            </div>
            
            <select
              value={filters.course}
              onChange={(e) => handleFilterChange('course', e.target.value)}
              className="input w-auto"
            >
              <option value="">All Courses</option>
              {uniqueCourses.map(course => (
                <option key={course} value={course}>{course}</option>
              ))}
            </select>

            <select
              value={filters.year}
              onChange={(e) => handleFilterChange('year', e.target.value)}
              className="input w-auto"
            >
              <option value="">All Years</option>
              {uniqueYears.map(year => (
                <option key={year} value={year}>Year {year}</option>
              ))}
            </select>

            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="input w-auto"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="graduated">Graduated</option>
              <option value="suspended">Suspended</option>
            </select>

            {(filters.course || filters.year || filters.status || searchTerm) && (
              <button onClick={clearFilters} className="btn-secondary btn-sm">
                Clear Filters
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Students Table */}
      <div className="card">
        <div className="card-content p-0">
          {students.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <table className="table">
                  <thead className="table-header">
                    <tr>
                      <th className="table-head">Photo</th>
                      <th className="table-head">Student ID</th>
                      <th className="table-head">Name</th>
                      <th className="table-head">Email</th>
                      <th className="table-head">Course</th>
                      <th className="table-head">Year</th>
                      <th className="table-head">Status</th>
                      <th className="table-head">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {students.map((student) => (
                      <tr key={student.id} className="table-row">
                        <td className="table-cell">
                          {student.photo_url ? (
                            <img
                              src={`http://localhost:5001${student.photo_url}`}
                              alt={`${student.first_name} ${student.last_name}`}
                              className="h-10 w-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-600">
                                {student.first_name?.[0]}{student.last_name?.[0]}
                              </span>
                            </div>
                          )}
                        </td>
                        <td className="table-cell font-medium">{student.student_id}</td>
                        <td className="table-cell">
                          {student.first_name} {student.last_name}
                        </td>
                        <td className="table-cell text-gray-600">{student.email}</td>
                        <td className="table-cell">{student.course}</td>
                        <td className="table-cell">{student.year_of_study}</td>
                        <td className="table-cell">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            student.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : student.status === 'inactive'
                              ? 'bg-gray-100 text-gray-800'
                              : student.status === 'graduated'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {student.status}
                          </span>
                        </td>
                        <td className="table-cell">
                          <div className="flex items-center gap-2">
                            <Link
                              to={`/students/${student.id}`}
                              className="text-gray-600 hover:text-gray-900"
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </Link>
                            <Link
                              to={`/students/${student.id}/edit`}
                              className="text-blue-600 hover:text-blue-900"
                              title="Edit"
                            >
                              <Edit className="h-4 w-4" />
                            </Link>
                            <Link
                              to={`/template-gallery?studentId=${student.id}`}
                              className="text-green-600 hover:text-green-900"
                              title="Generate ID Card"
                            >
                              <CreditCard className="h-4 w-4" />
                            </Link>
                            <button
                              onClick={() => handleDelete(student.id, `${student.first_name} ${student.last_name}`)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      Showing {((pagination.currentPage - 1) * 10) + 1} to {Math.min(pagination.currentPage * 10, pagination.totalStudents)} of {pagination.totalStudents} students
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handlePageChange(pagination.currentPage - 1)}
                        disabled={!pagination.hasPrev}
                        className="btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      <span className="text-sm text-gray-700">
                        Page {pagination.currentPage} of {pagination.totalPages}
                      </span>
                      <button
                        onClick={() => handlePageChange(pagination.currentPage + 1)}
                        disabled={!pagination.hasNext}
                        className="btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No students found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || filters.course || filters.year || filters.status
                  ? 'Try adjusting your search or filters.'
                  : 'Get started by adding a new student.'}
              </p>
              {!searchTerm && !filters.course && !filters.year && !filters.status && (
                <div className="mt-6">
                  <Link to="/students/new" className="btn-primary">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Student
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
