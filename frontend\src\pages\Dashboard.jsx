import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Users, CreditCard, Layout as LayoutIcon, TrendingUp } from 'lucide-react'
import { studentAPI, templateAPI } from '../services/api'
import toast from 'react-hot-toast'

export default function Dashboard() {
  const [stats, setStats] = useState({
    totalStudents: 0,
    activeStudents: 0,
    totalTemplates: 0,
    cardsGenerated: 0
  })
  const [recentStudents, setRecentStudents] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch students data
      const studentsResponse = await studentAPI.getAll({ limit: 5 })
      const students = studentsResponse.data.students || []
      const totalStudents = studentsResponse.data.pagination?.totalStudents || 0
      
      // Count active students
      const activeStudents = students.filter(s => s.status === 'active').length
      
      // Fetch templates
      const templatesResponse = await templateAPI.getAll()
      const totalTemplates = templatesResponse.data.length || 0
      
      setStats({
        totalStudents,
        activeStudents,
        totalTemplates,
        cardsGenerated: 0 // This would need a separate API endpoint
      })
      
      setRecentStudents(students)
      
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      toast.error('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: 'Total Students',
      value: stats.totalStudents,
      icon: Users,
      color: 'bg-blue-500',
      link: '/students'
    },
    {
      title: 'Active Students',
      value: stats.activeStudents,
      icon: TrendingUp,
      color: 'bg-green-500',
      link: '/students?status=active'
    },
    {
      title: 'Templates',
      value: stats.totalTemplates,
      icon: LayoutIcon,
      color: 'bg-purple-500',
      link: '/templates'
    },
    {
      title: 'Cards Generated',
      value: stats.cardsGenerated,
      icon: CreditCard,
      color: 'bg-orange-500',
      link: '/card-generator'
    }
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="card p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to the Student Management System</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => (
          <Link
            key={stat.title}
            to={stat.link}
            className="card p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <div className={`p-3 rounded-lg ${stat.color}`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Recent Students */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold">Recent Students</h2>
            <Link to="/students" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              View all
            </Link>
          </div>
        </div>
        <div className="card-content">
          {recentStudents.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-head">Student ID</th>
                    <th className="table-head">Name</th>
                    <th className="table-head">Course</th>
                    <th className="table-head">Year</th>
                    <th className="table-head">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {recentStudents.map((student) => (
                    <tr key={student.id} className="table-row">
                      <td className="table-cell font-medium">{student.student_id}</td>
                      <td className="table-cell">
                        {student.first_name} {student.last_name}
                      </td>
                      <td className="table-cell">{student.course}</td>
                      <td className="table-cell">{student.year_of_study}</td>
                      <td className="table-cell">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          student.status === 'active' 
                            ? 'bg-green-100 text-green-800'
                            : student.status === 'inactive'
                            ? 'bg-gray-100 text-gray-800'
                            : student.status === 'graduated'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {student.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No students</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by adding a new student.</p>
              <div className="mt-6">
                <Link to="/students/new" className="btn-primary">
                  Add Student
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <Link to="/students/new" className="card p-6 hover:shadow-md transition-shadow">
          <div className="text-center">
            <Users className="mx-auto h-8 w-8 text-primary-600" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Add Student</h3>
            <p className="mt-1 text-sm text-gray-500">Register a new student</p>
          </div>
        </Link>
        
        <Link to="/templates/new" className="card p-6 hover:shadow-md transition-shadow">
          <div className="text-center">
            <LayoutIcon className="mx-auto h-8 w-8 text-primary-600" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Create Template</h3>
            <p className="mt-1 text-sm text-gray-500">Design a new ID card template</p>
          </div>
        </Link>
        
        <Link to="/card-generator" className="card p-6 hover:shadow-md transition-shadow">
          <div className="text-center">
            <CreditCard className="mx-auto h-8 w-8 text-primary-600" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Generate Cards</h3>
            <p className="mt-1 text-sm text-gray-500">Create student ID cards</p>
          </div>
        </Link>
      </div>
    </div>
  )
}
