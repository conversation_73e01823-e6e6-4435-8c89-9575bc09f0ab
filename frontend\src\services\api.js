import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5001/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Student API
export const studentAPI = {
  // Get all students with pagination
  getAll: (params = {}) => api.get('/students', { params }),
  
  // Get student by ID
  getById: (id) => api.get(`/students/${id}`),
  
  // Create new student
  create: (data) => {
    const formData = new FormData()
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key])
      }
    })
    return api.post('/students', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // Update student
  update: (id, data) => {
    const formData = new FormData()
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key])
      }
    })
    return api.put(`/students/${id}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  
  // Delete student
  delete: (id) => api.delete(`/students/${id}`),
  
  // Search students
  search: (query) => api.get(`/students/search/${encodeURIComponent(query)}`),
  
  // Filter students
  filterByCourse: (course) => api.get(`/students/filter/course/${encodeURIComponent(course)}`),
  filterByYear: (year) => api.get(`/students/filter/year/${year}`),
  filterByStatus: (status) => api.get(`/students/filter/status/${status}`),
  
  // Get card history
  getCardHistory: (id) => api.get(`/students/${id}/card-history`),
}

// Template API
export const templateAPI = {
  // Get all templates
  getAll: () => api.get('/cards/templates'),
  
  // Get template by ID
  getById: (id) => api.get(`/cards/templates/${id}`),
  
  // Create new template
  create: (data) => api.post('/cards/templates', data),
  
  // Update template
  update: (id, data) => api.put(`/cards/templates/${id}`, data),
  
  // Delete template
  delete: (id) => api.delete(`/cards/templates/${id}`),
}

// Card API
export const cardAPI = {
  // Generate card with default template
  generate: (studentId) => api.post(`/cards/generate/${studentId}`),
  
  // Generate card with specific template
  generateWithTemplate: (studentId, templateId) => 
    api.post(`/cards/generate/${studentId}/template/${templateId}`),
  
  // Get generated cards for student
  getGenerated: (studentId) => api.get(`/cards/generated/${studentId}`),
  
  // Delete generated card
  deleteGenerated: (id) => api.delete(`/cards/generated/${id}`),
  
  // Preview card
  preview: (data) => api.post('/cards/preview', data),
}

// Health check
export const healthAPI = {
  check: () => api.get('/health'),
}

export default api
