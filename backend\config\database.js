const sqlite3 = require('sqlite3').verbose();
const mysql = require('mysql2/promise');
const path = require('path');
const fs = require('fs');

let db;
let connection; // For MySQL

// Initialize database connection
function initializeDatabase() {
  return new Promise((resolve, reject) => {
    const dbPath = process.env.DB_PATH || './database/students.db';
    const dbDir = path.dirname(dbPath);
    
    // Create database directory if it doesn't exist
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
    
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
        reject(err);
      } else {
        console.log('Connected to SQLite database');
        createTables()
          .then(resolve)
          .catch(reject);
      }
    });
  });
}

// Create database tables
function createTables() {
  return new Promise((resolve, reject) => {
    const createStudentsTable = `
      CREATE TABLE IF NOT EXISTS students (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id VARCHAR(20) UNIQUE NOT NULL,
        name VARCHAR(100) NOT NULL,
        course VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        address TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    const createCardTemplatesTable = `
      CREATE TABLE IF NOT EXISTS card_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL,
        template_data TEXT NOT NULL,
        is_default BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    const createGeneratedCardsTable = `
      CREATE TABLE IF NOT EXISTS generated_cards (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        student_id INTEGER NOT NULL,
        template_id INTEGER NOT NULL,
        card_url VARCHAR(255),
        generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students (id),
        FOREIGN KEY (template_id) REFERENCES card_templates (id)
      )
    `;
    
    db.serialize(() => {
      db.run(createStudentsTable, (err) => {
        if (err) {
          console.error('Error creating students table:', err);
          reject(err);
          return;
        }
      });
      
      db.run(createCardTemplatesTable, (err) => {
        if (err) {
          console.error('Error creating card_templates table:', err);
          reject(err);
          return;
        }
      });
      
      db.run(createGeneratedCardsTable, (err) => {
        if (err) {
          console.error('Error creating generated_cards table:', err);
          reject(err);
          return;
        }
        
        // Insert default template
        insertDefaultTemplate()
          .then(() => {
            console.log('Database tables created successfully');
            resolve();
          })
          .catch(reject);
      });
    });
  });
}

// Insert default card template
function insertDefaultTemplate() {
  return new Promise((resolve, reject) => {
    const defaultTemplate = {
      width: 400,
      height: 250,
      backgroundColor: '#ffffff',
      elements: [
        {
          type: 'text',
          content: 'STUDENT ID CARD',
          x: 200,
          y: 30,
          fontSize: 18,
          fontWeight: 'bold',
          textAlign: 'center',
          color: '#333333'
        },
        {
          type: 'image',
          x: 30,
          y: 60,
          width: 80,
          height: 100,
          placeholder: 'student_photo'
        },
        {
          type: 'text',
          content: '{name}',
          x: 50,
          y: 80,
          fontSize: 16,
          fontWeight: 'bold',
          color: '#333333'
        },
        {
          type: 'text',
          content: 'ID: {student_id}',
          x: 50,
          y: 105,
          fontSize: 12,
          color: '#666666'
        },
        {
          type: 'text',
          content: 'Course: {course}',
          x: 50,
          y: 125,
          fontSize: 12,
          color: '#666666'
        },
        {
          type: 'text',
          content: 'Email: {email}',
          x: 50,
          y: 145,
          fontSize: 10,
          color: '#666666'
        },
        {
          type: 'text',
          content: 'Address: {address}',
          x: 50,
          y: 165,
          fontSize: 10,
          color: '#666666'
        }
      ]
    };
    
    const checkQuery = 'SELECT COUNT(*) as count FROM card_templates WHERE is_default = 1';
    db.get(checkQuery, (err, row) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (row.count === 0) {
        const insertQuery = `
          INSERT INTO card_templates (name, template_data, is_default)
          VALUES (?, ?, ?)
        `;
        
        db.run(insertQuery, [
          'Default Template',
          JSON.stringify(defaultTemplate),
          1
        ], (err) => {
          if (err) {
            reject(err);
          } else {
            console.log('Default template inserted');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  });
}

// Get database instance
function getDatabase() {
  return db;
}

// Close database connection
function closeDatabase() {
  return new Promise((resolve) => {
    if (db) {
      db.close((err) => {
        if (err) {
          console.error('Error closing database:', err);
        } else {
          console.log('Database connection closed');
        }
        resolve();
      });
    } else {
      resolve();
    }
  });
}

module.exports = {
  initializeDatabase,
  getDatabase,
  closeDatabase
};
