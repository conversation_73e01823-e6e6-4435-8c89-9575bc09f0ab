{"version": 3, "sources": ["../../lodash/_listCacheClear.js", "../../lodash/eq.js", "../../lodash/_assocIndexOf.js", "../../lodash/_listCacheDelete.js", "../../lodash/_listCacheGet.js", "../../lodash/_listCacheHas.js", "../../lodash/_listCacheSet.js", "../../lodash/_ListCache.js", "../../lodash/_stackClear.js", "../../lodash/_stackDelete.js", "../../lodash/_stackGet.js", "../../lodash/_stackHas.js", "../../lodash/_freeGlobal.js", "../../lodash/_root.js", "../../lodash/_Symbol.js", "../../lodash/_getRawTag.js", "../../lodash/_objectToString.js", "../../lodash/_baseGetTag.js", "../../lodash/isObject.js", "../../lodash/isFunction.js", "../../lodash/_coreJsData.js", "../../lodash/_isMasked.js", "../../lodash/_toSource.js", "../../lodash/_baseIsNative.js", "../../lodash/_getValue.js", "../../lodash/_getNative.js", "../../lodash/_Map.js", "../../lodash/_nativeCreate.js", "../../lodash/_hashClear.js", "../../lodash/_hashDelete.js", "../../lodash/_hashGet.js", "../../lodash/_hashHas.js", "../../lodash/_hashSet.js", "../../lodash/_Hash.js", "../../lodash/_mapCacheClear.js", "../../lodash/_isKeyable.js", "../../lodash/_getMapData.js", "../../lodash/_mapCacheDelete.js", "../../lodash/_mapCacheGet.js", "../../lodash/_mapCacheHas.js", "../../lodash/_mapCacheSet.js", "../../lodash/_MapCache.js", "../../lodash/_stackSet.js", "../../lodash/_Stack.js", "../../lodash/_setCacheAdd.js", "../../lodash/_setCacheHas.js", "../../lodash/_SetCache.js", "../../lodash/_arraySome.js", "../../lodash/_cacheHas.js", "../../lodash/_equalArrays.js", "../../lodash/_Uint8Array.js", "../../lodash/_mapToArray.js", "../../lodash/_setToArray.js", "../../lodash/_equalByTag.js", "../../lodash/_arrayPush.js", "../../lodash/isArray.js", "../../lodash/_baseGetAllKeys.js", "../../lodash/_arrayFilter.js", "../../lodash/stubArray.js", "../../lodash/_getSymbols.js", "../../lodash/_baseTimes.js", "../../lodash/isObjectLike.js", "../../lodash/_baseIsArguments.js", "../../lodash/isArguments.js", "../../lodash/stubFalse.js", "../../lodash/isBuffer.js", "../../lodash/_isIndex.js", "../../lodash/isLength.js", "../../lodash/_baseIsTypedArray.js", "../../lodash/_baseUnary.js", "../../lodash/_nodeUtil.js", "../../lodash/isTypedArray.js", "../../lodash/_arrayLikeKeys.js", "../../lodash/_isPrototype.js", "../../lodash/_overArg.js", "../../lodash/_nativeKeys.js", "../../lodash/_baseKeys.js", "../../lodash/isArrayLike.js", "../../lodash/keys.js", "../../lodash/_getAllKeys.js", "../../lodash/_equalObjects.js", "../../lodash/_DataView.js", "../../lodash/_Promise.js", "../../lodash/_Set.js", "../../lodash/_WeakMap.js", "../../lodash/_getTag.js", "../../lodash/_baseIsEqualDeep.js", "../../lodash/_baseIsEqual.js", "../../lodash/isEqualWith.js", "../../shallowequal/index.js", "../../lodash/_arrayEach.js", "../../lodash/_defineProperty.js", "../../lodash/_baseAssignValue.js", "../../lodash/_assignValue.js", "../../lodash/_copyObject.js", "../../lodash/_baseAssign.js", "../../lodash/_nativeKeysIn.js", "../../lodash/_baseKeysIn.js", "../../lodash/keysIn.js", "../../lodash/_baseAssignIn.js", "../../lodash/_cloneBuffer.js", "../../lodash/_copyArray.js", "../../lodash/_copySymbols.js", "../../lodash/_getPrototype.js", "../../lodash/_getSymbolsIn.js", "../../lodash/_copySymbolsIn.js", "../../lodash/_getAllKeysIn.js", "../../lodash/_initCloneArray.js", "../../lodash/_cloneArrayBuffer.js", "../../lodash/_cloneDataView.js", "../../lodash/_cloneRegExp.js", "../../lodash/_cloneSymbol.js", "../../lodash/_cloneTypedArray.js", "../../lodash/_initCloneByTag.js", "../../lodash/_baseCreate.js", "../../lodash/_initCloneObject.js", "../../lodash/_baseIsMap.js", "../../lodash/isMap.js", "../../lodash/_baseIsSet.js", "../../lodash/isSet.js", "../../lodash/_baseClone.js", "../../lodash/cloneDeep.js", "../../immer/src/utils/errors.ts", "../../immer/src/utils/common.ts", "../../immer/src/utils/plugins.ts", "../../immer/src/core/scope.ts", "../../immer/src/core/finalize.ts", "../../immer/src/core/proxy.ts", "../../immer/src/core/immerClass.ts", "../../immer/src/core/current.ts", "../../immer/src/plugins/es5.ts", "../../immer/src/plugins/patches.ts", "../../immer/src/plugins/mapset.ts", "../../immer/src/plugins/all.ts", "../../immer/src/immer.ts", "../../immer/src/utils/env.ts", "../../nanoid/index.browser.js", "../../tiny-invariant/dist/esm/tiny-invariant.js", "../../@craftjs/utils/src/constants.ts", "../../@craftjs/utils/src/History.ts", "../../@craftjs/utils/src/useMethods.ts", "../../@craftjs/utils/src/getDOMInfo.ts", "../../@craftjs/utils/src/useCollector.tsx", "../../@craftjs/utils/src/getRandomId.ts", "../../@craftjs/utils/src/EventHandlers/interfaces.ts", "../../@craftjs/utils/src/EventHandlers/ConnectorRegistry.ts", "../../@craftjs/utils/src/EventHandlers/EventHandlers.ts", "../../@craftjs/utils/src/EventHandlers/isEventBlockedByDescendant.ts", "../../@craftjs/utils/src/EventHandlers/DerivedEventHandlers.ts", "../../@craftjs/utils/src/EventHandlers/wrapConnectorHooks.tsx", "../../@craftjs/utils/src/RenderIndicator.tsx", "../../@craftjs/utils/src/useEffectOnce.tsx", "../../@craftjs/utils/src/deprecate.ts", "../../@craftjs/utils/src/platform.ts", "../../@craftjs/core/src/nodes/NodeContext.tsx", "../../@craftjs/core/src/editor/EditorContext.tsx", "../../@craftjs/core/src/events/EventContext.ts", "../../@craftjs/core/src/editor/useInternalEditor.ts", "../../@craftjs/core/src/nodes/useInternalNode.ts", "../../@craftjs/core/src/hooks/useNode.ts", "../../@craftjs/core/src/render/SimpleElement.tsx", "../../@craftjs/core/src/render/DefaultRender.tsx", "../../@craftjs/core/src/render/RenderNode.tsx", "../../@craftjs/core/src/nodes/NodeElement.tsx", "../../@craftjs/core/src/nodes/Element.tsx", "../../@craftjs/core/src/nodes/Canvas.tsx", "../../@craftjs/core/src/render/Frame.tsx", "../../@craftjs/core/src/interfaces/nodes.ts", "../../@craftjs/core/src/hooks/useEditor.tsx", "../../@craftjs/core/src/hooks/legacy/connectEditor.tsx", "../../@craftjs/core/src/hooks/legacy/connectNode.tsx", "../../@craftjs/core/src/utils/fromEntries.ts", "../../@craftjs/core/src/utils/getNodesFromSelector.ts", "../../@craftjs/core/src/utils/resolveComponent.ts", "../../@craftjs/core/src/utils/serializeNode.tsx", "../../@craftjs/core/src/editor/NodeHelpers.ts", "../../@craftjs/core/src/events/findPosition.ts", "../../@craftjs/core/src/utils/createNode.ts", "../../@craftjs/core/src/utils/deserializeNode.tsx", "../../@craftjs/core/src/utils/mergeTrees.tsx", "../../@craftjs/core/src/editor/query.tsx", "../../@craftjs/core/src/editor/EventHelpers.ts", "../../@craftjs/core/src/utils/parseNodeFromJSX.tsx", "../../@craftjs/core/src/events/CoreEventHandlers.ts", "../../@craftjs/core/src/events/Positioner.ts", "../../@craftjs/core/src/events/createShadow.ts", "../../@craftjs/core/src/events/DefaultEventHandlers.ts", "../../@craftjs/core/src/events/movePlaceholder.ts", "../../@craftjs/core/src/events/RenderEditorIndicator.tsx", "../../@craftjs/core/src/events/Events.tsx", "../../@craftjs/core/src/editor/store.tsx", "../../@craftjs/core/src/editor/actions.ts", "../../@craftjs/core/src/utils/removeNodeFromEvents.ts", "../../@craftjs/core/src/editor/Editor.tsx", "../../@craftjs/core/src/utils/testHelpers.ts"], "sourcesContent": ["/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n  this.size = 0;\n}\n\nmodule.exports = listCacheClear;\n", "/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\nmodule.exports = eq;\n", "var eq = require('./eq');\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\nmodule.exports = assocIndexOf;\n", "var assocIndexOf = require('./_assocIndexOf');\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  --this.size;\n  return true;\n}\n\nmodule.exports = listCacheDelete;\n", "var assocIndexOf = require('./_assocIndexOf');\n\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  return index < 0 ? undefined : data[index][1];\n}\n\nmodule.exports = listCacheGet;\n", "var assocIndexOf = require('./_assocIndexOf');\n\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction listCacheHas(key) {\n  return assocIndexOf(this.__data__, key) > -1;\n}\n\nmodule.exports = listCacheHas;\n", "var assocIndexOf = require('./_assocIndexOf');\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    ++this.size;\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\nmodule.exports = listCacheSet;\n", "var listCacheClear = require('./_listCacheClear'),\n    listCacheDelete = require('./_listCacheDelete'),\n    listCacheGet = require('./_listCacheGet'),\n    listCacheHas = require('./_listCacheHas'),\n    listCacheSet = require('./_listCacheSet');\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\nmodule.exports = ListCache;\n", "var ListCache = require('./_ListCache');\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache;\n  this.size = 0;\n}\n\nmodule.exports = stackClear;\n", "/**\n * Removes `key` and its value from the stack.\n *\n * @private\n * @name delete\n * @memberOf Stack\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction stackDelete(key) {\n  var data = this.__data__,\n      result = data['delete'](key);\n\n  this.size = data.size;\n  return result;\n}\n\nmodule.exports = stackDelete;\n", "/**\n * Gets the stack value for `key`.\n *\n * @private\n * @name get\n * @memberOf Stack\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction stackGet(key) {\n  return this.__data__.get(key);\n}\n\nmodule.exports = stackGet;\n", "/**\n * Checks if a stack value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Stack\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction stackHas(key) {\n  return this.__data__.has(key);\n}\n\nmodule.exports = stackHas;\n", "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nmodule.exports = freeGlobal;\n", "var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nmodule.exports = root;\n", "var root = require('./_root');\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nmodule.exports = Symbol;\n", "var Symbol = require('./_Symbol');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nmodule.exports = getRawTag;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nmodule.exports = objectToString;\n", "var Symbol = require('./_Symbol'),\n    getRawTag = require('./_getRawTag'),\n    objectToString = require('./_objectToString');\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nmodule.exports = baseGetTag;\n", "/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nmodule.exports = isObject;\n", "var baseGetTag = require('./_baseGetTag'),\n    isObject = require('./isObject');\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\nmodule.exports = isFunction;\n", "var root = require('./_root');\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\nmodule.exports = coreJsData;\n", "var coreJsData = require('./_coreJsData');\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\nmodule.exports = isMasked;\n", "/** Used for built-in method references. */\nvar funcProto = Function.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\nmodule.exports = toSource;\n", "var isFunction = require('./isFunction'),\n    isMasked = require('./_isMasked'),\n    isObject = require('./isObject'),\n    toSource = require('./_toSource');\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = isFunction(value) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\nmodule.exports = baseIsNative;\n", "/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\nmodule.exports = getValue;\n", "var baseIsNative = require('./_baseIsNative'),\n    getValue = require('./_getValue');\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\nmodule.exports = getNative;\n", "var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map');\n\nmodule.exports = Map;\n", "var getNative = require('./_getNative');\n\n/* Built-in method references that are verified to be native. */\nvar nativeCreate = getNative(Object, 'create');\n\nmodule.exports = nativeCreate;\n", "var nativeCreate = require('./_nativeCreate');\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n  this.size = 0;\n}\n\nmodule.exports = hashClear;\n", "/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  var result = this.has(key) && delete this.__data__[key];\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nmodule.exports = hashDelete;\n", "var nativeCreate = require('./_nativeCreate');\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\nmodule.exports = hashGet;\n", "var nativeCreate = require('./_nativeCreate');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);\n}\n\nmodule.exports = hashHas;\n", "var nativeCreate = require('./_nativeCreate');\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\nmodule.exports = hashSet;\n", "var hashClear = require('./_hashClear'),\n    hashDelete = require('./_hashDelete'),\n    hashGet = require('./_hashGet'),\n    hashHas = require('./_hashHas'),\n    hashSet = require('./_hashSet');\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\nmodule.exports = Hash;\n", "var Hash = require('./_Hash'),\n    ListCache = require('./_ListCache'),\n    Map = require('./_Map');\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\nmodule.exports = mapCacheClear;\n", "/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\nmodule.exports = isKeyable;\n", "var isKeyable = require('./_isKeyable');\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\nmodule.exports = getMapData;\n", "var getMapData = require('./_getMapData');\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  var result = getMapData(this, key)['delete'](key);\n  this.size -= result ? 1 : 0;\n  return result;\n}\n\nmodule.exports = mapCacheDelete;\n", "var getMapData = require('./_getMapData');\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\nmodule.exports = mapCacheGet;\n", "var getMapData = require('./_getMapData');\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\nmodule.exports = mapCacheHas;\n", "var getMapData = require('./_getMapData');\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\nmodule.exports = mapCacheSet;\n", "var mapCacheClear = require('./_mapCacheClear'),\n    mapCacheDelete = require('./_mapCacheDelete'),\n    mapCacheGet = require('./_mapCacheGet'),\n    mapCacheHas = require('./_mapCacheHas'),\n    mapCacheSet = require('./_mapCacheSet');\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\nmodule.exports = MapCache;\n", "var ListCache = require('./_ListCache'),\n    Map = require('./_Map'),\n    MapCache = require('./_MapCache');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * Sets the stack `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Stack\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the stack cache instance.\n */\nfunction stackSet(key, value) {\n  var data = this.__data__;\n  if (data instanceof ListCache) {\n    var pairs = data.__data__;\n    if (!Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {\n      pairs.push([key, value]);\n      this.size = ++data.size;\n      return this;\n    }\n    data = this.__data__ = new MapCache(pairs);\n  }\n  data.set(key, value);\n  this.size = data.size;\n  return this;\n}\n\nmodule.exports = stackSet;\n", "var ListCache = require('./_ListCache'),\n    stackClear = require('./_stackClear'),\n    stackDelete = require('./_stackDelete'),\n    stackGet = require('./_stackGet'),\n    stackHas = require('./_stackHas'),\n    stackSet = require('./_stackSet');\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n\nmodule.exports = Stack;\n", "/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/**\n * Adds `value` to the array cache.\n *\n * @private\n * @name add\n * @memberOf SetCache\n * @alias push\n * @param {*} value The value to cache.\n * @returns {Object} Returns the cache instance.\n */\nfunction setCacheAdd(value) {\n  this.__data__.set(value, HASH_UNDEFINED);\n  return this;\n}\n\nmodule.exports = setCacheAdd;\n", "/**\n * Checks if `value` is in the array cache.\n *\n * @private\n * @name has\n * @memberOf SetCache\n * @param {*} value The value to search for.\n * @returns {number} Returns `true` if `value` is found, else `false`.\n */\nfunction setCacheHas(value) {\n  return this.__data__.has(value);\n}\n\nmodule.exports = setCacheHas;\n", "var MapCache = require('./_MapCache'),\n    setCacheAdd = require('./_setCacheAdd'),\n    setCacheHas = require('./_setCacheHas');\n\n/**\n *\n * Creates an array cache object to store unique values.\n *\n * @private\n * @constructor\n * @param {Array} [values] The values to cache.\n */\nfunction SetCache(values) {\n  var index = -1,\n      length = values == null ? 0 : values.length;\n\n  this.__data__ = new MapCache;\n  while (++index < length) {\n    this.add(values[index]);\n  }\n}\n\n// Add methods to `SetCache`.\nSetCache.prototype.add = SetCache.prototype.push = setCacheAdd;\nSetCache.prototype.has = setCacheHas;\n\nmodule.exports = SetCache;\n", "/**\n * A specialized version of `_.some` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction arraySome(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (predicate(array[index], index, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nmodule.exports = arraySome;\n", "/**\n * Checks if a `cache` value for `key` exists.\n *\n * @private\n * @param {Object} cache The cache to query.\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction cacheHas(cache, key) {\n  return cache.has(key);\n}\n\nmodule.exports = cacheHas;\n", "var SetCache = require('./_SetCache'),\n    arraySome = require('./_arraySome'),\n    cacheHas = require('./_cacheHas');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/**\n * A specialized version of `baseIsEqualDeep` for arrays with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Array} array The array to compare.\n * @param {Array} other The other array to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `array` and `other` objects.\n * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.\n */\nfunction equalArrays(array, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      arrLength = array.length,\n      othLength = other.length;\n\n  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {\n    return false;\n  }\n  // Check that cyclic values are equal.\n  var arrStacked = stack.get(array);\n  var othStacked = stack.get(other);\n  if (arrStacked && othStacked) {\n    return arrStacked == other && othStacked == array;\n  }\n  var index = -1,\n      result = true,\n      seen = (bitmask & COMPARE_UNORDERED_FLAG) ? new SetCache : undefined;\n\n  stack.set(array, other);\n  stack.set(other, array);\n\n  // Ignore non-index properties.\n  while (++index < arrLength) {\n    var arrValue = array[index],\n        othValue = other[index];\n\n    if (customizer) {\n      var compared = isPartial\n        ? customizer(othValue, arrValue, index, other, array, stack)\n        : customizer(arrValue, othValue, index, array, other, stack);\n    }\n    if (compared !== undefined) {\n      if (compared) {\n        continue;\n      }\n      result = false;\n      break;\n    }\n    // Recursively compare arrays (susceptible to call stack limits).\n    if (seen) {\n      if (!arraySome(other, function(othValue, othIndex) {\n            if (!cacheHas(seen, othIndex) &&\n                (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {\n              return seen.push(othIndex);\n            }\n          })) {\n        result = false;\n        break;\n      }\n    } else if (!(\n          arrValue === othValue ||\n            equalFunc(arrValue, othValue, bitmask, customizer, stack)\n        )) {\n      result = false;\n      break;\n    }\n  }\n  stack['delete'](array);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalArrays;\n", "var root = require('./_root');\n\n/** Built-in value references. */\nvar Uint8Array = root.Uint8Array;\n\nmodule.exports = Uint8Array;\n", "/**\n * Converts `map` to its key-value pairs.\n *\n * @private\n * @param {Object} map The map to convert.\n * @returns {Array} Returns the key-value pairs.\n */\nfunction mapToArray(map) {\n  var index = -1,\n      result = Array(map.size);\n\n  map.forEach(function(value, key) {\n    result[++index] = [key, value];\n  });\n  return result;\n}\n\nmodule.exports = mapToArray;\n", "/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nmodule.exports = setToArray;\n", "var Symbol = require('./_Symbol'),\n    Uint8Array = require('./_Uint8Array'),\n    eq = require('./eq'),\n    equalArrays = require('./_equalArrays'),\n    mapToArray = require('./_mapToArray'),\n    setToArray = require('./_setToArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]';\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * A specialized version of `baseIsEqualDeep` for comparing objects of\n * the same `toStringTag`.\n *\n * **Note:** This function only supports comparing values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {string} tag The `toStringTag` of the objects to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n  switch (tag) {\n    case dataViewTag:\n      if ((object.byteLength != other.byteLength) ||\n          (object.byteOffset != other.byteOffset)) {\n        return false;\n      }\n      object = object.buffer;\n      other = other.buffer;\n\n    case arrayBufferTag:\n      if ((object.byteLength != other.byteLength) ||\n          !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\n        return false;\n      }\n      return true;\n\n    case boolTag:\n    case dateTag:\n    case numberTag:\n      // Coerce booleans to `1` or `0` and dates to milliseconds.\n      // Invalid dates are coerced to `NaN`.\n      return eq(+object, +other);\n\n    case errorTag:\n      return object.name == other.name && object.message == other.message;\n\n    case regexpTag:\n    case stringTag:\n      // Coerce regexes to strings and treat strings, primitives and objects,\n      // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\n      // for more details.\n      return object == (other + '');\n\n    case mapTag:\n      var convert = mapToArray;\n\n    case setTag:\n      var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\n      convert || (convert = setToArray);\n\n      if (object.size != other.size && !isPartial) {\n        return false;\n      }\n      // Assume cyclic values are equal.\n      var stacked = stack.get(object);\n      if (stacked) {\n        return stacked == other;\n      }\n      bitmask |= COMPARE_UNORDERED_FLAG;\n\n      // Recursively compare objects (susceptible to call stack limits).\n      stack.set(object, other);\n      var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n      stack['delete'](object);\n      return result;\n\n    case symbolTag:\n      if (symbolValueOf) {\n        return symbolValueOf.call(object) == symbolValueOf.call(other);\n      }\n  }\n  return false;\n}\n\nmodule.exports = equalByTag;\n", "/**\n * Appends the elements of `values` to `array`.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to append.\n * @returns {Array} Returns `array`.\n */\nfunction arrayPush(array, values) {\n  var index = -1,\n      length = values.length,\n      offset = array.length;\n\n  while (++index < length) {\n    array[offset + index] = values[index];\n  }\n  return array;\n}\n\nmodule.exports = arrayPush;\n", "/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\nmodule.exports = isArray;\n", "var arrayPush = require('./_arrayPush'),\n    isArray = require('./isArray');\n\n/**\n * The base implementation of `getAllKeys` and `getAllKeysIn` which uses\n * `keysFunc` and `symbolsFunc` to get the enumerable property names and\n * symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @param {Function} symbolsFunc The function to get the symbols of `object`.\n * @returns {Array} Returns the array of property names and symbols.\n */\nfunction baseGetAllKeys(object, keysFunc, symbolsFunc) {\n  var result = keysFunc(object);\n  return isArray(object) ? result : arrayPush(result, symbolsFunc(object));\n}\n\nmodule.exports = baseGetAllKeys;\n", "/**\n * A specialized version of `_.filter` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction arrayFilter(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      resIndex = 0,\n      result = [];\n\n  while (++index < length) {\n    var value = array[index];\n    if (predicate(value, index, array)) {\n      result[resIndex++] = value;\n    }\n  }\n  return result;\n}\n\nmodule.exports = arrayFilter;\n", "/**\n * This method returns a new empty array.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {Array} Returns the new empty array.\n * @example\n *\n * var arrays = _.times(2, _.stubArray);\n *\n * console.log(arrays);\n * // => [[], []]\n *\n * console.log(arrays[0] === arrays[1]);\n * // => false\n */\nfunction stubArray() {\n  return [];\n}\n\nmodule.exports = stubArray;\n", "var arrayFilter = require('./_arrayFilter'),\n    stubArray = require('./stubArray');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbols = !nativeGetSymbols ? stubArray : function(object) {\n  if (object == null) {\n    return [];\n  }\n  object = Object(object);\n  return arrayFilter(nativeGetSymbols(object), function(symbol) {\n    return propertyIsEnumerable.call(object, symbol);\n  });\n};\n\nmodule.exports = getSymbols;\n", "/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\nmodule.exports = baseTimes;\n", "/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return value != null && typeof value == 'object';\n}\n\nmodule.exports = isObjectLike;\n", "var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]';\n\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */\nfunction baseIsArguments(value) {\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n\nmodule.exports = baseIsArguments;\n", "var baseIsArguments = require('./_baseIsArguments'),\n    isObjectLike = require('./isObjectLike');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nvar isArguments = baseIsArguments(function() { return arguments; }()) ? baseIsArguments : function(value) {\n  return isObjectLike(value) && hasOwnProperty.call(value, 'callee') &&\n    !propertyIsEnumerable.call(value, 'callee');\n};\n\nmodule.exports = isArguments;\n", "/**\n * This method returns `false`.\n *\n * @static\n * @memberOf _\n * @since 4.13.0\n * @category Util\n * @returns {boolean} Returns `false`.\n * @example\n *\n * _.times(2, _.stubFalse);\n * // => [false, false]\n */\nfunction stubFalse() {\n  return false;\n}\n\nmodule.exports = stubFalse;\n", "var root = require('./_root'),\n    stubFalse = require('./stubFalse');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined;\n\n/**\n * Checks if `value` is a buffer.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.\n * @example\n *\n * _.isBuffer(new Buffer(2));\n * // => true\n *\n * _.isBuffer(new Uint8Array(2));\n * // => false\n */\nvar isBuffer = nativeIsBuffer || stubFalse;\n\nmodule.exports = isBuffer;\n", "/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  var type = typeof value;\n  length = length == null ? MAX_SAFE_INTEGER : length;\n\n  return !!length &&\n    (type == 'number' ||\n      (type != 'symbol' && reIsUint.test(value))) &&\n        (value > -1 && value % 1 == 0 && value < length);\n}\n\nmodule.exports = isIndex;\n", "/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\nmodule.exports = isLength;\n", "var baseGetTag = require('./_baseGetTag'),\n    isLength = require('./isLength'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    funcTag = '[object Function]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    objectTag = '[object Object]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    weakMapTag = '[object WeakMap]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/** Used to identify `toStringTag` values of typed arrays. */\nvar typedArrayTags = {};\ntypedArrayTags[float32Tag] = typedArrayTags[float64Tag] =\ntypedArrayTags[int8Tag] = typedArrayTags[int16Tag] =\ntypedArrayTags[int32Tag] = typedArrayTags[uint8Tag] =\ntypedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] =\ntypedArrayTags[uint32Tag] = true;\ntypedArrayTags[argsTag] = typedArrayTags[arrayTag] =\ntypedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] =\ntypedArrayTags[dataViewTag] = typedArrayTags[dateTag] =\ntypedArrayTags[errorTag] = typedArrayTags[funcTag] =\ntypedArrayTags[mapTag] = typedArrayTags[numberTag] =\ntypedArrayTags[objectTag] = typedArrayTags[regexpTag] =\ntypedArrayTags[setTag] = typedArrayTags[stringTag] =\ntypedArrayTags[weakMapTag] = false;\n\n/**\n * The base implementation of `_.isTypedArray` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n */\nfunction baseIsTypedArray(value) {\n  return isObjectLike(value) &&\n    isLength(value.length) && !!typedArrayTags[baseGetTag(value)];\n}\n\nmodule.exports = baseIsTypedArray;\n", "/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */\nfunction baseUnary(func) {\n  return function(value) {\n    return func(value);\n  };\n}\n\nmodule.exports = baseUnary;\n", "var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Detect free variable `process` from Node.js. */\nvar freeProcess = moduleExports && freeGlobal.process;\n\n/** Used to access faster Node.js helpers. */\nvar nodeUtil = (function() {\n  try {\n    // Use `util.types` for Node.js 10+.\n    var types = freeModule && freeModule.require && freeModule.require('util').types;\n\n    if (types) {\n      return types;\n    }\n\n    // Legacy `process.binding('util')` for Node.js < 10.\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\n  } catch (e) {}\n}());\n\nmodule.exports = nodeUtil;\n", "var baseIsTypedArray = require('./_baseIsTypedArray'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;\n\n/**\n * Checks if `value` is classified as a typed array.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.\n * @example\n *\n * _.isTypedArray(new Uint8Array);\n * // => true\n *\n * _.isTypedArray([]);\n * // => false\n */\nvar isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;\n\nmodule.exports = isTypedArray;\n", "var baseTimes = require('./_baseTimes'),\n    isArguments = require('./isArguments'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isIndex = require('./_isIndex'),\n    isTypedArray = require('./isTypedArray');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  var isArr = isArray(value),\n      isArg = !isArr && isArguments(value),\n      isBuff = !isArr && !isArg && isBuffer(value),\n      isType = !isArr && !isArg && !isBuff && isTypedArray(value),\n      skipIndexes = isArr || isArg || isBuff || isType,\n      result = skipIndexes ? baseTimes(value.length, String) : [],\n      length = result.length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (\n           // Safari 9 has enumerable `arguments.length` in strict mode.\n           key == 'length' ||\n           // Node.js 0.10 has enumerable non-index properties on buffers.\n           (isBuff && (key == 'offset' || key == 'parent')) ||\n           // PhantomJS 2 has enumerable non-index properties on typed arrays.\n           (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||\n           // Skip index properties.\n           isIndex(key, length)\n        ))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nmodule.exports = arrayLikeKeys;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\nmodule.exports = isPrototype;\n", "/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\nmodule.exports = overArg;\n", "var overArg = require('./_overArg');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object);\n\nmodule.exports = nativeKeys;\n", "var isPrototype = require('./_isPrototype'),\n    nativeKeys = require('./_nativeKeys');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseKeys;\n", "var isFunction = require('./isFunction'),\n    isLength = require('./isLength');\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\nmodule.exports = isArrayLike;\n", "var arrayLikeKeys = require('./_arrayLikeKeys'),\n    baseKeys = require('./_baseKeys'),\n    isArrayLike = require('./isArrayLike');\n\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */\nfunction keys(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n\nmodule.exports = keys;\n", "var baseGetAllKeys = require('./_baseGetAllKeys'),\n    getSymbols = require('./_getSymbols'),\n    keys = require('./keys');\n\n/**\n * Creates an array of own enumerable property names and symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names and symbols.\n */\nfunction getAllKeys(object) {\n  return baseGetAllKeys(object, keys, getSymbols);\n}\n\nmodule.exports = getAllKeys;\n", "var getAllKeys = require('./_getAllKeys');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqualDeep` for objects with support for\n * partial deep comparisons.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalObjects(object, other, bitmask, customizer, equalFunc, stack) {\n  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,\n      objProps = getAllKeys(object),\n      objLength = objProps.length,\n      othProps = getAllKeys(other),\n      othLength = othProps.length;\n\n  if (objLength != othLength && !isPartial) {\n    return false;\n  }\n  var index = objLength;\n  while (index--) {\n    var key = objProps[index];\n    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {\n      return false;\n    }\n  }\n  // Check that cyclic values are equal.\n  var objStacked = stack.get(object);\n  var othStacked = stack.get(other);\n  if (objStacked && othStacked) {\n    return objStacked == other && othStacked == object;\n  }\n  var result = true;\n  stack.set(object, other);\n  stack.set(other, object);\n\n  var skipCtor = isPartial;\n  while (++index < objLength) {\n    key = objProps[index];\n    var objValue = object[key],\n        othValue = other[key];\n\n    if (customizer) {\n      var compared = isPartial\n        ? customizer(othValue, objValue, key, other, object, stack)\n        : customizer(objValue, othValue, key, object, other, stack);\n    }\n    // Recursively compare objects (susceptible to call stack limits).\n    if (!(compared === undefined\n          ? (objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack))\n          : compared\n        )) {\n      result = false;\n      break;\n    }\n    skipCtor || (skipCtor = key == 'constructor');\n  }\n  if (result && !skipCtor) {\n    var objCtor = object.constructor,\n        othCtor = other.constructor;\n\n    // Non `Object` object instances with different constructors are not equal.\n    if (objCtor != othCtor &&\n        ('constructor' in object && 'constructor' in other) &&\n        !(typeof objCtor == 'function' && objCtor instanceof objCtor &&\n          typeof othCtor == 'function' && othCtor instanceof othCtor)) {\n      result = false;\n    }\n  }\n  stack['delete'](object);\n  stack['delete'](other);\n  return result;\n}\n\nmodule.exports = equalObjects;\n", "var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView');\n\nmodule.exports = DataView;\n", "var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Promise = getNative(root, 'Promise');\n\nmodule.exports = Promise;\n", "var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Set = getNative(root, 'Set');\n\nmodule.exports = Set;\n", "var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar WeakMap = getNative(root, 'WeakMap');\n\nmodule.exports = WeakMap;\n", "var DataView = require('./_DataView'),\n    Map = require('./_Map'),\n    Promise = require('./_Promise'),\n    Set = require('./_Set'),\n    WeakMap = require('./_WeakMap'),\n    baseGetTag = require('./_baseGetTag'),\n    toSource = require('./_toSource');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]',\n    objectTag = '[object Object]',\n    promiseTag = '[object Promise]',\n    setTag = '[object Set]',\n    weakMapTag = '[object WeakMap]';\n\nvar dataViewTag = '[object DataView]';\n\n/** Used to detect maps, sets, and weakmaps. */\nvar dataViewCtorString = toSource(DataView),\n    mapCtorString = toSource(Map),\n    promiseCtorString = toSource(Promise),\n    setCtorString = toSource(Set),\n    weakMapCtorString = toSource(WeakMap);\n\n/**\n * Gets the `toStringTag` of `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nvar getTag = baseGetTag;\n\n// Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.\nif ((DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag) ||\n    (Map && getTag(new Map) != mapTag) ||\n    (Promise && getTag(Promise.resolve()) != promiseTag) ||\n    (Set && getTag(new Set) != setTag) ||\n    (WeakMap && getTag(new WeakMap) != weakMapTag)) {\n  getTag = function(value) {\n    var result = baseGetTag(value),\n        Ctor = result == objectTag ? value.constructor : undefined,\n        ctorString = Ctor ? toSource(Ctor) : '';\n\n    if (ctorString) {\n      switch (ctorString) {\n        case dataViewCtorString: return dataViewTag;\n        case mapCtorString: return mapTag;\n        case promiseCtorString: return promiseTag;\n        case setCtorString: return setTag;\n        case weakMapCtorString: return weakMapTag;\n      }\n    }\n    return result;\n  };\n}\n\nmodule.exports = getTag;\n", "var Stack = require('./_Stack'),\n    equalArrays = require('./_equalArrays'),\n    equalByTag = require('./_equalByTag'),\n    equalObjects = require('./_equalObjects'),\n    getTag = require('./_getTag'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isTypedArray = require('./isTypedArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    objectTag = '[object Object]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * A specialized version of `baseIsEqual` for arrays and objects which performs\n * deep comparisons and tracks traversed objects enabling objects with circular\n * references to be compared.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} [stack] Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {\n  var objIsArr = isArray(object),\n      othIsArr = isArray(other),\n      objTag = objIsArr ? arrayTag : getTag(object),\n      othTag = othIsArr ? arrayTag : getTag(other);\n\n  objTag = objTag == argsTag ? objectTag : objTag;\n  othTag = othTag == argsTag ? objectTag : othTag;\n\n  var objIsObj = objTag == objectTag,\n      othIsObj = othTag == objectTag,\n      isSameTag = objTag == othTag;\n\n  if (isSameTag && isBuffer(object)) {\n    if (!isBuffer(other)) {\n      return false;\n    }\n    objIsArr = true;\n    objIsObj = false;\n  }\n  if (isSameTag && !objIsObj) {\n    stack || (stack = new Stack);\n    return (objIsArr || isTypedArray(object))\n      ? equalArrays(object, other, bitmask, customizer, equalFunc, stack)\n      : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);\n  }\n  if (!(bitmask & COMPARE_PARTIAL_FLAG)) {\n    var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),\n        othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');\n\n    if (objIsWrapped || othIsWrapped) {\n      var objUnwrapped = objIsWrapped ? object.value() : object,\n          othUnwrapped = othIsWrapped ? other.value() : other;\n\n      stack || (stack = new Stack);\n      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);\n    }\n  }\n  if (!isSameTag) {\n    return false;\n  }\n  stack || (stack = new Stack);\n  return equalObjects(object, other, bitmask, customizer, equalFunc, stack);\n}\n\nmodule.exports = baseIsEqualDeep;\n", "var baseIsEqualDeep = require('./_baseIsEqualDeep'),\n    isObjectLike = require('./isObjectLike');\n\n/**\n * The base implementation of `_.isEqual` which supports partial comparisons\n * and tracks traversed objects.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Unordered comparison\n *  2 - Partial comparison\n * @param {Function} [customizer] The function to customize comparisons.\n * @param {Object} [stack] Tracks traversed `value` and `other` objects.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n */\nfunction baseIsEqual(value, other, bitmask, customizer, stack) {\n  if (value === other) {\n    return true;\n  }\n  if (value == null || other == null || (!isObjectLike(value) && !isObjectLike(other))) {\n    return value !== value && other !== other;\n  }\n  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);\n}\n\nmodule.exports = baseIsEqual;\n", "var baseIsEqual = require('./_baseIsEqual');\n\n/**\n * This method is like `_.isEqual` except that it accepts `customizer` which\n * is invoked to compare values. If `customizer` returns `undefined`, comparisons\n * are handled by the method instead. The `customizer` is invoked with up to\n * six arguments: (objValue, othValue [, index|key, object, other, stack]).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {Function} [customizer] The function to customize comparisons.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * function isGreeting(value) {\n *   return /^h(?:i|ello)$/.test(value);\n * }\n *\n * function customizer(objValue, othValue) {\n *   if (isGreeting(objValue) && isGreeting(othValue)) {\n *     return true;\n *   }\n * }\n *\n * var array = ['hello', 'goodbye'];\n * var other = ['hi', 'goodbye'];\n *\n * _.isEqualWith(array, other, customizer);\n * // => true\n */\nfunction isEqualWith(value, other, customizer) {\n  customizer = typeof customizer == 'function' ? customizer : undefined;\n  var result = customizer ? customizer(value, other) : undefined;\n  return result === undefined ? baseIsEqual(value, other, undefined, customizer) : !!result;\n}\n\nmodule.exports = isEqualWith;\n", "//\n\nmodule.exports = function shallowEqual(objA, objB, compare, compareContext) {\n  var ret = compare ? compare.call(compareContext, objA, objB) : void 0;\n\n  if (ret !== void 0) {\n    return !!ret;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== \"object\" || !objA || typeof objB !== \"object\" || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = objA[key];\n    var valueB = objB[key];\n\n    ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n\n    if (ret === false || (ret === void 0 && valueA !== valueB)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n", "/**\n * A specialized version of `_.forEach` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns `array`.\n */\nfunction arrayEach(array, iteratee) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (iteratee(array[index], index, array) === false) {\n      break;\n    }\n  }\n  return array;\n}\n\nmodule.exports = arrayEach;\n", "var getNative = require('./_getNative');\n\nvar defineProperty = (function() {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}());\n\nmodule.exports = defineProperty;\n", "var defineProperty = require('./_defineProperty');\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\n\nmodule.exports = baseAssignValue;\n", "var baseAssignValue = require('./_baseAssignValue'),\n    eq = require('./eq');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nmodule.exports = assignValue;\n", "var assignValue = require('./_assignValue'),\n    baseAssignValue = require('./_baseAssignValue');\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n  return object;\n}\n\nmodule.exports = copyObject;\n", "var copyObject = require('./_copyObject'),\n    keys = require('./keys');\n\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssign(object, source) {\n  return object && copyObject(source, keys(source), object);\n}\n\nmodule.exports = baseAssign;\n", "/**\n * This function is like\n * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * except that it includes inherited enumerable properties.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction nativeKeysIn(object) {\n  var result = [];\n  if (object != null) {\n    for (var key in Object(object)) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nmodule.exports = nativeKeysIn;\n", "var isObject = require('./isObject'),\n    isPrototype = require('./_isPrototype'),\n    nativeKeysIn = require('./_nativeKeysIn');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeysIn(object) {\n  if (!isObject(object)) {\n    return nativeKeysIn(object);\n  }\n  var isProto = isPrototype(object),\n      result = [];\n\n  for (var key in object) {\n    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseKeysIn;\n", "var arrayLikeKeys = require('./_arrayLikeKeys'),\n    baseKeysIn = require('./_baseKeysIn'),\n    isArrayLike = require('./isArrayLike');\n\n/**\n * Creates an array of the own and inherited enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keysIn(new Foo);\n * // => ['a', 'b', 'c'] (iteration order is not guaranteed)\n */\nfunction keysIn(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);\n}\n\nmodule.exports = keysIn;\n", "var copyObject = require('./_copyObject'),\n    keysIn = require('./keysIn');\n\n/**\n * The base implementation of `_.assignIn` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssignIn(object, source) {\n  return object && copyObject(source, keysIn(source), object);\n}\n\nmodule.exports = baseAssignIn;\n", "var root = require('./_root');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined;\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {<PERSON>uffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var length = buffer.length,\n      result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n\n  buffer.copy(result);\n  return result;\n}\n\nmodule.exports = cloneBuffer;\n", "/**\n * Copies the values of `source` to `array`.\n *\n * @private\n * @param {Array} source The array to copy values from.\n * @param {Array} [array=[]] The array to copy values to.\n * @returns {Array} Returns `array`.\n */\nfunction copyArray(source, array) {\n  var index = -1,\n      length = source.length;\n\n  array || (array = Array(length));\n  while (++index < length) {\n    array[index] = source[index];\n  }\n  return array;\n}\n\nmodule.exports = copyArray;\n", "var copyObject = require('./_copyObject'),\n    getSymbols = require('./_getSymbols');\n\n/**\n * Copies own symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbols(source, object) {\n  return copyObject(source, getSymbols(source), object);\n}\n\nmodule.exports = copySymbols;\n", "var overArg = require('./_overArg');\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\n\nmodule.exports = getPrototype;\n", "var arrayPush = require('./_arrayPush'),\n    getPrototype = require('./_getPrototype'),\n    getSymbols = require('./_getSymbols'),\n    stubArray = require('./stubArray');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own and inherited enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbolsIn = !nativeGetSymbols ? stubArray : function(object) {\n  var result = [];\n  while (object) {\n    arrayPush(result, getSymbols(object));\n    object = getPrototype(object);\n  }\n  return result;\n};\n\nmodule.exports = getSymbolsIn;\n", "var copyObject = require('./_copyObject'),\n    getSymbolsIn = require('./_getSymbolsIn');\n\n/**\n * Copies own and inherited symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbolsIn(source, object) {\n  return copyObject(source, getSymbolsIn(source), object);\n}\n\nmodule.exports = copySymbolsIn;\n", "var baseGetAllKeys = require('./_baseGetAllKeys'),\n    getSymbolsIn = require('./_getSymbolsIn'),\n    keysIn = require('./keysIn');\n\n/**\n * Creates an array of own and inherited enumerable property names and\n * symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names and symbols.\n */\nfunction getAllKeysIn(object) {\n  return baseGetAllKeys(object, keysIn, getSymbolsIn);\n}\n\nmodule.exports = getAllKeysIn;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Initializes an array clone.\n *\n * @private\n * @param {Array} array The array to clone.\n * @returns {Array} Returns the initialized clone.\n */\nfunction initCloneArray(array) {\n  var length = array.length,\n      result = new array.constructor(length);\n\n  // Add properties assigned by `RegExp#exec`.\n  if (length && typeof array[0] == 'string' && hasOwnProperty.call(array, 'index')) {\n    result.index = array.index;\n    result.input = array.input;\n  }\n  return result;\n}\n\nmodule.exports = initCloneArray;\n", "var Uint8Array = require('./_Uint8Array');\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\nmodule.exports = cloneArrayBuffer;\n", "var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */\nfunction cloneDataView(dataView, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\n\nmodule.exports = cloneDataView;\n", "/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\n\nmodule.exports = cloneRegExp;\n", "var Symbol = require('./_Symbol');\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */\nfunction cloneSymbol(symbol) {\n  return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\n\nmodule.exports = cloneSymbol;\n", "var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\nmodule.exports = cloneTypedArray;\n", "var cloneArrayBuffer = require('./_cloneArrayBuffer'),\n    cloneDataView = require('./_cloneDataView'),\n    cloneRegExp = require('./_cloneRegExp'),\n    cloneSymbol = require('./_cloneSymbol'),\n    cloneTypedArray = require('./_cloneTypedArray');\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/**\n * Initializes an object clone based on its `toStringTag`.\n *\n * **Note:** This function only supports cloning values with tags of\n * `Boolean`, `Date`, `Error`, `Map`, `Number`, `RegExp`, `Set`, or `String`.\n *\n * @private\n * @param {Object} object The object to clone.\n * @param {string} tag The `toStringTag` of the object to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneByTag(object, tag, isDeep) {\n  var Ctor = object.constructor;\n  switch (tag) {\n    case arrayBufferTag:\n      return cloneArrayBuffer(object);\n\n    case boolTag:\n    case dateTag:\n      return new Ctor(+object);\n\n    case dataViewTag:\n      return cloneDataView(object, isDeep);\n\n    case float32Tag: case float64Tag:\n    case int8Tag: case int16Tag: case int32Tag:\n    case uint8Tag: case uint8ClampedTag: case uint16Tag: case uint32Tag:\n      return cloneTypedArray(object, isDeep);\n\n    case mapTag:\n      return new Ctor;\n\n    case numberTag:\n    case stringTag:\n      return new Ctor(object);\n\n    case regexpTag:\n      return cloneRegExp(object);\n\n    case setTag:\n      return new Ctor;\n\n    case symbolTag:\n      return cloneSymbol(object);\n  }\n}\n\nmodule.exports = initCloneByTag;\n", "var isObject = require('./isObject');\n\n/** Built-in value references. */\nvar objectCreate = Object.create;\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = (function() {\n  function object() {}\n  return function(proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object;\n    object.prototype = undefined;\n    return result;\n  };\n}());\n\nmodule.exports = baseCreate;\n", "var baseCreate = require('./_baseCreate'),\n    getPrototype = require('./_getPrototype'),\n    isPrototype = require('./_isPrototype');\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\nmodule.exports = initCloneObject;\n", "var getTag = require('./_getTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]';\n\n/**\n * The base implementation of `_.isMap` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n */\nfunction baseIsMap(value) {\n  return isObjectLike(value) && getTag(value) == mapTag;\n}\n\nmodule.exports = baseIsMap;\n", "var baseIsMap = require('./_baseIsMap'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsMap = nodeUtil && nodeUtil.isMap;\n\n/**\n * Checks if `value` is classified as a `Map` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n * @example\n *\n * _.isMap(new Map);\n * // => true\n *\n * _.isMap(new WeakMap);\n * // => false\n */\nvar isMap = nodeIsMap ? baseUnary(nodeIsMap) : baseIsMap;\n\nmodule.exports = isMap;\n", "var getTag = require('./_getTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar setTag = '[object Set]';\n\n/**\n * The base implementation of `_.isSet` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n */\nfunction baseIsSet(value) {\n  return isObjectLike(value) && getTag(value) == setTag;\n}\n\nmodule.exports = baseIsSet;\n", "var baseIsSet = require('./_baseIsSet'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsSet = nodeUtil && nodeUtil.isSet;\n\n/**\n * Checks if `value` is classified as a `Set` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n * @example\n *\n * _.isSet(new Set);\n * // => true\n *\n * _.isSet(new WeakSet);\n * // => false\n */\nvar isSet = nodeIsSet ? baseUnary(nodeIsSet) : baseIsSet;\n\nmodule.exports = isSet;\n", "var Stack = require('./_Stack'),\n    arrayEach = require('./_arrayEach'),\n    assignValue = require('./_assignValue'),\n    baseAssign = require('./_baseAssign'),\n    baseAssignIn = require('./_baseAssignIn'),\n    cloneBuffer = require('./_cloneBuffer'),\n    copyArray = require('./_copyArray'),\n    copySymbols = require('./_copySymbols'),\n    copySymbolsIn = require('./_copySymbolsIn'),\n    getAllKeys = require('./_getAllKeys'),\n    getAllKeysIn = require('./_getAllKeysIn'),\n    getTag = require('./_getTag'),\n    initCloneArray = require('./_initCloneArray'),\n    initCloneByTag = require('./_initCloneByTag'),\n    initCloneObject = require('./_initCloneObject'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isMap = require('./isMap'),\n    isObject = require('./isObject'),\n    isSet = require('./isSet'),\n    keys = require('./keys'),\n    keysIn = require('./keysIn');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_FLAT_FLAG = 2,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    objectTag = '[object Object]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]',\n    weakMapTag = '[object WeakMap]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/** Used to identify `toStringTag` values supported by `_.clone`. */\nvar cloneableTags = {};\ncloneableTags[argsTag] = cloneableTags[arrayTag] =\ncloneableTags[arrayBufferTag] = cloneableTags[dataViewTag] =\ncloneableTags[boolTag] = cloneableTags[dateTag] =\ncloneableTags[float32Tag] = cloneableTags[float64Tag] =\ncloneableTags[int8Tag] = cloneableTags[int16Tag] =\ncloneableTags[int32Tag] = cloneableTags[mapTag] =\ncloneableTags[numberTag] = cloneableTags[objectTag] =\ncloneableTags[regexpTag] = cloneableTags[setTag] =\ncloneableTags[stringTag] = cloneableTags[symbolTag] =\ncloneableTags[uint8Tag] = cloneableTags[uint8ClampedTag] =\ncloneableTags[uint16Tag] = cloneableTags[uint32Tag] = true;\ncloneableTags[errorTag] = cloneableTags[funcTag] =\ncloneableTags[weakMapTag] = false;\n\n/**\n * The base implementation of `_.clone` and `_.cloneDeep` which tracks\n * traversed objects.\n *\n * @private\n * @param {*} value The value to clone.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Deep clone\n *  2 - Flatten inherited properties\n *  4 - Clone symbols\n * @param {Function} [customizer] The function to customize cloning.\n * @param {string} [key] The key of `value`.\n * @param {Object} [object] The parent object of `value`.\n * @param {Object} [stack] Tracks traversed objects and their clone counterparts.\n * @returns {*} Returns the cloned value.\n */\nfunction baseClone(value, bitmask, customizer, key, object, stack) {\n  var result,\n      isDeep = bitmask & CLONE_DEEP_FLAG,\n      isFlat = bitmask & CLONE_FLAT_FLAG,\n      isFull = bitmask & CLONE_SYMBOLS_FLAG;\n\n  if (customizer) {\n    result = object ? customizer(value, key, object, stack) : customizer(value);\n  }\n  if (result !== undefined) {\n    return result;\n  }\n  if (!isObject(value)) {\n    return value;\n  }\n  var isArr = isArray(value);\n  if (isArr) {\n    result = initCloneArray(value);\n    if (!isDeep) {\n      return copyArray(value, result);\n    }\n  } else {\n    var tag = getTag(value),\n        isFunc = tag == funcTag || tag == genTag;\n\n    if (isBuffer(value)) {\n      return cloneBuffer(value, isDeep);\n    }\n    if (tag == objectTag || tag == argsTag || (isFunc && !object)) {\n      result = (isFlat || isFunc) ? {} : initCloneObject(value);\n      if (!isDeep) {\n        return isFlat\n          ? copySymbolsIn(value, baseAssignIn(result, value))\n          : copySymbols(value, baseAssign(result, value));\n      }\n    } else {\n      if (!cloneableTags[tag]) {\n        return object ? value : {};\n      }\n      result = initCloneByTag(value, tag, isDeep);\n    }\n  }\n  // Check for circular references and return its corresponding clone.\n  stack || (stack = new Stack);\n  var stacked = stack.get(value);\n  if (stacked) {\n    return stacked;\n  }\n  stack.set(value, result);\n\n  if (isSet(value)) {\n    value.forEach(function(subValue) {\n      result.add(baseClone(subValue, bitmask, customizer, subValue, value, stack));\n    });\n  } else if (isMap(value)) {\n    value.forEach(function(subValue, key) {\n      result.set(key, baseClone(subValue, bitmask, customizer, key, value, stack));\n    });\n  }\n\n  var keysFunc = isFull\n    ? (isFlat ? getAllKeysIn : getAllKeys)\n    : (isFlat ? keysIn : keys);\n\n  var props = isArr ? undefined : keysFunc(value);\n  arrayEach(props || value, function(subValue, key) {\n    if (props) {\n      key = subValue;\n      subValue = value[key];\n    }\n    // Recursively populate clone (susceptible to call stack limits).\n    assignValue(result, key, baseClone(subValue, bitmask, customizer, key, value, stack));\n  });\n  return result;\n}\n\nmodule.exports = baseClone;\n", "var baseClone = require('./_baseClone');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */\nfunction cloneDeep(value) {\n  return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);\n}\n\nmodule.exports = cloneDeep;\n", "const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ProxyType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n", "// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n", "import { urlAlphabet } from './url-alphabet/index.js'\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\nlet customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\nlet nanoid = (size = 21) =>\n  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n    byte &= 63\n    if (byte < 36) {\n      id += byte.toString(36)\n    } else if (byte < 62) {\n      id += (byte - 26).toString(36).toUpperCase()\n    } else if (byte > 62) {\n      id += '-'\n    } else {\n      id += '_'\n    }\n    return id\n  }, '')\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\n", "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n", "export const ROOT_NODE = 'ROOT';\nexport const DEPRECATED_ROOT_NODE = 'canvas-ROOT';\n\n// TODO: Use a better way to store/display error messages\nexport const ERROR_NOPARENT = 'Parent id cannot be ommited';\nexport const ERROR_DUPLICATE_NODEID =\n  'Attempting to add a node with duplicated id';\nexport const ERROR_INVALID_NODEID =\n  'Node does not exist, it may have been removed';\nexport const ERROR_TOP_LEVEL_ELEMENT_NO_ID =\n  'A <Element /> that is used inside a User Component must specify an `id` prop, eg: <Element id=\"text_element\">...</Element> ';\nexport const ERROR_MISSING_PLACEHOLDER_PLACEMENT =\n  'Placeholder required placement info (parent, index, or where) is missing';\nexport const ERROR_MOVE_CANNOT_DROP =\n  'Node cannot be dropped into target parent';\nexport const ERROR_MOVE_INCOMING_PARENT = 'Target parent rejects incoming node';\nexport const ERROR_MOVE_OUTGOING_PARENT =\n  'Current parent rejects outgoing node';\nexport const ERROR_MOVE_NONCANVAS_CHILD =\n  'Cannot move node that is not a direct child of a Canvas node';\nexport const ERROR_MOVE_TO_NONCANVAS_PARENT =\n  'Cannot move node into a non-Canvas parent';\nexport const ERROR_MOVE_TOP_LEVEL_NODE = 'A top-level Node cannot be moved';\nexport const ERROR_MOVE_ROOT_NODE = 'Root Node cannot be moved';\n\nexport const ERROR_MOVE_TO_DESCENDANT = 'Cannot move node into a descendant';\nexport const ERROR_NOT_IN_RESOLVER =\n  'The component type specified for this node (%node_type%) does not exist in the resolver';\nexport const ERROR_INFINITE_CANVAS =\n  \"The component specified in the <Canvas> `is` prop has additional Canvas specified in it's render template.\";\nexport const ERROR_CANNOT_DRAG =\n  'The node has specified a canDrag() rule that prevents it from being dragged';\nexport const ERROR_INVALID_NODE_ID = 'Invalid parameter Node Id specified';\nexport const ERROR_DELETE_TOP_LEVEL_NODE =\n  'Attempting to delete a top-level Node';\n\nexport const ERROR_RESOLVER_NOT_AN_OBJECT = `Resolver in <Editor /> has to be an object. For (de)serialization Craft.js needs a list of all the User Components. \n    \nMore info: https://craft.js.org/r/docs/api/editor#props`;\n\nexport const ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER = `An Error occurred while deserializing components: Cannot find component <%displayName% /> in resolver map. Please check your resolver in <Editor />\n\nAvailable components in resolver: %availableComponents%\n\nMore info: https://craft.js.org/r/docs/api/editor#props`;\n\nexport const ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT = `You can only use useEditor in the context of <Editor />. \n\nPlease only use useEditor in components that are children of the <Editor /> component.`;\n\nexport const ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT = `You can only use useNode in the context of <Editor />. \n\nPlease only use useNode in components that are children of the <Editor /> component.`;\n", "import { Patch, applyPatches } from 'immer';\n\ntype Timeline = Array<{\n  patches: Patch[];\n  inversePatches: Patch[];\n  timestamp: number;\n}>;\n\nexport const HISTORY_ACTIONS = {\n  UNDO: 'HISTORY_UNDO',\n  REDO: 'HISTORY_REDO',\n  THROTTLE: 'HISTORY_THROTTLE',\n  IGNORE: 'HISTORY_IGNORE',\n  MERGE: 'HISTORY_MERGE',\n  CLEAR: 'HISTORY_CLEAR',\n};\n\nexport class History {\n  timeline: Timeline = [];\n  pointer = -1;\n\n  add(patches: Patch[], inversePatches: Patch[]) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    this.pointer = this.pointer + 1;\n    this.timeline.length = this.pointer;\n    this.timeline[this.pointer] = {\n      patches,\n      inversePatches,\n      timestamp: Date.now(),\n    };\n  }\n\n  throttleAdd(\n    patches: Patch[],\n    inversePatches: Patch[],\n    throttleRate: number = 500\n  ) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    if (this.timeline.length && this.pointer >= 0) {\n      const {\n        patches: currPatches,\n        inversePatches: currInversePatches,\n        timestamp,\n      } = this.timeline[this.pointer];\n\n      const now = new Date();\n      const diff = now.getTime() - timestamp;\n\n      if (diff < throttleRate) {\n        this.timeline[this.pointer] = {\n          timestamp,\n          patches: [...currPatches, ...patches],\n          inversePatches: [...inversePatches, ...currInversePatches],\n        };\n        return;\n      }\n    }\n\n    this.add(patches, inversePatches);\n  }\n\n  merge(patches: Patch[], inversePatches: Patch[]) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    if (this.timeline.length && this.pointer >= 0) {\n      const {\n        patches: currPatches,\n        inversePatches: currInversePatches,\n        timestamp,\n      } = this.timeline[this.pointer];\n\n      this.timeline[this.pointer] = {\n        timestamp,\n        patches: [...currPatches, ...patches],\n        inversePatches: [...inversePatches, ...currInversePatches],\n      };\n      return;\n    }\n\n    this.add(patches, inversePatches);\n  }\n\n  clear() {\n    this.timeline = [];\n    this.pointer = -1;\n  }\n\n  canUndo() {\n    return this.pointer >= 0;\n  }\n\n  canRedo() {\n    return this.pointer < this.timeline.length - 1;\n  }\n\n  undo(state) {\n    if (!this.canUndo()) {\n      return;\n    }\n\n    const { inversePatches } = this.timeline[this.pointer];\n    this.pointer = this.pointer - 1;\n    return applyPatches(state, inversePatches);\n  }\n\n  redo(state) {\n    if (!this.canRedo()) {\n      return;\n    }\n\n    this.pointer = this.pointer + 1;\n    const { patches } = this.timeline[this.pointer];\n    return applyPatches(state, patches);\n  }\n}\n", "// https://github.com/pelotom/use-methods\nimport produce, {\n  Patch,\n  produceWithPatches,\n  enableMapSet,\n  enablePatches,\n} from 'immer';\nimport isEqualWith from 'lodash/isEqualWith';\nimport { useMemo, useEffect, useRef, useCallback } from 'react';\n\nimport { History, HISTORY_ACTIONS } from './History';\nimport { Delete } from './utilityTypes';\n\nenableMapSet();\nenablePatches();\n\nexport type SubscriberAndCallbacksFor<\n  M extends MethodsOrOptions,\n  Q extends QueryMethods = any\n> = {\n  subscribe: Watcher<StateFor<M>>['subscribe'];\n  getState: () => { prev: StateFor<M>; current: StateFor<M> };\n  actions: CallbacksFor<M>;\n  query: QueryCallbacksFor<Q>;\n  history: History;\n};\n\nexport type StateFor<M extends MethodsOrOptions> = M extends MethodsOrOptions<\n  infer S,\n  any\n>\n  ? S\n  : never;\n\nexport type CallbacksFor<\n  M extends MethodsOrOptions\n> = M extends MethodsOrOptions<any, infer R>\n  ? {\n      [T in ActionUnion<R>['type']]: (\n        ...payload: ActionByType<ActionUnion<R>, T>['payload']\n      ) => void;\n    } & {\n      history: {\n        undo: () => void;\n        redo: () => void;\n        clear: () => void;\n        throttle: (\n          rate?: number\n        ) => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n        merge: () => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n        ignore: () => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n      };\n    }\n  : {};\n\nexport type Methods<S = any, R extends MethodRecordBase<S> = any, Q = any> = (\n  state: S,\n  query: Q\n) => R;\n\nexport type Options<S = any, R extends MethodRecordBase<S> = any, Q = any> = {\n  methods: Methods<S, R, Q>;\n  ignoreHistoryForActions: ReadonlyArray<keyof MethodRecordBase>;\n  normalizeHistory?: (state: S) => void;\n};\n\nexport type MethodsOrOptions<\n  S = any,\n  R extends MethodRecordBase<S> = any,\n  Q = any\n> = Methods<S, R, Q> | Options<S, R, Q>;\n\nexport type MethodRecordBase<S = any> = Record<\n  string,\n  (...args: any[]) => S extends object ? S | void : S\n>;\n\nexport type Action<T = any, P = any> = {\n  type: T;\n  payload?: P;\n  config?: Record<string, any>;\n};\n\nexport type ActionUnion<R extends MethodRecordBase> = {\n  [T in keyof R]: { type: T; payload: Parameters<R[T]> };\n}[keyof R];\n\nexport type ActionByType<A, T> = A extends { type: infer T2 }\n  ? T extends T2\n    ? A\n    : never\n  : never;\n\nexport type QueryMethods<\n  S = any,\n  O = any,\n  R extends MethodRecordBase<S> = any\n> = (state?: S, options?: O) => R;\nexport type QueryCallbacksFor<M extends QueryMethods> = M extends QueryMethods<\n  any,\n  any,\n  infer R\n>\n  ? {\n      [T in ActionUnion<R>['type']]: (\n        ...payload: ActionByType<ActionUnion<R>, T>['payload']\n      ) => ReturnType<R[T]>;\n    } & {\n      history: {\n        canUndo: () => boolean;\n        canRedo: () => boolean;\n      };\n    }\n  : {};\n\nexport type PatchListenerAction<M extends MethodsOrOptions> = {\n  type: keyof CallbacksFor<M>;\n  params: any;\n  patches: Patch[];\n};\n\nexport type PatchListener<\n  S,\n  M extends MethodsOrOptions,\n  Q extends QueryMethods\n> = (\n  newState: S,\n  previousState: S,\n  actionPerformedWithPatches: PatchListenerAction<M>,\n  query: QueryCallbacksFor<Q>,\n  normalizer: (cb: (draft: S) => void) => void\n) => void;\n\nexport function useMethods<S, R extends MethodRecordBase<S>>(\n  methodsOrOptions: MethodsOrOptions<S, R>, // methods to manipulate the state\n  initialState: any\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods\n>(\n  methodsOrOptions: MethodsOrOptions<S, R, QueryCallbacksFor<Q>>, // methods to manipulate the state\n  initialState: any,\n  queryMethods: Q\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods\n>(\n  methodsOrOptions: MethodsOrOptions<S, R, QueryCallbacksFor<Q>>, // methods to manipulate the state\n  initialState: any,\n  queryMethods: Q,\n  patchListener: PatchListener<\n    S,\n    MethodsOrOptions<S, R, QueryCallbacksFor<Q>>,\n    Q\n  >\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods = null\n>(\n  methodsOrOptions: MethodsOrOptions<S, R>,\n  initialState: any,\n  queryMethods?: Q,\n  patchListener?: any\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q> {\n  const history = useMemo(() => new History(), []);\n\n  let methodsFactory: Methods<S, R>;\n  let ignoreHistoryForActionsRef = useRef([]);\n  let normalizeHistoryRef = useRef<any>(() => {});\n\n  if (typeof methodsOrOptions === 'function') {\n    methodsFactory = methodsOrOptions;\n  } else {\n    methodsFactory = methodsOrOptions.methods;\n    ignoreHistoryForActionsRef.current = methodsOrOptions.ignoreHistoryForActions as any;\n    normalizeHistoryRef.current = methodsOrOptions.normalizeHistory;\n  }\n\n  const patchListenerRef = useRef(patchListener);\n  patchListenerRef.current = patchListener;\n\n  const stateRef = useRef(initialState);\n\n  const reducer = useMemo(() => {\n    const { current: normalizeHistory } = normalizeHistoryRef;\n    const { current: ignoreHistoryForActions } = ignoreHistoryForActionsRef;\n    const { current: patchListener } = patchListenerRef;\n\n    return (state: S, action: Action) => {\n      const query =\n        queryMethods && createQuery(queryMethods, () => state, history);\n\n      let finalState;\n      let [nextState, patches, inversePatches] = (produceWithPatches as any)(\n        state,\n        (draft: S) => {\n          switch (action.type) {\n            case HISTORY_ACTIONS.UNDO: {\n              return history.undo(draft);\n            }\n            case HISTORY_ACTIONS.REDO: {\n              return history.redo(draft);\n            }\n            case HISTORY_ACTIONS.CLEAR: {\n              history.clear();\n              return {\n                ...draft,\n              };\n            }\n\n            // TODO: Simplify History API\n            case HISTORY_ACTIONS.IGNORE:\n            case HISTORY_ACTIONS.MERGE:\n            case HISTORY_ACTIONS.THROTTLE: {\n              const [type, ...params] = action.payload;\n              methodsFactory(draft, query)[type](...params);\n              break;\n            }\n            default:\n              methodsFactory(draft, query)[action.type](...action.payload);\n          }\n        }\n      );\n\n      finalState = nextState;\n\n      if (patchListener) {\n        patchListener(\n          nextState,\n          state,\n          { type: action.type, params: action.payload, patches },\n          query,\n          (cb) => {\n            let normalizedDraft = produceWithPatches(nextState, cb);\n            finalState = normalizedDraft[0];\n\n            patches = [...patches, ...normalizedDraft[1]];\n            inversePatches = [...normalizedDraft[2], ...inversePatches];\n          }\n        );\n      }\n\n      if (\n        [HISTORY_ACTIONS.UNDO, HISTORY_ACTIONS.REDO].includes(\n          action.type as any\n        ) &&\n        normalizeHistory\n      ) {\n        finalState = produce(finalState, normalizeHistory);\n      }\n\n      if (\n        ![\n          ...ignoreHistoryForActions,\n          HISTORY_ACTIONS.UNDO,\n          HISTORY_ACTIONS.REDO,\n          HISTORY_ACTIONS.IGNORE,\n          HISTORY_ACTIONS.CLEAR,\n        ].includes(action.type as any)\n      ) {\n        if (action.type === HISTORY_ACTIONS.THROTTLE) {\n          history.throttleAdd(\n            patches,\n            inversePatches,\n            action.config && action.config.rate\n          );\n        } else if (action.type === HISTORY_ACTIONS.MERGE) {\n          history.merge(patches, inversePatches);\n        } else {\n          history.add(patches, inversePatches);\n        }\n      }\n\n      return finalState;\n    };\n  }, [history, methodsFactory, queryMethods]);\n\n  const getState = useCallback(() => stateRef.current, []);\n  const watcher = useMemo(() => new Watcher<S>(getState), [getState]);\n\n  const dispatch = useCallback(\n    (action: any) => {\n      const newState = reducer(stateRef.current, action);\n      stateRef.current = newState;\n      watcher.notify();\n    },\n    [reducer, watcher]\n  );\n\n  useEffect(() => {\n    watcher.notify();\n  }, [watcher]);\n\n  const query = useMemo(\n    () =>\n      !queryMethods\n        ? []\n        : createQuery(queryMethods, () => stateRef.current, history),\n    [history, queryMethods]\n  );\n\n  const actions = useMemo(() => {\n    const actionTypes = Object.keys(methodsFactory(null, null));\n\n    const { current: ignoreHistoryForActions } = ignoreHistoryForActionsRef;\n\n    return {\n      ...actionTypes.reduce((accum, type) => {\n        accum[type] = (...payload) => dispatch({ type, payload });\n        return accum;\n      }, {} as any),\n      history: {\n        undo() {\n          return dispatch({\n            type: HISTORY_ACTIONS.UNDO,\n          });\n        },\n        redo() {\n          return dispatch({\n            type: HISTORY_ACTIONS.REDO,\n          });\n        },\n        clear: () => {\n          return dispatch({\n            type: HISTORY_ACTIONS.CLEAR,\n          });\n        },\n        throttle: (rate) => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.THROTTLE,\n                    payload: [type, ...payload],\n                    config: {\n                      rate: rate,\n                    },\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n        ignore: () => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.IGNORE,\n                    payload: [type, ...payload],\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n        merge: () => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.MERGE,\n                    payload: [type, ...payload],\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n      },\n    };\n  }, [dispatch, methodsFactory]);\n\n  return useMemo(\n    () => ({\n      getState,\n      subscribe: (collector, cb, collectOnCreate) =>\n        watcher.subscribe(collector, cb, collectOnCreate),\n      actions,\n      query,\n      history,\n    }),\n    [actions, query, watcher, getState, history]\n  ) as any;\n}\n\nexport function createQuery<Q extends QueryMethods>(\n  queryMethods: Q,\n  getState,\n  history: History\n) {\n  const queries = Object.keys(queryMethods()).reduce((accum, key) => {\n    return {\n      ...accum,\n      [key]: (...args: any) => {\n        return queryMethods(getState())[key](...args);\n      },\n    };\n  }, {} as QueryCallbacksFor<typeof queryMethods>);\n\n  return {\n    ...queries,\n    history: {\n      canUndo: () => history.canUndo(),\n      canRedo: () => history.canRedo(),\n    },\n  };\n}\n\nclass Watcher<S> {\n  getState;\n  subscribers: Subscriber[] = [];\n\n  constructor(getState) {\n    this.getState = getState;\n  }\n\n  /**\n   * Creates a Subscriber\n   * @returns {() => void} a Function that removes the Subscriber\n   */\n  subscribe<C>(\n    collector: (state: S) => C,\n    onChange: (collected: C) => void,\n    collectOnCreate?: boolean\n  ): () => void {\n    const subscriber = new Subscriber(\n      () => collector(this.getState()),\n      onChange,\n      collectOnCreate\n    );\n    this.subscribers.push(subscriber);\n    return this.unsubscribe.bind(this, subscriber);\n  }\n\n  unsubscribe(subscriber) {\n    if (this.subscribers.length) {\n      const index = this.subscribers.indexOf(subscriber);\n      if (index > -1) return this.subscribers.splice(index, 1);\n    }\n  }\n\n  notify() {\n    this.subscribers.forEach((subscriber) => subscriber.collect());\n  }\n}\n\nclass Subscriber {\n  collected: any;\n  collector: () => any;\n  onChange: (collected: any) => void;\n  id;\n\n  /**\n   * Creates a Subscriber\n   * @param collector The method that returns an object of values to be collected\n   * @param onChange A callback method that is triggered when the collected values has changed\n   * @param collectOnCreate If set to true, the collector/onChange will be called on instantiation\n   */\n  constructor(collector, onChange, collectOnCreate = false) {\n    this.collector = collector;\n    this.onChange = onChange;\n\n    // Collect and run onChange callback when Subscriber is created\n    if (collectOnCreate) this.collect();\n  }\n\n  collect() {\n    try {\n      const recollect = this.collector();\n      if (!isEqualWith(recollect, this.collected)) {\n        this.collected = recollect;\n        if (this.onChange) this.onChange(this.collected);\n      }\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.warn(err);\n    }\n  }\n}\n", "export const getDOMInfo = (el: HTMLElement) => {\n  const {\n    x,\n    y,\n    top,\n    left,\n    bottom,\n    right,\n    width,\n    height,\n  } = el.getBoundingClientRect() as DOMRect;\n\n  const style = window.getComputedStyle(el);\n\n  const margin = {\n    left: parseInt(style.marginLeft),\n    right: parseInt(style.marginRight),\n    bottom: parseInt(style.marginBottom),\n    top: parseInt(style.marginTop),\n  };\n\n  const padding = {\n    left: parseInt(style.paddingLeft),\n    right: parseInt(style.paddingRight),\n    bottom: parseInt(style.paddingBottom),\n    top: parseInt(style.paddingTop),\n  };\n\n  const styleInFlow = (parent: HTMLElement) => {\n    const parentStyle: any = getComputedStyle(parent);\n\n    if (style.overflow && style.overflow !== 'visible') {\n      return;\n    }\n\n    if (parentStyle.float !== 'none') {\n      return;\n    }\n\n    if (parentStyle.display === 'grid') {\n      return;\n    }\n\n    if (\n      parentStyle.display === 'flex' &&\n      parentStyle['flex-direction'] !== 'column'\n    ) {\n      return;\n    }\n\n    switch (style.position) {\n      case 'static':\n      case 'relative':\n        break;\n      default:\n        return;\n    }\n\n    switch (el.tagName) {\n      case 'TR':\n      case 'TBODY':\n      case 'THEAD':\n      case 'TFOOT':\n        return true;\n    }\n\n    switch (style.display) {\n      case 'block':\n      case 'list-item':\n      case 'table':\n      case 'flex':\n      case 'grid':\n        return true;\n    }\n\n    return;\n  };\n\n  return {\n    x,\n    y,\n    top,\n    left,\n    bottom,\n    right,\n    width,\n    height,\n    outerWidth: Math.round(width + margin.left + margin.right),\n    outerHeight: Math.round(height + margin.top + margin.bottom),\n    margin,\n    padding,\n    inFlow: el.parentElement && !!styleInFlow(el.parentElement),\n  };\n};\n", "import { useState, useCallback, useRef, useEffect } from 'react';\n\nimport { SubscriberAndCallbacksFor } from './useMethods';\nimport { ConditionallyMergeRecordTypes } from './utilityTypes';\n\ntype CollectorMethods<S extends SubscriberAndCallbacksFor<any, any>> = {\n  actions: S['actions'];\n  query: S['query'];\n};\n\nexport type useCollectorReturnType<\n  S extends SubscriberAndCallbacksFor<any, any>,\n  C = null\n> = ConditionallyMergeRecordTypes<C, CollectorMethods<S>>;\nexport function useCollector<S extends SubscriberAndCallbacksFor<any, any>, C>(\n  store: S,\n  collector?: (\n    state: ReturnType<S['getState']>['current'],\n    query: S['query']\n  ) => C\n): useCollectorReturnType<S, C> {\n  const { subscribe, getState, actions, query } = store;\n\n  const initial = useRef(true);\n  const collected = useRef<any>(null);\n  const collectorRef = useRef(collector);\n  collectorRef.current = collector;\n\n  const onCollect = useCallback(\n    (collected) => {\n      return { ...collected, actions, query };\n    },\n    [actions, query]\n  );\n\n  // Collect states for initial render\n  if (initial.current && collector) {\n    collected.current = collector(getState(), query);\n    initial.current = false;\n  }\n\n  const [renderCollected, setRenderCollected] = useState(\n    onCollect(collected.current)\n  );\n\n  // Collect states on state change\n  useEffect(() => {\n    let unsubscribe;\n    if (collectorRef.current) {\n      unsubscribe = subscribe(\n        (current) => collectorRef.current(current, query),\n        (collected) => {\n          setRenderCollected(onCollect(collected));\n        }\n      );\n    }\n    return () => {\n      if (unsubscribe) unsubscribe();\n    };\n  }, [onCollect, query, subscribe]);\n\n  return renderCollected;\n}\n", "import { nanoid } from 'nanoid';\n\n// By default nanoid generate an ID with 21 characters. To reduce the footprint, we default to 10 characters.\n// We have a higher probability for collisions, though\n\n/**\n * Generate a random ID. That ID can for example be used as a node ID.\n *\n * @param size The number of characters that are generated for the ID. Defaults to `10`\n * @returns A random id\n */\nexport const getRandomId = (size: number = 10) => nanoid(size);\n", "import { EventHandlers } from './EventHandlers';\n\nexport type Connector = (el: HTMLElement, ...args: any) => any;\n\nexport type ConnectorsRecord = Record<string, Connector>;\n\nexport type ChainableConnector<T extends Connector, O extends any> = T extends (\n  element: infer E,\n  ...args: infer P\n) => any\n  ? <B extends E | O>(element: B, ...args: P) => B\n  : never;\n\nexport type ChainableConnectors<\n  H extends ConnectorsRecord,\n  E extends any = HTMLElement\n> = {\n  [T in keyof H]: H[T] extends Connector ? ChainableConnector<H[T], E> : never;\n};\n\nexport type CraftDOMEvent<T extends Event> = T & {\n  craft: {\n    stopPropagation: () => void;\n    blockedEvents: Record<string, HTMLElement[]>;\n  };\n};\n\nexport type CraftEventListener<K extends keyof HTMLElementEventMap> = (\n  ev: CraftDOMEvent<HTMLElementEventMap[K]>\n) => any;\n\nexport type EventHandlerConnectors<\n  H extends EventHandlers,\n  E extends any = HTMLElement\n> = ChainableConnectors<ReturnType<H['handlers']>, E>;\n\nexport type ConnectorsUsage<H extends EventHandlers> = {\n  register: () => void;\n  cleanup: () => void;\n  connectors: EventHandlerConnectors<H>;\n};\n\nexport enum EventHandlerUpdates {\n  HandlerDisabled,\n  HandlerEnabled,\n}\n\nexport type ConnectorToRegister = {\n  name: string;\n  required: any;\n  connector: Connector;\n  options?: Record<string, any>;\n};\n\nexport type RegisteredConnector = {\n  id: string;\n  required: any;\n  enable: () => void;\n  disable: () => void;\n  remove: () => void;\n};\n", "import isEqual from 'shallowequal';\n\nimport { ConnectorToRegister, RegisteredConnector } from './interfaces';\n\nimport { getRandomId } from '../getRandomId';\n\n/**\n * Stores all connected DOM elements and their connectors here\n * This allows us to easily enable/disable and perform cleanups\n */\nexport class ConnectorRegistry {\n  private isEnabled: boolean = true;\n\n  private elementIdMap: WeakMap<HTMLElement, string> = new WeakMap();\n  private registry: Map<String, RegisteredConnector> = new Map();\n\n  private getElementId(element: HTMLElement) {\n    const existingId = this.elementIdMap.get(element);\n    if (existingId) {\n      return existingId;\n    }\n\n    const newId = getRandomId();\n\n    this.elementIdMap.set(element, newId);\n    return newId;\n  }\n\n  getConnectorId(element: HTMLElement, connectorName: string) {\n    const elementId = this.getElementId(element);\n    return `${connectorName}--${elementId}`;\n  }\n\n  register(element: HTMLElement, connectorPayload: ConnectorToRegister) {\n    const existingConnector = this.getByElement(element, connectorPayload.name);\n\n    if (existingConnector) {\n      if (isEqual(connectorPayload.required, existingConnector.required)) {\n        return existingConnector;\n      }\n\n      this.getByElement(element, connectorPayload.name).disable();\n    }\n\n    let cleanup: () => void | null = null;\n\n    const id = this.getConnectorId(element, connectorPayload.name);\n    this.registry.set(id, {\n      id,\n      required: connectorPayload.required,\n      enable: () => {\n        if (cleanup) {\n          cleanup();\n        }\n\n        cleanup = connectorPayload.connector(\n          element,\n          connectorPayload.required,\n          connectorPayload.options\n        );\n      },\n      disable: () => {\n        if (!cleanup) {\n          return;\n        }\n\n        cleanup();\n      },\n      remove: () => {\n        return this.remove(id);\n      },\n    });\n\n    if (this.isEnabled) {\n      this.registry.get(id).enable();\n    }\n\n    return this.registry.get(id);\n  }\n\n  get(id: string) {\n    return this.registry.get(id);\n  }\n\n  remove(id: string) {\n    const connector = this.get(id);\n    if (!connector) {\n      return;\n    }\n\n    connector.disable();\n    this.registry.delete(connector.id);\n  }\n\n  enable() {\n    this.isEnabled = true;\n    this.registry.forEach((connectors) => {\n      connectors.enable();\n    });\n  }\n\n  disable() {\n    this.isEnabled = false;\n    this.registry.forEach((connectors) => {\n      connectors.disable();\n    });\n  }\n\n  getByElement(element: HTMLElement, connectorName: string) {\n    return this.get(this.getConnectorId(element, connectorName));\n  }\n\n  removeByElement(element: HTMLElement, connectorName: string) {\n    return this.remove(this.getConnectorId(element, connectorName));\n  }\n\n  clear() {\n    this.disable();\n    this.elementIdMap = new WeakMap();\n    this.registry = new Map();\n  }\n}\n", "import { ConnectorRegistry } from './ConnectorRegistry';\nimport {\n  EventHandlerUpdates,\n  CraftEventListener,\n  EventHandlerConnectors,\n  CraftDOMEvent,\n  Connector,\n  ConnectorsUsage,\n  RegisteredConnector,\n} from './interfaces';\nimport { isEventBlockedByDescendant } from './isEventBlockedByDescendant';\n\nexport abstract class EventHandlers<O extends Record<string, any> = {}> {\n  options: O;\n\n  private registry: ConnectorRegistry = new ConnectorRegistry();\n  private subscribers: Set<(msg: EventHandlerUpdates) => void> = new Set();\n\n  onEnable?(): void;\n  onDisable?(): void;\n\n  constructor(options?: O) {\n    this.options = options;\n  }\n\n  listen(cb: (msg: EventHandlerUpdates) => void) {\n    this.subscribers.add(cb);\n    return () => this.subscribers.delete(cb);\n  }\n\n  disable() {\n    if (this.onDisable) {\n      this.onDisable();\n    }\n\n    this.registry.disable();\n\n    this.subscribers.forEach((listener) => {\n      listener(EventHandlerUpdates.HandlerDisabled);\n    });\n  }\n\n  enable() {\n    if (this.onEnable) {\n      this.onEnable();\n    }\n\n    this.registry.enable();\n\n    this.subscribers.forEach((listener) => {\n      listener(EventHandlerUpdates.HandlerEnabled);\n    });\n  }\n\n  cleanup() {\n    this.disable();\n    this.subscribers.clear();\n    this.registry.clear();\n  }\n\n  addCraftEventListener<K extends keyof HTMLElementEventMap>(\n    el: HTMLElement,\n    eventName: K,\n    listener: CraftEventListener<K>,\n    options?: boolean | AddEventListenerOptions\n  ) {\n    const bindedListener = (e: CraftDOMEvent<HTMLElementEventMap[K]>) => {\n      if (!isEventBlockedByDescendant(e, eventName, el)) {\n        e.craft.stopPropagation = () => {\n          if (!e.craft.blockedEvents[eventName]) {\n            e.craft.blockedEvents[eventName] = [];\n          }\n\n          e.craft.blockedEvents[eventName].push(el);\n        };\n\n        listener(e);\n      }\n    };\n\n    el.addEventListener(eventName, bindedListener, options);\n\n    return () => el.removeEventListener(eventName, bindedListener, options);\n  }\n\n  // Defines the connectors and their logic\n  abstract handlers(): Record<string, (el: HTMLElement, ...args: any[]) => any>;\n\n  /**\n   * Creates a record of chainable connectors and tracks their usages\n   */\n  createConnectorsUsage(): ConnectorsUsage<this> {\n    const handlers = this.handlers();\n\n    // Track all active connector ids here\n    // This is so we can return a cleanup method below so the callee can programmatically cleanup all connectors\n\n    const activeConnectorIds: Set<string> = new Set();\n\n    let canRegisterConnectors = false;\n    const connectorsToRegister: Map<\n      string,\n      () => RegisteredConnector\n    > = new Map();\n\n    const connectors = Object.entries(handlers).reduce<\n      Record<string, Connector>\n    >(\n      (accum, [name, handler]) => ({\n        ...accum,\n        [name]: (el, required, options) => {\n          const registerConnector = () => {\n            const connector = this.registry.register(el, {\n              required,\n              name,\n              options,\n              connector: handler,\n            });\n\n            activeConnectorIds.add(connector.id);\n            return connector;\n          };\n\n          connectorsToRegister.set(\n            this.registry.getConnectorId(el, name),\n            registerConnector\n          );\n\n          /**\n           * If register() has been called,\n           * register the connector immediately.\n           *\n           * Otherwise, registration is deferred until after register() is called\n           */\n          if (canRegisterConnectors) {\n            registerConnector();\n          }\n\n          return el;\n        },\n      }),\n      {}\n    ) as any;\n\n    return {\n      connectors,\n      register: () => {\n        canRegisterConnectors = true;\n\n        connectorsToRegister.forEach((registerConnector) => {\n          registerConnector();\n        });\n      },\n      cleanup: () => {\n        canRegisterConnectors = false;\n\n        activeConnectorIds.forEach((connectorId) =>\n          this.registry.remove(connectorId)\n        );\n      },\n    };\n  }\n\n  derive<C extends EventHandlers>(\n    type: {\n      new (...args: any[]): C;\n    },\n    opts: C['options']\n  ) {\n    return new type(this, opts);\n  }\n\n  // This method allows us to execute multiple connectors and returns a single cleanup method for all of them\n  protected createProxyHandlers<H extends EventHandlers>(\n    instance: H,\n    cb: (connectors: EventHandlerConnectors<H>) => void\n  ) {\n    const connectorsToCleanup = [];\n    const handlers = instance.handlers();\n\n    const proxiedHandlers = new Proxy(handlers, {\n      get: (target, key: any, receiver) => {\n        if (key in handlers === false) {\n          return Reflect.get(target, key, receiver);\n        }\n\n        return (el, ...args) => {\n          const cleanup = handlers[key](el, ...args);\n          if (!cleanup) {\n            return;\n          }\n\n          connectorsToCleanup.push(cleanup);\n        };\n      },\n    });\n\n    cb(proxiedHandlers as any);\n\n    return () => {\n      connectorsToCleanup.forEach((cleanup) => {\n        cleanup();\n      });\n    };\n  }\n\n  // This lets us to execute and cleanup sibling connectors\n  reflect(cb: (connectors: EventHandlerConnectors<this>) => void) {\n    return this.createProxyHandlers(this, cb);\n  }\n}\n", "import { CraftDOMEvent } from './interfaces';\n\n/**\n * Check if a specified event is blocked by a child\n * that's a descendant of the specified element\n */\nexport function isEventBlockedByDescendant<K extends keyof HTMLElementEventMap>(\n  e: CraftDOMEvent<HTMLElementEventMap[K]>,\n  eventName: K,\n  el: HTMLElement\n) {\n  // Store initial Craft event value\n  if (!e.craft) {\n    e.craft = {\n      stopPropagation: () => {},\n      blockedEvents: {},\n    };\n  }\n\n  const blockingElements = (e.craft && e.craft.blockedEvents[eventName]) || [];\n\n  for (let i = 0; i < blockingElements.length; i++) {\n    const blockingElement = blockingElements[i];\n\n    if (el !== blockingElement && el.contains(blockingElement)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n", "import { EventHandlers } from './EventHandlers';\nimport { EventHandlerConnectors, EventHandlerUpdates } from './interfaces';\n\n// Creates EventHandlers that depends on another EventHandlers instance\n// This lets us to easily create new connectors that composites of the parent EventHandlers instance\nexport abstract class DerivedEventHandlers<\n  P extends EventHandlers,\n  O extends Record<string, any> = {}\n> extends EventHandlers<O> {\n  derived: P;\n  unsubscribeParentHandlerListener: () => void;\n\n  constructor(derived: P, options?: O) {\n    super(options);\n    this.derived = derived;\n    this.options = options;\n\n    // Automatically disable/enable depending on the parent handlers\n    this.unsubscribeParentHandlerListener = this.derived.listen((msg) => {\n      switch (msg) {\n        case EventHandlerUpdates.HandlerEnabled: {\n          return this.enable();\n        }\n        case EventHandlerUpdates.HandlerDisabled: {\n          return this.disable();\n        }\n        default: {\n          return;\n        }\n      }\n    });\n  }\n\n  // A method to easily inherit parent connectors\n  inherit(cb: (connectors: EventHandlerConnectors<P>) => void) {\n    return this.createProxyHandlers(this.derived, cb);\n  }\n\n  cleanup() {\n    super.cleanup();\n    this.unsubscribeParentHandlerListener();\n  }\n}\n", "// https://github.com/react-dnd/react-dnd\nimport { isValidElement, ReactElement } from 'react';\nimport { cloneElement } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { ChainableConnectors, ConnectorsRecord } from './interfaces';\n\nfunction setRef(ref: any, node: any) {\n  if (node) {\n    if (typeof ref === 'function') {\n      ref(node);\n    } else {\n      ref.current = node;\n    }\n  }\n}\n\nexport function cloneWithRef(\n  element: any,\n  newRef: any\n): React.ReactElement<any> {\n  const previousRef = element.ref;\n  invariant(\n    typeof previousRef !== 'string',\n    'Cannot connect to an element with an existing string ref. ' +\n      'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' +\n      'Read more: https://facebook.github.io/react/docs/more-about-refs.html#the-ref-callback-attribute'\n  );\n\n  if (!previousRef) {\n    // When there is no ref on the element, use the new ref directly\n    return cloneElement(element, {\n      ref: newRef,\n    });\n  } else {\n    return cloneElement(element, {\n      ref: (node: any) => {\n        setRef(previousRef, node);\n        setRef(newRef, node);\n      },\n    });\n  }\n}\n\nfunction throwIfCompositeComponentElement(element: React.ReactElement<any>) {\n  if (typeof element.type === 'string') {\n    return;\n  }\n\n  throw new Error();\n}\n\nexport function wrapHookToRecognizeElement(\n  hook: (node: any, ...args: any[]) => void\n) {\n  return (elementOrNode = null, ...args: any) => {\n    // When passed a node, call the hook straight away.\n    if (!isValidElement(elementOrNode)) {\n      if (!elementOrNode) {\n        return;\n      }\n\n      const node = elementOrNode;\n      node && hook(node, ...args);\n      return node;\n    }\n\n    // If passed a ReactElement, clone it and attach this function as a ref.\n    // This helps us achieve a neat API where user doesn't even know that refs\n    // are being used under the hood.\n    const element: ReactElement | null = elementOrNode;\n    throwIfCompositeComponentElement(element as any);\n\n    return cloneWithRef(element, hook);\n  };\n}\n\n// A React wrapper for our connectors\n// Wrap all our connectors so that would additionally accept React.ReactElement\nexport function wrapConnectorHooks<H extends ConnectorsRecord>(\n  connectors: H\n): ChainableConnectors<H, React.ReactElement | HTMLElement> {\n  return Object.keys(connectors).reduce((accum, key) => {\n    accum[key] = wrapHookToRecognizeElement((...args) => {\n      // @ts-ignore\n      return connectors[key](...args);\n    });\n\n    return accum;\n  }, {}) as any;\n}\n", "import React from 'react';\nimport ReactDOM from 'react-dom';\n\ntype RenderIndicatorProps = {\n  style: React.CSSProperties;\n  className?: string;\n  parentDom?: HTMLElement;\n};\n\nexport const RenderIndicator = ({\n  style,\n  className,\n  parentDom,\n}: RenderIndicatorProps) => {\n  const indicator = (\n    <div\n      className={className}\n      style={{\n        position: 'fixed',\n        display: 'block',\n        opacity: 1,\n        borderStyle: 'solid',\n        borderWidth: '1px',\n        borderColor: 'transparent',\n        zIndex: 99999,\n        ...style,\n      }}\n    ></div>\n  );\n\n  if (parentDom && parentDom.ownerDocument !== document) {\n    return ReactDOM.createPortal(indicator, parentDom.ownerDocument.body);\n  }\n\n  return indicator;\n};\n", "import { useEffect } from 'react';\n\nexport const useEffectOnce = (effect: () => void) => {\n  /* eslint-disable-next-line react-hooks/exhaustive-deps */\n  useEffect(effect, []);\n};\n", "type DeprecationPayload = Partial<{\n  suggest: string;\n  doc: string;\n}>;\n\nexport const deprecationWarning = (name, payload?: DeprecationPayload) => {\n  let message = `Deprecation warning: ${name} will be deprecated in future relases.`;\n\n  const { suggest, doc } = payload;\n\n  if (suggest) {\n    message += ` Please use ${suggest} instead.`;\n  }\n\n  // URL link to Documentation\n  if (doc) {\n    message += `(${doc})`;\n  }\n\n  // eslint-disable-next-line no-console\n  console.warn(message);\n};\n", "export const isClientSide = () => typeof window !== 'undefined';\n\nexport const isLinux = () =>\n  isClientSide() && /Linux/i.test(window.navigator.userAgent);\n\nexport const isChromium = () =>\n  isClientSide() && /Chrome/i.test(window.navigator.userAgent);\n", "import React from 'react';\n\nimport { NodeId } from '../interfaces';\n\nexport type NodeContextType = {\n  id: NodeId;\n  related?: boolean;\n};\n\nexport const NodeContext = React.createContext<NodeContextType>(null);\n\nexport type NodeProviderProps = Omit<NodeContextType, 'connectors'> & {\n  children?: React.ReactNode;\n};\n\nexport const NodeProvider = ({\n  id,\n  related = false,\n  children,\n}: NodeProviderProps) => {\n  return (\n    <NodeContext.Provider value={{ id, related }}>\n      {children}\n    </NodeContext.Provider>\n  );\n};\n", "import { createContext } from 'react';\n\nimport { EditorStore } from './store';\n\nexport type EditorContextType = EditorStore;\nexport const EditorContext = createContext<EditorContextType>(null);\n", "import { createContext, useContext } from 'react';\n\nimport { CoreEventHandlers } from './CoreEventHandlers';\n\nexport const EventHandlerContext = createContext<CoreEventHandlers>(null);\n\nexport const useEventHandler = () => useContext(EventHandlerContext);\n", "import {\n  useCollector,\n  useCollectorReturnType,\n  QueryCallbacksFor,\n  wrapConnector<PERSON><PERSON>s,\n  EventHandlerConnectors,\n  ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT,\n} from '@craftjs/utils';\nimport { useContext, useEffect, useMemo } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EditorContext } from './EditorContext';\nimport { QueryMethods } from './query';\nimport { EditorStore } from './store';\n\nimport { CoreEventHandlers } from '../events/CoreEventHandlers';\nimport { useEventHandler } from '../events/EventContext';\nimport { EditorState } from '../interfaces';\n\nexport type EditorCollector<C> = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => C;\n\nexport type useInternalEditorReturnType<C = null> = useCollectorReturnType<\n  EditorStore,\n  C\n> & {\n  inContext: boolean;\n  store: EditorStore;\n  connectors: EventHandlerConnectors<CoreEventHandlers, React.ReactElement>;\n};\n\nexport function useInternalEditor<C>(\n  collector?: EditorCollector<C>\n): useInternalEditorReturnType<C> {\n  const handler = useEventHandler();\n  const store = useContext(EditorContext);\n  invariant(store, ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT);\n\n  const collected = useCollector(store, collector);\n\n  const connectorsUsage = useMemo(\n    () => handler && handler.createConnectorsUsage(),\n    [handler]\n  );\n\n  useEffect(() => {\n    connectorsUsage.register();\n\n    return () => {\n      connectorsUsage.cleanup();\n    };\n  }, [connectorsUsage]);\n\n  const connectors = useMemo(\n    () => connectorsUsage && wrapConnectorHooks(connectorsUsage.connectors),\n    [connectorsUsage]\n  );\n\n  return {\n    ...collected,\n    connectors,\n    inContext: !!store,\n    store,\n  };\n}\n", "import {\n  wrapConnectorHooks,\n  ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT,\n} from '@craftjs/utils';\nimport { useMemo, useContext } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { NodeContext } from './NodeContext';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { Node } from '../interfaces';\n\nexport function useInternalNode<S = null>(collect?: (node: Node) => S) {\n  const context = useContext(NodeContext);\n  invariant(context, ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT);\n\n  const { id, related } = context;\n\n  const {\n    actions: EditorActions,\n    query,\n    connectors: editorConnectors,\n    ...collected\n  } = useInternalEditor(\n    (state) => id && state.nodes[id] && collect && collect(state.nodes[id])\n  );\n\n  const connectors = useMemo(\n    () =>\n      wrapConnectorHooks({\n        connect: (dom: HTMLElement) => editorConnectors.connect(dom, id),\n        drag: (dom: HTMLElement) => editorConnectors.drag(dom, id),\n      }),\n    [editorConnectors, id]\n  );\n\n  const actions = useMemo(() => {\n    return {\n      setProp: (cb: any, throttleRate?: number) => {\n        if (throttleRate) {\n          EditorActions.history.throttle(throttleRate).setProp(id, cb);\n        } else {\n          EditorActions.setProp(id, cb);\n        }\n      },\n      setCustom: (cb: any, throttleRate?: number) => {\n        if (throttleRate) {\n          EditorActions.history.throttle(throttleRate).setCustom(id, cb);\n        } else {\n          EditorActions.setCustom(id, cb);\n        }\n      },\n      setHidden: (bool: boolean) => EditorActions.setHidden(id, bool),\n    };\n  }, [EditorActions, id]);\n\n  return {\n    ...collected,\n    id,\n    related,\n    inNodeContext: !!context,\n    actions,\n    connectors,\n  };\n}\n", "import { deprecationWarning } from '@craftjs/utils';\n\nimport { Node } from '../interfaces';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\n/**\n * A Hook to that provides methods and state information related to the corresponding Node that manages the current component.\n * @param collect - Collector function to consume values from the corresponding Node's state\n */\nexport function useNode<S = null>(collect?: (node: Node) => S) {\n  const {\n    id,\n    related,\n    actions,\n    inNodeContext,\n    connectors,\n    ...collected\n  } = useInternalNode(collect);\n\n  return {\n    ...collected,\n    actions,\n    id,\n    related,\n    setProp: (\n      cb: (props: Record<string, any>) => void,\n      throttleRate?: number\n    ) => {\n      deprecationWarning('useNode().setProp()', {\n        suggest: 'useNode().actions.setProp()',\n      });\n      return actions.setProp(cb, throttleRate);\n    },\n    inNodeContext,\n    connectors,\n  };\n}\n", "import React from 'react';\n\nimport { useNode } from '../hooks/useNode';\n\nexport const SimpleElement = ({ render }: any) => {\n  const {\n    connectors: { connect, drag },\n  } = useNode();\n\n  return typeof render.type === 'string'\n    ? connect(drag(React.cloneElement(render)))\n    : render;\n};\n", "import React, { useMemo } from 'react';\n\nimport { SimpleElement } from './SimpleElement';\n\nimport { NodeId } from '../interfaces';\nimport { NodeElement } from '../nodes/NodeElement';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\nexport const DefaultRender = () => {\n  const { type, props, nodes, hydrationTimestamp } = useInternalNode(\n    (node) => ({\n      type: node.data.type,\n      props: node.data.props,\n      nodes: node.data.nodes,\n      hydrationTimestamp: node._hydrationTimestamp,\n    })\n  );\n\n  return useMemo(() => {\n    let children = props.children;\n\n    if (nodes && nodes.length > 0) {\n      children = (\n        <React.Fragment>\n          {nodes.map((id: NodeId) => (\n            <NodeElement id={id} key={id} />\n          ))}\n        </React.Fragment>\n      );\n    }\n\n    const render = React.createElement(type, props, children);\n\n    if (typeof type == 'string') {\n      return <SimpleElement render={render} />;\n    }\n\n    return render;\n    // eslint-disable-next-line  react-hooks/exhaustive-deps\n  }, [type, props, hydrationTimestamp, nodes]);\n};\n", "import React from 'react';\n\nimport { DefaultRender } from './DefaultRender';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\ntype RenderNodeToElementProps = {\n  render?: React.ReactElement;\n  children?: React.ReactNode;\n};\nexport const RenderNodeToElement = ({ render }: RenderNodeToElementProps) => {\n  const { hidden } = useInternalNode((node) => ({\n    hidden: node.data.hidden,\n  }));\n\n  const { onRender } = useInternalEditor((state) => ({\n    onRender: state.options.onRender,\n  }));\n\n  // don't display the node since it's hidden\n  if (hidden) {\n    return null;\n  }\n\n  return React.createElement(onRender, { render: render || <DefaultRender /> });\n};\n", "import React from 'react';\n\nimport { NodeProvider } from './NodeContext';\n\nimport { NodeId } from '../interfaces';\nimport { RenderNodeToElement } from '../render/RenderNode';\n\nexport type NodeElementProps = {\n  id: NodeId;\n  render?: React.ReactElement;\n};\n\nexport const NodeElement = ({ id, render }: NodeElementProps) => {\n  return (\n    <NodeProvider id={id}>\n      <RenderNodeToElement render={render} />\n    </NodeProvider>\n  );\n};\n", "import { ERROR_TOP_LEVEL_ELEMENT_NO_ID } from '@craftjs/utils';\nimport React, { useState } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { NodeElement } from './NodeElement';\nimport { useInternalNode } from './useInternalNode';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { NodeId } from '../interfaces';\n\nexport const defaultElementProps = {\n  is: 'div',\n  canvas: false,\n  custom: {},\n  hidden: false,\n};\n\nexport const elementPropToNodeData = {\n  is: 'type',\n  canvas: 'isCanvas',\n};\n\nexport type ElementProps<T extends React.ElementType> = {\n  id?: NodeId;\n  is?: T;\n  custom?: Record<string, any>;\n  children?: React.ReactNode;\n  canvas?: boolean;\n  hidden?: boolean;\n} & React.ComponentProps<T>;\n\nexport function Element<T extends React.ElementType>({\n  id,\n  children,\n  ...elementProps\n}: ElementProps<T>) {\n  const { is } = {\n    ...defaultElementProps,\n    ...elementProps,\n  };\n\n  const { query, actions } = useInternalEditor();\n  const { id: nodeId, inNodeContext } = useInternalNode();\n\n  const [linkedNodeId] = useState<NodeId | null>(() => {\n    invariant(!!id, ERROR_TOP_LEVEL_ELEMENT_NO_ID);\n    const node = query.node(nodeId).get();\n\n    if (inNodeContext) {\n      const existingNode = node.data.linkedNodes[id]\n        ? query.node(node.data.linkedNodes[id]).get()\n        : null;\n\n      // Render existing linked Node if it already exists (and is the same type as the JSX)\n      if (existingNode && existingNode.data.type === is) {\n        return existingNode.id;\n      }\n\n      // otherwise, create and render a new linked Node\n      const linkedElement = React.createElement(\n        Element,\n        elementProps,\n        children\n      );\n\n      const tree = query.parseReactElement(linkedElement).toNodeTree();\n\n      actions.history.ignore().addLinkedNodeFromTree(tree, nodeId, id);\n      return tree.rootNodeId;\n    }\n    return null;\n  });\n\n  return linkedNodeId ? <NodeElement id={linkedNodeId} /> : null;\n}\n", "import { deprecationWarning } from '@craftjs/utils';\nimport React, { useEffect } from 'react';\n\nimport { Element, ElementProps } from './Element';\n\nexport type CanvasProps<T extends React.ElementType> = ElementProps<T>;\n\nexport const deprecateCanvasComponent = () =>\n  deprecationWarning('<Canvas />', {\n    suggest: '<Element canvas={true} />',\n  });\n\nexport function Canvas<T extends React.ElementType>({\n  ...props\n}: CanvasProps<T>) {\n  useEffect(() => deprecateCanvasComponent(), []);\n\n  return <Element {...props} canvas={true} />;\n}\n", "import { deprecationWarning, ROOT_NODE } from '@craftjs/utils';\nimport React, { useRef } from 'react';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { SerializedNodes } from '../interfaces';\nimport { NodeElement } from '../nodes/NodeElement';\n\nexport type FrameProps = {\n  children?: React.ReactNode;\n  json?: string;\n  data?: string | SerializedNodes;\n};\n\nconst RenderRootNode = () => {\n  const { timestamp } = useInternalEditor((state) => ({\n    timestamp:\n      state.nodes[ROOT_NODE] && state.nodes[ROOT_NODE]._hydrationTimestamp,\n  }));\n\n  if (!timestamp) {\n    return null;\n  }\n\n  return <NodeElement id={ROOT_NODE} key={timestamp} />;\n};\n\n/**\n * A React Component that defines the editable area\n */\nexport const Frame = ({ children, json, data }: FrameProps) => {\n  const { actions, query } = useInternalEditor();\n\n  if (!!json) {\n    deprecationWarning('<Frame json={...} />', {\n      suggest: '<Frame data={...} />',\n    });\n  }\n\n  const isLoaded = useRef(false);\n\n  if (!isLoaded.current) {\n    const initialData = data || json;\n\n    if (initialData) {\n      actions.history.ignore().deserialize(initialData);\n    } else if (children) {\n      const rootNode = React.Children.only(children) as React.ReactElement;\n\n      const node = query.parseReactElement(rootNode).toNodeTree((node, jsx) => {\n        if (jsx === rootNode) {\n          node.id = ROOT_NODE;\n        }\n        return node;\n      });\n\n      actions.history.ignore().addNodeTree(node);\n    }\n\n    isLoaded.current = true;\n  }\n\n  return <RenderRootNode />;\n};\n", "import { QueryCallbacksFor } from '@craftjs/utils';\nimport React from 'react';\n\nimport { QueryMethods } from '../editor/query';\n\nexport type UserComponentConfig<T> = {\n  displayName: string;\n  rules: Partial<NodeRules>;\n  related: Partial<NodeRelated>;\n  props: Partial<T>;\n  custom: Record<string, any>;\n  info: Record<string, any>;\n  isCanvas: boolean;\n\n  // TODO: Deprecate\n  name: string;\n  defaultProps: Partial<T>;\n};\n\nexport type UserComponent<T = any> = React.ComponentType<T> & {\n  craft?: Partial<UserComponentConfig<T>>;\n};\n\nexport type NodeId = string;\nexport type NodeEventTypes = 'selected' | 'dragged' | 'hovered';\n\nexport type Node = {\n  id: NodeId;\n  data: NodeData;\n  info: Record<string, any>;\n  events: Record<NodeEventTypes, boolean>;\n  dom: HTMLElement | null;\n  related: Record<string, React.ElementType>;\n  rules: NodeRules;\n  _hydrationTimestamp: number;\n};\n\nexport type NodeHelpersType = QueryCallbacksFor<typeof QueryMethods>['node'];\nexport type NodeRules = {\n  canDrag(node: Node, helpers: NodeHelpersType): boolean;\n  canDrop(dropTarget: Node, self: Node, helpers: NodeHelpersType): boolean;\n  canMoveIn(canMoveIn: Node[], self: Node, helpers: NodeHelpersType): boolean;\n  canMoveOut(canMoveOut: Node[], self: Node, helpers: NodeHelpersType): boolean;\n};\nexport type NodeRelated = Record<string, React.ElementType>;\n\nexport type NodeData = {\n  props: Record<string, any>;\n  type: string | React.ElementType;\n  name: string;\n  displayName: string;\n  isCanvas: boolean;\n  parent: NodeId | null;\n  linkedNodes: Record<string, NodeId>;\n  nodes: NodeId[];\n  hidden: boolean;\n  custom?: any;\n  _childCanvas?: Record<string, NodeId>; // TODO: Deprecate in favour of linkedNodes\n};\n\nexport type FreshNode = {\n  id?: NodeId;\n  data: Partial<NodeData> & Required<Pick<NodeData, 'type'>>;\n};\n\nexport type ReduceCompType =\n  | string\n  | {\n      resolvedName: string;\n    };\n\nexport type ReducedComp = {\n  type: ReduceCompType;\n  isCanvas: boolean;\n  props: any;\n};\n\nexport type SerializedNode = Omit<\n  NodeData,\n  'type' | 'subtype' | 'name' | 'event'\n> &\n  ReducedComp;\n\nexport type SerializedNodes = Record<NodeId, SerializedNode>;\n\n// TODO: Deprecate in favor of SerializedNode\nexport type SerializedNodeData = SerializedNode;\n\nexport type Nodes = Record<NodeId, Node>;\n\n/**\n * A NodeTree is an internal data structure for CRUD operations that involve\n * more than a single node.\n *\n * For example, when we drop a component we use a tree because we\n * need to drop more than a single component.\n */\nexport interface NodeTree {\n  rootNodeId: NodeId;\n  nodes: Nodes;\n}\n\ntype NodeIdSelector = NodeId | NodeId[];\ntype NodeObjSelector = Node | Node[];\n\nexport enum NodeSelectorType {\n  Any,\n  Id,\n  Obj,\n}\n\nexport type NodeSelector<\n  T extends NodeSelectorType = NodeSelectorType.Any\n> = T extends NodeSelectorType.Id\n  ? NodeIdSelector\n  : T extends NodeSelectorType.Obj\n  ? NodeObjSelector\n  : NodeIdSelector | NodeObjSelector;\n\nexport type NodeSelectorWrapper = {\n  node: Node;\n  exists: boolean;\n};\n", "import { Overwrite, Delete, OverwriteFnReturnType } from '@craftjs/utils';\nimport { useMemo } from 'react';\n\nimport {\n  useInternalEditor,\n  EditorCollector,\n  useInternalEditorReturnType,\n} from '../editor/useInternalEditor';\n\ntype PrivateActions =\n  | 'addLinkedNodeFromTree'\n  | 'setNodeEvent'\n  | 'setDOM'\n  | 'replaceNodes'\n  | 'reset';\n\nconst getPublicActions = (actions) => {\n  const {\n    addLinkedNodeFromTree,\n    setDOM,\n    setNodeEvent,\n    replaceNodes,\n    reset,\n    ...EditorActions\n  } = actions;\n\n  return EditorActions;\n};\n\nexport type WithoutPrivateActions<S = null> = Delete<\n  useInternalEditorReturnType<S>['actions'],\n  PrivateActions | 'history'\n> & {\n  history: Overwrite<\n    useInternalEditorReturnType<S>['actions']['history'],\n    {\n      ignore: OverwriteFnReturnType<\n        useInternalEditorReturnType<S>['actions']['history']['ignore'],\n        PrivateActions\n      >;\n      throttle: OverwriteFnReturnType<\n        useInternalEditorReturnType<S>['actions']['history']['throttle'],\n        PrivateActions\n      >;\n    }\n  >;\n};\n\nexport type useEditorReturnType<S = null> = Overwrite<\n  useInternalEditorReturnType<S>,\n  {\n    actions: WithoutPrivateActions;\n    query: Delete<useInternalEditorReturnType<S>['query'], 'deserialize'>;\n  }\n>;\n\n/**\n * A Hook that that provides methods and information related to the entire editor state.\n * @param collector Collector function to consume values from the editor's state\n */\nexport function useEditor(): useEditorReturnType;\nexport function useEditor<S>(\n  collect: EditorCollector<S>\n): useEditorReturnType<S>;\n\nexport function useEditor<S>(collect?: any): useEditorReturnType<S> {\n  const {\n    connectors,\n    actions: internalActions,\n    query,\n    store,\n    ...collected\n  } = useInternalEditor(collect);\n\n  const EditorActions = getPublicActions(internalActions);\n\n  const actions = useMemo(() => {\n    return {\n      ...EditorActions,\n      history: {\n        ...EditorActions.history,\n        ignore: (...args) =>\n          getPublicActions(EditorActions.history.ignore(...args)),\n        throttle: (...args) =>\n          getPublicActions(EditorActions.history.throttle(...args)),\n      },\n    };\n  }, [EditorActions]);\n\n  return {\n    connectors,\n    actions,\n    query,\n    store,\n    ...(collected as any),\n  };\n}\n", "import React from 'react';\n\nimport { EditorState } from '../../interfaces';\nimport { useEditor } from '../useEditor';\n\nexport function connectEditor<C>(collect?: (state: EditorState) => C) {\n  return (WrappedComponent: React.ElementType) => {\n    return (props: any) => {\n      const Editor = collect ? useEditor(collect) : useEditor();\n      return <WrappedComponent {...Editor} {...props} />;\n    };\n  };\n}\n", "import React from 'react';\n\nimport { Node } from '../../interfaces';\nimport { useNode } from '../useNode';\n\nexport function connectNode<C>(collect?: (state: Node) => C) {\n  return function (WrappedComponent: React.ElementType) {\n    return (props: any) => {\n      const node = useNode(collect);\n      return <WrappedComponent {...node} {...props} />;\n    };\n  };\n}\n", "export const fromEntries = (pairs) => {\n  if (Object.fromEntries) {\n    return Object.fromEntries(pairs);\n  }\n  return pairs.reduce(\n    (accum, [id, value]) => ({\n      ...accum,\n      [id]: value,\n    }),\n    {}\n  );\n};\n", "import { ERROR_INVALID_NODEID } from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { Nodes, Node, NodeSelectorWrapper, NodeSelector } from '../interfaces';\n\ntype config = { existOnly: boolean; idOnly: boolean };\nexport const getNodesFromSelector = (\n  nodes: Nodes,\n  selector: NodeSelector,\n  config?: Partial<config>\n): NodeSelectorWrapper[] => {\n  const items = Array.isArray(selector) ? selector : [selector];\n\n  const mergedConfig = {\n    existOnly: false,\n    idOnly: false,\n    ...(config || {}),\n  };\n\n  const nodeSelectors = items\n    .filter((item) => !!item)\n    .map((item) => {\n      if (typeof item === 'string') {\n        return {\n          node: nodes[item],\n          exists: !!nodes[item],\n        };\n      }\n\n      if (typeof item === 'object' && !mergedConfig.idOnly) {\n        const node = item as Node;\n        return {\n          node,\n          exists: !!nodes[node.id],\n        };\n      }\n\n      return {\n        node: null,\n        exists: false,\n      };\n    });\n\n  if (mergedConfig.existOnly) {\n    invariant(\n      nodeSelectors.filter((selector) => !selector.exists).length === 0,\n      ERROR_INVALID_NODEID\n    );\n  }\n\n  return nodeSelectors;\n};\n", "import { ERROR_NOT_IN_RESOLVER } from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { Resolver } from '../interfaces';\n\ntype ReversedResolver = Map<React.ComponentType | string, string>;\n\ntype CachedResolverData = {\n  resolver: Resolver;\n  reversed: ReversedResolver;\n};\n\nlet CACHED_RESOLVER_DATA: CachedResolverData | null = null;\n\nconst getReversedResolver = (resolver: Resolver): ReversedResolver => {\n  if (CACHED_RESOLVER_DATA && CACHED_RESOLVER_DATA.resolver === resolver) {\n    return CACHED_RESOLVER_DATA.reversed;\n  }\n\n  CACHED_RESOLVER_DATA = {\n    resolver,\n    reversed: new Map(),\n  };\n\n  for (const [name, comp] of Object.entries(resolver)) {\n    CACHED_RESOLVER_DATA.reversed.set(comp, name);\n  }\n\n  return CACHED_RESOLVER_DATA.reversed;\n};\n\nconst getComponentName = (component: React.ElementType): string | undefined => {\n  return (component as any).name || (component as any).displayName;\n};\n\nconst searchComponentInResolver = (\n  resolver: Resolver,\n  comp: React.ElementType\n): string | null => {\n  const name = getReversedResolver(resolver).get(comp);\n  return name !== undefined ? name : null;\n};\n\nexport const resolveComponent = (\n  resolver: Resolver,\n  comp: React.ElementType | string\n): string => {\n  if (typeof comp === 'string') {\n    return comp;\n  }\n\n  const resolvedName = searchComponentInResolver(resolver, comp);\n\n  invariant(\n    resolvedName,\n    ERROR_NOT_IN_RESOLVER.replace('%node_type%', getComponentName(comp))\n  );\n\n  return resolvedName;\n};\n", "import React, { Children } from 'react';\n\nimport { resolveComponent } from './resolveComponent';\n\nimport { NodeData, ReducedComp, SerializedNode } from '../interfaces';\nimport { Resolver } from '../interfaces';\n\nconst reduceType = (type: React.ElementType | string, resolver: Resolver) => {\n  if (typeof type === 'string') {\n    return type;\n  }\n  return { resolvedName: resolveComponent(resolver, type) };\n};\n\nexport const serializeComp = (\n  data: Pick<NodeData, 'type' | 'isCanvas' | 'props'>,\n  resolver: Resolver\n): ReducedComp => {\n  let { type, isCanvas, props } = data;\n  props = Object.keys(props).reduce((result: Record<string, any>, key) => {\n    const prop = props[key];\n\n    if (prop === undefined || prop === null || typeof prop === 'function') {\n      return result;\n    }\n\n    if (key === 'children' && typeof prop !== 'string') {\n      result[key] = Children.map(prop, (child) => {\n        if (typeof child === 'string') {\n          return child;\n        }\n        return serializeComp(child, resolver);\n      });\n    } else if (typeof prop.type === 'function') {\n      result[key] = serializeComp(prop, resolver);\n    } else {\n      result[key] = prop;\n    }\n    return result;\n  }, {});\n\n  return {\n    type: reduceType(type, resolver),\n    isCanvas: !!isCanvas,\n    props,\n  };\n};\n\nexport const serializeNode = (\n  data: Omit<NodeData, 'event'>,\n  resolver: Resolver\n): SerializedNode => {\n  const { type, props, isCanvas, name, ...nodeData } = data;\n\n  const reducedComp = serializeComp({ type, isCanvas, props }, resolver);\n\n  return {\n    ...reducedComp,\n    ...nodeData,\n  };\n};\n", "import {\n  deprecationWarning,\n  ERROR_CANNOT_DRAG,\n  ERROR_DUPLICATE_NODEID,\n  ERROR_INVALID_NODE_ID,\n  ERROR_MOVE_INCOMING_PARENT,\n  ERROR_MOVE_NONCANVAS_CHILD,\n  ERROR_MOVE_OUTGOING_PARENT,\n  ERROR_MOVE_TO_DESCENDANT,\n  ERROR_MOVE_TO_NONCANVAS_PARENT,\n  ERROR_MOVE_TOP_LEVEL_NODE,\n  ERROR_MOVE_CANNOT_DROP,\n  ROOT_NODE,\n} from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { EditorState, NodeId, NodeSelector } from '../interfaces';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { serializeNode } from '../utils/serializeNode';\n\nexport function NodeHelpers(state: EditorState, id: NodeId) {\n  invariant(typeof id == 'string', ERROR_INVALID_NODE_ID);\n\n  const node = state.nodes[id];\n\n  const nodeHelpers = (id) => NodeHelpers(state, id);\n\n  return {\n    isCanvas() {\n      return !!node.data.isCanvas;\n    },\n    isRoot() {\n      return node.id === ROOT_NODE;\n    },\n    isLinkedNode() {\n      return (\n        node.data.parent &&\n        nodeHelpers(node.data.parent).linkedNodes().includes(node.id)\n      );\n    },\n    isTopLevelNode() {\n      return this.isRoot() || this.isLinkedNode();\n    },\n    isDeletable() {\n      return !this.isTopLevelNode();\n    },\n    isParentOfTopLevelNodes: () =>\n      node.data.linkedNodes && Object.keys(node.data.linkedNodes).length > 0,\n    isParentOfTopLevelCanvas() {\n      deprecationWarning('query.node(id).isParentOfTopLevelCanvas', {\n        suggest: 'query.node(id).isParentOfTopLevelNodes',\n      });\n      return this.isParentOfTopLevelNodes();\n    },\n    isSelected() {\n      return state.events.selected.has(id);\n    },\n    isHovered() {\n      return state.events.hovered.has(id);\n    },\n    isDragged() {\n      return state.events.dragged.has(id);\n    },\n    get() {\n      return node;\n    },\n    ancestors(deep = false): NodeId[] {\n      function appendParentNode(\n        id: NodeId,\n        ancestors: NodeId[] = [],\n        depth: number = 0\n      ) {\n        const node = state.nodes[id];\n        if (!node) {\n          return ancestors;\n        }\n\n        ancestors.push(id);\n\n        if (!node.data.parent) {\n          return ancestors;\n        }\n\n        if (deep || (!deep && depth === 0)) {\n          ancestors = appendParentNode(node.data.parent, ancestors, depth + 1);\n        }\n        return ancestors;\n      }\n      return appendParentNode(node.data.parent);\n    },\n    descendants(\n      deep = false,\n      includeOnly?: 'linkedNodes' | 'childNodes'\n    ): NodeId[] {\n      function appendChildNode(\n        id: NodeId,\n        descendants: NodeId[] = [],\n        depth: number = 0\n      ) {\n        if (deep || (!deep && depth === 0)) {\n          const node = state.nodes[id];\n\n          if (!node) {\n            return descendants;\n          }\n\n          if (includeOnly !== 'childNodes') {\n            // Include linkedNodes if any\n            const linkedNodes = nodeHelpers(id).linkedNodes();\n\n            linkedNodes.forEach((nodeId) => {\n              descendants.push(nodeId);\n              descendants = appendChildNode(nodeId, descendants, depth + 1);\n            });\n          }\n\n          if (includeOnly !== 'linkedNodes') {\n            const childNodes = nodeHelpers(id).childNodes();\n\n            childNodes.forEach((nodeId) => {\n              descendants.push(nodeId);\n              descendants = appendChildNode(nodeId, descendants, depth + 1);\n            });\n          }\n\n          return descendants;\n        }\n        return descendants;\n      }\n      return appendChildNode(id);\n    },\n    linkedNodes() {\n      return Object.values(node.data.linkedNodes || {});\n    },\n    childNodes() {\n      return node.data.nodes || [];\n    },\n    isDraggable(onError?: (err: string) => void) {\n      try {\n        const targetNode = node;\n        invariant(!this.isTopLevelNode(), ERROR_MOVE_TOP_LEVEL_NODE);\n        invariant(\n          NodeHelpers(state, targetNode.data.parent).isCanvas(),\n          ERROR_MOVE_NONCANVAS_CHILD\n        );\n        invariant(\n          targetNode.rules.canDrag(targetNode, nodeHelpers),\n          ERROR_CANNOT_DRAG\n        );\n        return true;\n      } catch (err) {\n        if (onError) {\n          onError(err);\n        }\n        return false;\n      }\n    },\n    isDroppable(selector: NodeSelector, onError?: (err: string) => void) {\n      const targets = getNodesFromSelector(state.nodes, selector);\n\n      const newParentNode = node;\n\n      try {\n        invariant(this.isCanvas(), ERROR_MOVE_TO_NONCANVAS_PARENT);\n        invariant(\n          newParentNode.rules.canMoveIn(\n            targets.map((selector) => selector.node),\n            newParentNode,\n            nodeHelpers\n          ),\n          ERROR_MOVE_INCOMING_PARENT\n        );\n\n        const parentNodes = {};\n\n        targets.forEach(({ node: targetNode, exists }) => {\n          invariant(\n            targetNode.rules.canDrop(newParentNode, targetNode, nodeHelpers),\n            ERROR_MOVE_CANNOT_DROP\n          );\n\n          // Ignore other checking if the Node is new\n          if (!exists) {\n            return;\n          }\n\n          invariant(\n            !nodeHelpers(targetNode.id).isTopLevelNode(),\n            ERROR_MOVE_TOP_LEVEL_NODE\n          );\n\n          const targetDeepNodes = nodeHelpers(targetNode.id).descendants(true);\n\n          invariant(\n            !targetDeepNodes.includes(newParentNode.id) &&\n              newParentNode.id !== targetNode.id,\n            ERROR_MOVE_TO_DESCENDANT\n          );\n\n          const currentParentNode =\n            targetNode.data.parent && state.nodes[targetNode.data.parent];\n\n          invariant(\n            currentParentNode.data.isCanvas,\n            ERROR_MOVE_NONCANVAS_CHILD\n          );\n\n          invariant(\n            currentParentNode ||\n              (!currentParentNode && !state.nodes[targetNode.id]),\n            ERROR_DUPLICATE_NODEID\n          );\n\n          if (currentParentNode.id !== newParentNode.id) {\n            if (!parentNodes[currentParentNode.id]) {\n              parentNodes[currentParentNode.id] = [];\n            }\n\n            parentNodes[currentParentNode.id].push(targetNode);\n          }\n        });\n\n        Object.keys(parentNodes).forEach((parentNodeId) => {\n          const childNodes = parentNodes[parentNodeId];\n          const parentNode = state.nodes[parentNodeId];\n\n          invariant(\n            parentNode.rules.canMoveOut(childNodes, parentNode, nodeHelpers),\n            ERROR_MOVE_OUTGOING_PARENT\n          );\n        });\n\n        return true;\n      } catch (err) {\n        if (onError) {\n          onError(err);\n        }\n        return false;\n      }\n    },\n    toSerializedNode() {\n      return serializeNode(node.data, state.options.resolver);\n    },\n    toNodeTree(includeOnly?: 'linkedNodes' | 'childNodes') {\n      const nodes = [id, ...this.descendants(true, includeOnly)].reduce(\n        (accum, descendantId) => {\n          accum[descendantId] = nodeHelpers(descendantId).get();\n          return accum;\n        },\n        {}\n      );\n\n      return {\n        rootNodeId: id,\n        nodes,\n      };\n    },\n\n    /**\n     Deprecated NodeHelpers\n     **/\n\n    decendants(deep = false) {\n      deprecationWarning('query.node(id).decendants', {\n        suggest: 'query.node(id).descendants',\n      });\n      return this.descendants(deep);\n    },\n    isTopLevelCanvas() {\n      return !this.isRoot() && !node.data.parent;\n    },\n  };\n}\n", "import { Node, NodeInfo, DropPosition } from '../interfaces';\n\nexport default function findPosition(\n  parent: Node,\n  dims: NodeInfo[],\n  posX: number,\n  posY: number\n) {\n  let result: DropPosition = {\n    parent,\n    index: 0,\n    where: 'before',\n  };\n\n  let leftLimit = 0,\n    xLimit = 0,\n    dimRight = 0,\n    yLimit = 0,\n    xCenter = 0,\n    yCenter = 0,\n    dimDown = 0;\n\n  // Each dim is: Top, Left, Height, Width\n  for (let i = 0, len = dims.length; i < len; i++) {\n    const dim = dims[i];\n\n    // Right position of the element. Left + Width\n    dimRight = dim.left + dim.outerWidth;\n    // Bottom position of the element. Top + Height\n    dimDown = dim.top + dim.outerHeight;\n    // X center position of the element. Left + (Width / 2)\n    xCenter = dim.left + dim.outerWidth / 2;\n    // Y center position of the element. Top + (Height / 2)\n    yCenter = dim.top + dim.outerHeight / 2;\n    // Skip if over the limits\n    if (\n      (xLimit && dim.left > xLimit) ||\n      (yLimit && yCenter >= yLimit) || // >= avoid issue with clearfixes\n      (leftLimit && dimRight < leftLimit)\n    )\n      continue;\n\n    result.index = i;\n    // If it's not in flow (like 'float' element)\n    if (!dim.inFlow) {\n      if (posY < dimDown) yLimit = dimDown;\n      //If x lefter than center\n      if (posX < xCenter) {\n        xLimit = xCenter;\n        result.where = 'before';\n      } else {\n        leftLimit = xCenter;\n        result.where = 'after';\n      }\n    } else {\n      // If y upper than center\n      if (posY < yCenter) {\n        result.where = 'before';\n        break;\n      } else result.where = 'after'; // After last element\n    }\n  }\n\n  return result;\n}\n", "import { getRandomId as getRandomNodeId } from '@craftjs/utils';\nimport React from 'react';\n\nimport { Node, FreshNode, UserComponentConfig } from '../interfaces';\nimport {\n  defaultElementProps,\n  Element,\n  Canvas,\n  elementPropToNodeData,\n  deprecateCanvasComponent,\n} from '../nodes';\nimport { NodeProvider } from '../nodes/NodeContext';\n\nconst getNodeTypeName = (type: string | { name: string }) =>\n  typeof type == 'string' ? type : type.name;\n\nexport function createNode(\n  newNode: FreshNode,\n  normalize?: (node: Node) => void\n) {\n  let actualType = newNode.data.type as any;\n  let id = newNode.id || getRandomNodeId();\n\n  const node: Node = {\n    id,\n    _hydrationTimestamp: Date.now(),\n    data: {\n      type: actualType,\n      name: getNodeTypeName(actualType),\n      displayName: getNodeTypeName(actualType),\n      props: {},\n      custom: {},\n      parent: null,\n      isCanvas: false,\n      hidden: false,\n      nodes: [],\n      linkedNodes: {},\n      ...newNode.data,\n    },\n    info: {},\n    related: {},\n    events: {\n      selected: false,\n      dragged: false,\n      hovered: false,\n    },\n    rules: {\n      canDrag: () => true,\n      canDrop: () => true,\n      canMoveIn: () => true,\n      canMoveOut: () => true,\n    },\n    dom: null,\n  };\n\n  // @ts-ignore\n  if (node.data.type === Element || node.data.type === Canvas) {\n    const mergedProps = {\n      ...defaultElementProps,\n      ...node.data.props,\n    };\n\n    node.data.props = Object.keys(node.data.props).reduce((props, key) => {\n      if (Object.keys(defaultElementProps).includes(key)) {\n        // If a <Element /> specific props is found (ie: \"is\", \"canvas\")\n        // Replace the node.data with the value specified in the prop\n        node.data[elementPropToNodeData[key] || key] = mergedProps[key];\n      } else {\n        // Otherwise include the props in the node as usual\n        props[key] = node.data.props[key];\n      }\n\n      return props;\n    }, {});\n\n    actualType = node.data.type;\n    node.data.name = getNodeTypeName(actualType);\n    node.data.displayName = getNodeTypeName(actualType);\n\n    const usingDeprecatedCanvas = node.data.type === Canvas;\n    if (usingDeprecatedCanvas) {\n      node.data.isCanvas = true;\n      deprecateCanvasComponent();\n    }\n  }\n\n  if (normalize) {\n    normalize(node);\n  }\n\n  // TODO: use UserComponentConfig type\n  const userComponentConfig = actualType.craft as UserComponentConfig<any>;\n\n  if (userComponentConfig) {\n    node.data.displayName =\n      userComponentConfig.displayName ||\n      userComponentConfig.name ||\n      node.data.displayName;\n\n    node.data.props = {\n      ...(userComponentConfig.props || userComponentConfig.defaultProps || {}),\n      ...node.data.props,\n    };\n\n    node.data.custom = {\n      ...(userComponentConfig.custom || {}),\n      ...node.data.custom,\n    };\n\n    if (\n      userComponentConfig.isCanvas !== undefined &&\n      userComponentConfig.isCanvas !== null\n    ) {\n      node.data.isCanvas = userComponentConfig.isCanvas;\n    }\n\n    if (userComponentConfig.rules) {\n      Object.keys(userComponentConfig.rules).forEach((key) => {\n        if (['canDrag', 'canDrop', 'canMoveIn', 'canMoveOut'].includes(key)) {\n          node.rules[key] = userComponentConfig.rules[key];\n        }\n      });\n    }\n\n    if (userComponentConfig.related) {\n      const relatedNodeContext = {\n        id: node.id,\n        related: true,\n      };\n\n      Object.keys(userComponentConfig.related).forEach((comp) => {\n        node.related[comp] = (props) =>\n          React.createElement(\n            NodeProvider,\n            relatedNodeContext,\n            React.createElement(userComponentConfig.related[comp], props)\n          );\n      });\n    }\n\n    if (userComponentConfig.info) {\n      node.info = userComponentConfig.info;\n    }\n  }\n\n  return node;\n}\n", "import { ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER } from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { resolveComponent } from './resolveComponent';\n\nimport {\n  NodeData,\n  SerializedNode,\n  ReducedComp,\n  ReduceCompType,\n} from '../interfaces';\nimport { Resolver } from '../interfaces';\nimport { Canvas } from '../nodes/Canvas';\n\ntype DeserialisedType = React.JSX.Element & { name: string };\n\nconst restoreType = (type: ReduceCompType, resolver: Resolver) =>\n  typeof type === 'object' && type.resolvedName\n    ? type.resolvedName === 'Canvas'\n      ? Canvas\n      : resolver[type.resolvedName]\n    : typeof type === 'string'\n    ? type\n    : null;\n\nexport const deserializeComp = (\n  data: ReducedComp,\n  resolver: Resolver,\n  index?: number\n): DeserialisedType | void => {\n  let { type, props } = data;\n\n  const main = restoreType(type, resolver);\n\n  if (!main) {\n    return;\n  }\n\n  props = Object.keys(props).reduce((result: Record<string, any>, key) => {\n    const prop = props[key];\n    if (prop === null || prop === undefined) {\n      result[key] = null;\n    } else if (typeof prop === 'object' && prop.resolvedName) {\n      result[key] = deserializeComp(prop, resolver);\n    } else if (key === 'children' && Array.isArray(prop)) {\n      result[key] = prop.map((child) => {\n        if (typeof child === 'string') {\n          return child;\n        }\n        return deserializeComp(child, resolver);\n      });\n    } else {\n      result[key] = prop;\n    }\n    return result;\n  }, {});\n\n  if (index) {\n    props.key = index;\n  }\n\n  const jsx = {\n    ...React.createElement(main, {\n      ...props,\n    }),\n  };\n\n  return {\n    ...jsx,\n    name: resolveComponent(resolver, jsx.type),\n  };\n};\n\nexport const deserializeNode = (\n  data: SerializedNode,\n  resolver: Resolver\n): Omit<NodeData, 'event'> => {\n  const { type: Comp, props: Props, ...nodeData } = data;\n\n  const isCompAnHtmlElement = Comp !== undefined && typeof Comp === 'string';\n  const isCompAUserComponent =\n    Comp !== undefined &&\n    (Comp as { resolvedName?: string }).resolvedName !== undefined;\n\n  invariant(\n    isCompAnHtmlElement || isCompAUserComponent,\n    ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER.replace(\n      '%displayName%',\n      data.displayName\n    ).replace('%availableComponents%', Object.keys(resolver).join(', '))\n  );\n\n  const { type, name, props } = (deserializeComp(\n    data,\n    resolver\n  ) as unknown) as NodeData;\n\n  const { parent, custom, displayName, isCanvas, nodes, hidden } = nodeData;\n\n  const linkedNodes = nodeData.linkedNodes || nodeData._childCanvas;\n\n  return {\n    type,\n    name,\n    displayName: displayName || name,\n    props,\n    custom: custom || {},\n    isCanvas: !!isCanvas,\n    hidden: !!hidden,\n    parent,\n    linkedNodes: linkedNodes || {},\n    nodes: nodes || [],\n  };\n};\n", "import { Node, NodeTree } from '../interfaces';\n\nconst mergeNodes = (rootNode: Node, childrenNodes: NodeTree[]) => {\n  if (childrenNodes.length < 1) {\n    return { [rootNode.id]: rootNode };\n  }\n  const nodes = childrenNodes.map(({ rootNodeId }) => rootNodeId);\n  const nodeWithChildren = { ...rootNode, data: { ...rootNode.data, nodes } };\n  const rootNodes = { [rootNode.id]: nodeWithChildren };\n  return childrenNodes.reduce((accum, tree) => {\n    const currentNode = tree.nodes[tree.rootNodeId];\n    return {\n      ...accum,\n      ...tree.nodes,\n      // set the parent id for the current node\n      [currentNode.id]: {\n        ...currentNode,\n        data: {\n          ...currentNode.data,\n          parent: rootNode.id,\n        },\n      },\n    };\n  }, rootNodes);\n};\n\nexport const mergeTrees = (\n  rootNode: Node,\n  childrenNodes: NodeTree[]\n): NodeTree => ({\n  rootNodeId: rootNode.id,\n  nodes: mergeNodes(rootNode, childrenNodes),\n});\n", "import {\n  QueryCallbacksFor,\n  ERROR_NOT_IN_RESOLVER,\n  getDOMInfo,\n  deprecationWarning,\n  DEPRECATED_ROOT_NODE,\n  ROOT_NODE,\n} from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EventHelpers } from './EventHelpers';\nimport { NodeHelpers } from './NodeHelpers';\n\nimport findPosition from '../events/findPosition';\nimport {\n  NodeId,\n  EditorState,\n  Indicator,\n  Node,\n  Options,\n  NodeEventTypes,\n  NodeInfo,\n  NodeSelector,\n  NodeTree,\n  SerializedNodes,\n  SerializedNode,\n  FreshNode,\n} from '../interfaces';\nimport { createNode } from '../utils/createNode';\nimport { deserializeNode } from '../utils/deserializeNode';\nimport { fromEntries } from '../utils/fromEntries';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { mergeTrees } from '../utils/mergeTrees';\nimport { parseNodeFromJSX } from '../utils/parseNodeFromJSX';\nimport { resolveComponent } from '../utils/resolveComponent';\n\nexport function QueryMethods(state: EditorState) {\n  const options = state && state.options;\n\n  const _: () => QueryCallbacksFor<typeof QueryMethods> = () =>\n    QueryMethods(state) as any;\n\n  return {\n    /**\n     * Determine the best possible location to drop the source Node relative to the target Node\n     *\n     * TODO: replace with Positioner.computeIndicator();\n     */\n    getDropPlaceholder: (\n      source: NodeSelector,\n      target: NodeId,\n      pos: { x: number; y: number },\n      nodesToDOM: (node: Node) => HTMLElement = (node) =>\n        state.nodes[node.id].dom\n    ) => {\n      const targetNode = state.nodes[target],\n        isTargetCanvas = _().node(targetNode.id).isCanvas();\n\n      const targetParent = isTargetCanvas\n        ? targetNode\n        : state.nodes[targetNode.data.parent];\n\n      if (!targetParent) return;\n\n      const targetParentNodes = targetParent.data.nodes || [];\n\n      const dimensionsInContainer = targetParentNodes\n        ? targetParentNodes.reduce((result, id: NodeId) => {\n            const dom = nodesToDOM(state.nodes[id]);\n            if (dom) {\n              const info: NodeInfo = {\n                id,\n                ...getDOMInfo(dom),\n              };\n\n              result.push(info);\n            }\n            return result;\n          }, [] as NodeInfo[])\n        : [];\n\n      const dropAction = findPosition(\n        targetParent,\n        dimensionsInContainer,\n        pos.x,\n        pos.y\n      );\n      const currentNode =\n        targetParentNodes.length &&\n        state.nodes[targetParentNodes[dropAction.index]];\n\n      const output: Indicator = {\n        placement: {\n          ...dropAction,\n          currentNode,\n        },\n        error: null,\n      };\n\n      const sourceNodes = getNodesFromSelector(state.nodes, source);\n\n      sourceNodes.forEach(({ node, exists }) => {\n        // If source Node is already in the editor, check if it's draggable\n        if (exists) {\n          _()\n            .node(node.id)\n            .isDraggable((err) => (output.error = err));\n        }\n      });\n\n      // Check if source Node is droppable in target\n      _()\n        .node(targetParent.id)\n        .isDroppable(source, (err) => (output.error = err));\n\n      return output;\n    },\n\n    /**\n     * Get the current Editor options\n     */\n    getOptions(): Options {\n      return options;\n    },\n\n    getNodes() {\n      return state.nodes;\n    },\n\n    /**\n     * Helper methods to describe the specified Node\n     * @param id\n     */\n    node(id: NodeId) {\n      return NodeHelpers(state, id);\n    },\n\n    /**\n     * Returns all the `nodes` in a serialized format\n     */\n    getSerializedNodes(): SerializedNodes {\n      const nodePairs = Object.keys(state.nodes).map((id: NodeId) => [\n        id,\n        this.node(id).toSerializedNode(),\n      ]);\n      return fromEntries(nodePairs);\n    },\n\n    getEvent(eventType: NodeEventTypes) {\n      return EventHelpers(state, eventType);\n    },\n\n    /**\n     * Retrieve the JSON representation of the editor's Nodes\n     */\n    serialize(): string {\n      return JSON.stringify(this.getSerializedNodes());\n    },\n\n    parseReactElement: (reactElement: React.ReactElement<any>) => ({\n      toNodeTree(\n        normalize?: (node: Node, jsx: React.ReactElement<any>) => void\n      ): NodeTree {\n        let node = parseNodeFromJSX(reactElement, (node, jsx) => {\n          const name = resolveComponent(state.options.resolver, node.data.type);\n\n          node.data.displayName = node.data.displayName || name;\n          node.data.name = name;\n\n          if (normalize) {\n            normalize(node, jsx);\n          }\n        });\n\n        let childrenNodes: NodeTree[] = [];\n\n        if (reactElement.props && reactElement.props.children) {\n          childrenNodes = React.Children.toArray(\n            reactElement.props.children\n          ).reduce<NodeTree[]>((accum, child: any) => {\n            if (React.isValidElement(child)) {\n              accum.push(_().parseReactElement(child).toNodeTree(normalize));\n            }\n            return accum;\n          }, []);\n        }\n\n        return mergeTrees(node, childrenNodes);\n      },\n    }),\n\n    parseSerializedNode: (serializedNode: SerializedNode) => ({\n      toNode(normalize?: (node: Node) => void): Node {\n        const data = deserializeNode(serializedNode, state.options.resolver);\n        invariant(data.type, ERROR_NOT_IN_RESOLVER);\n\n        const id = typeof normalize === 'string' && normalize;\n\n        if (id) {\n          deprecationWarning(`query.parseSerializedNode(...).toNode(id)`, {\n            suggest: `query.parseSerializedNode(...).toNode(node => node.id = id)`,\n          });\n        }\n\n        return _()\n          .parseFreshNode({\n            ...(id ? { id } : {}),\n            data,\n          })\n          .toNode(!id && normalize);\n      },\n    }),\n\n    parseFreshNode: (node: FreshNode) => ({\n      toNode(normalize?: (node: Node) => void): Node {\n        return createNode(node, (node) => {\n          if (node.data.parent === DEPRECATED_ROOT_NODE) {\n            node.data.parent = ROOT_NODE;\n          }\n\n          const name = resolveComponent(state.options.resolver, node.data.type);\n          invariant(name !== null, ERROR_NOT_IN_RESOLVER);\n          node.data.displayName = node.data.displayName || name;\n          node.data.name = name;\n\n          if (normalize) {\n            normalize(node);\n          }\n        });\n      },\n    }),\n\n    createNode(reactElement: React.ReactElement, extras?: any) {\n      deprecationWarning(`query.createNode(${reactElement})`, {\n        suggest: `query.parseReactElement(${reactElement}).toNodeTree()`,\n      });\n\n      const tree = this.parseReactElement(reactElement).toNodeTree();\n\n      const node = tree.nodes[tree.rootNodeId];\n\n      if (!extras) {\n        return node;\n      }\n\n      if (extras.id) {\n        node.id = extras.id;\n      }\n\n      if (extras.data) {\n        node.data = {\n          ...node.data,\n          ...extras.data,\n        };\n      }\n\n      return node;\n    },\n\n    getState() {\n      return state;\n    },\n  };\n}\n", "import { EditorState, NodeId, NodeEventTypes } from '../interfaces';\n\nexport function EventHelpers(state: EditorState, eventType: NodeEventTypes) {\n  const event = state.events[eventType];\n  return {\n    contains(id: NodeId) {\n      return event.has(id);\n    },\n    isEmpty() {\n      return this.all().length === 0;\n    },\n    first() {\n      const values = this.all();\n      return values[0];\n    },\n    last() {\n      const values = this.all();\n      return values[values.length - 1];\n    },\n    all() {\n      return Array.from(event);\n    },\n    size() {\n      return this.all().length;\n    },\n    at(i: number) {\n      return this.all()[i];\n    },\n    raw() {\n      return event;\n    },\n  };\n}\n", "import React, { Fragment } from 'react';\n\nimport { createNode } from './createNode';\n\nimport { Node } from '../interfaces';\n\nexport function parseNodeFromJSX(\n  jsx: React.ReactElement<any> | string,\n  normalize?: (node: Node, jsx: React.ReactElement<any>) => void\n) {\n  let element = jsx as React.ReactElement<any>;\n\n  if (typeof element === 'string') {\n    element = React.createElement(Fragment, {}, element) as React.ReactElement<\n      any\n    >;\n  }\n\n  let actualType = element.type as any;\n\n  return createNode(\n    {\n      data: {\n        type: actualType,\n        props: { ...element.props },\n      },\n    },\n    (node) => {\n      if (normalize) {\n        normalize(node, element);\n      }\n    }\n  );\n}\n", "import { DerivedEventHandlers, EventHandlers } from '@craftjs/utils';\n\nimport { EditorStore } from '../editor/store';\nimport { NodeId, NodeTree } from '../interfaces/nodes';\n\nexport interface CreateHandlerOptions {\n  onCreate: (nodeTree: NodeTree) => void;\n}\n\nexport class CoreEventHandlers<O = {}> extends EventHandlers<\n  { store: EditorStore; removeHoverOnMouseleave: boolean } & O\n> {\n  handlers() {\n    return {\n      connect: (el: HTMLElement, id: NodeId) => {},\n      select: (el: HTMLElement, id: NodeId) => {},\n      hover: (el: HTMLElement, id: NodeId) => {},\n      drag: (el: HTMLElement, id: NodeId) => {},\n      drop: (el: HTMLElement, id: NodeId) => {},\n      create: (\n        el: HTMLElement,\n        UserElement: React.ReactElement | (() => NodeTree | React.ReactElement),\n        options?: Partial<CreateHandlerOptions>\n      ) => {},\n    };\n  }\n}\n\nexport abstract class DerivedCoreEventHandlers<\n  O = {}\n> extends DerivedEventHandlers<CoreEventHandlers, O> {}\n", "import { getDOMInfo, ROOT_NODE } from '@craftjs/utils';\n\nimport findPosition from './findPosition';\n\nimport { EditorStore } from '../editor/store';\nimport {\n  DragTarget,\n  DropPosition,\n  Indicator,\n  Node,\n  NodeId,\n  NodeInfo,\n  NodeSelectorWrapper,\n} from '../interfaces';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\n\n// Hack: to trigger dragend event immediate\n// Otherwise we would have to wait until the native animation is completed before we can actually drop an block\nconst documentDragoverEventHandler = (e: DragEvent) => {\n  e.preventDefault();\n};\n\n/**\n * Positioner is responsible for computing the drop Indicator during a sequence of drag-n-drop events\n */\nexport class Positioner {\n  static BORDER_OFFSET = 10;\n\n  // Current Node being hovered on\n  private currentDropTargetId: NodeId | null;\n  // Current closest Canvas Node relative to the currentDropTarget\n  private currentDropTargetCanvasAncestorId: NodeId | null;\n\n  private currentIndicator: Indicator | null = null;\n\n  private currentTargetId: NodeId | null;\n  private currentTargetChildDimensions: NodeInfo[] | null;\n\n  private dragError: string | null;\n  private draggedNodes: NodeSelectorWrapper[];\n\n  private onScrollListener: (e: Event) => void;\n\n  constructor(readonly store: EditorStore, readonly dragTarget: DragTarget) {\n    this.currentDropTargetId = null;\n    this.currentDropTargetCanvasAncestorId = null;\n\n    this.currentTargetId = null;\n    this.currentTargetChildDimensions = null;\n\n    this.currentIndicator = null;\n\n    this.dragError = null;\n    this.draggedNodes = this.getDraggedNodes();\n\n    this.validateDraggedNodes();\n\n    this.onScrollListener = this.onScroll.bind(this);\n    window.addEventListener('scroll', this.onScrollListener, true);\n    window.addEventListener('dragover', documentDragoverEventHandler, false);\n  }\n\n  cleanup() {\n    window.removeEventListener('scroll', this.onScrollListener, true);\n    window.removeEventListener('dragover', documentDragoverEventHandler, false);\n  }\n\n  private onScroll(e: Event) {\n    const scrollBody = e.target;\n    const rootNode = this.store.query.node(ROOT_NODE).get();\n\n    // Clear the currentTargetChildDimensions if the user has scrolled\n    // Because we will have to recompute new dimensions relative to the new scroll pos\n    const shouldClearChildDimensionsCache =\n      scrollBody instanceof Element &&\n      rootNode &&\n      rootNode.dom &&\n      scrollBody.contains(rootNode.dom);\n\n    if (!shouldClearChildDimensionsCache) {\n      return;\n    }\n\n    this.currentTargetChildDimensions = null;\n  }\n\n  private getDraggedNodes() {\n    if (this.dragTarget.type === 'new') {\n      return getNodesFromSelector(\n        this.store.query.getNodes(),\n        this.dragTarget.tree.nodes[this.dragTarget.tree.rootNodeId]\n      );\n    }\n\n    return getNodesFromSelector(\n      this.store.query.getNodes(),\n      this.dragTarget.nodes\n    );\n  }\n\n  // Check if the elements being dragged are allowed to be dragged\n  private validateDraggedNodes() {\n    // We don't need to check for dragTarget.type = \"new\" because those nodes are not yet in the state (ie: via the .create() connector)\n    if (this.dragTarget.type === 'new') {\n      return;\n    }\n\n    this.draggedNodes.forEach(({ node, exists }) => {\n      if (!exists) {\n        return;\n      }\n\n      this.store.query.node(node.id).isDraggable((err) => {\n        this.dragError = err;\n      });\n    });\n  }\n\n  private isNearBorders(\n    domInfo: ReturnType<typeof getDOMInfo>,\n    x: number,\n    y: number\n  ) {\n    const { top, bottom, left, right } = domInfo;\n\n    if (\n      top + Positioner.BORDER_OFFSET > y ||\n      bottom - Positioner.BORDER_OFFSET < y ||\n      left + Positioner.BORDER_OFFSET > x ||\n      right - Positioner.BORDER_OFFSET < x\n    ) {\n      return true;\n    }\n\n    return false;\n  }\n\n  private isDiff(newPosition: DropPosition) {\n    if (\n      this.currentIndicator &&\n      this.currentIndicator.placement.parent.id === newPosition.parent.id &&\n      this.currentIndicator.placement.index === newPosition.index &&\n      this.currentIndicator.placement.where === newPosition.where\n    ) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Get dimensions of every child Node in the specified parent Node\n   */\n  private getChildDimensions(newParentNode: Node) {\n    // Use previously computed child dimensions if newParentNode is the same as the previous one\n    const existingTargetChildDimensions = this.currentTargetChildDimensions;\n    if (\n      this.currentTargetId === newParentNode.id &&\n      existingTargetChildDimensions\n    ) {\n      return existingTargetChildDimensions;\n    }\n\n    return newParentNode.data.nodes.reduce((result, id: NodeId) => {\n      const dom = this.store.query.node(id).get().dom;\n\n      if (dom) {\n        result.push({\n          id,\n          ...getDOMInfo(dom),\n        });\n      }\n\n      return result;\n    }, [] as NodeInfo[]);\n  }\n\n  /**\n   * Get closest Canvas node relative to the dropTargetId\n   * Return dropTargetId if it itself is a Canvas node\n   *\n   * In most cases it will be the dropTarget itself or its immediate parent.\n   * We typically only need to traverse 2 levels or more if the dropTarget is a linked node\n   *\n   * TODO: We should probably have some special rules to handle linked nodes\n   */\n  private getCanvasAncestor(dropTargetId: NodeId) {\n    // If the dropTargetId is the same as the previous one\n    // Return the canvas ancestor node that we found previuously\n    if (\n      dropTargetId === this.currentDropTargetId &&\n      this.currentDropTargetCanvasAncestorId\n    ) {\n      const node = this.store.query\n        .node(this.currentDropTargetCanvasAncestorId)\n        .get();\n\n      if (node) {\n        return node;\n      }\n    }\n\n    const getCanvas = (nodeId: NodeId): Node => {\n      const node = this.store.query.node(nodeId).get();\n\n      if (node && node.data.isCanvas) {\n        return node;\n      }\n\n      if (!node.data.parent) {\n        return null;\n      }\n\n      return getCanvas(node.data.parent);\n    };\n\n    return getCanvas(dropTargetId);\n  }\n\n  /**\n   * Compute a new Indicator object based on the dropTarget and x,y coords\n   * Returns null if theres no change from the previous Indicator\n   */\n  computeIndicator(dropTargetId: NodeId, x: number, y: number): Indicator {\n    let newParentNode = this.getCanvasAncestor(dropTargetId);\n\n    if (!newParentNode) {\n      return;\n    }\n\n    this.currentDropTargetId = dropTargetId;\n    this.currentDropTargetCanvasAncestorId = newParentNode.id;\n\n    // Get parent if we're hovering at the border of the current node\n    if (\n      newParentNode.data.parent &&\n      this.isNearBorders(getDOMInfo(newParentNode.dom), x, y) &&\n      // Ignore if linked node because there's won't be an adjacent sibling anyway\n      !this.store.query.node(newParentNode.id).isLinkedNode()\n    ) {\n      newParentNode = this.store.query.node(newParentNode.data.parent).get();\n    }\n\n    if (!newParentNode) {\n      return;\n    }\n\n    this.currentTargetChildDimensions = this.getChildDimensions(newParentNode);\n    this.currentTargetId = newParentNode.id;\n\n    const position = findPosition(\n      newParentNode,\n      this.currentTargetChildDimensions,\n      x,\n      y\n    );\n\n    // Ignore if the position is similar as the previous one\n    if (!this.isDiff(position)) {\n      return;\n    }\n\n    let error = this.dragError;\n\n    // Last thing to check for is if the dragged nodes can be dropped in the target area\n    if (!error) {\n      this.store.query.node(newParentNode.id).isDroppable(\n        this.draggedNodes.map((sourceNode) => sourceNode.node),\n        (dropError) => {\n          error = dropError;\n        }\n      );\n    }\n\n    const currentNodeId = newParentNode.data.nodes[position.index];\n    const currentNode =\n      currentNodeId && this.store.query.node(currentNodeId).get();\n\n    this.currentIndicator = {\n      placement: {\n        ...position,\n        currentNode,\n      },\n      error,\n    };\n\n    return this.currentIndicator;\n  }\n\n  getIndicator() {\n    return this.currentIndicator;\n  }\n}\n", "// Works partially with Linux (except on Chrome)\n// We'll need an alternate way to create drag shadows\nexport const createShadow = (\n  e: DragEvent,\n  shadowsToCreate: HTMLElement[],\n  forceSingleShadow: boolean = false\n) => {\n  if (shadowsToCreate.length === 1 || forceSingleShadow) {\n    const { width, height } = shadowsToCreate[0].getBoundingClientRect();\n    const shadow = shadowsToCreate[0].cloneNode(true) as HTMLElement;\n\n    shadow.style.position = `absolute`;\n    shadow.style.left = `-100%`;\n    shadow.style.top = `-100%`;\n    shadow.style.width = `${width}px`;\n    shadow.style.height = `${height}px`;\n    shadow.style.pointerEvents = 'none';\n    shadow.classList.add('drag-shadow');\n\n    document.body.appendChild(shadow);\n    e.dataTransfer.setDragImage(shadow, 0, 0);\n\n    return shadow;\n  }\n\n  /**\n   * If there's supposed to be multiple drag shadows, we will create a single container div to store them\n   * That container will be used as the drag shadow for the current drag event\n   */\n  const container = document.createElement('div');\n  container.style.position = 'absolute';\n  container.style.left = '-100%';\n  container.style.top = `-100%`;\n  container.style.width = '100%';\n  container.style.height = '100%';\n  container.style.pointerEvents = 'none';\n  container.classList.add('drag-shadow-container');\n\n  shadowsToCreate.forEach((dom) => {\n    const { width, height, top, left } = dom.getBoundingClientRect();\n    const shadow = dom.cloneNode(true) as HTMLElement;\n\n    shadow.style.position = `absolute`;\n    shadow.style.left = `${left}px`;\n    shadow.style.top = `${top}px`;\n    shadow.style.width = `${width}px`;\n    shadow.style.height = `${height}px`;\n    shadow.classList.add('drag-shadow');\n\n    container.appendChild(shadow);\n  });\n\n  document.body.appendChild(container);\n  e.dataTransfer.setDragImage(container, e.clientX, e.clientY);\n\n  return container;\n};\n", "import { isChromium, isLinux } from '@craftjs/utils';\nimport isFunction from 'lodash/isFunction';\nimport React from 'react';\n\nimport { CoreEventHandlers, CreateHandlerOptions } from './CoreEventHandlers';\nimport { Positioner } from './Positioner';\nimport { createShadow } from './createShadow';\n\nimport { Indicator, NodeId, DragTarget, NodeTree } from '../interfaces';\n\nexport type DefaultEventHandlersOptions = {\n  isMultiSelectEnabled: (e: MouseEvent) => boolean;\n  removeHoverOnMouseleave: boolean;\n};\n\n/**\n * Specifies Editor-wide event handlers and connectors\n */\nexport class DefaultEventHandlers<O = {}> extends CoreEventHandlers<\n  DefaultEventHandlersOptions & O\n> {\n  /**\n   * Note: Multiple drag shadows (ie: via multiselect in v0.2 and higher) do not look good on Linux Chromium due to way it renders drag shadows in general,\n   * so will have to fallback to the single shadow approach above for the time being\n   * see: https://bugs.chromium.org/p/chromium/issues/detail?id=550999\n   */\n  static forceSingleDragShadow = isChromium() && isLinux();\n\n  draggedElementShadow: HTMLElement;\n  dragTarget: DragTarget;\n  positioner: Positioner | null = null;\n  currentSelectedElementIds = [];\n\n  onDisable() {\n    this.options.store.actions.clearEvents();\n  }\n\n  handlers() {\n    const store = this.options.store;\n\n    return {\n      connect: (el: HTMLElement, id: NodeId) => {\n        store.actions.setDOM(id, el);\n\n        return this.reflect((connectors) => {\n          connectors.select(el, id);\n          connectors.hover(el, id);\n          connectors.drop(el, id);\n        });\n      },\n      select: (el: HTMLElement, id: NodeId) => {\n        const unbindOnMouseDown = this.addCraftEventListener(\n          el,\n          'mousedown',\n          (e) => {\n            e.craft.stopPropagation();\n\n            let newSelectedElementIds = [];\n\n            if (id) {\n              const { query } = store;\n              const selectedElementIds = query.getEvent('selected').all();\n              const isMultiSelect = this.options.isMultiSelectEnabled(e);\n\n              /**\n               * Retain the previously select elements if the multi-select condition is enabled\n               * or if the currentNode is already selected\n               *\n               * so users can just click to drag the selected elements around without holding the multi-select key\n               */\n\n              if (isMultiSelect || selectedElementIds.includes(id)) {\n                newSelectedElementIds = selectedElementIds.filter(\n                  (selectedId) => {\n                    const descendants = query\n                      .node(selectedId)\n                      .descendants(true);\n                    const ancestors = query.node(selectedId).ancestors(true);\n\n                    // Deselect ancestors/descendants\n                    if (descendants.includes(id) || ancestors.includes(id)) {\n                      return false;\n                    }\n\n                    return true;\n                  }\n                );\n              }\n\n              if (!newSelectedElementIds.includes(id)) {\n                newSelectedElementIds.push(id);\n              }\n            }\n\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          }\n        );\n\n        const unbindOnClick = this.addCraftEventListener(el, 'click', (e) => {\n          e.craft.stopPropagation();\n\n          const { query } = store;\n          const selectedElementIds = query.getEvent('selected').all();\n\n          const isMultiSelect = this.options.isMultiSelectEnabled(e);\n          const isNodeAlreadySelected = this.currentSelectedElementIds.includes(\n            id\n          );\n\n          let newSelectedElementIds = [...selectedElementIds];\n\n          if (isMultiSelect && isNodeAlreadySelected) {\n            newSelectedElementIds.splice(newSelectedElementIds.indexOf(id), 1);\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          } else if (!isMultiSelect && selectedElementIds.length > 1) {\n            newSelectedElementIds = [id];\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          }\n\n          this.currentSelectedElementIds = newSelectedElementIds;\n        });\n\n        return () => {\n          unbindOnMouseDown();\n          unbindOnClick();\n        };\n      },\n      hover: (el: HTMLElement, id: NodeId) => {\n        const unbindMouseover = this.addCraftEventListener(\n          el,\n          'mouseover',\n          (e) => {\n            e.craft.stopPropagation();\n            store.actions.setNodeEvent('hovered', id);\n          }\n        );\n\n        let unbindMouseleave: (() => void) | null = null;\n\n        if (this.options.removeHoverOnMouseleave) {\n          unbindMouseleave = this.addCraftEventListener(\n            el,\n            'mouseleave',\n            (e) => {\n              e.craft.stopPropagation();\n              store.actions.setNodeEvent('hovered', null);\n            }\n          );\n        }\n\n        return () => {\n          unbindMouseover();\n\n          if (!unbindMouseleave) {\n            return;\n          }\n\n          unbindMouseleave();\n        };\n      },\n      drop: (el: HTMLElement, targetId: NodeId) => {\n        const unbindDragOver = this.addCraftEventListener(\n          el,\n          'dragover',\n          (e) => {\n            e.craft.stopPropagation();\n            e.preventDefault();\n\n            if (!this.positioner) {\n              return;\n            }\n\n            const indicator = this.positioner.computeIndicator(\n              targetId,\n              e.clientX,\n              e.clientY\n            );\n\n            if (!indicator) {\n              return;\n            }\n\n            store.actions.setIndicator(indicator);\n          }\n        );\n\n        const unbindDragEnter = this.addCraftEventListener(\n          el,\n          'dragenter',\n          (e) => {\n            e.craft.stopPropagation();\n            e.preventDefault();\n          }\n        );\n\n        return () => {\n          unbindDragEnter();\n          unbindDragOver();\n        };\n      },\n      drag: (el: HTMLElement, id: NodeId) => {\n        if (!store.query.node(id).isDraggable()) {\n          return () => {};\n        }\n\n        el.setAttribute('draggable', 'true');\n\n        const unbindDragStart = this.addCraftEventListener(\n          el,\n          'dragstart',\n          (e) => {\n            e.craft.stopPropagation();\n\n            const { query, actions } = store;\n\n            let selectedElementIds = query.getEvent('selected').all();\n\n            const isMultiSelect = this.options.isMultiSelectEnabled(e);\n            const isNodeAlreadySelected = this.currentSelectedElementIds.includes(\n              id\n            );\n\n            if (!isNodeAlreadySelected) {\n              if (isMultiSelect) {\n                selectedElementIds = [...selectedElementIds, id];\n              } else {\n                selectedElementIds = [id];\n              }\n              store.actions.setNodeEvent('selected', selectedElementIds);\n            }\n\n            actions.setNodeEvent('dragged', selectedElementIds);\n\n            const selectedDOMs = selectedElementIds.map(\n              (id) => query.node(id).get().dom\n            );\n\n            this.draggedElementShadow = createShadow(\n              e,\n              selectedDOMs,\n              DefaultEventHandlers.forceSingleDragShadow\n            );\n\n            this.dragTarget = {\n              type: 'existing',\n              nodes: selectedElementIds,\n            };\n\n            this.positioner = new Positioner(\n              this.options.store,\n              this.dragTarget\n            );\n          }\n        );\n\n        const unbindDragEnd = this.addCraftEventListener(el, 'dragend', (e) => {\n          e.craft.stopPropagation();\n\n          this.dropElement((dragTarget, indicator) => {\n            if (dragTarget.type === 'new') {\n              return;\n            }\n\n            const index =\n              indicator.placement.index +\n              (indicator.placement.where === 'after' ? 1 : 0);\n\n            store.actions.move(\n              dragTarget.nodes,\n              indicator.placement.parent.id,\n              index\n            );\n          });\n        });\n\n        return () => {\n          el.setAttribute('draggable', 'false');\n          unbindDragStart();\n          unbindDragEnd();\n        };\n      },\n      create: (\n        el: HTMLElement,\n        userElement: React.ReactElement | (() => NodeTree | React.ReactElement),\n        options?: Partial<CreateHandlerOptions>\n      ) => {\n        el.setAttribute('draggable', 'true');\n\n        const unbindDragStart = this.addCraftEventListener(\n          el,\n          'dragstart',\n          (e) => {\n            e.craft.stopPropagation();\n            let tree;\n            if (typeof userElement === 'function') {\n              const result = userElement();\n              if (React.isValidElement(result)) {\n                tree = store.query.parseReactElement(result).toNodeTree();\n              } else {\n                tree = result;\n              }\n            } else {\n              tree = store.query.parseReactElement(userElement).toNodeTree();\n            }\n\n            const dom = e.currentTarget as HTMLElement;\n            this.draggedElementShadow = createShadow(\n              e,\n              [dom],\n              DefaultEventHandlers.forceSingleDragShadow\n            );\n            this.dragTarget = {\n              type: 'new',\n              tree,\n            };\n\n            this.positioner = new Positioner(\n              this.options.store,\n              this.dragTarget\n            );\n          }\n        );\n\n        const unbindDragEnd = this.addCraftEventListener(el, 'dragend', (e) => {\n          e.craft.stopPropagation();\n          this.dropElement((dragTarget, indicator) => {\n            if (dragTarget.type === 'existing') {\n              return;\n            }\n\n            const index =\n              indicator.placement.index +\n              (indicator.placement.where === 'after' ? 1 : 0);\n            store.actions.addNodeTree(\n              dragTarget.tree,\n              indicator.placement.parent.id,\n              index\n            );\n\n            if (options && isFunction(options.onCreate)) {\n              options.onCreate(dragTarget.tree);\n            }\n          });\n        });\n\n        return () => {\n          el.removeAttribute('draggable');\n          unbindDragStart();\n          unbindDragEnd();\n        };\n      },\n    };\n  }\n\n  private dropElement(\n    onDropNode: (dragTarget: DragTarget, placement: Indicator) => void\n  ) {\n    const store = this.options.store;\n\n    if (!this.positioner) {\n      return;\n    }\n\n    const draggedElementShadow = this.draggedElementShadow;\n\n    const indicator = this.positioner.getIndicator();\n\n    if (this.dragTarget && indicator && !indicator.error) {\n      onDropNode(this.dragTarget, indicator);\n    }\n\n    if (draggedElementShadow) {\n      draggedElementShadow.parentNode.removeChild(draggedElementShadow);\n      this.draggedElementShadow = null;\n    }\n\n    this.dragTarget = null;\n\n    store.actions.setIndicator(null);\n    store.actions.setNodeEvent('dragged', null);\n    this.positioner.cleanup();\n\n    this.positioner = null;\n  }\n}\n", "import { DropPosition, DOMInfo } from '../interfaces';\n\nexport default function movePlaceholder(\n  pos: DropPosition,\n  canvasDOMInfo: DOMInfo, // which canvas is cursor at\n  bestTargetDomInfo: DOMInfo | null, // closest element in canvas (null if canvas is empty)\n  thickness: number = 2\n) {\n  let t = 0,\n    l = 0,\n    w = 0,\n    h = 0,\n    where = pos.where;\n\n  const elDim = bestTargetDomInfo;\n\n  if (elDim) {\n    // If it's not in flow (like 'float' element)\n    if (!elDim.inFlow) {\n      w = thickness;\n      h = elDim.outerHeight;\n      t = elDim.top;\n      l = where === 'before' ? elDim.left : elDim.left + elDim.outerWidth;\n    } else {\n      w = elDim.outerWidth;\n      h = thickness;\n      t = where === 'before' ? elDim.top : elDim.bottom;\n      l = elDim.left;\n    }\n  } else {\n    if (canvasDOMInfo) {\n      t = canvasDOMInfo.top + canvasDOMInfo.padding.top;\n      l = canvasDOMInfo.left + canvasDOMInfo.padding.left;\n      w =\n        canvasDOMInfo.outerWidth -\n        canvasDOMInfo.padding.right -\n        canvasDOMInfo.padding.left -\n        canvasDOMInfo.margin.left -\n        canvasDOMInfo.margin.right;\n      h = thickness;\n    }\n  }\n  return {\n    top: `${t}px`,\n    left: `${l}px`,\n    width: `${w}px`,\n    height: `${h}px`,\n  };\n}\n", "import { RenderIndicator, getDOMInfo } from '@craftjs/utils';\nimport React, { useEffect } from 'react';\n\nimport { useEventHandler } from './EventContext';\nimport movePlaceholder from './movePlaceholder';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\n\nexport const RenderEditorIndicator = () => {\n  const { indicator, indicatorOptions, enabled } = useInternalEditor(\n    (state) => ({\n      indicator: state.indicator,\n      indicatorOptions: state.options.indicator,\n      enabled: state.options.enabled,\n    })\n  );\n\n  const handler = useEventHandler();\n\n  useEffect(() => {\n    if (!handler) {\n      return;\n    }\n\n    if (!enabled) {\n      handler.disable();\n      return;\n    }\n\n    handler.enable();\n  }, [enabled, handler]);\n\n  if (!indicator) {\n    return null;\n  }\n\n  return React.createElement(RenderIndicator, {\n    className: indicatorOptions.className,\n    style: {\n      ...movePlaceholder(\n        indicator.placement,\n        getDOMInfo(indicator.placement.parent.dom),\n        indicator.placement.currentNode &&\n          getDOMInfo(indicator.placement.currentNode.dom),\n        indicatorOptions.thickness\n      ),\n      backgroundColor: indicator.error\n        ? indicatorOptions.error\n        : indicatorOptions.success,\n      transition: indicatorOptions.transition || '0.2s ease-in',\n      ...(indicatorOptions.style ?? {}),\n    },\n    parentDom: indicator.placement.parent.dom,\n  });\n};\n", "import React, { useContext, useMemo } from 'react';\n\nimport { EventHandlerContext } from './EventContext';\nimport { RenderEditorIndicator } from './RenderEditorIndicator';\n\nimport { EditorContext } from '../editor/EditorContext';\n\ntype EventsProps = {\n  children?: React.ReactNode;\n};\n\nexport const Events = ({ children }: EventsProps) => {\n  const store = useContext(EditorContext);\n\n  const handler = useMemo(() => store.query.getOptions().handlers(store), [\n    store,\n  ]);\n\n  if (!handler) {\n    return null;\n  }\n\n  return (\n    <EventHandlerContext.Provider value={handler}>\n      <RenderEditorIndicator />\n      {children}\n    </EventHandlerContext.Provider>\n  );\n};\n", "import {\n  useMethods,\n  SubscriberAndCallbacksFor,\n  PatchListener,\n} from '@craftjs/utils';\n\nimport { ActionMethods } from './actions';\nimport { QueryMethods } from './query';\n\nimport { DefaultEventHandlers } from '../events';\nimport { EditorState, Options, NodeEventTypes, NodeId } from '../interfaces';\n\nexport const editorInitialState: EditorState = {\n  nodes: {},\n  events: {\n    dragged: new Set<NodeId>(),\n    selected: new Set<NodeId>(),\n    hovered: new Set<NodeId>(),\n  },\n  indicator: null,\n  options: {\n    onNodesChange: () => null,\n    onRender: ({ render }) => render,\n    onBeforeMoveEnd: () => null,\n    resolver: {},\n    enabled: true,\n    indicator: {\n      error: 'red',\n      success: 'rgb(98, 196, 98)',\n    },\n    handlers: (store) =>\n      new DefaultEventHandlers({\n        store,\n        removeHoverOnMouseleave: false,\n        isMultiSelectEnabled: (e: MouseEvent) => !!e.meta<PERSON>ey,\n      }),\n    normalizeNodes: () => {},\n  },\n};\n\nexport const ActionMethodsWithConfig = {\n  methods: ActionMethods,\n  ignoreHistoryForActions: [\n    'setDOM',\n    'setNodeEvent',\n    'selectNode',\n    'clearEvents',\n    'setOptions',\n    'setIndicator',\n  ] as const,\n  normalizeHistory: (state: EditorState) => {\n    /**\n     * On every undo/redo, we remove events pointing to deleted Nodes\n     */\n    Object.keys(state.events).forEach((eventName: NodeEventTypes) => {\n      const nodeIds = Array.from(state.events[eventName] || []);\n\n      nodeIds.forEach((id) => {\n        if (!state.nodes[id]) {\n          state.events[eventName].delete(id);\n        }\n      });\n    });\n\n    // Remove any invalid node[nodeId].events\n    // TODO(prev): it's really cumbersome to have to ensure state.events and state.nodes[nodeId].events are in sync\n    // Find a way to make it so that once state.events is set, state.nodes[nodeId] automatically reflects that (maybe using proxies?)\n    Object.keys(state.nodes).forEach((id) => {\n      const node = state.nodes[id];\n\n      Object.keys(node.events).forEach((eventName: NodeEventTypes) => {\n        const isEventActive = !!node.events[eventName];\n\n        if (\n          isEventActive &&\n          state.events[eventName] &&\n          !state.events[eventName].has(node.id)\n        ) {\n          node.events[eventName] = false;\n        }\n      });\n    });\n  },\n};\n\nexport type EditorStore = SubscriberAndCallbacksFor<\n  typeof ActionMethodsWithConfig,\n  typeof QueryMethods\n>;\n\nexport const useEditorStore = (\n  options: Partial<Options>,\n  patchListener: PatchListener<\n    EditorState,\n    typeof ActionMethodsWithConfig,\n    typeof QueryMethods\n  >\n): EditorStore => {\n  // TODO: fix type\n  return useMethods(\n    ActionMethodsWithConfig,\n    {\n      ...editorInitialState,\n      options: {\n        ...editorInitialState.options,\n        ...options,\n      },\n    },\n    QueryMethods,\n    patchListener\n  ) as EditorStore;\n};\n", "import {\n  deprecationWarning,\n  ERROR_INVALID_NODEID,\n  ROOT_NODE,\n  DEPRECATED_ROOT_NODE,\n  QueryCallbacksFor,\n  ERROR_NOPARENT,\n  ERROR_DELETE_TOP_LEVEL_NODE,\n  CallbacksFor,\n  Delete,\n  ERROR_NOT_IN_RESOLVER,\n} from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { QueryMethods } from './query';\n\nimport {\n  EditorState,\n  Indicator,\n  NodeId,\n  Node,\n  Nodes,\n  Options,\n  NodeEventTypes,\n  NodeTree,\n  SerializedNodes,\n  NodeSelector,\n  NodeSelectorType,\n} from '../interfaces';\nimport { fromEntries } from '../utils/fromEntries';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { removeNodeFromEvents } from '../utils/removeNodeFromEvents';\n\nconst Methods = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => {\n  /** Helper functions */\n  const addNodeTreeToParent = (\n    tree: NodeTree,\n    parentId?: NodeId,\n    addNodeType?:\n      | {\n          type: 'child';\n          index: number;\n        }\n      | {\n          type: 'linked';\n          id: string;\n        }\n  ) => {\n    const iterateChildren = (id: NodeId, parentId?: NodeId) => {\n      const node = tree.nodes[id];\n\n      if (typeof node.data.type !== 'string') {\n        invariant(\n          state.options.resolver[node.data.name],\n          ERROR_NOT_IN_RESOLVER.replace(\n            '%node_type%',\n            `${(node.data.type as any).name}`\n          )\n        );\n      }\n\n      state.nodes[id] = {\n        ...node,\n        data: {\n          ...node.data,\n          parent: parentId,\n        },\n      };\n\n      if (node.data.nodes.length > 0) {\n        delete state.nodes[id].data.props.children;\n        node.data.nodes.forEach((childNodeId) =>\n          iterateChildren(childNodeId, node.id)\n        );\n      }\n\n      Object.values(node.data.linkedNodes).forEach((linkedNodeId) =>\n        iterateChildren(linkedNodeId, node.id)\n      );\n    };\n\n    iterateChildren(tree.rootNodeId, parentId);\n\n    if (!parentId && tree.rootNodeId === ROOT_NODE) {\n      return;\n    }\n\n    const parent = getParentAndValidate(parentId);\n\n    if (addNodeType.type === 'child') {\n      const index = addNodeType.index;\n\n      if (index != null) {\n        parent.data.nodes.splice(index, 0, tree.rootNodeId);\n      } else {\n        parent.data.nodes.push(tree.rootNodeId);\n      }\n\n      return;\n    }\n\n    parent.data.linkedNodes[addNodeType.id] = tree.rootNodeId;\n  };\n\n  const getParentAndValidate = (parentId: NodeId): Node => {\n    invariant(parentId, ERROR_NOPARENT);\n    const parent = state.nodes[parentId];\n    invariant(parent, ERROR_INVALID_NODEID);\n    return parent;\n  };\n\n  const deleteNode = (id: NodeId) => {\n    const targetNode = state.nodes[id],\n      parentNode = state.nodes[targetNode.data.parent];\n\n    if (targetNode.data.nodes) {\n      // we deep clone here because otherwise immer will mutate the node\n      // object as we remove nodes\n      [...targetNode.data.nodes].forEach((childId) => deleteNode(childId));\n    }\n\n    if (targetNode.data.linkedNodes) {\n      Object.values(targetNode.data.linkedNodes).map((linkedNodeId) =>\n        deleteNode(linkedNodeId)\n      );\n    }\n\n    const isChildNode = parentNode.data.nodes.includes(id);\n\n    if (isChildNode) {\n      const parentChildren = parentNode.data.nodes;\n      parentChildren.splice(parentChildren.indexOf(id), 1);\n    } else {\n      const linkedId = Object.keys(parentNode.data.linkedNodes).find(\n        (id) => parentNode.data.linkedNodes[id] === id\n      );\n      if (linkedId) {\n        delete parentNode.data.linkedNodes[linkedId];\n      }\n    }\n\n    removeNodeFromEvents(state, id);\n    delete state.nodes[id];\n  };\n\n  return {\n    /**\n     * @private\n     * Add a new linked Node to the editor.\n     * Only used internally by the <Element /> component\n     *\n     * @param tree\n     * @param parentId\n     * @param id\n     */\n    addLinkedNodeFromTree(tree: NodeTree, parentId: NodeId, id: string) {\n      const parent = getParentAndValidate(parentId);\n\n      const existingLinkedNode = parent.data.linkedNodes[id];\n\n      if (existingLinkedNode) {\n        deleteNode(existingLinkedNode);\n      }\n\n      addNodeTreeToParent(tree, parentId, { type: 'linked', id });\n    },\n\n    /**\n     * Add a new Node to the editor.\n     *\n     * @param nodeToAdd\n     * @param parentId\n     * @param index\n     */\n    add(nodeToAdd: Node | Node[], parentId?: NodeId, index?: number) {\n      // TODO: Deprecate adding array of Nodes to keep implementation simpler\n      let nodes = [nodeToAdd];\n      if (Array.isArray(nodeToAdd)) {\n        deprecationWarning('actions.add(node: Node[])', {\n          suggest: 'actions.add(node: Node)',\n        });\n        nodes = nodeToAdd;\n      }\n      nodes.forEach((node: Node) => {\n        addNodeTreeToParent(\n          {\n            nodes: {\n              [node.id]: node,\n            },\n            rootNodeId: node.id,\n          },\n          parentId,\n          { type: 'child', index }\n        );\n      });\n    },\n\n    /**\n     * Add a NodeTree to the editor\n     *\n     * @param tree\n     * @param parentId\n     * @param index\n     */\n    addNodeTree(tree: NodeTree, parentId?: NodeId, index?: number) {\n      addNodeTreeToParent(tree, parentId, { type: 'child', index });\n    },\n\n    /**\n     * Delete a Node\n     * @param id\n     */\n    delete(selector: NodeSelector<NodeSelectorType.Id>) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        existOnly: true,\n        idOnly: true,\n      });\n\n      targets.forEach(({ node }) => {\n        invariant(\n          !query.node(node.id).isTopLevelNode(),\n          ERROR_DELETE_TOP_LEVEL_NODE\n        );\n        deleteNode(node.id);\n      });\n    },\n\n    deserialize(input: SerializedNodes | string) {\n      const dehydratedNodes =\n        typeof input == 'string' ? JSON.parse(input) : input;\n\n      const nodePairs = Object.keys(dehydratedNodes).map((id) => {\n        let nodeId = id;\n\n        if (id === DEPRECATED_ROOT_NODE) {\n          nodeId = ROOT_NODE;\n        }\n\n        return [\n          nodeId,\n          query\n            .parseSerializedNode(dehydratedNodes[id])\n            .toNode((node) => (node.id = nodeId)),\n        ];\n      });\n\n      this.replaceNodes(fromEntries(nodePairs));\n    },\n\n    /**\n     * Move a target Node to a new Parent at a given index\n     * @param targetId\n     * @param newParentId\n     * @param index\n     */\n    move(selector: NodeSelector, newParentId: NodeId, index: number) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        existOnly: true,\n      });\n\n      const newParent = state.nodes[newParentId];\n\n      const nodesArrToCleanup = new Set<string[]>();\n\n      targets.forEach(({ node: targetNode }, i) => {\n        const targetId = targetNode.id;\n        const currentParentId = targetNode.data.parent;\n\n        query.node(newParentId).isDroppable([targetId], (err) => {\n          throw new Error(err);\n        });\n\n        // modify node props\n        state.options.onBeforeMoveEnd(\n          targetNode,\n          newParent,\n          state.nodes[currentParentId]\n        );\n\n        const currentParent = state.nodes[currentParentId];\n        const currentParentNodes = currentParent.data.nodes;\n\n        nodesArrToCleanup.add(currentParentNodes);\n\n        const oldIndex = currentParentNodes.indexOf(targetId);\n        currentParentNodes[oldIndex] = '$$'; // mark for deletion\n\n        newParent.data.nodes.splice(index + i, 0, targetId);\n\n        state.nodes[targetId].data.parent = newParentId;\n      });\n\n      nodesArrToCleanup.forEach((nodes) => {\n        const length = nodes.length;\n\n        [...nodes].reverse().forEach((value, index) => {\n          if (value !== '$$') {\n            return;\n          }\n\n          nodes.splice(length - 1 - index, 1);\n        });\n      });\n    },\n\n    replaceNodes(nodes: Nodes) {\n      this.clearEvents();\n      state.nodes = nodes;\n    },\n\n    clearEvents() {\n      this.setNodeEvent('selected', null);\n      this.setNodeEvent('hovered', null);\n      this.setNodeEvent('dragged', null);\n      this.setIndicator(null);\n    },\n\n    /**\n     * Resets all the editor state.\n     */\n    reset() {\n      this.clearEvents();\n      this.replaceNodes({});\n    },\n\n    /**\n     * Set editor options via a callback function\n     *\n     * @param cb: function used to set the options.\n     */\n    setOptions(cb: (options: Partial<Options>) => void) {\n      cb(state.options);\n    },\n\n    setNodeEvent(\n      eventType: NodeEventTypes,\n      nodeIdSelector: NodeSelector<NodeSelectorType.Id>\n    ) {\n      state.events[eventType].forEach((id) => {\n        if (state.nodes[id]) {\n          state.nodes[id].events[eventType] = false;\n        }\n      });\n\n      state.events[eventType] = new Set();\n\n      if (!nodeIdSelector) {\n        return;\n      }\n\n      const targets = getNodesFromSelector(state.nodes, nodeIdSelector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      const nodeIds: Set<NodeId> = new Set(targets.map(({ node }) => node.id));\n      nodeIds.forEach((id) => {\n        state.nodes[id].events[eventType] = true;\n      });\n      state.events[eventType] = nodeIds;\n    },\n\n    /**\n     * Set custom values to a Node\n     * @param id\n     * @param cb\n     */\n    setCustom<T extends NodeId>(\n      selector: NodeSelector<NodeSelectorType.Id>,\n      cb: (data: EditorState['nodes'][T]['data']['custom']) => void\n    ) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      targets.forEach(({ node }) => cb(state.nodes[node.id].data.custom));\n    },\n\n    /**\n     * Given a `id`, it will set the `dom` porperty of that node.\n     *\n     * @param id of the node we want to set\n     * @param dom\n     */\n    setDOM(id: NodeId, dom: HTMLElement) {\n      if (!state.nodes[id]) {\n        return;\n      }\n\n      state.nodes[id].dom = dom;\n    },\n\n    setIndicator(indicator: Indicator | null) {\n      if (\n        indicator &&\n        (!indicator.placement.parent.dom ||\n          (indicator.placement.currentNode &&\n            !indicator.placement.currentNode.dom))\n      )\n        return;\n      state.indicator = indicator;\n    },\n\n    /**\n     * Hide a Node\n     * @param id\n     * @param bool\n     */\n    setHidden(id: NodeId, bool: boolean) {\n      state.nodes[id].data.hidden = bool;\n    },\n\n    /**\n     * Update the props of a Node\n     * @param id\n     * @param cb\n     */\n    setProp(\n      selector: NodeSelector<NodeSelectorType.Id>,\n      cb: (props: any) => void\n    ) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      targets.forEach(({ node }) => cb(state.nodes[node.id].data.props));\n    },\n\n    selectNode(nodeIdSelector?: NodeSelector<NodeSelectorType.Id>) {\n      if (nodeIdSelector) {\n        const targets = getNodesFromSelector(state.nodes, nodeIdSelector, {\n          idOnly: true,\n          existOnly: true,\n        });\n\n        this.setNodeEvent(\n          'selected',\n          targets.map(({ node }) => node.id)\n        );\n      } else {\n        this.setNodeEvent('selected', null);\n      }\n\n      this.setNodeEvent('hovered', null);\n    },\n  };\n};\n\nexport const ActionMethods = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => {\n  return {\n    ...Methods(state, query),\n    // Note: Beware: advanced method! You most likely don't need to use this\n    // TODO: fix parameter types and cleanup the method\n    setState(\n      cb: (\n        state: EditorState,\n        actions: Delete<CallbacksFor<typeof Methods>, 'history'>\n      ) => void\n    ) {\n      const { history, ...actions } = this;\n\n      // We pass the other actions as the second parameter, so that devs could still make use of the predefined actions\n      cb(state, actions);\n    },\n  };\n};\n", "import { EditorState, NodeId } from '../interfaces';\n\nexport const removeNodeFromEvents = (state: EditorState, nodeId: NodeId) =>\n  Object.keys(state.events).forEach((key) => {\n    const eventSet = state.events[key];\n    if (eventSet && eventSet.has && eventSet.has(nodeId)) {\n      state.events[key] = new Set(\n        Array.from(eventSet).filter((id) => nodeId !== id)\n      );\n    }\n  });\n", "import { ERROR_RESOLVER_NOT_AN_OBJECT, HISTORY_ACTIONS } from '@craftjs/utils';\nimport * as React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EditorContext } from './EditorContext';\nimport { useEditorStore } from './store';\n\nimport { Events } from '../events';\nimport { Options } from '../interfaces';\n\ntype EditorProps = Partial<Options> & {\n  children?: React.ReactNode;\n};\n\n/**\n * A React Component that provides the Editor context\n */\nexport const Editor = ({ children, ...options }: EditorProps) => {\n  // we do not want to warn the user if no resolver was supplied\n  if (options.resolver !== undefined) {\n    invariant(\n      typeof options.resolver === 'object' &&\n        !Array.isArray(options.resolver) &&\n        options.resolver !== null,\n      ERROR_RESOLVER_NOT_AN_OBJECT\n    );\n  }\n\n  const optionsRef = React.useRef(options);\n\n  const context = useEditorStore(\n    optionsRef.current,\n    (state, previousState, actionPerformedWithPatches, query, normalizer) => {\n      if (!actionPerformedWithPatches) {\n        return;\n      }\n\n      const { patches, ...actionPerformed } = actionPerformedWithPatches;\n\n      for (let i = 0; i < patches.length; i++) {\n        const { path } = patches[i];\n        const isModifyingNodeData =\n          path.length > 2 && path[0] === 'nodes' && path[2] === 'data';\n\n        let actionType = actionPerformed.type;\n\n        if (\n          [HISTORY_ACTIONS.IGNORE, HISTORY_ACTIONS.THROTTLE].includes(\n            actionType\n          ) &&\n          actionPerformed.params\n        ) {\n          actionPerformed.type = actionPerformed.params[0];\n        }\n\n        if (\n          ['setState', 'deserialize'].includes(actionPerformed.type) ||\n          isModifyingNodeData\n        ) {\n          normalizer((draft) => {\n            if (state.options.normalizeNodes) {\n              state.options.normalizeNodes(\n                draft,\n                previousState,\n                actionPerformed,\n                query\n              );\n            }\n          });\n          break; // we exit the loop as soon as we find a change in node.data\n        }\n      }\n    }\n  );\n\n  // sync enabled prop with editor store options\n  React.useEffect(() => {\n    if (!context) {\n      return;\n    }\n\n    if (\n      options.enabled === undefined ||\n      context.query.getOptions().enabled === options.enabled\n    ) {\n      return;\n    }\n\n    context.actions.setOptions((editorOptions) => {\n      editorOptions.enabled = options.enabled;\n    });\n  }, [context, options.enabled]);\n\n  React.useEffect(() => {\n    context.subscribe(\n      (_) => ({\n        json: context.query.serialize(),\n      }),\n      () => {\n        context.query.getOptions().onNodesChange(context.query);\n      }\n    );\n  }, [context]);\n\n  if (!context) {\n    return null;\n  }\n\n  return (\n    <EditorContext.Provider value={context}>\n      <Events>{children}</Events>\n    </EditorContext.Provider>\n  );\n};\n", "import cloneDeep from 'lodash/cloneDeep';\n\nimport { createNode } from './createNode';\n\nimport { editorInitialState } from '../editor/store';\nimport { Nodes } from '../interfaces';\n\nconst getTestNode = (parentNode) => {\n  const {\n    events,\n    data: { nodes: childNodes, linkedNodes },\n    ...restParentNode\n  } = parentNode;\n  const validParentNode = createNode(cloneDeep(parentNode));\n  parentNode = {\n    ...validParentNode,\n    ...restParentNode,\n    events: {\n      ...validParentNode.events,\n      ...events,\n    },\n    dom: parentNode.dom || validParentNode.dom,\n  };\n\n  return {\n    node: parentNode,\n    childNodes,\n    linkedNodes,\n  };\n};\n\nexport const expectEditorState = (lhs, rhs) => {\n  const { nodes: nodesRhs, ...restRhs } = rhs;\n  const { nodes: nodesLhs, ...restLhs } = lhs;\n  expect(restLhs).toEqual(restRhs);\n\n  const nodesRhsSimplified = Object.keys(nodesRhs).reduce((accum, id) => {\n    const { _hydrationTimestamp, rules, ...node } = nodesRhs[id];\n    accum[id] = node;\n    return accum;\n  }, {});\n\n  const nodesLhsSimplified = Object.keys(nodesLhs).reduce((accum, id) => {\n    const { _hydrationTimestamp, rules, ...node } = nodesLhs[id];\n    accum[id] = node;\n    return accum;\n  }, {});\n\n  expect(nodesLhsSimplified).toEqual(nodesRhsSimplified);\n};\n\nexport const createTestNodes = (rootNode): Nodes => {\n  const nodes = {};\n  const iterateNodes = (testNode) => {\n    const { node: parentNode, childNodes, linkedNodes } = getTestNode(testNode);\n    nodes[parentNode.id] = parentNode;\n\n    if (childNodes) {\n      childNodes.forEach((childTestNode, i) => {\n        const {\n          node: childNode,\n          childNodes: grandChildNodes,\n          linkedNodes: grandChildLinkedNodes,\n        } = getTestNode(childTestNode);\n        childNode.data.parent = parentNode.id;\n        nodes[childNode.id] = childNode;\n        parentNode.data.nodes[i] = childNode.id;\n        iterateNodes({\n          ...childNode,\n          data: {\n            ...childNode.data,\n            nodes: grandChildNodes || [],\n            linkedNodes: grandChildLinkedNodes || {},\n          },\n        });\n      });\n    }\n\n    if (linkedNodes) {\n      Object.keys(linkedNodes).forEach((linkedId) => {\n        const {\n          node: childNode,\n          childNodes: grandChildNodes,\n          linkedNodes: grandChildLinkedNodes,\n        } = getTestNode(linkedNodes[linkedId]);\n        parentNode.data.linkedNodes[linkedId] = childNode.id;\n\n        childNode.data.parent = parentNode.id;\n        nodes[childNode.id] = childNode;\n        iterateNodes({\n          ...childNode,\n          data: {\n            ...childNode.data,\n            nodes: grandChildNodes || [],\n            linkedNodes: grandChildLinkedNodes || {},\n          },\n        });\n      });\n    }\n  };\n\n  iterateNodes(rootNode);\n\n  return nodes;\n};\n\nexport const createTestState = (state = {} as any) => {\n  const { nodes: rootNode, events } = state;\n\n  return {\n    ...editorInitialState,\n    ...state,\n    nodes: rootNode ? createTestNodes(rootNode) : {},\n    events: {\n      ...editorInitialState.events,\n      ...(events || {}),\n    },\n  };\n};\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAOA,aAAS,iBAAiB;AACxB,WAAK,WAAW,CAAC;AACjB,WAAK,OAAO;AAAA,IACd;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACZjB;AAAA;AAgCA,aAAS,GAAG,OAAO,OAAO;AACxB,aAAO,UAAU,SAAU,UAAU,SAAS,UAAU;AAAA,IAC1D;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpCjB;AAAA;AAAA,QAAI,KAAK;AAUT,aAAS,aAAa,OAAO,KAAK;AAChC,UAAI,SAAS,MAAM;AACnB,aAAO,UAAU;AACf,YAAI,GAAG,MAAM,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG;AAC7B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAAA,QAAI,eAAe;AAGnB,QAAI,aAAa,MAAM;AAGvB,QAAI,SAAS,WAAW;AAWxB,aAAS,gBAAgB,KAAK;AAC5B,UAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AACA,UAAI,YAAY,KAAK,SAAS;AAC9B,UAAI,SAAS,WAAW;AACtB,aAAK,IAAI;AAAA,MACX,OAAO;AACL,eAAO,KAAK,MAAM,OAAO,CAAC;AAAA,MAC5B;AACA,QAAE,KAAK;AACP,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClCjB;AAAA;AAAA,QAAI,eAAe;AAWnB,aAAS,aAAa,KAAK;AACzB,UAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,aAAO,QAAQ,IAAI,SAAY,KAAK,KAAK,EAAE,CAAC;AAAA,IAC9C;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAAA,QAAI,eAAe;AAWnB,aAAS,aAAa,KAAK;AACzB,aAAO,aAAa,KAAK,UAAU,GAAG,IAAI;AAAA,IAC5C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,eAAe;AAYnB,aAAS,aAAa,KAAK,OAAO;AAChC,UAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,UAAI,QAAQ,GAAG;AACb,UAAE,KAAK;AACP,aAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,MACxB,OAAO;AACL,aAAK,KAAK,EAAE,CAAC,IAAI;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzBjB;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,kBAAkB;AADtB,QAEI,eAAe;AAFnB,QAGI,eAAe;AAHnB,QAII,eAAe;AASnB,aAAS,UAAU,SAAS;AAC1B,UAAI,QAAQ,IACR,SAAS,WAAW,OAAO,IAAI,QAAQ;AAE3C,WAAK,MAAM;AACX,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,QAAQ,KAAK;AACzB,aAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MAC7B;AAAA,IACF;AAGA,cAAU,UAAU,QAAQ;AAC5B,cAAU,UAAU,QAAQ,IAAI;AAChC,cAAU,UAAU,MAAM;AAC1B,cAAU,UAAU,MAAM;AAC1B,cAAU,UAAU,MAAM;AAE1B,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA,QAAI,YAAY;AAShB,aAAS,aAAa;AACpB,WAAK,WAAW,IAAI;AACpB,WAAK,OAAO;AAAA,IACd;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACdjB;AAAA;AASA,aAAS,YAAY,KAAK;AACxB,UAAI,OAAO,KAAK,UACZ,SAAS,KAAK,QAAQ,EAAE,GAAG;AAE/B,WAAK,OAAO,KAAK;AACjB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AASA,aAAS,SAAS,KAAK;AACrB,aAAO,KAAK,SAAS,IAAI,GAAG;AAAA,IAC9B;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACbjB;AAAA;AASA,aAAS,SAAS,KAAK;AACrB,aAAO,KAAK,SAAS,IAAI,GAAG;AAAA,IAC9B;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACbjB;AAAA;AACA,QAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,WAAW,UAAU;AAEpF,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA,QAAI,aAAa;AAGjB,QAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU;AAG5E,QAAI,OAAO,cAAc,YAAY,SAAS,aAAa,EAAE;AAE7D,WAAO,UAAU;AAAA;AAAA;;;ACRjB;AAAA;AAAA,QAAI,OAAO;AAGX,QAAIA,UAAS,KAAK;AAElB,WAAO,UAAUA;AAAA;AAAA;;;ACLjB;AAAA;AAAA,QAAIC,UAAS;AAGb,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAOjC,QAAI,uBAAuB,YAAY;AAGvC,QAAI,iBAAiBA,UAASA,QAAO,cAAc;AASnD,aAAS,UAAU,OAAO;AACxB,UAAI,QAAQ,eAAe,KAAK,OAAO,cAAc,GACjD,MAAM,MAAM,cAAc;AAE9B,UAAI;AACF,cAAM,cAAc,IAAI;AACxB,YAAI,WAAW;AAAA,MACjB,SAAS,GAAG;AAAA,MAAC;AAEb,UAAI,SAAS,qBAAqB,KAAK,KAAK;AAC5C,UAAI,UAAU;AACZ,YAAI,OAAO;AACT,gBAAM,cAAc,IAAI;AAAA,QAC1B,OAAO;AACL,iBAAO,MAAM,cAAc;AAAA,QAC7B;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7CjB;AAAA;AACA,QAAI,cAAc,OAAO;AAOzB,QAAI,uBAAuB,YAAY;AASvC,aAAS,eAAe,OAAO;AAC7B,aAAO,qBAAqB,KAAK,KAAK;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA,QAAIC,UAAS;AAAb,QACI,YAAY;AADhB,QAEI,iBAAiB;AAGrB,QAAI,UAAU;AAAd,QACI,eAAe;AAGnB,QAAI,iBAAiBA,UAASA,QAAO,cAAc;AASnD,aAAS,WAAW,OAAO;AACzB,UAAI,SAAS,MAAM;AACjB,eAAO,UAAU,SAAY,eAAe;AAAA,MAC9C;AACA,aAAQ,kBAAkB,kBAAkB,OAAO,KAAK,IACpD,UAAU,KAAK,IACf,eAAe,KAAK;AAAA,IAC1B;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3BjB;AAAA;AAyBA,aAAS,SAAS,OAAO;AACvB,UAAI,OAAO,OAAO;AAClB,aAAO,SAAS,SAAS,QAAQ,YAAY,QAAQ;AAAA,IACvD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC9BjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,WAAW;AAGf,QAAI,WAAW;AAAf,QACI,UAAU;AADd,QAEI,SAAS;AAFb,QAGI,WAAW;AAmBf,aAAS,WAAW,OAAO;AACzB,UAAI,CAAC,SAAS,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAGA,UAAI,MAAM,WAAW,KAAK;AAC1B,aAAO,OAAO,WAAW,OAAO,UAAU,OAAO,YAAY,OAAO;AAAA,IACtE;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpCjB;AAAA;AAAA,QAAI,OAAO;AAGX,QAAI,aAAa,KAAK,oBAAoB;AAE1C,WAAO,UAAU;AAAA;AAAA;;;ACLjB;AAAA;AAAA,QAAI,aAAa;AAGjB,QAAI,aAAc,WAAW;AAC3B,UAAI,MAAM,SAAS,KAAK,cAAc,WAAW,QAAQ,WAAW,KAAK,YAAY,EAAE;AACvF,aAAO,MAAO,mBAAmB,MAAO;AAAA,IAC1C,EAAE;AASF,aAAS,SAAS,MAAM;AACtB,aAAO,CAAC,CAAC,cAAe,cAAc;AAAA,IACxC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AACA,QAAI,YAAY,SAAS;AAGzB,QAAI,eAAe,UAAU;AAS7B,aAAS,SAAS,MAAM;AACtB,UAAI,QAAQ,MAAM;AAChB,YAAI;AACF,iBAAO,aAAa,KAAK,IAAI;AAAA,QAC/B,SAAS,GAAG;AAAA,QAAC;AACb,YAAI;AACF,iBAAQ,OAAO;AAAA,QACjB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,WAAW;AADf,QAEI,WAAW;AAFf,QAGI,WAAW;AAMf,QAAI,eAAe;AAGnB,QAAI,eAAe;AAGnB,QAAI,YAAY,SAAS;AAAzB,QACI,cAAc,OAAO;AAGzB,QAAI,eAAe,UAAU;AAG7B,QAAI,iBAAiB,YAAY;AAGjC,QAAI,aAAa;AAAA,MAAO,MACtB,aAAa,KAAK,cAAc,EAAE,QAAQ,cAAc,MAAM,EAC7D,QAAQ,0DAA0D,OAAO,IAAI;AAAA,IAChF;AAUA,aAAS,aAAa,OAAO;AAC3B,UAAI,CAAC,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AACvC,eAAO;AAAA,MACT;AACA,UAAI,UAAU,WAAW,KAAK,IAAI,aAAa;AAC/C,aAAO,QAAQ,KAAK,SAAS,KAAK,CAAC;AAAA,IACrC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC9CjB;AAAA;AAQA,aAAS,SAAS,QAAQ,KAAK;AAC7B,aAAO,UAAU,OAAO,SAAY,OAAO,GAAG;AAAA,IAChD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACZjB;AAAA;AAAA,QAAI,eAAe;AAAnB,QACI,WAAW;AAUf,aAAS,UAAU,QAAQ,KAAK;AAC9B,UAAI,QAAQ,SAAS,QAAQ,GAAG;AAChC,aAAO,aAAa,KAAK,IAAI,QAAQ;AAAA,IACvC;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,OAAO;AAGX,QAAIC,OAAM,UAAU,MAAM,KAAK;AAE/B,WAAO,UAAUA;AAAA;AAAA;;;ACNjB;AAAA;AAAA,QAAI,YAAY;AAGhB,QAAI,eAAe,UAAU,QAAQ,QAAQ;AAE7C,WAAO,UAAU;AAAA;AAAA;;;ACLjB;AAAA;AAAA,QAAI,eAAe;AASnB,aAAS,YAAY;AACnB,WAAK,WAAW,eAAe,aAAa,IAAI,IAAI,CAAC;AACrD,WAAK,OAAO;AAAA,IACd;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACdjB;AAAA;AAUA,aAAS,WAAW,KAAK;AACvB,UAAI,SAAS,KAAK,IAAI,GAAG,KAAK,OAAO,KAAK,SAAS,GAAG;AACtD,WAAK,QAAQ,SAAS,IAAI;AAC1B,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA,QAAI,eAAe;AAGnB,QAAI,iBAAiB;AAGrB,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAWjC,aAAS,QAAQ,KAAK;AACpB,UAAI,OAAO,KAAK;AAChB,UAAI,cAAc;AAChB,YAAI,SAAS,KAAK,GAAG;AACrB,eAAO,WAAW,iBAAiB,SAAY;AAAA,MACjD;AACA,aAAO,eAAe,KAAK,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI;AAAA,IACtD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAI,eAAe;AAGnB,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAWjC,aAAS,QAAQ,KAAK;AACpB,UAAI,OAAO,KAAK;AAChB,aAAO,eAAgB,KAAK,GAAG,MAAM,SAAa,eAAe,KAAK,MAAM,GAAG;AAAA,IACjF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,eAAe;AAGnB,QAAI,iBAAiB;AAYrB,aAAS,QAAQ,KAAK,OAAO;AAC3B,UAAI,OAAO,KAAK;AAChB,WAAK,QAAQ,KAAK,IAAI,GAAG,IAAI,IAAI;AACjC,WAAK,GAAG,IAAK,gBAAgB,UAAU,SAAa,iBAAiB;AACrE,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,aAAa;AADjB,QAEI,UAAU;AAFd,QAGI,UAAU;AAHd,QAII,UAAU;AASd,aAAS,KAAK,SAAS;AACrB,UAAI,QAAQ,IACR,SAAS,WAAW,OAAO,IAAI,QAAQ;AAE3C,WAAK,MAAM;AACX,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,QAAQ,KAAK;AACzB,aAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MAC7B;AAAA,IACF;AAGA,SAAK,UAAU,QAAQ;AACvB,SAAK,UAAU,QAAQ,IAAI;AAC3B,SAAK,UAAU,MAAM;AACrB,SAAK,UAAU,MAAM;AACrB,SAAK,UAAU,MAAM;AAErB,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA,QAAI,OAAO;AAAX,QACI,YAAY;AADhB,QAEIC,OAAM;AASV,aAAS,gBAAgB;AACvB,WAAK,OAAO;AACZ,WAAK,WAAW;AAAA,QACd,QAAQ,IAAI;AAAA,QACZ,OAAO,KAAKA,QAAO;AAAA,QACnB,UAAU,IAAI;AAAA,MAChB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAOA,aAAS,UAAU,OAAO;AACxB,UAAI,OAAO,OAAO;AAClB,aAAQ,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YACvE,UAAU,cACV,UAAU;AAAA,IACjB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACdjB;AAAA;AAAA,QAAI,YAAY;AAUhB,aAAS,WAAW,KAAK,KAAK;AAC5B,UAAI,OAAO,IAAI;AACf,aAAO,UAAU,GAAG,IAChB,KAAK,OAAO,OAAO,WAAW,WAAW,MAAM,IAC/C,KAAK;AAAA,IACX;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,aAAa;AAWjB,aAAS,eAAe,KAAK;AAC3B,UAAI,SAAS,WAAW,MAAM,GAAG,EAAE,QAAQ,EAAE,GAAG;AAChD,WAAK,QAAQ,SAAS,IAAI;AAC1B,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,aAAa;AAWjB,aAAS,YAAY,KAAK;AACxB,aAAO,WAAW,MAAM,GAAG,EAAE,IAAI,GAAG;AAAA,IACtC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,aAAa;AAWjB,aAAS,YAAY,KAAK;AACxB,aAAO,WAAW,MAAM,GAAG,EAAE,IAAI,GAAG;AAAA,IACtC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,aAAa;AAYjB,aAAS,YAAY,KAAK,OAAO;AAC/B,UAAI,OAAO,WAAW,MAAM,GAAG,GAC3B,OAAO,KAAK;AAEhB,WAAK,IAAI,KAAK,KAAK;AACnB,WAAK,QAAQ,KAAK,QAAQ,OAAO,IAAI;AACrC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA,QAAI,gBAAgB;AAApB,QACI,iBAAiB;AADrB,QAEI,cAAc;AAFlB,QAGI,cAAc;AAHlB,QAII,cAAc;AASlB,aAAS,SAAS,SAAS;AACzB,UAAI,QAAQ,IACR,SAAS,WAAW,OAAO,IAAI,QAAQ;AAE3C,WAAK,MAAM;AACX,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,QAAQ,KAAK;AACzB,aAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MAC7B;AAAA,IACF;AAGA,aAAS,UAAU,QAAQ;AAC3B,aAAS,UAAU,QAAQ,IAAI;AAC/B,aAAS,UAAU,MAAM;AACzB,aAAS,UAAU,MAAM;AACzB,aAAS,UAAU,MAAM;AAEzB,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACIC,OAAM;AADV,QAEI,WAAW;AAGf,QAAI,mBAAmB;AAYvB,aAAS,SAAS,KAAK,OAAO;AAC5B,UAAI,OAAO,KAAK;AAChB,UAAI,gBAAgB,WAAW;AAC7B,YAAI,QAAQ,KAAK;AACjB,YAAI,CAACA,QAAQ,MAAM,SAAS,mBAAmB,GAAI;AACjD,gBAAM,KAAK,CAAC,KAAK,KAAK,CAAC;AACvB,eAAK,OAAO,EAAE,KAAK;AACnB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,WAAW,IAAI,SAAS,KAAK;AAAA,MAC3C;AACA,WAAK,IAAI,KAAK,KAAK;AACnB,WAAK,OAAO,KAAK;AACjB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjCjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,aAAa;AADjB,QAEI,cAAc;AAFlB,QAGI,WAAW;AAHf,QAII,WAAW;AAJf,QAKI,WAAW;AASf,aAAS,MAAM,SAAS;AACtB,UAAI,OAAO,KAAK,WAAW,IAAI,UAAU,OAAO;AAChD,WAAK,OAAO,KAAK;AAAA,IACnB;AAGA,UAAM,UAAU,QAAQ;AACxB,UAAM,UAAU,QAAQ,IAAI;AAC5B,UAAM,UAAU,MAAM;AACtB,UAAM,UAAU,MAAM;AACtB,UAAM,UAAU,MAAM;AAEtB,WAAO,UAAU;AAAA;AAAA;;;AC1BjB;AAAA;AACA,QAAI,iBAAiB;AAYrB,aAAS,YAAY,OAAO;AAC1B,WAAK,SAAS,IAAI,OAAO,cAAc;AACvC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AASA,aAAS,YAAY,OAAO;AAC1B,aAAO,KAAK,SAAS,IAAI,KAAK;AAAA,IAChC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACbjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,cAAc;AADlB,QAEI,cAAc;AAUlB,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,IACR,SAAS,UAAU,OAAO,IAAI,OAAO;AAEzC,WAAK,WAAW,IAAI;AACpB,aAAO,EAAE,QAAQ,QAAQ;AACvB,aAAK,IAAI,OAAO,KAAK,CAAC;AAAA,MACxB;AAAA,IACF;AAGA,aAAS,UAAU,MAAM,SAAS,UAAU,OAAO;AACnD,aAAS,UAAU,MAAM;AAEzB,WAAO,UAAU;AAAA;AAAA;;;AC1BjB;AAAA;AAUA,aAAS,UAAU,OAAO,WAAW;AACnC,UAAI,QAAQ,IACR,SAAS,SAAS,OAAO,IAAI,MAAM;AAEvC,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,UAAU,MAAM,KAAK,GAAG,OAAO,KAAK,GAAG;AACzC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAQA,aAAS,SAAS,OAAO,KAAK;AAC5B,aAAO,MAAM,IAAI,GAAG;AAAA,IACtB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACZjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,YAAY;AADhB,QAEI,WAAW;AAGf,QAAI,uBAAuB;AAA3B,QACI,yBAAyB;AAe7B,aAAS,YAAY,OAAO,OAAO,SAAS,YAAY,WAAW,OAAO;AACxE,UAAI,YAAY,UAAU,sBACtB,YAAY,MAAM,QAClB,YAAY,MAAM;AAEtB,UAAI,aAAa,aAAa,EAAE,aAAa,YAAY,YAAY;AACnE,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,MAAM,IAAI,KAAK;AAChC,UAAI,aAAa,MAAM,IAAI,KAAK;AAChC,UAAI,cAAc,YAAY;AAC5B,eAAO,cAAc,SAAS,cAAc;AAAA,MAC9C;AACA,UAAI,QAAQ,IACR,SAAS,MACT,OAAQ,UAAU,yBAA0B,IAAI,aAAW;AAE/D,YAAM,IAAI,OAAO,KAAK;AACtB,YAAM,IAAI,OAAO,KAAK;AAGtB,aAAO,EAAE,QAAQ,WAAW;AAC1B,YAAI,WAAW,MAAM,KAAK,GACtB,WAAW,MAAM,KAAK;AAE1B,YAAI,YAAY;AACd,cAAI,WAAW,YACX,WAAW,UAAU,UAAU,OAAO,OAAO,OAAO,KAAK,IACzD,WAAW,UAAU,UAAU,OAAO,OAAO,OAAO,KAAK;AAAA,QAC/D;AACA,YAAI,aAAa,QAAW;AAC1B,cAAI,UAAU;AACZ;AAAA,UACF;AACA,mBAAS;AACT;AAAA,QACF;AAEA,YAAI,MAAM;AACR,cAAI,CAAC,UAAU,OAAO,SAASC,WAAU,UAAU;AAC7C,gBAAI,CAAC,SAAS,MAAM,QAAQ,MACvB,aAAaA,aAAY,UAAU,UAAUA,WAAU,SAAS,YAAY,KAAK,IAAI;AACxF,qBAAO,KAAK,KAAK,QAAQ;AAAA,YAC3B;AAAA,UACF,CAAC,GAAG;AACN,qBAAS;AACT;AAAA,UACF;AAAA,QACF,WAAW,EACL,aAAa,YACX,UAAU,UAAU,UAAU,SAAS,YAAY,KAAK,IACzD;AACL,mBAAS;AACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ,EAAE,KAAK;AACrB,YAAM,QAAQ,EAAE,KAAK;AACrB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnFjB;AAAA;AAAA,QAAI,OAAO;AAGX,QAAIC,cAAa,KAAK;AAEtB,WAAO,UAAUA;AAAA;AAAA;;;ACLjB;AAAA;AAOA,aAAS,WAAW,KAAK;AACvB,UAAI,QAAQ,IACR,SAAS,MAAM,IAAI,IAAI;AAE3B,UAAI,QAAQ,SAAS,OAAO,KAAK;AAC/B,eAAO,EAAE,KAAK,IAAI,CAAC,KAAK,KAAK;AAAA,MAC/B,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAOA,aAAS,WAAW,KAAK;AACvB,UAAI,QAAQ,IACR,SAAS,MAAM,IAAI,IAAI;AAE3B,UAAI,QAAQ,SAAS,OAAO;AAC1B,eAAO,EAAE,KAAK,IAAI;AAAA,MACpB,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAIC,UAAS;AAAb,QACIC,cAAa;AADjB,QAEI,KAAK;AAFT,QAGI,cAAc;AAHlB,QAII,aAAa;AAJjB,QAKI,aAAa;AAGjB,QAAI,uBAAuB;AAA3B,QACI,yBAAyB;AAG7B,QAAI,UAAU;AAAd,QACI,UAAU;AADd,QAEI,WAAW;AAFf,QAGI,SAAS;AAHb,QAII,YAAY;AAJhB,QAKI,YAAY;AALhB,QAMI,SAAS;AANb,QAOI,YAAY;AAPhB,QAQI,YAAY;AAEhB,QAAI,iBAAiB;AAArB,QACI,cAAc;AAGlB,QAAI,cAAcD,UAASA,QAAO,YAAY;AAA9C,QACI,gBAAgB,cAAc,YAAY,UAAU;AAmBxD,aAAS,WAAW,QAAQ,OAAO,KAAK,SAAS,YAAY,WAAW,OAAO;AAC7E,cAAQ,KAAK;AAAA,QACX,KAAK;AACH,cAAK,OAAO,cAAc,MAAM,cAC3B,OAAO,cAAc,MAAM,YAAa;AAC3C,mBAAO;AAAA,UACT;AACA,mBAAS,OAAO;AAChB,kBAAQ,MAAM;AAAA,QAEhB,KAAK;AACH,cAAK,OAAO,cAAc,MAAM,cAC5B,CAAC,UAAU,IAAIC,YAAW,MAAM,GAAG,IAAIA,YAAW,KAAK,CAAC,GAAG;AAC7D,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QAET,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAGH,iBAAO,GAAG,CAAC,QAAQ,CAAC,KAAK;AAAA,QAE3B,KAAK;AACH,iBAAO,OAAO,QAAQ,MAAM,QAAQ,OAAO,WAAW,MAAM;AAAA,QAE9D,KAAK;AAAA,QACL,KAAK;AAIH,iBAAO,UAAW,QAAQ;AAAA,QAE5B,KAAK;AACH,cAAI,UAAU;AAAA,QAEhB,KAAK;AACH,cAAI,YAAY,UAAU;AAC1B,sBAAY,UAAU;AAEtB,cAAI,OAAO,QAAQ,MAAM,QAAQ,CAAC,WAAW;AAC3C,mBAAO;AAAA,UACT;AAEA,cAAI,UAAU,MAAM,IAAI,MAAM;AAC9B,cAAI,SAAS;AACX,mBAAO,WAAW;AAAA,UACpB;AACA,qBAAW;AAGX,gBAAM,IAAI,QAAQ,KAAK;AACvB,cAAI,SAAS,YAAY,QAAQ,MAAM,GAAG,QAAQ,KAAK,GAAG,SAAS,YAAY,WAAW,KAAK;AAC/F,gBAAM,QAAQ,EAAE,MAAM;AACtB,iBAAO;AAAA,QAET,KAAK;AACH,cAAI,eAAe;AACjB,mBAAO,cAAc,KAAK,MAAM,KAAK,cAAc,KAAK,KAAK;AAAA,UAC/D;AAAA,MACJ;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/GjB;AAAA;AAQA,aAAS,UAAU,OAAO,QAAQ;AAChC,UAAI,QAAQ,IACR,SAAS,OAAO,QAChB,SAAS,MAAM;AAEnB,aAAO,EAAE,QAAQ,QAAQ;AACvB,cAAM,SAAS,KAAK,IAAI,OAAO,KAAK;AAAA,MACtC;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAuBA,QAAI,UAAU,MAAM;AAEpB,WAAO,UAAU;AAAA;AAAA;;;ACzBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,UAAU;AAad,aAAS,eAAe,QAAQ,UAAU,aAAa;AACrD,UAAI,SAAS,SAAS,MAAM;AAC5B,aAAO,QAAQ,MAAM,IAAI,SAAS,UAAU,QAAQ,YAAY,MAAM,CAAC;AAAA,IACzE;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AASA,aAAS,YAAY,OAAO,WAAW;AACrC,UAAI,QAAQ,IACR,SAAS,SAAS,OAAO,IAAI,MAAM,QACnC,WAAW,GACX,SAAS,CAAC;AAEd,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,MAAM,KAAK;AACvB,YAAI,UAAU,OAAO,OAAO,KAAK,GAAG;AAClC,iBAAO,UAAU,IAAI;AAAA,QACvB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAkBA,aAAS,YAAY;AACnB,aAAO,CAAC;AAAA,IACV;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,YAAY;AAGhB,QAAI,cAAc,OAAO;AAGzB,QAAI,uBAAuB,YAAY;AAGvC,QAAI,mBAAmB,OAAO;AAS9B,QAAI,aAAa,CAAC,mBAAmB,YAAY,SAAS,QAAQ;AAChE,UAAI,UAAU,MAAM;AAClB,eAAO,CAAC;AAAA,MACV;AACA,eAAS,OAAO,MAAM;AACtB,aAAO,YAAY,iBAAiB,MAAM,GAAG,SAAS,QAAQ;AAC5D,eAAO,qBAAqB,KAAK,QAAQ,MAAM;AAAA,MACjD,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AASA,aAAS,UAAUC,IAAG,UAAU;AAC9B,UAAI,QAAQ,IACR,SAAS,MAAMA,EAAC;AAEpB,aAAO,EAAE,QAAQA,IAAG;AAClB,eAAO,KAAK,IAAI,SAAS,KAAK;AAAA,MAChC;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAwBA,aAAS,aAAa,OAAO;AAC3B,aAAO,SAAS,QAAQ,OAAO,SAAS;AAAA,IAC1C;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5BjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,eAAe;AAGnB,QAAI,UAAU;AASd,aAAS,gBAAgB,OAAO;AAC9B,aAAO,aAAa,KAAK,KAAK,WAAW,KAAK,KAAK;AAAA,IACrD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,kBAAkB;AAAtB,QACI,eAAe;AAGnB,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAGjC,QAAI,uBAAuB,YAAY;AAoBvC,QAAI,cAAc,gBAAgB,WAAW;AAAE,aAAO;AAAA,IAAW,EAAE,CAAC,IAAI,kBAAkB,SAAS,OAAO;AACxG,aAAO,aAAa,KAAK,KAAK,eAAe,KAAK,OAAO,QAAQ,KAC/D,CAAC,qBAAqB,KAAK,OAAO,QAAQ;AAAA,IAC9C;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnCjB;AAAA;AAaA,aAAS,YAAY;AACnB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,OAAO;AAAX,QACI,YAAY;AAGhB,QAAI,cAAc,OAAO,WAAW,YAAY,WAAW,CAAC,QAAQ,YAAY;AAGhF,QAAI,aAAa,eAAe,OAAO,UAAU,YAAY,UAAU,CAAC,OAAO,YAAY;AAG3F,QAAI,gBAAgB,cAAc,WAAW,YAAY;AAGzD,QAAI,SAAS,gBAAgB,KAAK,SAAS;AAG3C,QAAI,iBAAiB,SAAS,OAAO,WAAW;AAmBhD,QAAI,WAAW,kBAAkB;AAEjC,WAAO,UAAU;AAAA;AAAA;;;ACrCjB;AAAA;AACA,QAAI,mBAAmB;AAGvB,QAAI,WAAW;AAUf,aAAS,QAAQ,OAAO,QAAQ;AAC9B,UAAI,OAAO,OAAO;AAClB,eAAS,UAAU,OAAO,mBAAmB;AAE7C,aAAO,CAAC,CAAC,WACN,QAAQ,YACN,QAAQ,YAAY,SAAS,KAAK,KAAK,OACrC,QAAQ,MAAM,QAAQ,KAAK,KAAK,QAAQ;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AACA,QAAI,mBAAmB;AA4BvB,aAAS,SAAS,OAAO;AACvB,aAAO,OAAO,SAAS,YACrB,QAAQ,MAAM,QAAQ,KAAK,KAAK,SAAS;AAAA,IAC7C;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClCjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,WAAW;AADf,QAEI,eAAe;AAGnB,QAAI,UAAU;AAAd,QACI,WAAW;AADf,QAEI,UAAU;AAFd,QAGI,UAAU;AAHd,QAII,WAAW;AAJf,QAKI,UAAU;AALd,QAMI,SAAS;AANb,QAOI,YAAY;AAPhB,QAQI,YAAY;AARhB,QASI,YAAY;AAThB,QAUI,SAAS;AAVb,QAWI,YAAY;AAXhB,QAYI,aAAa;AAEjB,QAAI,iBAAiB;AAArB,QACI,cAAc;AADlB,QAEI,aAAa;AAFjB,QAGI,aAAa;AAHjB,QAII,UAAU;AAJd,QAKI,WAAW;AALf,QAMI,WAAW;AANf,QAOI,WAAW;AAPf,QAQI,kBAAkB;AARtB,QASI,YAAY;AAThB,QAUI,YAAY;AAGhB,QAAI,iBAAiB,CAAC;AACtB,mBAAe,UAAU,IAAI,eAAe,UAAU,IACtD,eAAe,OAAO,IAAI,eAAe,QAAQ,IACjD,eAAe,QAAQ,IAAI,eAAe,QAAQ,IAClD,eAAe,eAAe,IAAI,eAAe,SAAS,IAC1D,eAAe,SAAS,IAAI;AAC5B,mBAAe,OAAO,IAAI,eAAe,QAAQ,IACjD,eAAe,cAAc,IAAI,eAAe,OAAO,IACvD,eAAe,WAAW,IAAI,eAAe,OAAO,IACpD,eAAe,QAAQ,IAAI,eAAe,OAAO,IACjD,eAAe,MAAM,IAAI,eAAe,SAAS,IACjD,eAAe,SAAS,IAAI,eAAe,SAAS,IACpD,eAAe,MAAM,IAAI,eAAe,SAAS,IACjD,eAAe,UAAU,IAAI;AAS7B,aAAS,iBAAiB,OAAO;AAC/B,aAAO,aAAa,KAAK,KACvB,SAAS,MAAM,MAAM,KAAK,CAAC,CAAC,eAAe,WAAW,KAAK,CAAC;AAAA,IAChE;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3DjB;AAAA;AAOA,aAAS,UAAU,MAAM;AACvB,aAAO,SAAS,OAAO;AACrB,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACbjB;AAAA;AAAA,QAAI,aAAa;AAGjB,QAAI,cAAc,OAAO,WAAW,YAAY,WAAW,CAAC,QAAQ,YAAY;AAGhF,QAAI,aAAa,eAAe,OAAO,UAAU,YAAY,UAAU,CAAC,OAAO,YAAY;AAG3F,QAAI,gBAAgB,cAAc,WAAW,YAAY;AAGzD,QAAI,cAAc,iBAAiB,WAAW;AAG9C,QAAI,WAAY,WAAW;AACzB,UAAI;AAEF,YAAI,QAAQ,cAAc,WAAW,WAAW,WAAW,QAAQ,MAAM,EAAE;AAE3E,YAAI,OAAO;AACT,iBAAO;AAAA,QACT;AAGA,eAAO,eAAe,YAAY,WAAW,YAAY,QAAQ,MAAM;AAAA,MACzE,SAAS,GAAG;AAAA,MAAC;AAAA,IACf,EAAE;AAEF,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAI,mBAAmB;AAAvB,QACI,YAAY;AADhB,QAEI,WAAW;AAGf,QAAI,mBAAmB,YAAY,SAAS;AAmB5C,QAAI,eAAe,mBAAmB,UAAU,gBAAgB,IAAI;AAEpE,WAAO,UAAU;AAAA;AAAA;;;AC1BjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,cAAc;AADlB,QAEI,UAAU;AAFd,QAGI,WAAW;AAHf,QAII,UAAU;AAJd,QAKI,eAAe;AAGnB,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAUjC,aAAS,cAAc,OAAO,WAAW;AACvC,UAAI,QAAQ,QAAQ,KAAK,GACrB,QAAQ,CAAC,SAAS,YAAY,KAAK,GACnC,SAAS,CAAC,SAAS,CAAC,SAAS,SAAS,KAAK,GAC3C,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,UAAU,aAAa,KAAK,GAC1D,cAAc,SAAS,SAAS,UAAU,QAC1C,SAAS,cAAc,UAAU,MAAM,QAAQ,MAAM,IAAI,CAAC,GAC1D,SAAS,OAAO;AAEpB,eAAS,OAAO,OAAO;AACrB,aAAK,aAAa,eAAe,KAAK,OAAO,GAAG,MAC5C,EAAE;AAAA,SAEC,OAAO;AAAA,QAEN,WAAW,OAAO,YAAY,OAAO;AAAA,QAErC,WAAW,OAAO,YAAY,OAAO,gBAAgB,OAAO;AAAA,QAE7D,QAAQ,KAAK,MAAM,KAClB;AACN,iBAAO,KAAK,GAAG;AAAA,QACjB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChDjB;AAAA;AACA,QAAI,cAAc,OAAO;AASzB,aAAS,YAAY,OAAO;AAC1B,UAAI,OAAO,SAAS,MAAM,aACtB,QAAS,OAAO,QAAQ,cAAc,KAAK,aAAc;AAE7D,aAAO,UAAU;AAAA,IACnB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAQA,aAAS,QAAQ,MAAM,WAAW;AAChC,aAAO,SAAS,KAAK;AACnB,eAAO,KAAK,UAAU,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACdjB;AAAA;AAAA,QAAI,UAAU;AAGd,QAAI,aAAa,QAAQ,OAAO,MAAM,MAAM;AAE5C,WAAO,UAAU;AAAA;AAAA;;;ACLjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,aAAa;AAGjB,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AASjC,aAAS,SAAS,QAAQ;AACxB,UAAI,CAAC,YAAY,MAAM,GAAG;AACxB,eAAO,WAAW,MAAM;AAAA,MAC1B;AACA,UAAI,SAAS,CAAC;AACd,eAAS,OAAO,OAAO,MAAM,GAAG;AAC9B,YAAI,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,eAAe;AAC5D,iBAAO,KAAK,GAAG;AAAA,QACjB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,WAAW;AA2Bf,aAAS,YAAY,OAAO;AAC1B,aAAO,SAAS,QAAQ,SAAS,MAAM,MAAM,KAAK,CAAC,WAAW,KAAK;AAAA,IACrE;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAAA,QAAI,gBAAgB;AAApB,QACI,WAAW;AADf,QAEI,cAAc;AA8BlB,aAAS,KAAK,QAAQ;AACpB,aAAO,YAAY,MAAM,IAAI,cAAc,MAAM,IAAI,SAAS,MAAM;AAAA,IACtE;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpCjB;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,aAAa;AADjB,QAEI,OAAO;AASX,aAAS,WAAW,QAAQ;AAC1B,aAAO,eAAe,QAAQ,MAAM,UAAU;AAAA,IAChD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,aAAa;AAGjB,QAAI,uBAAuB;AAG3B,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAejC,aAAS,aAAa,QAAQ,OAAO,SAAS,YAAY,WAAW,OAAO;AAC1E,UAAI,YAAY,UAAU,sBACtB,WAAW,WAAW,MAAM,GAC5B,YAAY,SAAS,QACrB,WAAW,WAAW,KAAK,GAC3B,YAAY,SAAS;AAEzB,UAAI,aAAa,aAAa,CAAC,WAAW;AACxC,eAAO;AAAA,MACT;AACA,UAAI,QAAQ;AACZ,aAAO,SAAS;AACd,YAAI,MAAM,SAAS,KAAK;AACxB,YAAI,EAAE,YAAY,OAAO,QAAQ,eAAe,KAAK,OAAO,GAAG,IAAI;AACjE,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,aAAa,MAAM,IAAI,MAAM;AACjC,UAAI,aAAa,MAAM,IAAI,KAAK;AAChC,UAAI,cAAc,YAAY;AAC5B,eAAO,cAAc,SAAS,cAAc;AAAA,MAC9C;AACA,UAAI,SAAS;AACb,YAAM,IAAI,QAAQ,KAAK;AACvB,YAAM,IAAI,OAAO,MAAM;AAEvB,UAAI,WAAW;AACf,aAAO,EAAE,QAAQ,WAAW;AAC1B,cAAM,SAAS,KAAK;AACpB,YAAI,WAAW,OAAO,GAAG,GACrB,WAAW,MAAM,GAAG;AAExB,YAAI,YAAY;AACd,cAAI,WAAW,YACX,WAAW,UAAU,UAAU,KAAK,OAAO,QAAQ,KAAK,IACxD,WAAW,UAAU,UAAU,KAAK,QAAQ,OAAO,KAAK;AAAA,QAC9D;AAEA,YAAI,EAAE,aAAa,SACV,aAAa,YAAY,UAAU,UAAU,UAAU,SAAS,YAAY,KAAK,IAClF,WACD;AACL,mBAAS;AACT;AAAA,QACF;AACA,qBAAa,WAAW,OAAO;AAAA,MACjC;AACA,UAAI,UAAU,CAAC,UAAU;AACvB,YAAI,UAAU,OAAO,aACjB,UAAU,MAAM;AAGpB,YAAI,WAAW,YACV,iBAAiB,UAAU,iBAAiB,UAC7C,EAAE,OAAO,WAAW,cAAc,mBAAmB,WACnD,OAAO,WAAW,cAAc,mBAAmB,UAAU;AACjE,mBAAS;AAAA,QACX;AAAA,MACF;AACA,YAAM,QAAQ,EAAE,MAAM;AACtB,YAAM,QAAQ,EAAE,KAAK;AACrB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzFjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,OAAO;AAGX,QAAI,WAAW,UAAU,MAAM,UAAU;AAEzC,WAAO,UAAU;AAAA;AAAA;;;ACNjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,OAAO;AAGX,QAAIC,WAAU,UAAU,MAAM,SAAS;AAEvC,WAAO,UAAUA;AAAA;AAAA;;;ACNjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,OAAO;AAGX,QAAIC,OAAM,UAAU,MAAM,KAAK;AAE/B,WAAO,UAAUA;AAAA;AAAA;;;ACNjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,OAAO;AAGX,QAAIC,WAAU,UAAU,MAAM,SAAS;AAEvC,WAAO,UAAUA;AAAA;AAAA;;;ACNjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACIC,OAAM;AADV,QAEIC,WAAU;AAFd,QAGIC,OAAM;AAHV,QAIIC,WAAU;AAJd,QAKI,aAAa;AALjB,QAMI,WAAW;AAGf,QAAI,SAAS;AAAb,QACI,YAAY;AADhB,QAEI,aAAa;AAFjB,QAGI,SAAS;AAHb,QAII,aAAa;AAEjB,QAAI,cAAc;AAGlB,QAAI,qBAAqB,SAAS,QAAQ;AAA1C,QACI,gBAAgB,SAASH,IAAG;AADhC,QAEI,oBAAoB,SAASC,QAAO;AAFxC,QAGI,gBAAgB,SAASC,IAAG;AAHhC,QAII,oBAAoB,SAASC,QAAO;AASxC,QAAI,SAAS;AAGb,QAAK,YAAY,OAAO,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,eACxDH,QAAO,OAAO,IAAIA,MAAG,KAAK,UAC1BC,YAAW,OAAOA,SAAQ,QAAQ,CAAC,KAAK,cACxCC,QAAO,OAAO,IAAIA,MAAG,KAAK,UAC1BC,YAAW,OAAO,IAAIA,UAAO,KAAK,YAAa;AAClD,eAAS,SAAS,OAAO;AACvB,YAAI,SAAS,WAAW,KAAK,GACzB,OAAO,UAAU,YAAY,MAAM,cAAc,QACjD,aAAa,OAAO,SAAS,IAAI,IAAI;AAEzC,YAAI,YAAY;AACd,kBAAQ,YAAY;AAAA,YAClB,KAAK;AAAoB,qBAAO;AAAA,YAChC,KAAK;AAAe,qBAAO;AAAA,YAC3B,KAAK;AAAmB,qBAAO;AAAA,YAC/B,KAAK;AAAe,qBAAO;AAAA,YAC3B,KAAK;AAAmB,qBAAO;AAAA,UACjC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzDjB;AAAA;AAAA,QAAI,QAAQ;AAAZ,QACI,cAAc;AADlB,QAEI,aAAa;AAFjB,QAGI,eAAe;AAHnB,QAII,SAAS;AAJb,QAKI,UAAU;AALd,QAMI,WAAW;AANf,QAOI,eAAe;AAGnB,QAAI,uBAAuB;AAG3B,QAAI,UAAU;AAAd,QACI,WAAW;AADf,QAEI,YAAY;AAGhB,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAgBjC,aAAS,gBAAgB,QAAQ,OAAO,SAAS,YAAY,WAAW,OAAO;AAC7E,UAAI,WAAW,QAAQ,MAAM,GACzB,WAAW,QAAQ,KAAK,GACxB,SAAS,WAAW,WAAW,OAAO,MAAM,GAC5C,SAAS,WAAW,WAAW,OAAO,KAAK;AAE/C,eAAS,UAAU,UAAU,YAAY;AACzC,eAAS,UAAU,UAAU,YAAY;AAEzC,UAAI,WAAW,UAAU,WACrB,WAAW,UAAU,WACrB,YAAY,UAAU;AAE1B,UAAI,aAAa,SAAS,MAAM,GAAG;AACjC,YAAI,CAAC,SAAS,KAAK,GAAG;AACpB,iBAAO;AAAA,QACT;AACA,mBAAW;AACX,mBAAW;AAAA,MACb;AACA,UAAI,aAAa,CAAC,UAAU;AAC1B,kBAAU,QAAQ,IAAI;AACtB,eAAQ,YAAY,aAAa,MAAM,IACnC,YAAY,QAAQ,OAAO,SAAS,YAAY,WAAW,KAAK,IAChE,WAAW,QAAQ,OAAO,QAAQ,SAAS,YAAY,WAAW,KAAK;AAAA,MAC7E;AACA,UAAI,EAAE,UAAU,uBAAuB;AACrC,YAAI,eAAe,YAAY,eAAe,KAAK,QAAQ,aAAa,GACpE,eAAe,YAAY,eAAe,KAAK,OAAO,aAAa;AAEvE,YAAI,gBAAgB,cAAc;AAChC,cAAI,eAAe,eAAe,OAAO,MAAM,IAAI,QAC/C,eAAe,eAAe,MAAM,MAAM,IAAI;AAElD,oBAAU,QAAQ,IAAI;AACtB,iBAAO,UAAU,cAAc,cAAc,SAAS,YAAY,KAAK;AAAA,QACzE;AAAA,MACF;AACA,UAAI,CAAC,WAAW;AACd,eAAO;AAAA,MACT;AACA,gBAAU,QAAQ,IAAI;AACtB,aAAO,aAAa,QAAQ,OAAO,SAAS,YAAY,WAAW,KAAK;AAAA,IAC1E;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClFjB;AAAA;AAAA,QAAI,kBAAkB;AAAtB,QACI,eAAe;AAgBnB,aAAS,YAAY,OAAO,OAAO,SAAS,YAAY,OAAO;AAC7D,UAAI,UAAU,OAAO;AACnB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,QAAQ,SAAS,QAAS,CAAC,aAAa,KAAK,KAAK,CAAC,aAAa,KAAK,GAAI;AACpF,eAAO,UAAU,SAAS,UAAU;AAAA,MACtC;AACA,aAAO,gBAAgB,OAAO,OAAO,SAAS,YAAY,aAAa,KAAK;AAAA,IAC9E;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3BjB;AAAA;AAAA,QAAI,cAAc;AAkClB,aAAS,YAAY,OAAO,OAAO,YAAY;AAC7C,mBAAa,OAAO,cAAc,aAAa,aAAa;AAC5D,UAAI,SAAS,aAAa,WAAW,OAAO,KAAK,IAAI;AACrD,aAAO,WAAW,SAAY,YAAY,OAAO,OAAO,QAAW,UAAU,IAAI,CAAC,CAAC;AAAA,IACrF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxCjB;AAAA;AAEA,WAAO,UAAU,SAAS,aAAa,MAAM,MAAM,SAAS,gBAAgB;AAC1E,UAAI,MAAM,UAAU,QAAQ,KAAK,gBAAgB,MAAM,IAAI,IAAI;AAE/D,UAAI,QAAQ,QAAQ;AAClB,eAAO,CAAC,CAAC;AAAA,MACX;AAEA,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,SAAS,YAAY,CAAC,QAAQ,OAAO,SAAS,YAAY,CAAC,MAAM;AAC1E,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,UAAI,QAAQ,OAAO,KAAK,IAAI;AAE5B,UAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,eAAO;AAAA,MACT;AAEA,UAAI,kBAAkB,OAAO,UAAU,eAAe,KAAK,IAAI;AAG/D,eAAS,MAAM,GAAG,MAAM,MAAM,QAAQ,OAAO;AAC3C,YAAI,MAAM,MAAM,GAAG;AAEnB,YAAI,CAAC,gBAAgB,GAAG,GAAG;AACzB,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,KAAK,GAAG;AACrB,YAAI,SAAS,KAAK,GAAG;AAErB,cAAM,UAAU,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ,GAAG,IAAI;AAEpE,YAAI,QAAQ,SAAU,QAAQ,UAAU,WAAW,QAAS;AAC1D,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC7CA;AAAA;AASA,aAAS,UAAU,OAAO,UAAU;AAClC,UAAI,QAAQ,IACR,SAAS,SAAS,OAAO,IAAI,MAAM;AAEvC,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,SAAS,MAAM,KAAK,GAAG,OAAO,KAAK,MAAM,OAAO;AAClD;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA,QAAI,YAAY;AAEhB,QAAI,iBAAkB,WAAW;AAC/B,UAAI;AACF,YAAI,OAAO,UAAU,QAAQ,gBAAgB;AAC7C,aAAK,CAAC,GAAG,IAAI,CAAC,CAAC;AACf,eAAO;AAAA,MACT,SAAS,GAAG;AAAA,MAAC;AAAA,IACf,EAAE;AAEF,WAAO,UAAU;AAAA;AAAA;;;ACVjB;AAAA;AAAA,QAAI,iBAAiB;AAWrB,aAAS,gBAAgB,QAAQ,KAAK,OAAO;AAC3C,UAAI,OAAO,eAAe,gBAAgB;AACxC,uBAAe,QAAQ,KAAK;AAAA,UAC1B,gBAAgB;AAAA,UAChB,cAAc;AAAA,UACd,SAAS;AAAA,UACT,YAAY;AAAA,QACd,CAAC;AAAA,MACH,OAAO;AACL,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAAA,QAAI,kBAAkB;AAAtB,QACI,KAAK;AAGT,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAYjC,aAAS,YAAY,QAAQ,KAAK,OAAO;AACvC,UAAI,WAAW,OAAO,GAAG;AACzB,UAAI,EAAE,eAAe,KAAK,QAAQ,GAAG,KAAK,GAAG,UAAU,KAAK,MACvD,UAAU,UAAa,EAAE,OAAO,SAAU;AAC7C,wBAAgB,QAAQ,KAAK,KAAK;AAAA,MACpC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3BjB;AAAA;AAAA,QAAI,cAAc;AAAlB,QACI,kBAAkB;AAYtB,aAAS,WAAW,QAAQ,OAAO,QAAQ,YAAY;AACrD,UAAI,QAAQ,CAAC;AACb,iBAAW,SAAS,CAAC;AAErB,UAAI,QAAQ,IACR,SAAS,MAAM;AAEnB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,MAAM,MAAM,KAAK;AAErB,YAAI,WAAW,aACX,WAAW,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,KAAK,QAAQ,MAAM,IACxD;AAEJ,YAAI,aAAa,QAAW;AAC1B,qBAAW,OAAO,GAAG;AAAA,QACvB;AACA,YAAI,OAAO;AACT,0BAAgB,QAAQ,KAAK,QAAQ;AAAA,QACvC,OAAO;AACL,sBAAY,QAAQ,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvCjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,OAAO;AAWX,aAAS,WAAW,QAAQ,QAAQ;AAClC,aAAO,UAAU,WAAW,QAAQ,KAAK,MAAM,GAAG,MAAM;AAAA,IAC1D;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AASA,aAAS,aAAa,QAAQ;AAC5B,UAAI,SAAS,CAAC;AACd,UAAI,UAAU,MAAM;AAClB,iBAAS,OAAO,OAAO,MAAM,GAAG;AAC9B,iBAAO,KAAK,GAAG;AAAA,QACjB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,cAAc;AADlB,QAEI,eAAe;AAGnB,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AASjC,aAAS,WAAW,QAAQ;AAC1B,UAAI,CAAC,SAAS,MAAM,GAAG;AACrB,eAAO,aAAa,MAAM;AAAA,MAC5B;AACA,UAAI,UAAU,YAAY,MAAM,GAC5B,SAAS,CAAC;AAEd,eAAS,OAAO,QAAQ;AACtB,YAAI,EAAE,OAAO,kBAAkB,WAAW,CAAC,eAAe,KAAK,QAAQ,GAAG,KAAK;AAC7E,iBAAO,KAAK,GAAG;AAAA,QACjB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAAA,QAAI,gBAAgB;AAApB,QACI,aAAa;AADjB,QAEI,cAAc;AAyBlB,aAAS,OAAO,QAAQ;AACtB,aAAO,YAAY,MAAM,IAAI,cAAc,QAAQ,IAAI,IAAI,WAAW,MAAM;AAAA,IAC9E;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,SAAS;AAWb,aAAS,aAAa,QAAQ,QAAQ;AACpC,aAAO,UAAU,WAAW,QAAQ,OAAO,MAAM,GAAG,MAAM;AAAA,IAC5D;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA,QAAI,OAAO;AAGX,QAAI,cAAc,OAAO,WAAW,YAAY,WAAW,CAAC,QAAQ,YAAY;AAGhF,QAAI,aAAa,eAAe,OAAO,UAAU,YAAY,UAAU,CAAC,OAAO,YAAY;AAG3F,QAAI,gBAAgB,cAAc,WAAW,YAAY;AAGzD,QAAI,SAAS,gBAAgB,KAAK,SAAS;AAA3C,QACI,cAAc,SAAS,OAAO,cAAc;AAUhD,aAAS,YAAY,QAAQ,QAAQ;AACnC,UAAI,QAAQ;AACV,eAAO,OAAO,MAAM;AAAA,MACtB;AACA,UAAI,SAAS,OAAO,QAChB,SAAS,cAAc,YAAY,MAAM,IAAI,IAAI,OAAO,YAAY,MAAM;AAE9E,aAAO,KAAK,MAAM;AAClB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClCjB;AAAA;AAQA,aAAS,UAAU,QAAQ,OAAO;AAChC,UAAI,QAAQ,IACR,SAAS,OAAO;AAEpB,gBAAU,QAAQ,MAAM,MAAM;AAC9B,aAAO,EAAE,QAAQ,QAAQ;AACvB,cAAM,KAAK,IAAI,OAAO,KAAK;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,aAAa;AAUjB,aAAS,YAAY,QAAQ,QAAQ;AACnC,aAAO,WAAW,QAAQ,WAAW,MAAM,GAAG,MAAM;AAAA,IACtD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,UAAU;AAGd,QAAI,eAAe,QAAQ,OAAO,gBAAgB,MAAM;AAExD,WAAO,UAAU;AAAA;AAAA;;;ACLjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,eAAe;AADnB,QAEI,aAAa;AAFjB,QAGI,YAAY;AAGhB,QAAI,mBAAmB,OAAO;AAS9B,QAAI,eAAe,CAAC,mBAAmB,YAAY,SAAS,QAAQ;AAClE,UAAI,SAAS,CAAC;AACd,aAAO,QAAQ;AACb,kBAAU,QAAQ,WAAW,MAAM,CAAC;AACpC,iBAAS,aAAa,MAAM;AAAA,MAC9B;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,eAAe;AAUnB,aAAS,cAAc,QAAQ,QAAQ;AACrC,aAAO,WAAW,QAAQ,aAAa,MAAM,GAAG,MAAM;AAAA,IACxD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,eAAe;AADnB,QAEI,SAAS;AAUb,aAAS,aAAa,QAAQ;AAC5B,aAAO,eAAe,QAAQ,QAAQ,YAAY;AAAA,IACpD;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AACA,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AASjC,aAAS,eAAe,OAAO;AAC7B,UAAI,SAAS,MAAM,QACf,SAAS,IAAI,MAAM,YAAY,MAAM;AAGzC,UAAI,UAAU,OAAO,MAAM,CAAC,KAAK,YAAY,eAAe,KAAK,OAAO,OAAO,GAAG;AAChF,eAAO,QAAQ,MAAM;AACrB,eAAO,QAAQ,MAAM;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzBjB;AAAA;AAAA,QAAIC,cAAa;AASjB,aAAS,iBAAiB,aAAa;AACrC,UAAI,SAAS,IAAI,YAAY,YAAY,YAAY,UAAU;AAC/D,UAAIA,YAAW,MAAM,EAAE,IAAI,IAAIA,YAAW,WAAW,CAAC;AACtD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,mBAAmB;AAUvB,aAAS,cAAc,UAAU,QAAQ;AACvC,UAAI,SAAS,SAAS,iBAAiB,SAAS,MAAM,IAAI,SAAS;AACnE,aAAO,IAAI,SAAS,YAAY,QAAQ,SAAS,YAAY,SAAS,UAAU;AAAA,IAClF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AACA,QAAI,UAAU;AASd,aAAS,YAAY,QAAQ;AAC3B,UAAI,SAAS,IAAI,OAAO,YAAY,OAAO,QAAQ,QAAQ,KAAK,MAAM,CAAC;AACvE,aAAO,YAAY,OAAO;AAC1B,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChBjB;AAAA;AAAA,QAAIC,UAAS;AAGb,QAAI,cAAcA,UAASA,QAAO,YAAY;AAA9C,QACI,gBAAgB,cAAc,YAAY,UAAU;AASxD,aAAS,YAAY,QAAQ;AAC3B,aAAO,gBAAgB,OAAO,cAAc,KAAK,MAAM,CAAC,IAAI,CAAC;AAAA,IAC/D;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,mBAAmB;AAUvB,aAAS,gBAAgB,YAAY,QAAQ;AAC3C,UAAI,SAAS,SAAS,iBAAiB,WAAW,MAAM,IAAI,WAAW;AACvE,aAAO,IAAI,WAAW,YAAY,QAAQ,WAAW,YAAY,WAAW,MAAM;AAAA,IACpF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACfjB;AAAA;AAAA,QAAI,mBAAmB;AAAvB,QACI,gBAAgB;AADpB,QAEI,cAAc;AAFlB,QAGI,cAAc;AAHlB,QAII,kBAAkB;AAGtB,QAAI,UAAU;AAAd,QACI,UAAU;AADd,QAEI,SAAS;AAFb,QAGI,YAAY;AAHhB,QAII,YAAY;AAJhB,QAKI,SAAS;AALb,QAMI,YAAY;AANhB,QAOI,YAAY;AAEhB,QAAI,iBAAiB;AAArB,QACI,cAAc;AADlB,QAEI,aAAa;AAFjB,QAGI,aAAa;AAHjB,QAII,UAAU;AAJd,QAKI,WAAW;AALf,QAMI,WAAW;AANf,QAOI,WAAW;AAPf,QAQI,kBAAkB;AARtB,QASI,YAAY;AAThB,QAUI,YAAY;AAchB,aAAS,eAAe,QAAQ,KAAK,QAAQ;AAC3C,UAAI,OAAO,OAAO;AAClB,cAAQ,KAAK;AAAA,QACX,KAAK;AACH,iBAAO,iBAAiB,MAAM;AAAA,QAEhC,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI,KAAK,CAAC,MAAM;AAAA,QAEzB,KAAK;AACH,iBAAO,cAAc,QAAQ,MAAM;AAAA,QAErC,KAAK;AAAA,QAAY,KAAK;AAAA,QACtB,KAAK;AAAA,QAAS,KAAK;AAAA,QAAU,KAAK;AAAA,QAClC,KAAK;AAAA,QAAU,KAAK;AAAA,QAAiB,KAAK;AAAA,QAAW,KAAK;AACxD,iBAAO,gBAAgB,QAAQ,MAAM;AAAA,QAEvC,KAAK;AACH,iBAAO,IAAI;AAAA,QAEb,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI,KAAK,MAAM;AAAA,QAExB,KAAK;AACH,iBAAO,YAAY,MAAM;AAAA,QAE3B,KAAK;AACH,iBAAO,IAAI;AAAA,QAEb,KAAK;AACH,iBAAO,YAAY,MAAM;AAAA,MAC7B;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5EjB;AAAA;AAAA,QAAI,WAAW;AAGf,QAAI,eAAe,OAAO;AAU1B,QAAI,aAAc,WAAW;AAC3B,eAAS,SAAS;AAAA,MAAC;AACnB,aAAO,SAAS,OAAO;AACrB,YAAI,CAAC,SAAS,KAAK,GAAG;AACpB,iBAAO,CAAC;AAAA,QACV;AACA,YAAI,cAAc;AAChB,iBAAO,aAAa,KAAK;AAAA,QAC3B;AACA,eAAO,YAAY;AACnB,YAAI,SAAS,IAAI;AACjB,eAAO,YAAY;AACnB,eAAO;AAAA,MACT;AAAA,IACF,EAAE;AAEF,WAAO,UAAU;AAAA;AAAA;;;AC7BjB;AAAA;AAAA,QAAI,aAAa;AAAjB,QACI,eAAe;AADnB,QAEI,cAAc;AASlB,aAAS,gBAAgB,QAAQ;AAC/B,aAAQ,OAAO,OAAO,eAAe,cAAc,CAAC,YAAY,MAAM,IAClE,WAAW,aAAa,MAAM,CAAC,IAC/B,CAAC;AAAA,IACP;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,SAAS;AAAb,QACI,eAAe;AAGnB,QAAI,SAAS;AASb,aAAS,UAAU,OAAO;AACxB,aAAO,aAAa,KAAK,KAAK,OAAO,KAAK,KAAK;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,YAAY;AADhB,QAEI,WAAW;AAGf,QAAI,YAAY,YAAY,SAAS;AAmBrC,QAAI,QAAQ,YAAY,UAAU,SAAS,IAAI;AAE/C,WAAO,UAAU;AAAA;AAAA;;;AC1BjB;AAAA;AAAA,QAAI,SAAS;AAAb,QACI,eAAe;AAGnB,QAAI,SAAS;AASb,aAAS,UAAU,OAAO;AACxB,aAAO,aAAa,KAAK,KAAK,OAAO,KAAK,KAAK;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,YAAY;AADhB,QAEI,WAAW;AAGf,QAAI,YAAY,YAAY,SAAS;AAmBrC,QAAI,QAAQ,YAAY,UAAU,SAAS,IAAI;AAE/C,WAAO,UAAU;AAAA;AAAA;;;AC1BjB;AAAA;AAAA,QAAI,QAAQ;AAAZ,QACI,YAAY;AADhB,QAEI,cAAc;AAFlB,QAGI,aAAa;AAHjB,QAII,eAAe;AAJnB,QAKI,cAAc;AALlB,QAMI,YAAY;AANhB,QAOI,cAAc;AAPlB,QAQI,gBAAgB;AARpB,QASI,aAAa;AATjB,QAUI,eAAe;AAVnB,QAWI,SAAS;AAXb,QAYI,iBAAiB;AAZrB,QAaI,iBAAiB;AAbrB,QAcI,kBAAkB;AAdtB,QAeI,UAAU;AAfd,QAgBI,WAAW;AAhBf,QAiBI,QAAQ;AAjBZ,QAkBI,WAAW;AAlBf,QAmBI,QAAQ;AAnBZ,QAoBI,OAAO;AApBX,QAqBI,SAAS;AAGb,QAAI,kBAAkB;AAAtB,QACI,kBAAkB;AADtB,QAEI,qBAAqB;AAGzB,QAAI,UAAU;AAAd,QACI,WAAW;AADf,QAEI,UAAU;AAFd,QAGI,UAAU;AAHd,QAII,WAAW;AAJf,QAKI,UAAU;AALd,QAMI,SAAS;AANb,QAOI,SAAS;AAPb,QAQI,YAAY;AARhB,QASI,YAAY;AAThB,QAUI,YAAY;AAVhB,QAWI,SAAS;AAXb,QAYI,YAAY;AAZhB,QAaI,YAAY;AAbhB,QAcI,aAAa;AAEjB,QAAI,iBAAiB;AAArB,QACI,cAAc;AADlB,QAEI,aAAa;AAFjB,QAGI,aAAa;AAHjB,QAII,UAAU;AAJd,QAKI,WAAW;AALf,QAMI,WAAW;AANf,QAOI,WAAW;AAPf,QAQI,kBAAkB;AARtB,QASI,YAAY;AAThB,QAUI,YAAY;AAGhB,QAAI,gBAAgB,CAAC;AACrB,kBAAc,OAAO,IAAI,cAAc,QAAQ,IAC/C,cAAc,cAAc,IAAI,cAAc,WAAW,IACzD,cAAc,OAAO,IAAI,cAAc,OAAO,IAC9C,cAAc,UAAU,IAAI,cAAc,UAAU,IACpD,cAAc,OAAO,IAAI,cAAc,QAAQ,IAC/C,cAAc,QAAQ,IAAI,cAAc,MAAM,IAC9C,cAAc,SAAS,IAAI,cAAc,SAAS,IAClD,cAAc,SAAS,IAAI,cAAc,MAAM,IAC/C,cAAc,SAAS,IAAI,cAAc,SAAS,IAClD,cAAc,QAAQ,IAAI,cAAc,eAAe,IACvD,cAAc,SAAS,IAAI,cAAc,SAAS,IAAI;AACtD,kBAAc,QAAQ,IAAI,cAAc,OAAO,IAC/C,cAAc,UAAU,IAAI;AAkB5B,aAAS,UAAU,OAAO,SAAS,YAAY,KAAK,QAAQ,OAAO;AACjE,UAAI,QACA,SAAS,UAAU,iBACnB,SAAS,UAAU,iBACnB,SAAS,UAAU;AAEvB,UAAI,YAAY;AACd,iBAAS,SAAS,WAAW,OAAO,KAAK,QAAQ,KAAK,IAAI,WAAW,KAAK;AAAA,MAC5E;AACA,UAAI,WAAW,QAAW;AACxB,eAAO;AAAA,MACT;AACA,UAAI,CAAC,SAAS,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,QAAQ,KAAK;AACzB,UAAI,OAAO;AACT,iBAAS,eAAe,KAAK;AAC7B,YAAI,CAAC,QAAQ;AACX,iBAAO,UAAU,OAAO,MAAM;AAAA,QAChC;AAAA,MACF,OAAO;AACL,YAAI,MAAM,OAAO,KAAK,GAClB,SAAS,OAAO,WAAW,OAAO;AAEtC,YAAI,SAAS,KAAK,GAAG;AACnB,iBAAO,YAAY,OAAO,MAAM;AAAA,QAClC;AACA,YAAI,OAAO,aAAa,OAAO,WAAY,UAAU,CAAC,QAAS;AAC7D,mBAAU,UAAU,SAAU,CAAC,IAAI,gBAAgB,KAAK;AACxD,cAAI,CAAC,QAAQ;AACX,mBAAO,SACH,cAAc,OAAO,aAAa,QAAQ,KAAK,CAAC,IAChD,YAAY,OAAO,WAAW,QAAQ,KAAK,CAAC;AAAA,UAClD;AAAA,QACF,OAAO;AACL,cAAI,CAAC,cAAc,GAAG,GAAG;AACvB,mBAAO,SAAS,QAAQ,CAAC;AAAA,UAC3B;AACA,mBAAS,eAAe,OAAO,KAAK,MAAM;AAAA,QAC5C;AAAA,MACF;AAEA,gBAAU,QAAQ,IAAI;AACtB,UAAI,UAAU,MAAM,IAAI,KAAK;AAC7B,UAAI,SAAS;AACX,eAAO;AAAA,MACT;AACA,YAAM,IAAI,OAAO,MAAM;AAEvB,UAAI,MAAM,KAAK,GAAG;AAChB,cAAM,QAAQ,SAAS,UAAU;AAC/B,iBAAO,IAAI,UAAU,UAAU,SAAS,YAAY,UAAU,OAAO,KAAK,CAAC;AAAA,QAC7E,CAAC;AAAA,MACH,WAAW,MAAM,KAAK,GAAG;AACvB,cAAM,QAAQ,SAAS,UAAUC,MAAK;AACpC,iBAAO,IAAIA,MAAK,UAAU,UAAU,SAAS,YAAYA,MAAK,OAAO,KAAK,CAAC;AAAA,QAC7E,CAAC;AAAA,MACH;AAEA,UAAI,WAAW,SACV,SAAS,eAAe,aACxB,SAAS,SAAS;AAEvB,UAAI,QAAQ,QAAQ,SAAY,SAAS,KAAK;AAC9C,gBAAU,SAAS,OAAO,SAAS,UAAUA,MAAK;AAChD,YAAI,OAAO;AACT,UAAAA,OAAM;AACN,qBAAW,MAAMA,IAAG;AAAA,QACtB;AAEA,oBAAY,QAAQA,MAAK,UAAU,UAAU,SAAS,YAAYA,MAAK,OAAO,KAAK,CAAC;AAAA,MACtF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrKjB;AAAA;AAAA,QAAI,YAAY;AAGhB,QAAI,kBAAkB;AAAtB,QACI,qBAAqB;AAoBzB,aAAS,UAAU,OAAO;AACxB,aAAO,UAAU,OAAO,kBAAkB,kBAAkB;AAAA,IAC9D;AAEA,WAAO,UAAU;AAAA;AAAA;;;SCgBDC,EAAIC,IAAAA;AAAAA,WAAAA,KAAAA,UAAAA,QAA+BC,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,IAAAA,GAAAA,IAAAA,IAAAA;AAAAA,IAAAA,GAAAA,IAAAA,CAAAA,IAAAA,UAAAA,CAAAA;AAAAA,MAAAA,MACrC;AAAA,QACNC,KAAIC,EAAOH,EAAAA,GACXI,KAAOF,KAEG,cAAA,OAANA,KACPA,GAAEG,MAAM,MAAMJ,EAAAA,IACdC,KAHA,uBAAuBF;AAAAA,UAIhBM,MAAAA,aAAiBF,EAAAA;EAAAA;AAAAA,QAElBE,MAAAA,gCACqBN,MAC7BC,GAAKM,SAAS,MAAMN,GAAKO,IAAI,SAAAC,IAAAA;AAAAA,WAAAA,MAASA,KAAAA;EAAAA,CAAAA,EAAMC,KAAK,GAAA,IAAO,MAAA,kDAAA;AAAA;AAAA,SCvC3CC,EAAQC,IAAAA;AAAAA,SAAAA,CAAAA,CACdA,MAAAA,CAAAA,CAAWA,GAAMC,CAAAA;AAAAA;AAAAA,SAKXC,EAAYF,IAAAA;AAAAA,MAAAA;AAAAA,SAAAA,CAAAA,CACtBA,OAAAA,SAawBA,IAAAA;AAAAA,QAAAA,CACxBA,MAA0B,YAAA,OAAVA;AAAoB,aAAA;AAAO,QAC1CG,KAAQC,OAAOC,eAAeL,EAAAA;AAAAA,QACtB,SAAVG;AAAAA,aAAAA;AACI,QAEFG,KACLF,OAAOG,eAAeC,KAAKL,IAAO,aAAA,KAAkBA,GAAMM;AAAAA,WAEvDH,OAASF,UAGG,cAAA,OAARE,MACPI,SAASC,SAASH,KAAKF,EAAAA,MAAUM;EAAAA,EAxBnBZ,EAAAA,KACda,MAAMC,QAAQd,EAAAA,KAAAA,CAAAA,CACZA,GAAMe,CAAAA,KAAAA,CAAAA,EAAAA,UAAAA,KACNf,GAAMS,gBAAAA,WAAAA,KAAAA,SAANO,GAAoBD,CAAAA,MACtBE,EAAMjB,EAAAA,KACNkB,EAAMlB,EAAAA;AAAAA;AA0DR,SAAgBmB,EAAKC,IAAUC,IAAWC,IAAAA;AAAAA,aAAAA,OAAAA,KAAAA,QAAiB,MACtDC,EAAYH,EAAAA,KACbE,KAAiBE,OAAOC,OAAOC,IAASN,EAAAA,EAAKO,QAAQ,SAAAC,GAAAA;AACjDN,IAAAA,MAAiC,YAAA,OAARM,KAAkBP,GAAKO,GAAKR,GAAIQ,CAAAA,GAAMR,EAAAA;EAAAA,CAAAA,IAGrEA,GAAIO,QAAQ,SAACE,IAAYC,GAAAA;AAAAA,WAAeT,GAAKS,GAAOD,IAAOT,EAAAA;EAAAA,CAAAA;AAAAA;AAAAA,SAK7CG,EAAYQ,IAAAA;AAAAA,MAErBC,KAAgCD,GAAME,CAAAA;AAAAA,SACrCD,KACJA,GAAME,IAAQ,IACbF,GAAME,IAAQ,IACbF,GAAME,IACRC,MAAMC,QAAQL,EAAAA,IAAAA,IAEdM,EAAMN,EAAAA,IAAAA,IAENO,EAAMP,EAAAA,IAAAA,IAAAA;AAAAA;AAAAA,SAMMQ,EAAIR,IAAYS,IAAAA;AAAAA,SAAAA,MACxBjB,EAAYQ,EAAAA,IAChBA,GAAMQ,IAAIC,EAAAA,IACVhB,OAAOiB,UAAUC,eAAeC,KAAKZ,IAAOS,EAAAA;AAAAA;AAAAA,SAIhCI,EAAIb,IAA2BS,IAAAA;AAAAA,SAAAA,MAEvCjB,EAAYQ,EAAAA,IAA0BA,GAAMa,IAAIJ,EAAAA,IAAQT,GAAMS,EAAAA;AAAAA;AAItE,SAAgBK,EAAId,IAAYe,IAA6BC,IAAAA;AAAAA,MACtDC,IAAIzB,EAAYQ,EAAAA;AAAAA,QAClBiB,IAAoBjB,GAAMc,IAAIC,IAAgBC,EAAAA,IAAAA,MACzCC,IACRjB,GAAMkB,IAAIF,EAAAA,IACJhB,GAAMe,EAAAA,IAAkBC;AAAAA;AAAAA,SAIhBG,EAAGC,IAAQC,IAAAA;AAAAA,SAEtBD,OAAMC,KACI,MAAND,MAAW,IAAIA,MAAM,IAAIC,KAEzBD,MAAMA,MAAKC,MAAMA;AAAAA;AAAAA,SAKVf,EAAMgB,IAAAA;AAAAA,SACdC,KAAUD,cAAkBE;AAAAA;AAAAA,SAIpBjB,EAAMe,IAAAA;AAAAA,SACdG,KAAUH,cAAkBI;AAAAA;AAAAA,SAGpBC,EAAO1B,IAAAA;AAAAA,SACfA,GAAM2B,KAAS3B,GAAM4B;AAAAA;AAAAA,SAIbC,EAAYC,IAAAA;AAAAA,MACvB3B,MAAMC,QAAQ0B,EAAAA;AAAO,WAAO3B,MAAMM,UAAUsB,MAAMpB,KAAKmB,EAAAA;AAAAA,MACrDE,KAAcC,GAA0BH,EAAAA;AAAAA,SACvCE,GAAY/B,CAAAA;AAAAA,WACfR,KAAOC,GAAQsC,EAAAA,GACVE,IAAI,GAAGA,IAAIzC,GAAK0C,QAAQD,KAAK;AAAA,QAC/BtC,KAAWH,GAAKyC,CAAAA,GAChBE,KAAOJ,GAAYpC,EAAAA;AAAAA,cACrBwC,GAAKC,aACRD,GAAKC,WAAAA,MACLD,GAAKE,eAAAA,QAKFF,GAAKxB,OAAOwB,GAAKvB,SACpBmB,GAAYpC,EAAAA,IAAO,EAClB0C,cAAAA,MACAD,UAAAA,MACAE,YAAYH,GAAKG,YACjBxB,OAAOe,GAAKlC,EAAAA,EAAAA;EAAAA;AAAAA,SAGRJ,OAAOgD,OAAOhD,OAAOiD,eAAeX,EAAAA,GAAOE,EAAAA;AAAAA;AAAAA,SAWnCU,EAAUtD,IAAUuD,GAAAA;AAAAA,SAAAA,WAAAA,MAAAA,IAAAA,QAC/BC,EAASxD,EAAAA,KAAQyD,EAAQzD,EAAAA,KAAAA,CAAS0D,EAAY1D,EAAAA,MAC9CG,EAAYH,EAAAA,IAAO,MACtBA,GAAIyB,MAAMzB,GAAI6B,MAAM7B,GAAI2D,QAAQ3D,GAAI4D,SAASC,IAE9CzD,OAAOkD,OAAOtD,EAAAA,GACVuD,KAAMxD,EAAKC,IAAK,SAACQ,IAAKmB,IAAAA;AAAAA,WAAU2B,EAAO3B,IAAAA,IAAO;EAAA,GAAA,IAAO,IALM3B;AAAAA;AAShE,SAAS6D,IAAAA;AACRC,IAAI,CAAA;AAAA;AAAA,SAGWN,EAASxD,IAAAA;AAAAA,SACb,QAAPA,MAA8B,YAAA,OAARA,MAEnBI,OAAOoD,SAASxD,EAAAA;AAAAA;AAAAA,SCxKR+D,EACfC,IAAAA;AAAAA,MAEMC,KAASC,GAAQF,EAAAA;AAAAA,SAClBC,MACJH,EAAI,IAAIE,EAAAA,GAGFC;AAAAA;AAAAA,SAGQE,EACfH,IACAI,IAAAA;AAEKF,KAAQF,EAAAA,MAAYE,GAAQF,EAAAA,IAAaI;AAAAA;AClC/C,SAAgBC,IAAAA;AAAAA,SACCC,KAAcR,EAAI,CAAA,GAC3BQ;AAAAA;AAAAA,SAkBQC,EACfC,IACAC,IAAAA;AAEIA,EAAAA,OACHV,EAAU,SAAA,GACVS,GAAME,IAAW,CAAA,GACjBF,GAAMG,IAAkB,CAAA,GACxBH,GAAMI,IAAiBH;AAAAA;AAAAA,SAITI,EAAYL,IAAAA;AAC3BM,IAAWN,EAAAA,GACXA,GAAMO,EAAQxE,QAAQyE,CAAAA,GAEtBR,GAAMO,IAAU;AAAA;AAAA,SAGDD,EAAWN,IAAAA;AACtBA,EAAAA,OAAUF,MACbA,IAAeE,GAAMS;AAAAA;AAAAA,SAIPC,EAAWC,IAAAA;AAAAA,SAClBb,IArCD,EACNS,GAAS,CAAA,GACTE,GAmCkCX,GAlClCc,GAkCgDD,IA/BhDE,GAAAA,MACAC,GAAoB,EAAA;AAAA;AAiCtB,SAASN,EAAYO,IAAAA;AAAAA,MACd3E,KAAoB2E,GAAM1E,CAAAA;AAAAA,QAE/BD,GAAME,KAAAA,MACNF,GAAME,IAENF,GAAM4E,EAAAA,IACF5E,GAAM6E,IAAAA;AAAW;AAAA,SC9DPC,EAAcC,IAAanB,GAAAA;AAC1CA,IAAMc,IAAqBd,EAAMO,EAAQhC;AAAAA,MACnC6C,KAAYpB,EAAMO,EAAS,CAAA,GAC3Bc,KAAAA,WAAaF,MAAwBA,OAAWC;AAAAA,SACjDpB,EAAMY,EAAOU,KACjB/B,EAAU,KAAA,EAAOgC,EAAiBvB,GAAOmB,IAAQE,EAAAA,GAC9CA,MACCD,GAAU/E,CAAAA,EAAamF,MAC1BnB,EAAYL,CAAAA,GACZV,EAAI,CAAA,IAEDJ,EAAYiC,EAAAA,MAEfA,KAASM,EAASzB,GAAOmB,EAAAA,GACpBnB,EAAMS,KAASiB,EAAY1B,GAAOmB,EAAAA,IAEpCnB,EAAME,KACTX,EAAU,SAAA,EAAWoC,EACpBP,GAAU/E,CAAAA,EAAa2B,GACvBmD,IACAnB,EAAME,GACNF,EAAMG,CAAAA,KAKRgB,KAASM,EAASzB,GAAOoB,IAAW,CAAA,CAAA,GAErCf,EAAYL,CAAAA,GACRA,EAAME,KACTF,EAAMI,EAAgBJ,EAAME,GAAUF,EAAMG,CAAAA,GAEtCgB,OAAWS,IAAUT,KAAAA;AAASU;AAGtC,SAASJ,EAASK,IAAuB3E,IAAY4E,IAAAA;AAAAA,MAEhD/C,EAAS7B,EAAAA;AAAQ,WAAOA;AAAAA,MAEtBf,IAAoBe,GAAMd,CAAAA;AAAAA,MAAAA,CAE3BD;AAAAA,WACJb,EACC4B,IACA,SAACnB,IAAKgG,IAAAA;AAAAA,aACLC,EAAiBH,IAAW1F,GAAOe,IAAOnB,IAAKgG,IAAYD,EAAAA;IAAAA,GAAAA,IAC5D,GAEM5E;AAAAA,MAGJf,EAAM8F,MAAWJ;AAAW,WAAO3E;AAAAA,MAAAA,CAElCf,EAAMoF;AAAAA,WACVE,EAAYI,IAAW1F,EAAM4B,GAAAA,IAAO,GAC7B5B,EAAM4B;AAAAA,MAAAA,CAGT5B,EAAM+F,GAAY;AACtB/F,MAAM+F,IAAAA,MACN/F,EAAM8F,EAAOpB;AAAAA,QACPK,KAAAA,MAEL/E,EAAME,KAAAA,MAAiCF,EAAME,IACzCF,EAAM2B,IAAQE,EAAY7B,EAAMgG,CAAAA,IACjChG,EAAM2B,GAKNsE,KAAalB,IACbzE,KAAAA;AAAQ,UACRN,EAAME,MACT+F,KAAa,IAAIxE,IAAIsD,EAAAA,GACrBA,GAAOhC,MAAAA,GACPzC,KAAAA,OAEDnB,EAAK8G,IAAY,SAACrG,IAAKgG,IAAAA;AAAAA,aACtBC,EAAiBH,IAAW1F,GAAO+E,IAAQnF,IAAKgG,IAAYD,IAAMrF,EAAAA;IAAAA,CAAAA,GAGnEgF,EAAYI,IAAWX,IAAAA,KAAQ,GAE3BY,MAAQD,GAAU5B,KACrBX,EAAU,SAAA,EAAW+C,EACpBlG,GACA2F,IACAD,GAAU5B,GACV4B,GAAU3B,CAAAA;EAAAA;AAAAA,SAIN/D,EAAM2B;AAAAA;AAGd,SAASkE,EACRH,GACAS,IACAC,IACA5F,IACAoF,IACAS,IACAC,IAAAA;AAAAA,MAEeV,OAAeQ,MAAclD,EAAI,CAAA,GAC5CL,EAAQ+C,EAAAA,GAAa;AAAA,QASlBW,KAAMlB,EAASK,GAAWE,IAP/BS,MACAF,MAAAA,MACAA,GAAajG,KAAAA,CACZK,EAAK4F,GAA8CK,GAAYhG,EAAAA,IAC7D6F,GAAUI,OAAOjG,EAAAA,IAAAA,MACjBiF;AAAAA,QAGJ5E,EAAIuF,IAAc5F,IAAM+F,EAAAA,GAAAA,CAGpB1D,EAAQ0D,EAAAA;AAEL;AADNb,MAAUjB,IAAAA;EAAiB;AAElB6B,IAAAA,MACVF,GAAanF,IAAI2E,EAAAA;AAAAA,MAGd9C,EAAY8C,EAAAA,KAAAA,CAAgBhD,EAASgD,EAAAA,GAAa;AAAA,QAAA,CAChDF,EAAUlB,EAAOkC,KAAehB,EAAUhB,IAAqB;AAAA;AAQpEW,MAASK,GAAWE,EAAAA,GAEfO,MAAgBA,GAAYL,EAAOzB,KACvCiB,EAAYI,GAAWE,EAAAA;EAAAA;AAAAA;AAI1B,SAASN,EAAY1B,IAAmB7C,IAAY4B,IAAAA;AAAAA,aAAAA,OAAAA,KAAAA,QAAO,CAErDiB,GAAMS,KAAWT,GAAMY,EAAOkC,KAAe9C,GAAMa,KACvD/B,EAAO3B,IAAO4B,EAAAA;AAAAA;ACqEhB,SAASgE,EAAKhC,IAAgBnE,IAAAA;AAAAA,MACvBR,KAAQ2E,GAAM1E,CAAAA;AAAAA,UACLD,KAAQ0B,EAAO1B,EAAAA,IAAS2E,IACzBnE,EAAAA;AAAAA;AAcf,SAASoG,EACRC,IACArG,IAAAA;AAAAA,MAGMA,MAAQqG;AAAAA,aACVC,KAAQtH,OAAOiD,eAAeoE,EAAAA,GAC3BC,MAAO;AAAA,UACP1E,IAAO5C,OAAOuH,yBAAyBD,IAAOtG,EAAAA;AAAAA,UAChD4B;AAAM,eAAOA;AACjB0E,MAAAA,KAAQtH,OAAOiD,eAAeqE,EAAAA;IAAAA;AAAAA;AAAAA,SAKhBE,EAAYhH,IAAAA;AACtBA,EAAAA,GAAMoF,MACVpF,GAAMoF,IAAAA,MACFpF,GAAMqE,KACT2C,EAAYhH,GAAMqE,CAAAA;AAAAA;AAAAA,SAKL4C,EAAYjH,IAAAA;AACtBA,EAAAA,GAAM2B,MACV3B,GAAM2B,IAAQE,EAAY7B,GAAM4B,CAAAA;AAAAA;ACtDlC,SAAgBsF,EACf3C,IACAxD,IACAoG,IAAAA;AAAAA,MAGMxC,IAAiBtE,EAAMU,EAAAA,IAC1BoC,EAAU,QAAA,EAAUiE,EAAUrG,IAAOoG,EAAAA,IACrC7G,EAAMS,EAAAA,IACNoC,EAAU,QAAA,EAAUkE,EAAUtG,IAAOoG,EAAAA,IACrC5C,GAAMW,IAAAA,SDvLTpD,IACAqF,IAAAA;AAAAA,QAEM/G,KAAUD,MAAMC,QAAQ0B,EAAAA,GACxB9B,KAAoB,EACzBE,GAAOE,KAAAA,IAAkC,GAEzC0F,GAAQqB,KAASA,GAAOrB,IAASrC,EAAAA,GAEjC2B,GAAAA,OAEAW,GAAAA,OAEAS,GAAW,CAAA,GAEXnC,GAAS8C,IAETvF,GAAOE,IAEPkE,GAAQ,MAERrE,GAAO,MAEPiD,GAAS,MACT0C,GAAAA,MAAW,GASRjG,KAAYrB,IACZuH,KAA2CC;AAC3CpH,IAAAA,OACHiB,KAAS,CAACrB,EAAAA,GACVuH,KAAQE;AAAAA,QAAAA,KAGeC,MAAMC,UAAUtG,IAAQkG,EAAAA,GAAzCK,KAAAA,GAAAA,QAAQC,KAAAA,GAAAA;AAAAA,WACf7H,GAAMgG,IAAS6B,IACf7H,GAAM4E,IAAUgD,IACTC;EAAAA,EC6Ia9G,IAAOoG,EAAAA,IACxBhE,EAAU,KAAA,EAAO2E,EAAgB/G,IAAOoG,EAAAA;AAAAA,UAE7BA,KAASA,GAAOrB,IAASrC,EAAAA,GACjCU,EAAQ4D,KAAKpD,CAAAA,GACZA;AAAAA;AAAAA,SC9NQqD,EAAQjH,GAAAA;AAAAA,SAClB8B,EAAQ9B,CAAAA,KAAQmC,EAAI,IAAInC,CAAAA,GAI9B,SAASkH,GAAYlH,IAAAA;AAAAA,QAAAA,CACf+B,EAAY/B,EAAAA;AAAQ,aAAOA;AAAAA,QAE5BmH,IADElI,KAAgCe,GAAMd,CAAAA,GAEtCkI,KAAW5I,EAAYwB,EAAAA;AAAAA,QACzBf,IAAO;AAAA,UAAA,CAERA,GAAMoF,MACNpF,GAAME,IAAQ,KAAA,CAAMiD,EAAU,KAAA,EAAOiF,EAAYpI,EAAAA;AAElD,eAAOA,GAAM4B;AAEd5B,MAAAA,GAAM+F,IAAAA,MACNmC,KAAOG,EAAWtH,IAAOoH,EAAAA,GACzBnI,GAAM+F,IAAAA;IAAa;AAEnBmC,MAAAA,KAAOG,EAAWtH,IAAOoH,EAAAA;AAAAA,WAG1BhJ,EAAK+I,IAAM,SAACtI,IAAKgG,IAAAA;AACZ5F,MAAAA,MAASY,EAAIZ,GAAM4B,GAAOhC,EAAAA,MAASgG,MACvC/E,EAAIqH,IAAMtI,IAAKqI,GAAYrC,EAAAA,CAAAA;IAAAA,CAAAA,GAAAA,MAGrBuC,KAA4B,IAAI1G,IAAIyG,EAAAA,IAAQA;EAAAA,EA3BhCnH,CAAAA;AAAAA;AA8BpB,SAASsH,EAAWtH,IAAYoH,IAAAA;AAAAA,UAEvBA,IAAAA;IAAAA,KAAAA;AAAAA,aAEC,IAAI5G,IAAIR,EAAAA;IAAAA,KAAAA;AAAAA,aAGRZ,MAAMmI,KAAKvH,EAAAA;EAAAA;AAAAA,SAEbc,EAAYd,EAAAA;AAAAA;AC8MCwH,SC9OLC,IAAAA;AAAAA,WA6PNC,EAAoBC,IAAAA;AAAAA,QAAAA,CACvBC,EAAYD,EAAAA;AAAM,aAAOA;AAAAA,QAC1BE,MAAMC,QAAQH,EAAAA;AAAM,aAAOA,GAAII,IAAIL,CAAAA;AAAAA,QACnCM,EAAML,EAAAA;AACT,aAAO,IAAIM,IACVJ,MAAMK,KAAKP,GAAIQ,QAAAA,CAAAA,EAAWJ,IAAI,SAAAK,IAAA;AAAA,eAAY,CAAAA,GAAA,CAAA,GAAIV,EAAAA,GAAAA,CAAAA,CAAAA,CAAAA;MAAAA,CAAAA,CAAAA;AAAAA,QAE5CW,EAAMV,EAAAA;AAAM,aAAO,IAAIW,IAAIT,MAAMK,KAAKP,EAAAA,EAAKI,IAAIL,CAAAA,CAAAA;AAAAA,QAC7Ca,KAASC,OAAOC,OAAOD,OAAOE,eAAef,EAAAA,CAAAA;AAAAA,aACxCgB,MAAOhB;AAAKY,MAAAA,GAAOI,EAAAA,IAAOjB,EAAoBC,GAAIgB,EAAAA,CAAAA;AAAAA,WACzDC,EAAIjB,IAAKkB,CAAAA,MAAYN,GAAOM,CAAAA,IAAalB,GAAIkB,CAAAA,IAC1CN;EAAAA;AAAAA,WAGCO,GAA2BnB,IAAAA;AAAAA,WAC/BoB,EAAQpB,EAAAA,IACJD,EAAoBC,EAAAA,IACdA;EAAAA;AAAAA,MA5QTqB,KAAM;AA+QZC,IAAW,WAAW,EACrBC,GAAAA,SAlGyBC,IAAUC,IAAAA;AAAAA,WACnCA,GAAQC,QAAQ,SAAAC,IAAAA;AAAAA,eACRC,KAAYD,GAAZC,MAAMC,KAAMF,GAANE,IAETC,KAAYN,IACPO,KAAI,GAAGA,KAAIH,GAAKI,SAAS,GAAGD,MAAK;AAAA,YACnCE,KAAaC,EAAYJ,EAAAA,GAC3BK,KAAIP,GAAKG,EAAAA;AACI,oBAAA,OAANI,MAA+B,YAAA,OAANA,OACnCA,KAAI,KAAKA,KAAAA,MAKRF,MAAAA,MAAkCA,MAC5B,gBAANE,MAA2B,kBAANA,MAEtBC,EAAI,EAAA,GACe,cAAA,OAATN,MAA6B,gBAANK,MAAmBC,EAAI,EAAA,GAErC,YAAA,QADpBN,KAAOO,EAAIP,IAAMK,EAAAA,MACaC,EAAI,IAAIR,GAAKU,KAAK,GAAA,CAAA;MAAA;AAAA,UAG3CC,KAAOL,EAAYJ,EAAAA,GACnBU,KAAQzC,EAAoB4B,GAAMa,KAAAA,GAClCxB,KAAMY,GAAKA,GAAKI,SAAS,CAAA;AAAA,cACvBH,IAAAA;QAAAA,KAzMM;AAAA,kBA2MJU,IAAAA;YAAAA,KAAAA;AAAAA,qBAECT,GAAKW,IAAIzB,IAAKwB,EAAAA;YAAAA,KAAAA;AAGrBJ,gBAAI,EAAA;YAAA;AAAA,qBAMIN,GAAKd,EAAAA,IAAOwB;UAAAA;QAAAA,KAElBnB;AAAAA,kBACIkB,IAAAA;YAAAA,KAAAA;AAAAA,qBAES,QAARvB,KACJc,GAAKY,KAAKF,EAAAA,IACVV,GAAKa,OAAO3B,IAAY,GAAGwB,EAAAA;YAAAA,KAAAA;AAAAA,qBAEvBV,GAAKW,IAAIzB,IAAKwB,EAAAA;YAAAA,KAAAA;AAAAA,qBAEdV,GAAKc,IAAIJ,EAAAA;YAAAA;AAAAA,qBAERV,GAAKd,EAAAA,IAAOwB;UAAAA;QAAAA,KAjOX;AAAA,kBAoOHD,IAAAA;YAAAA,KAAAA;AAAAA,qBAECT,GAAKa,OAAO3B,IAAY,CAAA;YAAA,KAAA;AAAA,qBAExBc,GAAKe,OAAO7B,EAAAA;YAAAA,KAAAA;AAAAA,qBAEZc,GAAKe,OAAOlB,GAAMa,KAAAA;YAAAA;AAAAA,qBAAAA,OAEXV,GAAKd,EAAAA;UAAAA;QAAAA;AAGrBoB,YAAI,IAAIP,EAAAA;MAAAA;IAAAA,CAAAA,GAIJL;EAAAA,GA6BPsB,GAAAA,SA7QAjD,IACAkD,IACAtB,IACAuB,IAAAA;AAAAA,YAEQnD,GAAMoD,GAAAA;MAAAA,KAAAA;MAAAA,KAAAA;MAAAA,KAAAA;AAAAA,eAAAA,SAgFdpD,IACAkD,IACAtB,IACAuB,IAAAA;AAAAA,cAEOE,KAAgBrD,GAAhBqD,GAAOC,KAAStD,GAATsD;AACdC,YAAKvD,GAAMwD,GAAY,SAACrC,IAAKsC,IAAAA;AAAAA,gBACtBC,KAAYlB,EAAIa,IAAOlC,EAAAA,GACvBwB,KAAQH,EAAIc,IAAQnC,EAAAA,GACpBa,KAAMyB,KAAyBrC,EAAIiC,IAAOlC,EAAAA,IAnGlC,YAmGmDK,KAjGpD;AAAA,gBAkGTkC,OAAcf,MApGJ,cAoGaX,IAAAA;AAAAA,kBACrBD,KAAOmB,GAASS,OAAOxC,EAAAA;AAC7BS,cAAAA,GAAQiB,KApGK,aAoGAb,KAAgB,EAACA,IAAAA,IAAID,MAAAA,GAAAA,IAAQ,EAACC,IAAAA,IAAID,MAAAA,IAAMY,OAAAA,GAAAA,CAAAA,GACrDQ,GAAeN,KACdb,OAAOR,KACJ,EAACQ,IAvGQ,UAuGID,MAAAA,GAAAA,IAvGJ,aAwGTC,KACA,EAACA,IAAIR,IAAKO,MAAAA,IAAMY,OAAOrB,GAAwBoC,EAAAA,EAAAA,IAC/C,EAAC1B,IA5GS,WA4GID,MAAAA,IAAMY,OAAOrB,GAAwBoC,EAAAA,EAAAA,CAAAA;YAAAA;UAAAA,CAAAA;QAAAA,EA7FrD1D,IACAkD,IACAtB,IACAuB,EAAAA;MAAAA,KAAAA;MAAAA,KAAAA;AAAAA,eAAAA,SAgBHnD,IACAkD,IACAtB,IACAuB,IAAAA;AAAAA,cAEKE,KAAoBrD,GAApBqD,GAAOG,KAAaxD,GAAbwD,GACRF,KAAQtD,GAAMsD;AAAAA,cAGdA,GAAMnB,SAASkB,GAAMlB,QAAQ;AAAA,gBAAAyB,KAEd,CAACN,IAAOD,EAAAA;AAAxBA,YAAAA,KAAAA,GAAAA,CAAAA,GAAOC,KAAAA,GAAAA,CAAAA;AAAAA,gBAAAA,KACoB,CAACH,IAAgBvB,EAAAA;AAA5CA,YAAAA,KAAAA,GAAAA,CAAAA,GAASuB,KAAAA,GAAAA,CAAAA;UAAAA;AAAAA,mBAIHjB,KAAI,GAAGA,KAAImB,GAAMlB,QAAQD;AAAAA,gBAC7BsB,GAAUtB,EAAAA,KAAMoB,GAAMpB,EAAAA,MAAOmB,GAAMnB,EAAAA,GAAI;AAAA,kBACpCH,KAAOmB,GAASS,OAAO,CAACzB,EAAAA,CAAAA;AAC9BN,cAAAA,GAAQiB,KAAK,EACZb,IAtDY,WAuDZD,MAAAA,IAGAY,OAAOrB,GAAwBgC,GAAMpB,EAAAA,CAAAA,EAAAA,CAAAA,GAEtCiB,GAAeN,KAAK,EACnBb,IA7DY,WA8DZD,MAAAA,IACAY,OAAOrB,GAAwB+B,GAAMnB,EAAAA,CAAAA,EAAAA,CAAAA;YAAAA;AAAAA,mBAM/BA,KAAImB,GAAMlB,QAAQD,KAAIoB,GAAMnB,QAAQD,MAAK;AAAA,gBAC3CH,KAAOmB,GAASS,OAAO,CAACzB,EAAAA,CAAAA;AAC9BN,YAAAA,GAAQiB,KAAK,EACZb,IAAIR,IACJO,MAAAA,IAGAY,OAAOrB,GAAwBgC,GAAMpB,EAAAA,CAAAA,EAAAA,CAAAA;UAAAA;AAGnCmB,UAAAA,GAAMlB,SAASmB,GAAMnB,UACxBgB,GAAeN,KAAK,EACnBb,IAjFa,WAkFbD,MAAMmB,GAASS,OAAO,CAAC,QAAA,CAAA,GACvBhB,OAAOU,GAAMlB,OAAAA,CAAAA;QAAAA,EA7DenC,IAAOkD,IAAUtB,IAASuB,EAAAA;MAAAA,KAAAA;AAAAA,eAAAA,SA4FxDnD,IACAkD,IACAtB,IACAuB,IAAAA;AAAAA,cAEKE,KAAgBrD,GAAhBqD,GAAOC,KAAStD,GAATsD,GAERpB,KAAI;AACRmB,UAAAA,GAAMxB,QAAQ,SAACc,IAAAA;AAAAA,gBAAAA,CACTW,GAAOlC,IAAIuB,EAAAA,GAAQ;AAAA,kBACjBZ,KAAOmB,GAASS,OAAO,CAACzB,EAAAA,CAAAA;AAC9BN,cAAAA,GAAQiB,KAAK,EACZb,IA5HW,UA6HXD,MAAAA,IACAY,OAAAA,GAAAA,CAAAA,GAEDQ,GAAeU,QAAQ,EACtB7B,IAAIR,IACJO,MAAAA,IACAY,OAAAA,GAAAA,CAAAA;YAAAA;AAGFT,YAAAA;UAAAA,CAAAA,GAEDA,KAAI,GACJoB,GAAOzB,QAAQ,SAACc,IAAAA;AAAAA,gBAAAA,CACVU,GAAMjC,IAAIuB,EAAAA,GAAQ;AAAA,kBAChBZ,KAAOmB,GAASS,OAAO,CAACzB,EAAAA,CAAAA;AAC9BN,cAAAA,GAAQiB,KAAK,EACZb,IAAIR,IACJO,MAAAA,IACAY,OAAAA,GAAAA,CAAAA,GAEDQ,GAAeU,QAAQ,EACtB7B,IAlJW,UAmJXD,MAAAA,IACAY,OAAAA,GAAAA,CAAAA;YAAAA;AAGFT,YAAAA;UAAAA,CAAAA;QAAAA,EAhIGlC,IACDkD,IACAtB,IACAuB,EAAAA;IAAAA;EAAAA,GAuPHW,GAAAA,SArHAC,IACAC,IACApC,IACAuB,IAAAA;AAEAvB,IAAAA,GAAQiB,KAAK,EACZb,IApKc,WAqKdD,MAAM,CAAA,GACNY,OAAOqB,OAAgBC,IAAAA,SAAsBD,GAAAA,CAAAA,GAE9Cb,GAAeN,KAAK,EACnBb,IAzKc,WA0KdD,MAAM,CAAA,GACNY,OAAOoB,GAAAA,CAAAA;EAAAA,EAAAA,CAAAA;AAAAA;ACrMV,SAmBgBG,IAAAA;AAAAA,WAgBNC,GAAUC,IAAQC,IAAAA;AAAAA,aAEjBC,KAAAA;AAAAA,WACHC,cAAcH;IAAAA;AAFpBI,IAAAA,GAAcJ,IAAGC,EAAAA,GAIjBD,GAAEK,aAECH,GAAGG,YAAYJ,GAAEI,WAAY,IAAIH;EAAAA;AAAAA,WA8J5BI,EAAe1E,IAAAA;AAClBA,IAAAA,GAAMsD,MACVtD,GAAMwD,IAAY,oBAAI/C,OACtBT,GAAMsD,IAAQ,IAAI7C,IAAIT,GAAMqD,CAAAA;EAAAA;AAAAA,WA0HrBsB,GAAe3E,IAAAA;AAClBA,IAAAA,GAAMsD,MAEVtD,GAAMsD,IAAQ,oBAAIxC,OAClBd,GAAMqD,EAAMxB,QAAQ,SAAAc,IAAAA;AAAAA,UACfvC,EAAYuC,EAAAA,GAAQ;AAAA,YACjBhB,KAAQiD,EAAY5E,GAAM6E,EAAOC,GAAQnC,IAAO3C,EAAAA;AACtDA,QAAAA,GAAM+E,EAAQnC,IAAID,IAAOhB,EAAAA,GACzB3B,GAAMsD,EAAOP,IAAIpB,EAAAA;MAAAA;AAEjB3B,QAAAA,GAAMsD,EAAOP,IAAIJ,EAAAA;IAAAA,CAAAA;EAAAA;AAAAA,WAMZqC,GAAgBhF,IAAAA;AACpBA,IAAAA,GAAMiF,KAAU1C,EAAI,GAAG2C,KAAKC,UAAUC,EAAOpF,EAAAA,CAAAA,CAAAA;EAAAA;AAAAA,MAjU9CwE,KAAgB,SAASJ,IAAQC,IAAAA;AAAAA,YACpCG,KACCxD,OAAOqE,kBACN,EAACC,WAAW,CAAA,EAAA,aAAejF,SAC3B,SAAS+D,IAAGC,IAAAA;AACXD,MAAAA,GAAEkB,YAAYjB;IAAAA,KAEhB,SAASD,IAAGC,IAAAA;AAAAA,eACF/B,MAAK+B;AAAOA,QAAAA,GAAEkB,eAAejD,EAAAA,MAAI8B,GAAE9B,EAAAA,IAAK+B,GAAE/B,EAAAA;IAAAA,GAEhC8B,IAAGC,EAAAA;EAAAA,GAcnBmB,KAAY,WAAA;AAAA,aAGRA,GAAoBC,IAAgBC,IAAAA;AAAAA,aAAAA,KACvCC,CAAAA,IAAe,EACnBvC,GAAAA,GACAwC,GAASF,IACTb,GAAQa,KAASA,GAAOb,IAASgB,EAAAA,GACjCC,GAAAA,OACAC,GAAAA,OACAzC,GAAAA,QACAE,GAAAA,QACAH,GAAOoC,IACPO,GAAQC,MACRC,GAAAA,OACAjB,GAAAA,MAAU,GAEJgB;IAAAA;AAhBR9B,IAAAA,GAAUqB,IAmJR/E,GAAAA;AAAAA,QAjII6B,KAAIkD,GAASf;AAAAA,WAEnBzD,OAAOmF,eAAe7D,IAAG,QAAQ,EAChCE,KAAK,WAAA;AAAA,aACG4C,EAAOa,KAAKN,CAAAA,CAAAA,EAAcS;IAAAA,EAAAA,CAAAA,GAMnC9D,GAAElB,MAAM,SAASD,IAAAA;AAAAA,aACTiE,EAAOa,KAAKN,CAAAA,CAAAA,EAAcvE,IAAID,EAAAA;IAAAA,GAGtCmB,GAAEM,MAAM,SAASzB,IAAUwB,IAAAA;AAAAA,UACpB3C,KAAkBiG,KAAKN,CAAAA;AAAAA,aAC7BX,GAAgBhF,EAAAA,GACXoF,EAAOpF,EAAAA,EAAOoB,IAAID,EAAAA,KAAQiE,EAAOpF,EAAAA,EAAOwC,IAAIrB,EAAAA,MAASwB,OACzD+B,EAAe1E,EAAAA,GACfqG,EAAYrG,EAAAA,GACZA,GAAMwD,EAAWZ,IAAIzB,IAAAA,IAAK,GAC1BnB,GAAMsD,EAAOV,IAAIzB,IAAKwB,EAAAA,GACtB3C,GAAMwD,EAAWZ,IAAIzB,IAAAA,IAAK,IAEpB8E;IAAAA,GAGR3D,GAAEU,SAAS,SAAS7B,IAAAA;AAAAA,UAAAA,CACd8E,KAAK7E,IAAID,EAAAA;AAAAA,eAAAA;AACN,UAGFnB,KAAkBiG,KAAKN,CAAAA;AAAAA,aAC7BX,GAAgBhF,EAAAA,GAChB0E,EAAe1E,EAAAA,GACfqG,EAAYrG,EAAAA,GACRA,GAAMqD,EAAMjC,IAAID,EAAAA,IACnBnB,GAAMwD,EAAWZ,IAAIzB,IAAAA,KAAK,IAE1BnB,GAAMwD,EAAWR,OAAO7B,EAAAA,GAEzBnB,GAAMsD,EAAON,OAAO7B,EAAAA,GAAAA;IACb,GAGRmB,GAAEgE,QAAQ,WAAA;AAAA,UACHtG,KAAkBiG,KAAKN,CAAAA;AAC7BX,MAAAA,GAAgBhF,EAAAA,GACZoF,EAAOpF,EAAAA,EAAOoG,SACjB1B,EAAe1E,EAAAA,GACfqG,EAAYrG,EAAAA,GACZA,GAAMwD,IAAY,oBAAI/C,OACtB8C,EAAKvD,GAAMqD,GAAO,SAAAlC,IAAAA;AACjBnB,QAAAA,GAAMwD,EAAWZ,IAAIzB,IAAAA,KAAK;MAAA,CAAA,GAE3BnB,GAAMsD,EAAOgD,MAAAA;IAAAA,GAIfhE,GAAET,UAAU,SACX0E,IACAC,IAAAA;AAAAA,UAAAA,KAAAA;AAGApB,QADwBa,KAAKN,CAAAA,CAAAA,EACf9D,QAAQ,SAAC4E,IAAatF,IAAAA;AACnCoF,QAAAA,GAAGG,KAAKF,IAASG,GAAKnE,IAAIrB,EAAAA,GAAMA,IAAKwF,EAAAA;MAAAA,CAAAA;IAAAA,GAIvCrE,GAAEE,MAAM,SAASrB,IAAAA;AAAAA,UACVnB,KAAkBiG,KAAKN,CAAAA;AAC7BX,MAAAA,GAAgBhF,EAAAA;AAAAA,UACV2C,KAAQyC,EAAOpF,EAAAA,EAAOwC,IAAIrB,EAAAA;AAAAA,UAC5BnB,GAAM+F,KAAAA,CAAe3F,EAAYuC,EAAAA;AAAAA,eAC7BA;AAAAA,UAEJA,OAAU3C,GAAMqD,EAAMb,IAAIrB,EAAAA;AAAAA,eACtBwB;AAAAA,UAGFhB,KAAQiD,EAAY5E,GAAM6E,EAAOC,GAAQnC,IAAO3C,EAAAA;AAAAA,aACtD0E,EAAe1E,EAAAA,GACfA,GAAMsD,EAAOV,IAAIzB,IAAKQ,EAAAA,GACfA;IAAAA,GAGRW,GAAEsE,OAAO,WAAA;AAAA,aACDxB,EAAOa,KAAKN,CAAAA,CAAAA,EAAciB,KAAAA;IAAAA,GAGlCtE,GAAEuE,SAAS,WAAA;AAAA,UAAAjG,IAAAkG,KAAA,MACJC,KAAWd,KAAKW,KAAAA;AAAAA,cAAAA,KAAAA,CAAAA,GAEpBI,CAAAA,IAAiB,WAAA;AAAA,eAAMC,GAAKJ,OAAAA;MAAAA,GAAAA,GAC7BK,OAAM,WAAA;AAAA,YACCJ,KAAIC,GAASG,KAAAA;AAAAA,eAEfJ,GAAEK,OAAaL,KAEZ,EACNK,MAAAA,OACAxE,OAHasE,GAAKzE,IAAIsE,GAAEnE,KAAAA,EAAAA;MAAAA,GAAAA;IAAAA,GAS5BL,GAAE3B,UAAU,WAAA;AAAA,UAAAC,IAAAkG,KAAA,MACLC,KAAWd,KAAKW,KAAAA;AAAAA,cAAAA,KAAAA,CAAAA,GAEpBI,CAAAA,IAAiB,WAAA;AAAA,eAAMI,GAAKzG,QAAAA;MAAAA,GAAAA,GAC7BuG,OAAM,WAAA;AAAA,YACCJ,KAAIC,GAASG,KAAAA;AAAAA,YAEfJ,GAAEK;AAAM,iBAAOL;AAAAA,YACbnE,KAAQyE,GAAK5E,IAAIsE,GAAEnE,KAAAA;AAAAA,eAClB,EACNwE,MAAAA,OACAxE,OAAO,CAACmE,GAAEnE,OAAOA,EAAAA,EAAAA;MAAAA,GAAAA;IAAAA,GAMrBL,GAAE0E,CAAAA,IAAkB,WAAA;AAAA,aACZf,KAAKtF,QAAAA;IAAAA,GAGN6E;EAAAA,EAnJU,GAkKZ6B,KAAY,WAAA;AAAA,aAGRA,GAAoB5B,IAAgBC,IAAAA;AAAAA,aAAAA,KACvCC,CAAAA,IAAe,EACnBvC,GAAAA,GACAwC,GAASF,IACTb,GAAQa,KAASA,GAAOb,IAASgB,EAAAA,GACjCC,GAAAA,OACAC,GAAAA,OACAzC,GAAAA,QACAD,GAAOoC,IACPO,GAAQC,MACRlB,GAAS,oBAAItE,OACbwE,GAAAA,OACAiB,GAAAA,MAAW,GAELD;IAAAA;AAhBR9B,IAAAA,GAAUkD,IA8GRvG,GAAAA;AAAAA,QA5FIwB,KAAI+E,GAAS5C;AAAAA,WAEnBzD,OAAOmF,eAAe7D,IAAG,QAAQ,EAChCE,KAAK,WAAA;AAAA,aACG4C,EAAOa,KAAKN,CAAAA,CAAAA,EAAcS;IAAAA,EAAAA,CAAAA,GAKnC9D,GAAElB,MAAM,SAASuB,IAAAA;AAAAA,UACV3C,KAAkBiG,KAAKN,CAAAA;AAAAA,aAC7BX,GAAgBhF,EAAAA,GAEXA,GAAMsD,IAAAA,CAAAA,CAGPtD,GAAMsD,EAAMlC,IAAIuB,EAAAA,KAAAA,EAAAA,CAChB3C,GAAM+E,EAAQ3D,IAAIuB,EAAAA,KAAAA,CAAU3C,GAAMsD,EAAMlC,IAAIpB,GAAM+E,EAAQvC,IAAIG,EAAAA,CAAAA,KAH1D3C,GAAMqD,EAAMjC,IAAIuB,EAAAA;IAAAA,GAQzBL,GAAES,MAAM,SAASJ,IAAAA;AAAAA,UACV3C,KAAkBiG,KAAKN,CAAAA;AAAAA,aAC7BX,GAAgBhF,EAAAA,GACXiG,KAAK7E,IAAIuB,EAAAA,MACbgC,GAAe3E,EAAAA,GACfqG,EAAYrG,EAAAA,GACZA,GAAMsD,EAAOP,IAAIJ,EAAAA,IAEXsD;IAAAA,GAGR3D,GAAEU,SAAS,SAASL,IAAAA;AAAAA,UAAAA,CACdsD,KAAK7E,IAAIuB,EAAAA;AAAAA,eAAAA;AACN,UAGF3C,KAAkBiG,KAAKN,CAAAA;AAAAA,aAC7BX,GAAgBhF,EAAAA,GAChB2E,GAAe3E,EAAAA,GACfqG,EAAYrG,EAAAA,GAEXA,GAAMsD,EAAON,OAAOL,EAAAA,KAAAA,CAAAA,CACnB3C,GAAM+E,EAAQ3D,IAAIuB,EAAAA,KAChB3C,GAAMsD,EAAON,OAAOhD,GAAM+E,EAAQvC,IAAIG,EAAAA,CAAAA;IAAAA,GAK3CL,GAAEgE,QAAQ,WAAA;AAAA,UACHtG,KAAkBiG,KAAKN,CAAAA;AAC7BX,MAAAA,GAAgBhF,EAAAA,GACZoF,EAAOpF,EAAAA,EAAOoG,SACjBzB,GAAe3E,EAAAA,GACfqG,EAAYrG,EAAAA,GACZA,GAAMsD,EAAOgD,MAAAA;IAAAA,GAIfhE,GAAEuE,SAAS,WAAA;AAAA,UACJ7G,KAAkBiG,KAAKN,CAAAA;AAAAA,aAC7BX,GAAgBhF,EAAAA,GAChB2E,GAAe3E,EAAAA,GACRA,GAAMsD,EAAOuD,OAAAA;IAAAA,GAGrBvE,GAAE3B,UAAU,WAAA;AAAA,UACLX,KAAkBiG,KAAKN,CAAAA;AAAAA,aAC7BX,GAAgBhF,EAAAA,GAChB2E,GAAe3E,EAAAA,GACRA,GAAMsD,EAAO3C,QAAAA;IAAAA,GAGrB2B,GAAEsE,OAAO,WAAA;AAAA,aACDX,KAAKY,OAAAA;IAAAA,GAGbvE,GAAE0E,CAAAA,IAAkB,WAAA;AAAA,aACZf,KAAKY,OAAAA;IAAAA,GAGbvE,GAAET,UAAU,SAAiB0E,IAASC,IAAAA;AAAAA,eAC/BO,KAAWd,KAAKY,OAAAA,GAClBS,KAASP,GAASG,KAAAA,GAAAA,CACdI,GAAOH;AACdZ,QAAAA,GAAGG,KAAKF,IAASc,GAAO3E,OAAO2E,GAAO3E,OAAOsD,IAAAA,GAC7CqB,KAASP,GAASG,KAAAA;IAAAA,GAIbG;EAAAA,EA9GU;AA0IlB5F,IAAW,UAAU,EAAC8F,GAAAA,SAtJe9B,IAAWC,IAAAA;AAAAA,WAExC,IAAIF,GAASC,IAAQC,EAAAA;EAAAA,GAoJI8B,GAAAA,SAzBI/B,IAAWC,IAAAA;AAAAA,WAExC,IAAI2B,GAAS5B,IAAQC,EAAAA;EAAAA,EAAAA,CAAAA;AAAAA;AEvNtB+B,IAAAA;AAAAA,ITnFJC;ASmFID,ICvGFE,IACa,eAAA,OAAXC,UAAiD,YAAA,OAAhBA,OAAO,GAAA;ADsGxCH,ICrGKI,IAAwB,eAAA,OAARC;ADqGrBL,ICpGKM,IAAwB,eAAA,OAARC;ADoGrBP,ICnGKQ,IACK,eAAA,OAAVC,SAAAA,WACAA,MAAMC,aACM,eAAA,OAAZC;ADgGAX,IC3FKY,IAAmBV,IAC7BC,OAAOU,IAAI,eAAA,MAAA,IAAA,CAAA,GACR,eAAA,IAAA,MAAkB;ADyFhBb,IC/EKc,IAA2BZ,IACrCC,OAAOU,IAAI,iBAAA,IACV;AD6EIb,IC3EKe,IAA6Bb,IACvCC,OAAOU,IAAI,aAAA,IACV;ADyEIb,ICtEKgB,IACM,eAAA,OAAVb,UAAyBA,OAAOc,YAAc;ADqE/CjB,IZ5GFkB,IAAS,EAAA,GACX,iBAAA,GACA,gDAAA,GACA,yDAAA,GAAA,SACDC,IAAAA;AAAAA,SAEA,yHACAA;AAAAA,GAAAA,GAGC,qHAAA,GACA,qCAAA,GACA,gEAAA,GACA,mEAAA,GACA,4FAAA,GACA,6EAAA,IACC,wCAAA,IACA,4DAAA,IACA,4DAAA,IACA,8CAAA,IACA,uEAAA,IAAA,SACDC,IAAAA;AAAAA,SACK,+CAA+CA;AAAAA,GAAAA,IAEnD,uCAAA,IAAA,SACDC,IAAAA;AAAAA,SACK,kCAAkCA;AAAAA,GAAAA,IAAAA,SAEvCC,IAAAA;AAAAA,SAAAA,qBACwBA,KAAAA,oFAAyFA,KAAAA;AAAAA,GAAAA,IAEhH,6EAAA,IAAA,SACDC,IAAAA;AAAAA,SAAAA,wJAC2JA,KAAAA;AAAAA,GAAAA,IAAAA,SAE3JA,IAAAA;AAAAA,SAAAA,qCACwCA;AAAAA,GAAAA,IAAAA,SAExCA,IAAAA;AAAAA,SAAAA,sCACyCA;AAAAA,GAAAA,IAExC,wFAAA;AYmEGvB,IXzEFwB,IAAmBC,KAAAA,OAAOC,UAAUC;AWyElC3B,IX7CK4B,KACO,eAAA,OAAZjB,WAA2BA,QAAQiB,UACvCjB,QAAQiB,UAAAA,WACDH,OAAOI,wBACd,SAAAC,IAAAA;AAAAA,SACAL,OAAOM,oBAAoBD,EAAAA,EAAKE,OAC/BP,OAAOI,sBAAsBC,EAAAA,CAAAA;AAAAA,IAEHL,OAAOM;AWqC9B/B,IXnCKiC,KACZR,OAAOQ,6BACP,SAAmCC,IAAAA;AAAAA,MAE5BC,KAAW,CAAA;AAAA,SACjBP,GAAQM,EAAAA,EAAQE,QAAQ,SAAAC,IAAAA;AACvBF,IAAAA,GAAIE,EAAAA,IAAOZ,OAAOa,yBAAyBJ,IAAQG,EAAAA;EAAAA,CAAAA,GAE7CF;AAAAA;AW2BDnC,IV9FFuC,KA4BF,CAAA;AUkEIvC,IPTKwC,KAAwC,EACpDC,KAAAA,SAAIC,IAAOC,IAAAA;AAAAA,MACNA,OAAS5B;AAAa,WAAO2B;AAAAA,MAE3BE,IAASC,EAAOH,EAAAA;AAAAA,MAAAA,CACjBI,EAAIF,GAAQD,EAAAA;AAAAA,WAwInB,SAA2BD,IAAmBE,IAAaD,IAAAA;AAAAA,UAAAA,IACpDI,KAAOC,EAAuBJ,IAAQD,EAAAA;AAAAA,aACrCI,KACJ,WAAWA,KACVA,GAAK/C,QAAAA,UAAAA,KAGL+C,GAAKN,QAAAA,WAAAA,KAAAA,SAALQ,GAAUC,KAAKR,GAAMS,CAAAA,IAAAA;IACtBC,EA9IwBV,IAAOE,GAAQD,EAAAA;AAAAA,MAEnC3C,KAAQ4C,EAAOD,EAAAA;AAAAA,SACjBD,GAAMW,KAAAA,CAAeC,EAAYtD,EAAAA,IAC7BA,KAIJA,OAAUuD,EAAKb,GAAMc,GAAOb,EAAAA,KAC/Bc,EAAYf,EAAAA,GACJA,GAAMgB,EAAOf,EAAAA,IAAegB,EACnCjB,GAAMkB,EAAOC,GACb7D,IACA0C,EAAAA,KAGK1C;AAAAA,GAER8C,KAAAA,SAAIJ,IAAOC,IAAAA;AAAAA,SACHA,MAAQE,EAAOH,EAAAA;AAAAA,GAEvBd,SAAAA,SAAQc,IAAAA;AAAAA,SACA/B,QAAQiB,QAAQiB,EAAOH,EAAAA,CAAAA;AAAAA,GAE/BoB,KAAAA,SACCpB,IACAC,IACA3C,IAAAA;AAAAA,MAEM+C,IAAOC,EAAuBH,EAAOH,EAAAA,GAAQC,EAAAA;AAAAA,MAC/CI,QAAAA,IAAAA,SAAAA,EAAMe;AAAAA,WAGTf,EAAKe,IAAIZ,KAAKR,GAAMS,GAAQnD,EAAAA,GAAAA;AACrB,MAAA,CAEH0C,GAAMqB,GAAW;AAAA,QAGfC,KAAUT,EAAKV,EAAOH,EAAAA,GAAQC,EAAAA,GAE9BsB,KAAiCD,QAAAA,KAAAA,SAAAA,GAAUjD,CAAAA;AAAAA,QAC7CkD,MAAgBA,GAAaT,MAAUxD;AAAAA,aAC1C0C,GAAMgB,EAAOf,EAAAA,IAAQ3C,IACrB0C,GAAMwB,EAAUvB,EAAAA,IAAAA,OAAQ;AACjB,QAEJwB,EAAGnE,IAAOgE,EAAAA,MAAAA,WAAahE,MAAuB8C,EAAIJ,GAAMc,GAAOb,EAAAA;AAClE,aAAA;AACDc,MAAYf,EAAAA,GACZ0B,EAAY1B,EAAAA;EAAAA;AAAAA,SAIXA,GAAMgB,EAAOf,EAAAA,MAAU3C,OAAAA,WAEtBA,MAAuB2C,MAAQD,GAAMgB,MAEtCW,OAAOC,MAAMtE,EAAAA,KAAUqE,OAAOC,MAAM5B,GAAMgB,EAAOf,EAAAA,CAAAA,MAKnDD,GAAMgB,EAAOf,EAAAA,IAAQ3C,IACrB0C,GAAMwB,EAAUvB,EAAAA,IAAAA,OAAQ;AAJhB,GAOT4B,gBAAAA,SAAe7B,IAAOC,IAAAA;AAAAA,SAAAA,WAEjBY,EAAKb,GAAMc,GAAOb,EAAAA,KAAuBA,MAAQD,GAAMc,KAC1Dd,GAAMwB,EAAUvB,EAAAA,IAAAA,OAChBc,EAAYf,EAAAA,GACZ0B,EAAY1B,EAAAA,KAAAA,OAGLA,GAAMwB,EAAUvB,EAAAA,GAGpBD,GAAMgB,KAAAA,OAAchB,GAAMgB,EAAMf,EAAAA,GAAAA;AAC7B,GAIRL,0BAAAA,SAAyBI,IAAOC,IAAAA;AAAAA,MACzB6B,KAAQ3B,EAAOH,EAAAA,GACfK,IAAOpC,QAAQ2B,yBAAyBkC,IAAO7B,EAAAA;AAAAA,SAChDI,IACE,EACN0B,UAAAA,MACAC,cAAAA,MAAchC,GAAMiC,KAA2C,aAAThC,IACtDiC,YAAY7B,EAAK6B,YACjB5E,OAAOwE,GAAM7B,EAAAA,EAAAA,IALII;AAAAA,GAQnB8B,gBAAAA,WAAAA;AACCC,IAAI,EAAA;AAAA,GAELC,gBAAAA,SAAerC,IAAAA;AAAAA,SACPjB,OAAOsD,eAAerC,GAAMc,CAAAA;AAAAA,GAEpCwB,gBAAAA,WAAAA;AACCF,IAAI,EAAA;AAAA,EAAA;AOnGE9E,IP2GFiF,KAA8C,CAAA;AACpDC,EAAK1C,IAAa,SAACH,IAAK8C,IAAAA;AAEvBF,KAAW5C,EAAAA,IAAO,WAAA;AAAA,WACjB+C,UAAU,CAAA,IAAKA,UAAU,CAAA,EAAG,CAAA,GACrBD,GAAGE,MAAMC,MAAMF,SAAAA;EAAAA;AAAAA,CAAAA,GAGxBH,GAAWV,iBAAiB,SAAS7B,IAAOC,IAAAA;AAAAA,SAC5B2B,MAAMiB,SAAS5C,EAAAA,CAAAA,KAAemC,EAAI,EAAA,GAE1CG,GAAWnB,IAAKZ,KAAKoC,MAAM5C,IAAOC,IAAAA,MAAMS;AAAAA,GAEhD6B,GAAWnB,MAAM,SAASpB,IAAOC,IAAM3C,GAAAA;AAAAA,SACd,aAAT2C,MAAqB2B,MAAMiB,SAAS5C,EAAAA,CAAAA,KAAemC,EAAI,EAAA,GAC/DtC,GAAYsB,IAAKZ,KAAKoC,MAAM5C,GAAM,CAAA,GAAIC,IAAM3C,GAAO0C,GAAM,CAAA,CAAA;AAAA;AAAA,ICpMpD8C,KAAb,WAAA;AAAA,WAAA,EAKaC,IAAAA;AAAAA,QAAAA,KAAAA;AAAAA,SAAAA,IAJWjF,GAAAA,KAAAA,IAAAA,MAEA,KAAA,UA4BH,SAACkF,IAAWC,IAAcC,IAAAA;AAAAA,UAEzB,cAAA,OAATF,MAAyC,cAAA,OAAXC,IAAuB;AAAA,YACzDE,KAAcF;AACpBA,QAAAA,KAASD;AAAAA,YAEHI,KAAOC;AAAAA,eACN,SAENL,IAAAA;AAAAA,cAAAA,KAAAA;AAAAA,qBAAAA,OAAAA,KAAOG;AAAAA,mBAAAA,KAAAA,UAAAA,QACJG,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,KAAAA,GAAAA,KAAAA,IAAAA;AAAAA,YAAAA,GAAAA,KAAAA,CAAAA,IAAAA,UAAAA,EAAAA;AAAAA,iBAEIF,GAAKG,QAAQP,IAAM,SAACQ,IAAAA;AAAAA,gBAAAA;AAAAA,oBAAAA,KAAmBP,IAAOzC,KAAAA,MAAAA,IAAAA,CAAKiD,IAAMD,EAAAA,EAAAA,OAAUF,EAAAA,CAAAA;UAAAA,CAAAA;QAAAA;MAAAA;AAAAA,UAQxEI;AAAAA,UAJkB,cAAA,OAAXT,MAAuBb,EAAI,CAAA,GAAA,WAClCc,MAAwD,cAAA,OAAlBA,MACzCd,EAAI,CAAA,GAKDxB,EAAYoC,EAAAA,GAAO;AAAA,YAChBW,KAAQC,EAAWP,EAAAA,GACnBQ,KAAQ5C,EAAYoC,IAAML,IAAAA,MAAMtC,GAClCoD,KAAAA;AAAW,YAAA;AAEdJ,UAAAA,KAAST,GAAOY,EAAAA,GAChBC,KAAAA;QAAW,UAAA;AAGPA,UAAAA,KAAUC,EAAYJ,EAAAA,IACrBK,EAAWL,EAAAA;QAAAA;AAAAA,eAEM,eAAA,OAAZM,WAA2BP,cAAkBO,UAChDP,GAAOQ,KACb,SAAAR,IAAAA;AAAAA,iBACCS,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAcV,IAAQC,EAAAA;QAAAA,GAE9B,SAAAU,IAAAA;AAAAA,gBACCN,EAAYJ,EAAAA,GACNU;QAAAA,CAAAA,KAITF,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAcV,IAAQC,EAAAA;MAAAA;AACvB,UAAA,CAAKX,MAAwB,YAAA,OAATA,IAAmB;AAAA,YAAA,YAC7CU,KAAST,GAAOD,EAAAA,OACUU,KAASV,KAC/BU,OAAWxF,MAASwF,KAAAA,SACpBL,GAAKiB,KAAaC,EAAOb,IAAAA,IAAQ,GACjCR,IAAe;AAAA,cACZsB,KAAa,CAAA,GACbC,KAAc,CAAA;AACpBC,YAAU,SAAA,EAAWC,EAA4B3B,IAAMU,IAAQc,IAAGC,EAAAA,GAClEvB,GAAcsB,IAAGC,EAAAA;QAAAA;AAAAA,eAEXf;MAAAA;AACDtB,QAAI,IAAIY,EAAAA;IAAAA,GAAAA,KAAAA,qBAG0B,SAACA,IAAWC,IAAAA;AAAAA,UAEjC,cAAA,OAATD;AAAAA,eACH,SAAChD,IAAAA;AAAAA,mBAAAA,KAAAA,UAAAA,QAAesD,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,KAAAA,GAAAA,KAAAA,IAAAA;AAAAA,YAAAA,GAAAA,KAAAA,CAAAA,IAAAA,UAAAA,EAAAA;AAAAA,iBACtBD,GAAKuB,mBAAmB5E,IAAO,SAACwD,IAAAA;AAAAA,mBAAeR,GAAAA,MAAAA,QAAAA,CAAKQ,EAAAA,EAAAA,OAAUF,EAAAA,CAAAA;UAAAA,CAAAA;QAAAA;AAAAA,UAG5DuB,IAAkBC,IAChBpB,KAASL,GAAKE,QAAQP,IAAMC,IAAQ,SAACuB,IAAYC,IAAAA;AACtDI,QAAAA,KAAUL,IACVM,KAAiBL;MAAAA,CAAAA;AAAAA,aAGK,eAAA,OAAZR,WAA2BP,cAAkBO,UAChDP,GAAOQ,KAAK,SAAAa,IAAAA;AAAAA,eAAa,CAACA,IAAWF,IAAUC,EAAAA;MAAAA,CAAAA,IAEhD,CAACpB,IAAQmB,IAAUC,EAAAA;IAAAA,GAzGQ,aAAA,QAAvB/B,QAAAA,KAAAA,SAAAA,GAAQiC,eAClBpC,KAAKqC,cAAclC,GAAQiC,UAAAA,GACM,aAAA,QAAvBjC,QAAAA,KAAAA,SAAAA,GAAQmC,eAClBtC,KAAKuC,cAAcpC,GAAQmC,UAAAA;EAAAA;AAAAA,MAAAA,KAAAA,EAAAA;AAAAA,SAAAA,GAyG7BE,cAAA,SAAiCpC,IAAAA;AAC3BpC,MAAYoC,EAAAA,KAAOZ,EAAI,CAAA,GACxBiD,EAAQrC,EAAAA,MAAOA,KAAO1B,EAAQ0B,EAAAA;AAAAA,QAC5BW,KAAQC,EAAWhB,IAAAA,GACnBiB,KAAQ5C,EAAY2B,MAAMI,IAAAA,MAAMtC;AAAAA,WACtCmD,GAAMxF,CAAAA,EAAaiH,IAAAA,MACnBtB,EAAWL,EAAAA,GACJE;EAAAA,GAAAA,GAGR0B,cAAA,SACC/B,IACAN,IAAAA;AAAAA,QAEMlD,KAAoBwD,MAAUA,GAAcnF,CAAAA;AAAAA,IAE5C2B,MAAUA,GAAMsF,KAAWlD,EAAI,CAAA,GAChCpC,GAAMW,KAAYyB,EAAI,EAAA;AAAA,QAEZuB,KAAS3D,GAAjBkB;AAAAA,WACPiD,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAAA,QAAyBT,EAAAA;EAAAA,GAAAA,GAQjCwB,gBAAA,SAAc7H,IAAAA;AAAAA,SACRgH,IAAchH;EAAAA,GAAAA,GASpB2H,gBAAA,SAAc3H,IAAAA;AACTA,IAAAA,MAAAA,CAAUQ,KACbsE,EAAI,EAAA,GAAA,KAEAoD,IAAclI;EAAAA,GAAAA,GAGpBmI,eAAA,SAAkCzC,IAAS6B,IAAAA;AAAAA,QAGtCa;AAAAA,SACCA,KAAIb,GAAQc,SAAS,GAAGD,MAAK,GAAGA,MAAK;AAAA,UACnCE,KAAQf,GAAQa,EAAAA;AAAAA,UACI,MAAtBE,GAAMlH,KAAKiH,UAA6B,cAAbC,GAAMjH,IAAkB;AACtDqE,QAAAA,KAAO4C,GAAMtI;AAAAA;MAAAA;IAAAA;AAMXoI,IAAAA,KAAAA,OACHb,KAAUA,GAAQgB,MAAMH,KAAI,CAAA;AAAA,QAGvBI,KAAmBpB,EAAU,SAAA,EAAWqB;AAAAA,WAC1CV,EAAQrC,EAAAA,IAEJ8C,GAAiB9C,IAAM6B,EAAAA,IAGxBjC,KAAKW,QAAQP,IAAM,SAACQ,IAAAA;AAAAA,aAC1BsC,GAAiBtC,IAAOqB,EAAAA;IAAAA,CAAAA;EAAAA,GAAAA;AAAAA,EAxL3B;ADoMiE,IOhN3DmB,KAAQ,IAAIlD;APgN+C,IO3LpDS,KAAoByC,GAAMzC;AP2L0B,IOpLpDqB,KAA0CoB,GAAMpB,mBAAmBqB,KAC/ED,EAAAA;APmLgE,IO3KpDb,KAAgBa,GAAMb,cAAcc,KAAKD,EAAAA;AP2KW,IOnKpDf,KAAgBe,GAAMf,cAAcgB,KAAKD,EAAAA;APmKW,IO5JpDP,KAAeO,GAAMP,aAAaQ,KAAKD,EAAAA;AP4Ja,IOtJpDZ,KAAcY,GAAMZ,YAAYa,KAAKD,EAAAA;APsJe,IO5IpDT,KAAcS,GAAMT,YAAYU,KAAKD,EAAAA;AAAAA,IAAAA,oBAAAA;;;;;;;;AEvElD,IAAI,SAAS,CAAC,OAAO,OACnB,OAAO,gBAAgB,IAAI,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,SAAS;AAChE,UAAQ;AACR,MAAI,OAAO,IAAI;AACb,UAAM,KAAK,SAAS,EAAE;AAAA,EACxB,WAAW,OAAO,IAAI;AACpB,WAAO,OAAO,IAAI,SAAS,EAAE,EAAE,YAAY;AAAA,EAC7C,WAAW,OAAO,IAAI;AACpB,UAAM;AAAA,EACR,OAAO;AACL,UAAM;AAAA,EACR;AACA,SAAO;AACT,GAAG,EAAE;;;AChCP,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,SAAS,UAAU,WAAW,SAAS;AACnC,MAAI,WAAW;AACX;AAAA,EACJ;AACA,MAAI,cAAc;AACd,UAAM,IAAI,MAAM,MAAM;AAAA,EAC1B;AACA,MAAI,WAAW,OAAO,YAAY,aAAa,QAAQ,IAAI;AAC3D,MAAI,QAAQ,WAAW,GAAG,OAAO,QAAQ,IAAI,EAAE,OAAO,QAAQ,IAAI;AAClE,QAAM,IAAI,MAAM,KAAK;AACzB;;;;;ACZO,IAAME,KAAY;AAAlB,IACMC,KAAuB;AAD7B,IAIMC,KAAiB;AAJvB,IAKMC,KACX;AANK,IAOMC,KACX;AARK,IASMC,KACX;AAVK,IAaMC,KACX;AAdK,IAeMC,KAA6B;AAfnC,IAgBMC,KACX;AAjBK,IAkBMC,KACX;AAnBK,IAoBMC,KACX;AArBK,IAsBMC,KAA4B;AAtBlC,IAyBMC,KAA2B;AAzBjC,IA0BMC,KACX;AA3BK,IA8BMC,KACX;AA/BK,IAgCMC,KAAwB;AAhC9B,IAiCMC,KACX;AAlCK,IAoCMC,KAE2C;AAtCjD,IAwCMC,KAI2C;AA5CjD,IA8CMC,KAE0E;AAhDhF,IAkDMC,KAEwE;AAAA,SAAAC,GAAA,GAAAC,IAAA;AAAA,MAAAC,KAAA,OAAA,KAAA,CAAA;AAAA,MAAA,OAAA,uBAAA;AAAA,QAAAC,KAAA,OAAA,sBAAA,CAAA;AAAA,IAAAF,OAAAE,KAAAA,GAAA,OAAA,SAAAF,IAAA;AAAA,aAAA,OAAA,yBAAA,GAAAA,EAAA,EAAA;IAAA,CAAA,IAAAC,GAAA,KAAA,MAAAA,IAAAC,EAAA;EAAA;AAAA,SAAAD;AAAA;AAAA,SAAAE,GAAA,GAAA;AAAA,WAAAH,KAAA,GAAAA,KAAA,UAAA,QAAAA,MAAA;AAAA,QAAAC,KAAA,QAAA,UAAAD,EAAA,IAAA,UAAAA,EAAA,IAAA,CAAA;AAAA,IAAAA,KAAA,IAAAD,GAAA,OAAAE,EAAA,GAAA,IAAA,EAAA,QAAA,SAAAD,IAAA;AAAA,QAAA,GAAAA,IAAAC,GAAAD,EAAA,CAAA;IAAA,CAAA,IAAA,OAAA,4BAAA,OAAA,iBAAA,GAAA,OAAA,0BAAAC,EAAA,CAAA,IAAAF,GAAA,OAAAE,EAAA,CAAA,EAAA,QAAA,SAAAD,IAAA;AAAA,aAAA,eAAA,GAAAA,IAAA,OAAA,yBAAAC,IAAAD,EAAA,CAAA;IAAA,CAAA;EAAA;AAAA,SAAA;AAAA;AAAA,SAAA,EAAA,GAAAA,IAAA;AAAA,MAAA,EAAA,aAAAA;AAAA,UAAA,IAAA,UAAA,mCAAA;AAAA;AAAA,SAAAI,GAAA,GAAAJ,IAAA;AAAA,WAAAC,KAAA,GAAAA,KAAAD,GAAA,QAAAC,MAAA;AAAA,QAAAC,KAAAF,GAAAC,EAAA;AAAA,IAAAC,GAAA,aAAAA,GAAA,cAAA,OAAAA,GAAA,eAAA,MAAA,WAAAA,OAAAA,GAAA,WAAA,OAAA,OAAA,eAAA,GAAA,GAAAA,GAAA,GAAA,GAAAA,EAAA;EAAA;AAAA;AAAA,SAAAG,GAAA,GAAAL,IAAAC,IAAA;AAAA,SAAAD,MAAAI,GAAA,EAAA,WAAAJ,EAAA,GAAAC,MAAAG,GAAA,GAAAH,EAAA,GAAA,OAAA,eAAA,GAAA,aAAA,EAAA,UAAA,MAAA,CAAA,GAAA;AAAA;AAAA,SAAA,EAAA,GAAAD,IAAAC,IAAA;AAAA,UAAAD,KAAA,GAAAA,EAAA,MAAA,IAAA,OAAA,eAAA,GAAAA,IAAA,EAAA,OAAAC,IAAA,YAAA,MAAA,cAAA,MAAA,UAAA,KAAA,CAAA,IAAA,EAAAD,EAAA,IAAAC,IAAA;AAAA;AAAA,SAAA,EAAA,GAAA;AAAA,SAAA,IAAA,OAAA,iBAAA,OAAA,eAAA,KAAA,IAAA,SAAAK,IAAA;AAAA,WAAAA,GAAA,aAAA,OAAA,eAAAA,EAAA;EAAA,GAAA,EAAA,CAAA;AAAA;AAAA,SAAA,EAAA,GAAAN,IAAA;AAAA,SAAA,IAAA,OAAA,iBAAA,OAAA,eAAA,KAAA,IAAA,SAAAM,IAAAN,IAAA;AAAA,WAAAM,GAAA,YAAAN,IAAAM;EAAA,GAAA,EAAA,GAAAN,EAAA;AAAA;AAAA,SAAAO,GAAA,GAAA;AAAA,MAAA,WAAA;AAAA,UAAA,IAAA,eAAA,2DAAA;AAAA,SAAA;AAAA;AAAA,SAAAC,KAAA;AAAA,SAAAA,KAAA,eAAA,OAAA,WAAA,QAAA,MAAA,QAAA,IAAA,KAAA,IAAA,SAAA,GAAAR,IAAAC,IAAA;AAAA,QAAAC,KAAA,SAAAI,IAAAN,IAAA;AAAA,aAAA,CAAA,OAAA,UAAA,eAAA,KAAAM,IAAAN,EAAA,KAAA,UAAAM,KAAA,EAAAA,EAAA;AAAA;AAAA,aAAAA;IAAA,EAAA,GAAAN,EAAA;AAAA,QAAAE,IAAA;AAAA,UAAAO,KAAA,OAAA,yBAAAP,IAAAF,EAAA;AAAA,aAAAS,GAAA,MAAAA,GAAA,IAAA,KAAA,UAAA,SAAA,IAAA,IAAAR,EAAA,IAAAQ,GAAA;IAAA;EAAA,GAAAD,GAAA,MAAA,MAAA,SAAA;AAAA;AAAA,SAAAE,GAAA,GAAAV,IAAA;AAAA,SAAA,GAAA,CAAA,KAAA,SAAAM,IAAAN,IAAA;AAAA,QAAAC,KAAA,QAAAK,KAAA,OAAA,eAAA,OAAA,UAAAA,GAAA,OAAA,QAAA,KAAAA,GAAA,YAAA;AAAA,QAAA,QAAAL,IAAA;AAAA,UAAAC,IAAAO,IAAAE,IAAAC,IAAAC,KAAA,CAAA,GAAAC,KAAA,MAAAC,KAAA;AAAA,UAAA;AAAA,YAAAJ,MAAAV,KAAAA,GAAA,KAAAK,EAAA,GAAA,MAAA,MAAAN,IAAA;AAAA,cAAA,OAAAC,EAAA,MAAAA;AAAA;AAAA,UAAAa,KAAA;QAAA;AAAA,iBAAA,EAAAA,MAAAZ,KAAAS,GAAA,KAAAV,EAAA,GAAA,UAAAY,GAAA,KAAAX,GAAA,KAAA,GAAAW,GAAA,WAAAb,KAAAc,KAAA;AAAA;MAAA,SAAAR,IAAA;AAAA,QAAAS,KAAA,MAAAN,KAAAH;MAAA,UAAA;AAAA,YAAA;AAAA,cAAA,CAAAQ,MAAA,QAAAb,GAAA,WAAAW,KAAAX,GAAA,OAAA,GAAA,OAAAW,EAAA,MAAAA;AAAA;QAAA,UAAA;AAAA,cAAAG;AAAA,kBAAAN;QAAA;MAAA;AAAA,aAAAI;IAAA;EAAA,EAAA,GAAAb,EAAA,KAAA,GAAA,GAAAA,EAAA,KAAA,GAAA;AAAA;AAAA,SAAAgB,GAAA,GAAA;AAAA,SAAA,SAAAV,IAAA;AAAA,QAAA,MAAA,QAAAA,EAAA;AAAA,aAAA,GAAAA,EAAA;EAAA,EAAA,CAAA,KAAA,GAAA,CAAA,KAAA,GAAA,CAAA,KAAA,WAAA;AAAA,UAAA,IAAA,UAAA,sIAAA;EAAA,EAAA;AAAA;AAAA,SAAA,GAAA,GAAA;AAAA,MAAA,MAAA,QAAA,CAAA;AAAA,WAAA;AAAA;AAAA,SAAA,GAAA,GAAA;AAAA,MAAA,eAAA,OAAA,UAAA,QAAA,EAAA,OAAA,QAAA,KAAA,QAAA,EAAA,YAAA;AAAA,WAAA,MAAA,KAAA,CAAA;AAAA;AAAA,SAAA,GAAA,GAAAN,IAAA;AAAA,MAAA,GAAA;AAAA,QAAA,YAAA,OAAA;AAAA,aAAA,GAAA,GAAAA,EAAA;AAAA,QAAAC,KAAA,OAAA,UAAA,SAAA,KAAA,CAAA,EAAA,MAAA,GAAA,EAAA;AAAA,WAAA,aAAAA,MAAA,EAAA,gBAAAA,KAAA,EAAA,YAAA,OAAA,UAAAA,MAAA,UAAAA,KAAA,MAAA,KAAA,CAAA,IAAA,gBAAAA,MAAA,2CAAA,KAAAA,EAAA,IAAA,GAAA,GAAAD,EAAA,IAAA;EAAA;AAAA;AAAA,SAAA,GAAA,GAAAA,IAAA;AAAA,GAAA,QAAAA,MAAAA,KAAA,EAAA,YAAAA,KAAA,EAAA;AAAA,WAAAC,KAAA,GAAAC,KAAA,IAAA,MAAAF,EAAA,GAAAC,KAAAD,IAAAC;AAAA,IAAAC,GAAAD,EAAA,IAAA,EAAAA,EAAA;AAAA,SAAAC;AAAA;AAAA,SAAA,KAAA;AAAA,QAAA,IAAA,UAAA,2IAAA;AAAA;AAAA,SAAA,GAAA,GAAA;AAAA,MAAAF,KAAA,SAAAM,IAAAN,IAAA;AAAA,QAAA,YAAA,OAAAM,MAAA,SAAAA;AAAA,aAAAA;AAAA,QAAAL,KAAAK,GAAA,OAAA,WAAA;AAAA,QAAA,WAAAL,IAAA;AAAA,UAAAC,KAAAD,GAAA,KAAAK,IAAA,QAAA;AAAA,UAAA,YAAA,OAAAJ;AAAA,eAAAA;AAAA,YAAA,IAAA,UAAA,8CAAA;IAAA;AAAA,WAAA,OAAAI,EAAA;EAAA,EAAA,CAAA;AAAA,SAAA,YAAA,OAAAN,KAAAA,KAAA,OAAAA,EAAA;AAAA;AC5C9E,IAAMiB,KAAkB,EAC7BC,MAAM,gBACNC,MAAM,gBACNC,UAAU,oBACVC,QAAQ,kBACRC,OAAO,iBACPC,OAAO,gBAAA;AANF,IASMC,KAAO,WAAA;AAAA,WAAAA,IAAAA;AAAAC,MAAAC,MAAAF,CAAAA,GAAAG,EAAAD,MAAA,YACG,CAAA,CAAA,GAAEC,EAAAD,MAAA,WAAA,EACZ;EAAC;AAsGX,SAtGWE,GAAAJ,GAAA,CAAA,EAAAK,KAAA,OAAAC,OAEZ,SAAIC,IAAkBC,IAAAA;AACG,UAAnBD,GAAQE,UAA0C,MAA1BD,GAAeC,WAI3CP,KAAKQ,UAAUR,KAAKQ,UAAU,GAC9BR,KAAKS,SAASF,SAASP,KAAKQ,SAC5BR,KAAKS,SAAST,KAAKQ,OAAAA,IAAW,EAC5BH,SAAAA,IACAC,gBAAAA,IACAI,WAAWC,KAAKC,IAAAA,EAAAA;EAEpB,EAAA,GAAC,EAAAT,KAAA,eAAAC,OAED,SACEC,IACAC,IAAAA;AAC0B,QAA1BO,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,IAAAA,UAAAA,CAAAA,IAAuB;AAEvB,QAAuB,MAAnBR,GAAQE,UAA0C,MAA1BD,GAAeC,QAA3C;AAIA,UAAIP,KAAKS,SAASF,UAAUP,KAAKQ,WAAW,GAAG;AAC7C,YAAAM,KAIId,KAAKS,SAAST,KAAKQ,OAAAA,GAHZO,KAAAA,GAATV,SACgBW,KAAAA,GAAhBV,gBACAI,KAAAA,GAAAA;AAMF,aAHY,oBAAIC,QACCM,QAAAA,IAAYP,KAElBG;AAMT,iBAAA,MALAb,KAAKS,SAAST,KAAKQ,OAAAA,IAAW,EAC5BE,WAAAA,IACAL,SAAaU,CAAAA,EAAAA,OAAAA,GAAAA,EAAAA,GAAgBV,GAAAA,EAAAA,CAAAA,GAC7BC,gBAAc,CAAA,EAAAY,OAAAC,GAAMb,EAAAA,GAAca,GAAKH,EAAAA,CAAAA,EAAAA;MAI7C;AAEAhB,WAAKoB,IAAIf,IAASC,EAAAA;IAtBlB;EAuBF,EAAA,GAAC,EAAAH,KAAA,SAAAC,OAED,SAAMC,IAAkBC,IAAAA;AACtB,QAAuB,MAAnBD,GAAQE,UAA0C,MAA1BD,GAAeC;AAI3C,UAAIP,KAAKS,SAASF,UAAUP,KAAKQ,WAAW,GAA5C;AACE,YAAAa,KAIIrB,KAAKS,SAAST,KAAKQ,OAAAA,GAFLQ,KAAAA,GAAhBV;AAIFN,aAAKS,SAAST,KAAKQ,OAAAA,IAAW,EAC5BE,WAAAA,GAJAA,WAKAL,SAAaU,CAAAA,EAAAA,OAAAA,GAAAA,GAPbV,OAAAA,GAO6BA,GAAAA,EAAAA,CAAAA,GAC7BC,gBAAc,CAAA,EAAAY,OAAAC,GAAMb,EAAAA,GAAca,GAAKH,EAAAA,CAAAA,EAAAA;MAG3C;AAEAhB,aAAKoB,IAAIf,IAASC,EAAAA;EACpB,EAAA,GAAC,EAAAH,KAAA,SAAAC,OAED,WAAA;AACEJ,SAAKS,WAAW,CAAA,GAChBT,KAAKQ,UAAAA;EACP,EAAA,GAAC,EAAAL,KAAA,WAAAC,OAED,WAAA;AACE,WAAOJ,KAAKQ,WAAW;EACzB,EAAA,GAAC,EAAAL,KAAA,WAAAC,OAED,WAAA;AACE,WAAOJ,KAAKQ,UAAUR,KAAKS,SAASF,SAAS;EAC/C,EAAA,GAAC,EAAAJ,KAAA,QAAAC,OAED,SAAKkB,IAAAA;AACH,QAAKtB,KAAKuB,QAAAA,GAAV;AAIA,UAAQjB,KAAmBN,KAAKS,SAAST,KAAKQ,OAAAA,EAAtCF;AAER,aADAN,KAAKQ,UAAUR,KAAKQ,UAAU,GACvBgB,GAAaF,IAAOhB,EAAAA;IAJ3B;EAKF,EAAA,GAAC,EAAAH,KAAA,QAAAC,OAED,SAAKkB,IAAAA;AACH,QAAKtB,KAAKyB,QAAAA;AAMV,aAFAzB,KAAKQ,UAAUR,KAAKQ,UAAU,GAEvBgB,GAAaF,IADAtB,KAAKS,SAAST,KAAKQ,OAAAA,EAA/BH,OAAAA;EAEV,EAAA,CAAA,CAAA,GAACP;AAAA,EAxGiB;ACuKd,SAAU4B,GAKdC,IACAC,IACAC,IACAC,IAAAA;AAEA,MAEIC,IAFEC,SAAUC,aAAAA,SAAQ,WAAA;AAAA,WAAM,IAAInC;EAAS,GAAE,CAAA,CAAA,GAGzCoC,SAA6BC,aAAAA,QAAO,CAAA,CAAA,GACpCC,SAAsBD,aAAAA,QAAY,WAAA;EAAK,CAAA;AAEX,gBAAA,OAArBR,KACTI,KAAiBJ,MAEjBI,KAAiBJ,GAAiBU,SAClCH,GAA2BI,UAAUX,GAAiBY,yBACtDH,GAAoBE,UAAUX,GAAiBa;AAGjD,MAAMC,SAAmBN,aAAAA,QAAOL,EAAAA;AAChCW,EAAAA,GAAiBH,UAAUR;AAE3B,MAAMY,SAAWP,aAAAA,QAAOP,EAAAA,GAElBe,SAAUV,aAAAA,SAAQ,WAAA;AACtB,QAAiBO,KAAqBJ,GAA9BE,SACSC,KAA4BL,GAArCI,SACSR,KAAkBW,GAA3BH;AAER,WAAO,SAAChB,IAAUsB,IAAAA;AAChB,UAGIC,IAHEC,KACJjB,MAAgBkB,GAAYlB,IAAc,WAAA;AAAA,eAAMP;MAAK,GAAEU,EAAAA,GAgCxDgB,KAAAC,GA7B2CC,GAC1C5B,IACA,SAAC6B,GAAAA;AAAY,YAAAC,IAAAA;AACX,gBAAQR,GAAOS,MAAAA;UACb,KAAK9D,GAAgBC;AACnB,mBAAOwC,GAAQsB,KAAKH,CAAAA;UAEtB,KAAK5D,GAAgBE;AACnB,mBAAOuC,GAAQuB,KAAKJ,CAAAA;UAEtB,KAAK5D,GAAgBM;AAEnB,mBADAmC,GAAQwB,MAAAA,GACRC,GAAA,CAAA,GACKN,CAAAA;UAKP,KAAK5D,GAAgBI;UACrB,KAAKJ,GAAgBK;UACrB,KAAKL,GAAgBG;AAAU,gBAAAgE,IACHd,KAAAA,GAAAA,KAAAA,GAAOe,OAAAA,KAAAA,GAAAA,EAAAA,KAAAA,GAAAA,EAAAA,KAAAA,GAAAA,GAA1BN,KAAIO,GAAA,CAAA,GAAKC,KAAMD,GAAAE,MAAA,CAAA;AAAA,aACtB/B,KAAAA,GAAeoB,GAAOL,EAAAA,GAAOO,EAAAA,EAASQ,MAAAA,IAAAA,GAAAA,EAAAA,CAAAA;AACtC;UAEF;AAAA,aACET,KAAArB,GAAeoB,GAAOL,EAAAA,GAAOF,GAAOS,IAAAA,EAAKU,MAAAX,IAAAjC,GAAIyB,GAAOe,OAAAA,CAAAA;QAAAA;MAE1D,CAAA,GACD,CAAA,GA7BIK,KAAShB,GAAA,CAAA,GAAE3C,KAAO2C,GAAA,CAAA,GAAE1C,KAAc0C,GAAA,CAAA;AAgFvC,aAjDAH,KAAamB,IAETlC,MACFA,GACEkC,IACA1C,IACA,EAAE+B,MAAMT,GAAOS,MAAMQ,QAAQjB,GAAOe,SAAStD,SAAAA,GAAAA,GAC7CyC,IACA,SAACmB,GAAAA;AACC,YAAIC,KAAkBhB,GAAmBc,IAAWC,CAAAA;AACpDpB,QAAAA,KAAaqB,GAAgB,CAAA,GAE7B7D,KAAAA,CAAAA,EAAAA,OAAAA,GAAcA,EAAAA,GAAOc,GAAK+C,GAAgB,CAAA,CAAA,CAAA,GAC1C5D,KAAAA,CAAAA,EAAAA,OAAAA,GAAqB4D,GAAgB,CAAA,CAAA,GAAE/C,GAAKb,EAAAA,CAAAA;MAC9C,CAAA,GAKF,CAACf,GAAgBC,MAAMD,GAAgBE,IAAAA,EAAM0E,SAC3CvB,GAAOS,IAAAA,KAETb,OAEAK,KAAauB,kBAAQvB,IAAYL,EAAAA,IAIhC,CAAA,EACID,OAAAA,GAAAA,EAAAA,GACHhD,CAAAA,GAAgBC,MAChBD,GAAgBE,MAChBF,GAAgBI,QAChBJ,GAAgBM,KAAAA,CAAAA,EAChBsE,SAASvB,GAAOS,IAAAA,MAEdT,GAAOS,SAAS9D,GAAgBG,WAClCsC,GAAQqC,YACNhE,IACAC,IACAsC,GAAO0B,UAAU1B,GAAO0B,OAAOC,IAAAA,IAExB3B,GAAOS,SAAS9D,GAAgBK,QACzCoC,GAAQwC,MAAMnE,IAASC,EAAAA,IAEvB0B,GAAQZ,IAAIf,IAASC,EAAAA,IAIlBuC;IAAAA;EAEV,GAAE,CAACb,IAASD,IAAgBF,EAAAA,CAAAA,GAEvB4C,SAAWC,aAAAA,aAAY,WAAA;AAAA,WAAMhC,GAASJ;EAAO,GAAE,CAAA,CAAA,GAC/CqC,SAAU1C,aAAAA,SAAQ,WAAA;AAAA,WAAM,IAAI2C,GAAWH,EAAAA;EAAAA,GAAW,CAACA,EAAAA,CAAAA,GAEnDI,SAAWH,aAAAA,aACf,SAAC9B,GAAAA;AACC,QAAMkC,KAAWnC,GAAQD,GAASJ,SAASM,CAAAA;AAC3CF,IAAAA,GAASJ,UAAUwC,IACnBH,GAAQI,OAAAA;EACV,GACA,CAACpC,IAASgC,EAAAA,CAAAA;AAGZK,mBAAAA,WAAU,WAAA;AACRL,IAAAA,GAAQI,OAAAA;EACV,GAAG,CAACJ,EAAAA,CAAAA;AAEJ,MAAM7B,SAAQb,aAAAA,SACZ,WAAA;AAAA,WACGJ,KAEGkB,GAAYlB,IAAc,WAAA;AAAA,aAAMa,GAASJ;IAAO,GAAEN,EAAAA,IADlD,CAAA;EAC0D,GAChE,CAACA,IAASH,EAAAA,CAAAA,GAGNoD,SAAUhD,aAAAA,SAAQ,WAAA;AACtB,QAAMiD,IAAcC,OAAOC,KAAKrD,GAAe,MAAM,IAAA,CAAA,GAEpCQ,KAA4BL,GAArCI;AAER,WACK4C,GAAAA,GAAAA,CAAAA,GAAAA,EAAYG,OAAO,SAACC,IAAOjC,IAAAA;AAE5B,aADAiC,GAAMjC,EAAAA,IAAQ,WAAA;AAAA,iBAAAkC,KAAAC,UAAAjF,QAAIoD,KAAO,IAAA8B,MAAAF,EAAAA,GAAAG,KAAA,GAAAA,KAAAH,IAAAG;AAAP/B,UAAAA,GAAO+B,EAAAA,IAAAF,UAAAE,EAAAA;AAAA,eAAKb,GAAS,EAAExB,MAAAA,IAAMM,SAAAA,GAAAA,CAAAA;MAAU,GAClD2B;IAAAA,GACN,CAAS,CAAA,CAAA,GAAC,CAAA,GAAA,EACbtD,SAAS,EACPsB,MAAI,WAAA;AACF,aAAOuB,GAAS,EACdxB,MAAM9D,GAAgBC,KAAAA,CAAAA;IAEzB,GACD+D,MAAI,WAAA;AACF,aAAOsB,GAAS,EACdxB,MAAM9D,GAAgBE,KAAAA,CAAAA;IAEzB,GACD+D,OAAO,WAAA;AACL,aAAOqB,GAAS,EACdxB,MAAM9D,GAAgBM,MAAAA,CAAAA;IAEzB,GACD8F,UAAU,SAACpB,IAAAA;AACT,aAAAd,GAAA,CAAA,GACKyB,EACAU,OAAO,SAACvC,IAAAA;AAAI,eAAA,CAAMd,GAAwB4B,SAASd,EAAAA;MAAK,CAAA,EACxDgC,OAAO,SAACC,IAAOjC,IAAAA;AASd,eARAiC,GAAMjC,EAAAA,IAAQ,WAAA;AAAA,mBAAAwC,KAAAL,UAAAjF,QAAIoD,KAAO,IAAA8B,MAAAI,EAAAA,GAAAC,KAAA,GAAAA,KAAAD,IAAAC;AAAPnC,YAAAA,GAAOmC,EAAAA,IAAAN,UAAAM,EAAAA;AAAA,iBACvBjB,GAAS,EACPxB,MAAM9D,GAAgBG,UACtBiE,SAAUN,CAAAA,EAAAA,EAASM,OAAAA,EAAAA,GACnBW,QAAQ,EACNC,MAAMA,GAAAA,EAAAA,CAAAA;QAER,GACGe;MAAAA,GACN,CAAA,CAAA,CAAA;IAER,GACDS,QAAQ,WAAA;AACN,aAAAtC,GAAA,CAAA,GACKyB,EACAU,OAAO,SAACvC,IAAAA;AAAI,eAAA,CAAMd,GAAwB4B,SAASd,EAAAA;MAAK,CAAA,EACxDgC,OAAO,SAACC,IAAOjC,IAAAA;AAMd,eALAiC,GAAMjC,EAAAA,IAAQ,WAAA;AAAA,mBAAA2C,KAAAR,UAAAjF,QAAIoD,KAAO,IAAA8B,MAAAO,EAAAA,GAAAC,KAAA,GAAAA,KAAAD,IAAAC;AAAPtC,YAAAA,GAAOsC,EAAAA,IAAAT,UAAAS,EAAAA;AAAA,iBACvBpB,GAAS,EACPxB,MAAM9D,GAAgBI,QACtBgE,SAAO,CAAGN,EAAAA,EAAInC,OAAKyC,EAAAA,EAAAA,CAAAA;QACnB,GACG2B;MAAAA,GACN,CAAA,CAAA,CAAA;IAER,GACDd,OAAO,WAAA;AACL,aAAAf,GAAA,CAAA,GACKyB,EACAU,OAAO,SAACvC,IAAAA;AAAI,eAAA,CAAMd,GAAwB4B,SAASd,EAAAA;MAAK,CAAA,EACxDgC,OAAO,SAACC,IAAOjC,IAAAA;AAMd,eALAiC,GAAMjC,EAAAA,IAAQ,WAAA;AAAA,mBAAA6C,KAAAV,UAAAjF,QAAIoD,KAAO,IAAA8B,MAAAS,EAAAA,GAAAC,KAAA,GAAAA,KAAAD,IAAAC;AAAPxC,YAAAA,GAAOwC,EAAAA,IAAAX,UAAAW,EAAAA;AAAA,iBACvBtB,GAAS,EACPxB,MAAM9D,GAAgBK,OACtB+D,SAAO,CAAGN,EAAAA,EAAInC,OAAKyC,EAAAA,EAAAA,CAAAA;QACnB,GACG2B;MAAAA,GACN,CAAA,CAAA,CAAA;IAET,EAAA,EAAA,CAAA;EAGN,GAAG,CAACT,IAAU9C,EAAAA,CAAAA;AAEd,aAAOE,aAAAA,SACL,WAAA;AAAA,WAAO,EACLwC,UAAAA,IACA2B,WAAW,SAACC,GAAWpC,IAAIqC,IAAAA;AAAe,aACxC3B,GAAQyB,UAAUC,GAAWpC,IAAIqC,EAAAA;IAAgB,GACnDrB,SAAAA,IACAnC,OAAAA,IACAd,SAAAA,GAAAA;EACD,GACD,CAACiD,IAASnC,IAAO6B,IAASF,IAAUzC,EAAAA,CAAAA;AAExC;AAAA,SAEgBe,GACdlB,GACA4C,IACAzC,IAAAA;AAEA,MAAMuE,KAAUpB,OAAOC,KAAKvD,EAAAA,CAAAA,EAAgBwD,OAAO,SAACC,IAAOnF,IAAAA;AACzD,WAAAsD,GAAAA,GAAA,CAAA,GACK6B,EAAAA,GAAK,CAAA,GAAArF,EAAA,CAAA,GACPE,IAAM,WAAA;AAAiB,UAAAqG;AACtB,cAAOA,KAAA3E,EAAa4C,GAAAA,CAAAA,GAAYtE,EAAAA,EAAa4D,MAAAyC,IAAAhB,SAAAA;IAC9C,CAAA,CAAA;EAEJ,GAAE,CAA4C,CAAA;AAE/C,SAAA/B,GAAAA,GAAA,CAAA,GACK8C,EAAAA,GAAO,CAAA,GAAA,EACVvE,SAAS,EACPT,SAAS,WAAA;AAAA,WAAMS,GAAQT,QAAAA;EAAS,GAChCE,SAAS,WAAA;AAAA,WAAMO,GAAQP,QAAAA;EAAS,EAAA,EAAA,CAAA;AAGtC;AA3aAgF,EAAAA,GACAC,EAAAA;AA0aC,IAEK9B,KAAO,WAAA;AAIX,WAAAA,EAAYH,IAAAA;AAAQ1E,MAAAC,MAAA4E,CAAAA,GAAA3E,EAAAD,MAAA,YAAA,MAAA,GAAAC,EAAAD,MAAA,eAFQ,CAAA,CAAA,GAG1BA,KAAKyE,WAAWA;EAClB;AA6BC,SA3BDvE,GAAA0E,GAAA,CAAA,EAAAzE,KAAA,aAAAC,OAIA,SACEiG,IACAM,IACAL,IAAAA;AAAyB,QAAAM,KAAA5G,MAEnB6G,KAAa,IAAIC,GACrB,WAAA;AAAA,aAAMT,GAAUO,GAAKnC,SAAAA,CAAAA;IAAAA,GACrBkC,IACAL,EAAAA;AAGF,WADAtG,KAAK+G,YAAYC,KAAKH,EAAAA,GACf7G,KAAKiH,YAAYC,KAAKlH,MAAM6G,EAAAA;EACrC,EAAA,GAAC,EAAA1G,KAAA,eAAAC,OAED,SAAYyG,IAAAA;AACV,QAAI7G,KAAK+G,YAAYxG,QAAQ;AAC3B,UAAM4G,KAAQnH,KAAK+G,YAAYK,QAAQP,EAAAA;AACvC,UAAIM,KAAAA;AAAY,eAAOnH,KAAK+G,YAAYM,OAAOF,IAAO,CAAA;IACxD;EACF,EAAA,GAAC,EAAAhH,KAAA,UAAAC,OAED,WAAA;AACEJ,SAAK+G,YAAYO,QAAQ,SAACT,IAAAA;AAAU,aAAKA,GAAWU,QAAAA;IAAAA,CAAAA;EACtD,EAAA,CAAA,CAAA,GAAC3C;AAAA,EAnCU;AAFZ,IAwCKkC,KAAU,WAAA;AAYd,WAAYT,EAAAA,IAAWM,IAAAA;AAAiC,QAAvBL,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,KAAAA,UAAAA,CAAAA;AAAuBvG,MAAAC,MAAA8G,CAAAA,GAAA7G,EAAAD,MAAA,aAAA,MAAA,GAAAC,EAAAD,MAAA,aAAA,MAAA,GAAAC,EAAAD,MAAA,YAAA,MAAA,GAAAC,EAAAD,MAAA,MAAA,MAAA,GACtDA,KAAKqG,YAAYA,IACjBrG,KAAK2G,WAAWA,IAGZL,MAAiBtG,KAAKuH,QAAAA;EAC5B;AAaC,SAbArH,GAAA4G,GAAA,CAAA,EAAA3G,KAAA,WAAAC,OAED,WAAA;AACE,QAAA;AACE,UAAMoH,KAAYxH,KAAKqG,UAAAA;AAClBoB,6BAAAA,SAAYD,IAAWxH,KAAK0H,SAAAA,MAC/B1H,KAAK0H,YAAYF,IACbxH,KAAK2G,YAAU3G,KAAK2G,SAAS3G,KAAK0H,SAAAA;IAK1C,SAHSC,IAAAA;AAEPC,cAAQC,KAAKF,EAAAA;IACf;EACF,EAAA,CAAA,CAAA,GAACb;AAAA,EA/Ba;AAxCf,ICxbYgB,KAAa,SAACC,GAAAA;AACzB,MASIA,KAAAA,EAAGC,sBAAAA,GARLC,KAAAA,GAAAA,GACAC,KAAAA,GAAAA,GACAC,KAAAA,GAAAA,KACAC,KAAAA,GAAAA,MACAC,KAAAA,GAAAA,QACAC,KAAAA,GAAAA,OACAC,KAAAA,GAAAA,OACAC,KAAAA,GAAAA,QAGIC,KAAQC,OAAOC,iBAAiBZ,CAAAA,GAEhCa,KAAS,EACbR,MAAMS,SAASJ,GAAMK,UAAAA,GACrBR,OAAOO,SAASJ,GAAMM,WAAAA,GACtBV,QAAQQ,SAASJ,GAAMO,YAAAA,GACvBb,KAAKU,SAASJ,GAAMQ,SAAAA,EAAAA,GAGhBC,KAAU,EACdd,MAAMS,SAASJ,GAAMU,WAAAA,GACrBb,OAAOO,SAASJ,GAAMW,YAAAA,GACtBf,QAAQQ,SAASJ,GAAMY,aAAAA,GACvBlB,KAAKU,SAASJ,GAAMa,UAAAA,EAAAA;AAqDtB,SAAO,EACLrB,GAAAA,IACAC,GAAAA,IACAC,KAAAA,IACAC,MAAAA,IACAC,QAAAA,IACAC,OAAAA,IACAC,OAAAA,IACAC,QAAAA,IACAe,YAAYC,KAAKC,MAAMlB,KAAQK,GAAOR,OAAOQ,GAAON,KAAAA,GACpDoB,aAAaF,KAAKC,MAAMjB,KAASI,GAAOT,MAAMS,GAAOP,MAAAA,GACrDO,QAAAA,IACAM,SAAAA,IACAS,QAAQ5B,EAAG6B,iBAAAA,CAAAA,CA/DO,SAACC,IAAAA;AACnB,QAAMC,KAAmBnB,iBAAiBkB,EAAAA;AAE1C,QAAA,EAAIpB,GAAMsB,YAA+B,cAAnBtB,GAAMsB,YAIF,WAAtBD,GAAYE,SAIY,WAAxBF,GAAYG,WAKU,WAAxBH,GAAYG,WACsB,aAAlCH,GAAY,gBAAA,IAFd;AAOA,cAAQrB,GAAMyB,UAAAA;QACZ,KAAK;QACL,KAAK;AACH;QACF;AACE;MAAA;AAGJ,cAAQnC,EAAGoC,SAAAA;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAA;MAAO;AAGX,cAAQ1B,GAAMwB,SAAAA;QACZ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAA;MAAO;IAxBX;EAAA,EA2C0ClC,EAAG6B,aAAAA,EAAAA;AAEjD;AC/EgB,SAAAQ,GACdC,GACAhE,IAAAA;AAKA,QAAA,EAAMD,WAAEA,IAAS3B,UAAEA,IAAQQ,SAAEA,IAAOnC,OAAEA,GAAAA,IAAUuH,GAE1CC,SAAUnI,aAAAA,QAAAA,IAAO,GACjBuF,SAAYvF,aAAAA,QAAY,IAAA,GACxBoI,SAAepI,aAAAA,QAAOkE,EAAAA;AAC5BkE,EAAAA,GAAajI,UAAU+D;AAEvB,QAAMmE,SAAY9F,aAAAA,aACfgD,CAAAA,QACQ,EAAA,GAAKA,IAAWzC,SAAAA,IAASnC,OAAAA,GAAAA,IAElC,CAACmC,IAASnC,EAAAA,CAAAA;AAIRwH,EAAAA,GAAQhI,WAAW+D,OACrBqB,GAAUpF,UAAU+D,GAAU5B,GAAAA,GAAY3B,EAAAA,GAC1CwH,GAAQhI,UAAAA;AAGV,QAAA,CAAOmI,IAAiBC,EAAAA,QAAsBC,aAAAA,UAC5CH,GAAU9C,GAAUpF,OAAAA,CAAAA;AAmBtB,aAfA0C,aAAAA,WAAU,MAAA;AACR,QAAIiC;AASJ,WARIsD,GAAajI,YACf2E,KAAcb,GACX9D,CAAAA,OAAYiI,GAAajI,QAAQA,IAASQ,EAAAA,GAC1C4E,CAAAA,OAAAA;AACCgD,MAAAA,GAAmBF,GAAU9C,EAAAA,CAAAA;IAAW,CAAA,IAIvC,MAAA;AACDT,MAAAA,MAAaA,GAAAA;IAAa;EAC/B,GACA,CAACuD,IAAW1H,IAAOsD,EAAAA,CAAAA,GAEfqE;AACT;ACnDaG,IC+BDC;AD/BCD,IAAAA,KAAc,WAAA;AAAkB,SAAKE,OAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,IAAAA,UAAAA,CAAAA,IAAP,EAAA;AAAmB;AAAjDF,IEDAG,KAAiB,WAAA;AAAA,WAAAA,IAAAA;AAAAhL,MAAAC,MAAA+K,CAAAA,GAAA9K,EAAAD,MAAA,aAAA,IACC,GAAIC,EAEoBD,MAAA,gBAAA,oBAAIgL,SAAAA,GAAS/K,EACbD,MAAA,YAAA,oBAAIiL,KAAAA;EAAK;AA0G7D,SA1G6D/K,GAAA6K,GAAA,CAAA,EAAA5K,KAAA,gBAAAC,OAEtD,SAAa8K,IAAAA;AACnB,QAAMC,KAAanL,KAAKoL,aAAaC,IAAIH,EAAAA;AACzC,QAAIC;AACF,aAAOA;AAGT,QAAMG,KAAQV,GAAAA;AAGd,WADA5K,KAAKoL,aAAaG,IAAIL,IAASI,EAAAA,GACxBA;EACT,EAAA,GAAC,EAAAnL,KAAA,kBAAAC,OAED,SAAe8K,IAAsBM,IAAAA;AACnC,QAAMC,KAAYzL,KAAK0L,aAAaR,EAAAA;AACpC,WAAUM,GAAAA,OAAAA,IAAAA,IAAAA,EAAAA,OAAkBC,EAAAA;EAC9B,EAAA,GAAC,EAAAtL,KAAA,YAAAC,OAED,SAAS8K,IAAsBS,IAAAA;AAAqC,QAAA/E,KAAA5G,MAC5D4L,KAAoB5L,KAAK6L,aAAaX,IAASS,GAAiBG,IAAAA;AAEtE,QAAIF,IAAmB;AACrB,cAAIG,oBAAAA,SAAQJ,GAAiBK,UAAUJ,GAAkBI,QAAAA;AACvD,eAAOJ;AAGT5L,WAAK6L,aAAaX,IAASS,GAAiBG,IAAAA,EAAMG,QAAAA;IACpD;AAEA,QAAIC,KAA6B,MAE3BC,KAAKnM,KAAKoM,eAAelB,IAASS,GAAiBG,IAAAA;AA+BzD,WA9BA9L,KAAKqM,SAASd,IAAIY,IAAI,EACpBA,IAAAA,IACAH,UAAUL,GAAiBK,UAC3BM,QAAQ,WAAA;AACFJ,MAAAA,MACFA,GAAAA,GAGFA,KAAUP,GAAiBY,UACzBrB,IACAS,GAAiBK,UACjBL,GAAiBa,OAAAA;IAEpB,GACDP,SAAS,WAAA;AACFC,MAAAA,MAILA,GAAAA;IACD,GACDO,QAAQ,WAAA;AACN,aAAO7F,GAAK6F,OAAON,EAAAA;IACrB,EAAA,CAAA,GAGEnM,KAAK0M,aACP1M,KAAKqM,SAAShB,IAAIc,EAAAA,EAAIG,OAAAA,GAGjBtM,KAAKqM,SAAShB,IAAIc,EAAAA;EAC3B,EAAA,GAAC,EAAAhM,KAAA,OAAAC,OAED,SAAI+L,IAAAA;AACF,WAAOnM,KAAKqM,SAAShB,IAAIc,EAAAA;EAC3B,EAAA,GAAC,EAAAhM,KAAA,UAAAC,OAED,SAAO+L,IAAAA;AACL,QAAMI,KAAYvM,KAAKqL,IAAIc,EAAAA;AACtBI,IAAAA,OAILA,GAAUN,QAAAA,GACVjM,KAAKqM,SAASM,OAAOJ,GAAUJ,EAAAA;EACjC,EAAA,GAAC,EAAAhM,KAAA,UAAAC,OAED,WAAA;AACEJ,SAAK0M,YAAAA,MACL1M,KAAKqM,SAAS/E,QAAQ,SAACsF,IAAAA;AACrBA,MAAAA,GAAWN,OAAAA;IACb,CAAA;EACF,EAAA,GAAC,EAAAnM,KAAA,WAAAC,OAED,WAAA;AACEJ,SAAK0M,YAAAA,OACL1M,KAAKqM,SAAS/E,QAAQ,SAACsF,IAAAA;AACrBA,MAAAA,GAAWX,QAAAA;IACb,CAAA;EACF,EAAA,GAAC,EAAA9L,KAAA,gBAAAC,OAED,SAAa8K,IAAsBM,IAAAA;AACjC,WAAOxL,KAAKqL,IAAIrL,KAAKoM,eAAelB,IAASM,EAAAA,CAAAA;EAC/C,EAAA,GAAC,EAAArL,KAAA,mBAAAC,OAED,SAAgB8K,IAAsBM,IAAAA;AACpC,WAAOxL,KAAKyM,OAAOzM,KAAKoM,eAAelB,IAASM,EAAAA,CAAAA;EAClD,EAAA,GAAC,EAAArL,KAAA,SAAAC,OAED,WAAA;AACEJ,SAAKiM,QAAAA,GACLjM,KAAKoL,eAAe,oBAAIJ,WACxBhL,KAAKqM,WAAW,oBAAIpB;EACtB,EAAA,CAAA,CAAA,GAACF;AAAA,EA9G2B;AAAA,CDgC9B,SAAYF,GAAAA;AACVA,IAAAA,EAAA,kBAAA,CAAA,IAAA,mBACAA,EAAAA,EAAA,iBAAA,CAAA,IAAA;AACD,EAHWA,OAAAA,KAGX,CAAA,EAAA;AEjCD,IAAsBgC,KAAa,WAAA;AASjC,WAAAA,EAAYL,IAAAA;AAAWzM,MAAAC,MAAA6M,CAAAA,GAAA5M,EAAAD,MAAA,WAAA,MAAA,GAAAC,EANeD,MAAA,YAAA,IAAI+K,IAAAA,GAAmB9K,EACED,MAAA,eAAA,oBAAI8M,KAAAA,GAMjE9M,KAAKwM,UAAUA;EACjB;AA0LC,SA1LAtM,GAAA2M,GAAA,CAAA,EAAA1M,KAAA,UAAAC,OAED,SAAO6D,IAAAA;AAAsC,QAAA2C,KAAA5G;AAE3C,WADAA,KAAK+G,YAAY3F,IAAI6C,EAAAA,GACd,WAAA;AAAA,aAAM2C,GAAKG,YAAY4F,OAAO1I,EAAAA;IAAG;EAC1C,EAAA,GAAC,EAAA9D,KAAA,WAAAC,OAED,WAAA;AACMJ,SAAK+M,aACP/M,KAAK+M,UAAAA,GAGP/M,KAAKqM,SAASJ,QAAAA,GAEdjM,KAAK+G,YAAYO,QAAQ,SAAC0F,IAAAA;AACxBA,MAAAA,GAASnC,GAAoBoC,eAAAA;IAC/B,CAAA;EACF,EAAA,GAAC,EAAA9M,KAAA,UAAAC,OAED,WAAA;AACMJ,SAAKkN,YACPlN,KAAKkN,SAAAA,GAGPlN,KAAKqM,SAASC,OAAAA,GAEdtM,KAAK+G,YAAYO,QAAQ,SAAC0F,IAAAA;AACxBA,MAAAA,GAASnC,GAAoBsC,cAAAA;IAC/B,CAAA;EACF,EAAA,GAAC,EAAAhN,KAAA,WAAAC,OAED,WAAA;AACEJ,SAAKiM,QAAAA,GACLjM,KAAK+G,YAAYvD,MAAAA,GACjBxD,KAAKqM,SAAS7I,MAAAA;EAChB,EAAA,GAAC,EAAArD,KAAA,yBAAAC,OAED,SACE2H,IACAqF,IACAJ,IACAR,IAAAA;AAEA,QAAMa,KAAiB,SAACzO,IAAAA;AAAAA,OAAAA,SC3D1BA,IACAwO,IACArF,IAAAA;AAGKnJ,QAAAA,GAAE0O,UACL1O,GAAE0O,QAAQ,EACRC,iBAAiB,WAAA;QAAQ,GACzBC,eAAe,CAAE,EAAA;AAMrB,iBAFMC,KAAoB7O,GAAE0O,SAAS1O,GAAE0O,MAAME,cAAcJ,EAAAA,KAAe,CAAA,GAEjErO,KAAI,GAAGA,KAAI0O,GAAiBlN,QAAQxB,MAAK;AAChD,cAAM2O,KAAkBD,GAAiB1O,EAAAA;AAEzC,cAAIgJ,OAAO2F,MAAmB3F,GAAG4F,SAASD,EAAAA;AACxC,mBAAA;QAEJ;AAEA,eAAA;MACF,GDqCsC9O,IAAGwO,IAAWrF,EAAAA,MAC5CnJ,GAAE0O,MAAMC,kBAAkB,WAAA;AACnB3O,QAAAA,GAAE0O,MAAME,cAAcJ,EAAAA,MACzBxO,GAAE0O,MAAME,cAAcJ,EAAAA,IAAa,CAAA,IAGrCxO,GAAE0O,MAAME,cAAcJ,EAAAA,EAAWpG,KAAKe,EAAAA;MAAAA,GAGxCiF,GAASpO,EAAAA;IAAAA;AAMb,WAFAmJ,GAAG6F,iBAAiBR,IAAWC,IAAgBb,EAAAA,GAExC,WAAA;AAAA,aAAMzE,GAAG8F,oBAAoBT,IAAWC,IAAgBb,EAAAA;IAAQ;EACzE,EAAA,GAKA,EAAArM,KAAA,yBAAAC,OAGA,WAAA;AAAqB,QAAA0N,KAAA9N,MACb+N,KAAW/N,KAAK+N,SAAAA,GAKhBC,KAAkC,oBAAIlB,OAExCmB,KAAAA,OACEC,KAGF,oBAAIjD;AAyCR,WAAO,EACL2B,YAxCiBzH,OAAOgJ,QAAQJ,EAAAA,EAAU1I,OAG1C,SAACC,IAAK8I,IAAAA;AAAA,UAAAC,KAAApL,GAAAmL,IAAA,CAAA,GAAGtC,KAAIuC,GAAA,CAAA,GAAEC,KAAOD,GAAA,CAAA;AAAA,aACjB/I,GAAAA,GAAAA,CAAAA,GAAAA,EAAAA,GACFwG,CAAAA,GAAAA,EAAAA,CAAAA,GAAAA,IAAO,SAAC/D,IAAIiE,IAAUQ,IAAAA;AACrB,YAAM+B,KAAoB,WAAA;AACxB,cAAMhC,KAAYuB,GAAKzB,SAASmC,SAASzG,IAAI,EAC3CiE,UAAAA,IACAF,MAAAA,IACAU,SAAAA,IACAD,WAAW+B,GAAAA,CAAAA;AAIb,iBADAN,GAAmB5M,IAAImL,GAAUJ,EAAAA,GAC1BI;QAAAA;AAkBT,eAfA2B,GAAqB3C,IACnBuC,GAAKzB,SAASD,eAAerE,IAAI+D,EAAAA,GACjCyC,EAAAA,GASEN,MACFM,GAAAA,GAGKxG;MACR,CAAA,CAAA;IACD,GACF,CAAE,CAAA,GAKFyG,UAAU,WAAA;AACRP,MAAAA,KAAAA,MAEAC,GAAqB5G,QAAQ,SAACiH,IAAAA;AAC5BA,QAAAA,GAAAA;MACF,CAAA;IACD,GACDrC,SAAS,WAAA;AACP+B,MAAAA,KAAAA,OAEAD,GAAmB1G,QAAQ,SAACmH,IAAAA;AAAW,eACrCX,GAAKzB,SAASI,OAAOgC,EAAAA;MAAAA,CAAAA;IAEzB,EAAA;EAEJ,EAAA,GAAC,EAAAtO,KAAA,UAAAC,OAED,SACEiD,IAGAqL,IAAAA;AAEA,WAAO,IAAIrL,GAAKrD,MAAM0O,EAAAA;EACxB,EAAA,GAEA,EAAAvO,KAAA,uBAAAC,OACU,SACRuO,IACA1K,IAAAA;AAEA,QAAM2K,KAAsB,CAAA,GACtBb,KAAWY,GAASZ,SAAAA,GAEpBc,KAAkB,IAAIC,MAAMf,IAAU,EAC1C1C,KAAK,SAAC0D,IAAQ5O,IAAU6O,IAAAA;AACtB,aAAI7O,MAAO4N,MAAa,IACfkB,QAAQ5D,IAAI0D,IAAQ5O,IAAK6O,EAAAA,IAG3B,SAACjH,IAAAA;AAAe,iBAAAxC,KAAAC,UAAAjF,QAAR2O,KAAI,IAAAzJ,MAAAF,KAAA,IAAAA,KAAA,IAAA,CAAA,GAAAG,KAAA,GAAAA,KAAAH,IAAAG;AAAJwJ,UAAAA,GAAIxJ,KAAA,CAAA,IAAAF,UAAAE,EAAAA;AACjB,YAAMwG,KAAU6B,GAAS5N,EAAAA,EAAT4N,MAAAA,IAAchG,CAAAA,EAAAA,EAAOmH,OAAAA,EAAAA,CAAAA;AAChChD,QAAAA,MAIL0C,GAAoB5H,KAAKkF,EAAAA;MAAAA;IAE7B,EAAA,CAAA;AAKF,WAFAjI,GAAG4K,EAAAA,GAEI,WAAA;AACLD,MAAAA,GAAoBtH,QAAQ,SAAC4E,IAAAA;AAC3BA,QAAAA,GAAAA;MACF,CAAA;IAAA;EAEJ,EAAA,GAEA,EAAA/L,KAAA,WAAAC,OACA,SAAQ6D,IAAAA;AACN,WAAOjE,KAAKmP,oBAAoBnP,MAAMiE,EAAAA;EACxC,EAAA,CAAA,CAAA,GAAC4I;AAAA,EArMgC;AAAnC,IEPsBuC,KAGpB,SAAAC,GAAAA;AAAAA,GAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,cAAAA,OAAAA,MAAAA,SAAAA;AAAAA,YAAAA,IAAAA,UAAAA,oDAAAA;AAAAA,IAAAA,GAAAA,YAAAA,OAAAA,OAAAA,MAAAA,GAAAA,WAAAA,EAAAA,aAAAA,EAAAA,OAAAA,IAAAA,UAAAA,MAAAA,cAAAA,KAAAA,EAAAA,CAAAA,GAAAA,OAAAA,eAAAA,IAAAA,aAAAA,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,MAAAA,EAAAA,IAAAA,EAAAA;EAAAA,EAAAD,IAAQvC,EAAAA;AAAR,MAAAvO,IAAAC,IAAA+Q,MAAAA,KAAAF,IAAAA,KAAAA,WAAAA;AAAAA,QAAAA,eAAAA,OAAAA,WAAAA,CAAAA,QAAAA;AAAAA,aAAAA;AAAAA,QAAAA,QAAAA,UAAAA;AAAAA,aAAAA;AAAAA,QAAAA,cAAAA,OAAAA;AAAAA,aAAAA;AAAAA,QAAAA;AAAAA,aAAAA,QAAAA,UAAAA,QAAAA,KAAAA,QAAAA,UAAAA,SAAAA,CAAAA,GAAAA,WAAAA;MAAAA,CAAAA,CAAAA,GAAAA;IAAAA,SAAAA,IAAAA;AAAAA,aAAAA;IAAAA;EAAAA,EAAAA,GAAAA,WAAAA;AAAAA,QAAAA,IAAAA,KAAAA,EAAAA,EAAAA;AAAAA,QAAAA,IAAAA;AAAAA,UAAAA,KAAAA,EAAAA,IAAAA,EAAAA;AAAAA,MAAAA,KAAAA,QAAAA,UAAAA,IAAAA,WAAAA,EAAAA;IAAAA;AAAAA,MAAAA,KAAAA,GAAAA,MAAAA,MAAAA,SAAAA;AAAAA,WAAAA,SAAAA,IAAAA,IAAAA;AAAAA,UAAAA,OAAAA,YAAAA,OAAAA,MAAAA,cAAAA,OAAAA;AAAAA,eAAAA;AAAAA,UAAAA,WAAAA;AAAAA,cAAAA,IAAAA,UAAAA,0DAAAA;AAAAA,aAAAA,GAAAA,EAAAA;IAAAA,EAAAA,MAAAA,EAAAA;EAAAA;AAIA,WAAYG,GAAAA,IAAY/C,IAAAA;AAAW,QAAA5F;AAkB9B,WAlB8B7G,EAAAC,MAAAoP,EAAAA,GAClBnP,EAAAuP,GAAf5I,KAAA0I,GAAAG,KAAAzP,MAAMwM,EAAAA,CAAAA,GAAS,WAAA,MAAA,GAAAvM,EAAAuP,GAAA5I,EAAAA,GAAA,oCAAA,MAAA,GACfA,GAAK2I,UAAUA,IACf3I,GAAK4F,UAAUA,IAGf5F,GAAK8I,mCAAmC9I,GAAK2I,QAAQI,OAAO,SAACC,IAAAA;AAC3D,cAAQA,IAAAA;QACN,KAAK/E,GAAoBsC;AACvB,iBAAOvG,GAAK0F,OAAAA;QAEd,KAAKzB,GAAoBoC;AACvB,iBAAOrG,GAAKqF,QAAAA;QAEd;AACE;MAAA;IAGN,CAAA,GAAGrF;EACL;AAUC,SARD1G,GAAAkP,IAAA,CAAA,EAAAjP,KAAA,WAAAC,OACA,SAAQ6D,IAAAA;AACN,WAAOjE,KAAKmP,oBAAoBnP,KAAKuP,SAAStL,EAAAA;EAChD,EAAA,GAAC,EAAA9D,KAAA,WAAAC,OAED,WAAA;AACEyP,IAAAA,GAAAC,EAAAV,GAAAW,SAAAA,GAAA,WAAA/P,IAAAA,EAAAyP,KAAAzP,IAAAA,GACAA,KAAK0P,iCAAAA;EACP,EAAA,CAAA,CAAA,GAACN;AAAA,EAjCD;ACDF,SAASY,GAAOC,GAAUC,IAAAA;AACpBA,EAAAA,OACiB,cAAA,OAARD,IACTA,EAAIC,EAAAA,IAEJD,EAAI3N,UAAU4N;AAGpB;AAEgB,SAAAC,GACdjF,GACAkF,IAAAA;AAEA,QAAMC,KAAcnF,EAAQ+E;AAQ5B,SAPAK,UACyB,YAAA,OAAhBD,IACP,gPAAA,OAWOE,aAAAA,cAAarF,GANjBmF,KAM0B,EAC3BJ,KAAMC,CAAAA,OAAAA;AACJF,OAAOK,IAAaH,EAAAA,GACpBF,GAAOI,IAAQF,EAAAA;EAAK,EAAA,IAPK,EAC3BD,KAAKG,GAAAA,CAAAA;AAUX;AAUM,SAAUI,GACdC,GAAAA;AAEA,SAAO,CAACC,KAAgB,SAASxB,OAAAA;AAE/B,QAAA,KAAKyB,aAAAA,gBAAeD,EAAAA,GAAgB;AAClC,UAAA,CAAKA;AACH;AAGF,YAAMR,KAAOQ;AAEb,aADAR,MAAQO,EAAKP,IAAAA,GAAShB,EAAAA,GACfgB;IACR;AAKD,UAAMhF,KAA+BwF;AAGrC,WA7BJ,SAA0CxF,IAAAA;AACxC,UAA4B,YAAA,OAAjBA,GAAQ7H;AAInB,cAAM,IAAIuN;IACZ,EAqBqC1F,EAAAA,GAE1BiF,GAAajF,IAASuF,CAAAA;EAAK;AAEtC;AAIM,SAAUI,GACdjE,GAAAA;AAEA,SAAOzH,OAAOC,KAAKwH,CAAAA,EAAYvH,OAAO,CAACC,IAAOnF,QAC5CmF,GAAMnF,EAAAA,IAAOqQ,GAA2B,IAAItB,OAEnCtC,EAAWzM,EAAAA,EAAAA,GAAQ+O,EAAAA,CAAAA,GAGrB5J,KACN,CAAE,CAAA;AACP;ACjFO,IAAMwL,KAAkB,CAAA,EAC7BrI,OAAAA,GACAsI,WAAAA,IACAC,WAAAA,GAAAA,MAAAA;AAEA,QAAMC,KACJC,aAAAA,QAAAC,cAAA,OAAA,EACEJ,WAAWA,IACXtI,OAAO,EACLyB,UAAU,SACVD,SAAS,SACTmH,SAAS,GACTC,aAAa,SACbC,aAAa,OACbC,aAAa,eACbC,QAAQ,OAAA,GACL/I,EAAAA,EAAAA,CAAAA;AAKT,SAAIuI,MAAaA,GAAUS,kBAAkBC,WACpCC,iBAAAA,QAASC,aAAaX,IAAWD,GAAUS,cAAcI,IAAAA,IAG3DZ;AAAS;AE7BX,IAAMa,KAAqB,SAACC,GAAMC,IAAAA;AACvC,MAAIC,KAAkCF,wBAAAA,OAAAA,GAA4C,wCAAA,GAE1EG,KAAiBF,GAAjBE,SAASC,KAAQH,GAARG;AAEbD,EAAAA,OACFD,MAAO,eAAAG,OAAmBF,IAAkB,WAAA,IAI1CC,OACFF,MAAO,IAAAG,OAAQD,IAAM,GAAA,IAIvBE,QAAQC,KAAKL,EAAAA;AACf;AAhBO,ICLMM,KAAe,WAAA;AAAH,SAA2B,eAAA,OAAXC;AAAsB;ADKxD,ICHMC,KAAU,WAAA;AAAH,SAClBF,GAAAA,KAAkB,SAASG,KAAKF,OAAOG,UAAUC,SAAAA;AAAU;ADEtD,ICAMC,KAAa,WAAA;AAAH,SACrBN,GAAAA,KAAkB,UAAUG,KAAKF,OAAOG,UAAUC,SAAAA;AAAU;;;;;;;;ACGvD,IAAME,KAAcC,cAAAA,QAAMC,cAA+B,IAAA;AAAzD,IAMMC,KAAe,CAAA,EAC1BC,IAAAA,GACAC,SAAAA,KAAAA,OACAC,UAAAA,GAAAA,MAGEL,cAAAA,QAACM,cAAAP,GAAYQ,UAAAA,EAASC,OAAO,EAAEL,IAAAA,GAAIC,SAAAA,GAAAA,EAAAA,GAChCC,EAAAA;AAAAA,SAAAA,GAAAA,GAAAA,IAAAA;AAAAA,MAAAA,KAAAA,OAAAA,KAAAA,CAAAA;AAAAA,MAAAA,OAAAA,uBAAAA;AAAAA,QAAAA,KAAAA,OAAAA,sBAAAA,CAAAA;AAAAA,IAAAA,OAAAA,KAAAA,GAAAA,OAAAA,SAAAA,IAAAA;AAAAA,aAAAA,OAAAA,yBAAAA,GAAAA,EAAAA,EAAAA;IAAAA,CAAAA,IAAAA,GAAAA,KAAAA,MAAAA,IAAAA,EAAAA;EAAAA;AAAAA,SAAAA;AAAAA;AAAAA,SAAAA,GAAAA,GAAAA;AAAAA,WAAAA,KAAAA,GAAAA,KAAAA,UAAAA,QAAAA,MAAAA;AAAAA,QAAAA,KAAAA,QAAAA,UAAAA,EAAAA,IAAAA,UAAAA,EAAAA,IAAAA,CAAAA;AAAAA,IAAAA,KAAAA,IAAAA,GAAAA,OAAAA,EAAAA,GAAAA,IAAAA,EAAAA,QAAAA,SAAAA,IAAAA;AAAAA,MAAAA,IAAAA,GAAAA,IAAAA,GAAAA,EAAAA,CAAAA;IAAAA,CAAAA,IAAAA,OAAAA,4BAAAA,OAAAA,iBAAAA,GAAAA,OAAAA,0BAAAA,EAAAA,CAAAA,IAAAA,GAAAA,OAAAA,EAAAA,CAAAA,EAAAA,QAAAA,SAAAA,IAAAA;AAAAA,aAAAA,eAAAA,GAAAA,IAAAA,OAAAA,yBAAAA,IAAAA,EAAAA,CAAAA;IAAAA,CAAAA;EAAAA;AAAAA,SAAAA;AAAAA;AAAAA,SAAAA,GAAAA,GAAAA;AAAAA,SAAAA,KAAAA,cAAAA,OAAAA,UAAAA,YAAAA,OAAAA,OAAAA,WAAAA,SAAAA,IAAAA;AAAAA,WAAAA,OAAAA;EAAAA,IAAAA,SAAAA,IAAAA;AAAAA,WAAAA,MAAAA,cAAAA,OAAAA,UAAAA,GAAAA,gBAAAA,UAAAA,OAAAA,OAAAA,YAAAA,WAAAA,OAAAA;EAAAA,GAAAA,GAAAA,CAAAA;AAAAA;AAAAA,SAAAA,GAAAA,GAAAA,IAAAA;AAAAA,MAAAA,EAAAA,aAAAA;AAAAA,UAAAA,IAAAA,UAAAA,mCAAAA;AAAAA;AAAAA,SAAAA,GAAAA,GAAAA,IAAAA;AAAAA,WAAAA,KAAAA,GAAAA,KAAAA,GAAAA,QAAAA,MAAAA;AAAAA,QAAAA,KAAAA,GAAAA,EAAAA;AAAAA,IAAAA,GAAAA,aAAAA,GAAAA,cAAAA,OAAAA,GAAAA,eAAAA,MAAAA,WAAAA,OAAAA,GAAAA,WAAAA,OAAAA,OAAAA,eAAAA,GAAAA,IAAAA,GAAAA,GAAAA,GAAAA,EAAAA;EAAAA;AAAAA;AAAAA,SAAAA,GAAAA,GAAAA,IAAAA,IAAAA;AAAAA,SAAAA,MAAAA,GAAAA,EAAAA,WAAAA,EAAAA,GAAAA,MAAAA,GAAAA,GAAAA,EAAAA,GAAAA,OAAAA,eAAAA,GAAAA,aAAAA,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA,IAAAA,IAAAA;AAAAA,UAAAA,KAAAA,IAAAA,EAAAA,MAAAA,IAAAA,OAAAA,eAAAA,GAAAA,IAAAA,EAAAA,OAAAA,IAAAA,YAAAA,MAAAA,cAAAA,MAAAA,UAAAA,KAAAA,CAAAA,IAAAA,EAAAA,EAAAA,IAAAA,IAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA,IAAAA;AAAAA,MAAAA,cAAAA,OAAAA,MAAAA,SAAAA;AAAAA,UAAAA,IAAAA,UAAAA,oDAAAA;AAAAA,IAAAA,YAAAA,OAAAA,OAAAA,MAAAA,GAAAA,WAAAA,EAAAA,aAAAA,EAAAA,OAAAA,GAAAA,UAAAA,MAAAA,cAAAA,KAAAA,EAAAA,CAAAA,GAAAA,OAAAA,eAAAA,GAAAA,aAAAA,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,MAAAA,IAAAA,GAAAA,EAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA;AAAAA,SAAAA,MAAAA,OAAAA,iBAAAA,OAAAA,eAAAA,KAAAA,IAAAA,SAAAA,IAAAA;AAAAA,WAAAA,GAAAA,aAAAA,OAAAA,eAAAA,EAAAA;EAAAA,GAAAA,IAAAA,CAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA,IAAAA;AAAAA,SAAAA,MAAAA,OAAAA,iBAAAA,OAAAA,eAAAA,KAAAA,IAAAA,SAAAA,IAAAA,IAAAA;AAAAA,WAAAA,GAAAA,YAAAA,IAAAA;EAAAA,GAAAA,IAAAA,GAAAA,EAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA,IAAAA;AAAAA,MAAAA,QAAAA;AAAAA,WAAAA,CAAAA;AAAAA,MAAAA,IAAAA,IAAAA,KAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,QAAAA;AAAAA,aAAAA,CAAAA;AAAAA,QAAAA,IAAAA,IAAAA,KAAAA,CAAAA,GAAAA,KAAAA,OAAAA,KAAAA,EAAAA;AAAAA,SAAAA,KAAAA,GAAAA,KAAAA,GAAAA,QAAAA;AAAAA,MAAAA,GAAAA,QAAAA,KAAAA,GAAAA,EAAAA,CAAAA,KAAAA,MAAAA,GAAAA,EAAAA,IAAAA,GAAAA,EAAAA;AAAAA,WAAAA;EAAAA,EAAAA,GAAAA,EAAAA;AAAAA,MAAAA,OAAAA,uBAAAA;AAAAA,QAAAA,KAAAA,OAAAA,sBAAAA,CAAAA;AAAAA,SAAAA,KAAAA,GAAAA,KAAAA,GAAAA,QAAAA;AAAAA,MAAAA,GAAAA,QAAAA,KAAAA,GAAAA,EAAAA,CAAAA,KAAAA,KAAAA,OAAAA,UAAAA,qBAAAA,KAAAA,GAAAA,EAAAA,MAAAA,GAAAA,EAAAA,IAAAA,EAAAA,EAAAA;EAAAA;AAAAA,SAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA;AAAAA,MAAAA,WAAAA;AAAAA,UAAAA,IAAAA,eAAAA,2DAAAA;AAAAA,SAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA;AAAAA,MAAAA,KAAAA,WAAAA;AAAAA,QAAAA,eAAAA,OAAAA,WAAAA,CAAAA,QAAAA;AAAAA,aAAAA;AAAAA,QAAAA,QAAAA,UAAAA;AAAAA,aAAAA;AAAAA,QAAAA,cAAAA,OAAAA;AAAAA,aAAAA;AAAAA,QAAAA;AAAAA,aAAAA,QAAAA,UAAAA,QAAAA,KAAAA,QAAAA,UAAAA,SAAAA,CAAAA,GAAAA,WAAAA;MAAAA,CAAAA,CAAAA,GAAAA;IAAAA,SAAAA,IAAAA;AAAAA,aAAAA;IAAAA;EAAAA,EAAAA;AAAAA,SAAAA,WAAAA;AAAAA,QAAAA,IAAAA,KAAAA,IAAAA,CAAAA;AAAAA,QAAAA,IAAAA;AAAAA,UAAAA,KAAAA,IAAAA,IAAAA,EAAAA;AAAAA,MAAAA,KAAAA,QAAAA,UAAAA,IAAAA,WAAAA,EAAAA;IAAAA;AAAAA,MAAAA,KAAAA,GAAAA,MAAAA,MAAAA,SAAAA;AAAAA,WAAAA,SAAAA,IAAAA,IAAAA;AAAAA,UAAAA,OAAAA,YAAAA,OAAAA,MAAAA,cAAAA,OAAAA;AAAAA,eAAAA;AAAAA,UAAAA,WAAAA;AAAAA,cAAAA,IAAAA,UAAAA,0DAAAA;AAAAA,aAAAA,IAAAA,EAAAA;IAAAA,EAAAA,MAAAA,EAAAA;EAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA,IAAAA;AAAAA,SAAAA,SAAAA,IAAAA;AAAAA,QAAAA,MAAAA,QAAAA,EAAAA;AAAAA,aAAAA;EAAAA,EAAAA,CAAAA,KAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,KAAAA,QAAAA,KAAAA,OAAAA,eAAAA,OAAAA,UAAAA,GAAAA,OAAAA,QAAAA,KAAAA,GAAAA,YAAAA;AAAAA,QAAAA,QAAAA,IAAAA;AAAAA,UAAAA,IAAAA,IAAAA,IAAAA,IAAAA,KAAAA,CAAAA,GAAAA,KAAAA,MAAAA,KAAAA;AAAAA,UAAAA;AAAAA,YAAAA,MAAAA,KAAAA,GAAAA,KAAAA,EAAAA,GAAAA,MAAAA,MAAAA,IAAAA;AAAAA,cAAAA,OAAAA,EAAAA,MAAAA;AAAAA;AAAAA,UAAAA,KAAAA;QAAAA;AAAAA,iBAAAA,EAAAA,MAAAA,KAAAA,GAAAA,KAAAA,EAAAA,GAAAA,UAAAA,GAAAA,KAAAA,GAAAA,KAAAA,GAAAA,GAAAA,WAAAA,KAAAA,KAAAA;AAAAA;MAAAA,SAAAA,IAAAA;AAAAA,QAAAA,KAAAA,MAAAA,KAAAA;MAAAA,UAAAA;AAAAA,YAAAA;AAAAA,cAAAA,CAAAA,MAAAA,QAAAA,GAAAA,WAAAA,KAAAA,GAAAA,OAAAA,GAAAA,OAAAA,EAAAA,MAAAA;AAAAA;QAAAA,UAAAA;AAAAA,cAAAA;AAAAA,kBAAAA;QAAAA;MAAAA;AAAAA,aAAAA;IAAAA;EAAAA,EAAAA,GAAAA,EAAAA,KAAAA,IAAAA,GAAAA,EAAAA,KAAAA,WAAAA;AAAAA,UAAAA,IAAAA,UAAAA,2IAAAA;EAAAA,EAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA;AAAAA,SAAAA,SAAAA,IAAAA;AAAAA,QAAAA,MAAAA,QAAAA,EAAAA;AAAAA,aAAAA,IAAAA,EAAAA;EAAAA,EAAAA,CAAAA,KAAAA,SAAAA,IAAAA;AAAAA,QAAAA,eAAAA,OAAAA,UAAAA,QAAAA,GAAAA,OAAAA,QAAAA,KAAAA,QAAAA,GAAAA,YAAAA;AAAAA,aAAAA,MAAAA,KAAAA,EAAAA;EAAAA,EAAAA,CAAAA,KAAAA,IAAAA,CAAAA,KAAAA,WAAAA;AAAAA,UAAAA,IAAAA,UAAAA,sIAAAA;EAAAA,EAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA,IAAAA;AAAAA,MAAAA,GAAAA;AAAAA,QAAAA,YAAAA,OAAAA;AAAAA,aAAAA,IAAAA,GAAAA,EAAAA;AAAAA,QAAAA,KAAAA,OAAAA,UAAAA,SAAAA,KAAAA,CAAAA,EAAAA,MAAAA,GAAAA,EAAAA;AAAAA,WAAAA,aAAAA,MAAAA,EAAAA,gBAAAA,KAAAA,EAAAA,YAAAA,OAAAA,UAAAA,MAAAA,UAAAA,KAAAA,MAAAA,KAAAA,CAAAA,IAAAA,gBAAAA,MAAAA,2CAAAA,KAAAA,EAAAA,IAAAA,IAAAA,GAAAA,EAAAA,IAAAA;EAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA,IAAAA;AAAAA,GAAAA,QAAAA,MAAAA,KAAAA,EAAAA,YAAAA,KAAAA,EAAAA;AAAAA,WAAAA,KAAAA,GAAAA,KAAAA,IAAAA,MAAAA,EAAAA,GAAAA,KAAAA,IAAAA;AAAAA,IAAAA,GAAAA,EAAAA,IAAAA,EAAAA,EAAAA;AAAAA,SAAAA;AAAAA;AAAAA,SAAAA,IAAAA,GAAAA;AAAAA,MAAAA,KAAAA,SAAAA,IAAAA,IAAAA;AAAAA,QAAAA,YAAAA,OAAAA,MAAAA,SAAAA;AAAAA,aAAAA;AAAAA,QAAAA,KAAAA,GAAAA,OAAAA,WAAAA;AAAAA,QAAAA,WAAAA,IAAAA;AAAAA,UAAAA,KAAAA,GAAAA,KAAAA,IAAAA,QAAAA;AAAAA,UAAAA,YAAAA,OAAAA;AAAAA,eAAAA;AAAAA,YAAAA,IAAAA,UAAAA,8CAAAA;IAAAA;AAAAA,WAAAA,OAAAA,EAAAA;EAAAA,EAAAA,CAAAA;AAAAA,SAAAA,YAAAA,OAAAA,KAAAA,KAAAA,OAAAA,EAAAA;AAAAA;ACjBA,IAAMI,UAAgBR,cAAAA,eAAiC,IAAA;ACDvD,IAAMS,UAAsBT,cAAAA,eAAiC,IAAA;AAA7D,IAEMU,MAAkB,WAAA;AAAH,aAASC,cAAAA,YAAWF,GAAAA;AAAoB;AC2B9D,SAAUG,IACdC,IAAAA;AAEA,MAAMC,KAAUJ,IAAAA,GACVK,SAAQJ,cAAAA,YAAWH,GAAAA;AACzBQ,YAAUD,IAAOE,EAAAA;AAEjB,MAAMC,KAAYC,GAAaJ,IAAOF,EAAAA,GAEhCO,SAAkBC,cAAAA,SACtB,WAAA;AAAA,WAAMP,MAAWA,GAAQQ,sBAAAA;EAAAA,GACzB,CAACR,EAAAA,CAAAA;AAGHS,oBAAAA,WAAU,WAAA;AAGR,WAFAH,GAAgBI,SAAAA,GAET,WAAA;AACLJ,MAAAA,GAAgBK,QAAAA;IAAAA;EAEpB,GAAG,CAACL,EAAAA,CAAAA;AAEJ,MAAMM,SAAaL,cAAAA,SACjB,WAAA;AAAA,WAAMD,MAAmBO,GAAmBP,GAAgBM,UAAAA;EAAAA,GAC5D,CAACN,EAAAA,CAAAA;AAGH,SAAAQ,GAAAA,GAAA,CAAA,GACKV,EAAAA,GAAS,CAAA,GAAA,EACZQ,YAAAA,IACAG,WAAAA,CAAAA,CAAad,IACbA,OAAAA,GAAAA,CAAAA;AAEJ;AAAA,IAAAe,MAAA,CAAA,WAAA,SAAA,YAAA;ACtDM,SAAUC,IAA0BC,GAAAA;AACxC,MAAMC,SAAUtB,cAAAA,YAAWb,EAAAA;AAC3BkB,YAAUiB,IAASC,EAAAA;AAEnB,MAAQhC,KAAgB+B,GAAhB/B,IAAIC,KAAY8B,GAAZ9B,SAORS,KAAAA,IACF,SAACuB,IAAAA;AAAK,WAAKjC,MAAMiC,GAAMC,MAAMlC,EAAAA,KAAO8B,KAAWA,EAAQG,GAAMC,MAAMlC,EAAAA,CAAAA;EAAAA,CAAAA,GAL1DmC,KAAAA,GAATC,SAEYC,KAAAA,GAAZb,YACGR,KAASsB,IAAAC,IAAAC,GAAAA,GAKRhB,SAAaL,cAAAA,SACjB,WAAA;AAAA,WACEM,GAAmB,EACjBgB,SAAS,SAACC,IAAAA;AAAgB,aAAKL,GAAiBI,QAAQC,IAAK1C,EAAAA;IAAG,GAChE2C,MAAM,SAACD,IAAAA;AAAgB,aAAKL,GAAiBM,KAAKD,IAAK1C,EAAAA;IAAG,EAAA,CAAA;EAC1D,GACJ,CAACqC,IAAkBrC,EAAAA,CAAAA,GAGfoC,SAAUjB,cAAAA,SAAQ,WAAA;AACtB,WAAO,EACLyB,SAAS,SAACC,IAASC,IAAAA;AACbA,MAAAA,KACFX,GAAcY,QAAQC,SAASF,EAAAA,EAAcF,QAAQ5C,IAAI6C,EAAAA,IAEzDV,GAAcS,QAAQ5C,IAAI6C,EAAAA;IAE7B,GACDI,WAAW,SAACJ,IAASC,IAAAA;AACfA,MAAAA,KACFX,GAAcY,QAAQC,SAASF,EAAAA,EAAcG,UAAUjD,IAAI6C,EAAAA,IAE3DV,GAAcc,UAAUjD,IAAI6C,EAAAA;IAE/B,GACDK,WAAW,SAACC,IAAAA;AAAa,aAAKhB,GAAce,UAAUlD,IAAImD,EAAAA;IAAK,EAAA;EAEnE,GAAG,CAAChB,IAAenC,EAAAA,CAAAA;AAEnB,SAAA0B,GAAAA,GAAA,CAAA,GACKV,EAAAA,GAAS,CAAA,GAAA,EACZhB,IAAAA,IACAC,SAAAA,IACAmD,eAAAA,CAAAA,CAAiBrB,IACjBK,SAAAA,IACAZ,YAAAA,GAAAA,CAAAA;AAEJ;AAAA,IAAA6B,MAAA,CAAA,MAAA,WAAA,WAAA,iBAAA,YAAA;ACvDM,SAAUC,GAAkBxB,GAAAA;AAChC,MAOID,KAAAA,IAAgBC,CAAAA,GANlB9B,KAAAA,GAAAA,IACAC,KAAAA,GAAAA,SACAmC,KAAAA,GAAAA,SACAgB,KAAAA,GAAAA,eACA5B,KAAAA,GAAAA;AAIF,SAAAE,GAAAA,GAAA,CAAA,GAHcY,IAAAiB,IAAAf,GAAAA,CAAAA,GAIA,CAAA,GAAA,EACZJ,SAAAA,IACApC,IAAAA,IACAC,SAAAA,IACA2C,SAAS,SACPC,IACAC,IAAAA;AAKA,WAHAU,GAAmB,uBAAuB,EACxCC,SAAS,8BAAA,CAAA,GAEJrB,GAAQQ,QAAQC,IAAIC,EAAAA;EAC5B,GACDM,eAAAA,IACA5B,YAAAA,GAAAA,CAAAA;AAEJ;AChCO,IAAMkC,MAAgB,CAAA,EAAGC,QAAAA,EAAAA,MAAAA;AAC9B,QAAA,EACEnC,YAAAA,EAAYiB,SAAEA,IAAOE,MAAEA,GAAAA,EAAAA,IACrBW,GAAAA;AAEJ,SAA8B,YAAA,OAAhBK,EAAOC,OACjBnB,GAAQE,GAAK9C,cAAAA,QAAMgE,aAAaF,CAAAA,CAAAA,CAAAA,IAChCA;AAAM;AAPL,ICIMG,MAAgB,MAAA;AAC3B,QAAA,EAAMF,MAAEA,GAAIG,OAAEA,IAAK7B,OAAEA,IAAK8B,oBAAEA,GAAAA,IAAuBnC,IAChDoC,CAAAA,QAAU,EACTL,MAAMK,GAAKC,KAAKN,MAChBG,OAAOE,GAAKC,KAAKH,OACjB7B,OAAO+B,GAAKC,KAAKhC,OACjB8B,oBAAoBC,GAAKE,oBAAAA,EAAAA;AAI7B,aAAOhD,cAAAA,SAAQ,MAAA;AACb,QAAIjB,KAAW6D,GAAM7D;AAEjBgC,IAAAA,MAASA,GAAMkC,SAAS,MAC1BlE,KACEL,cAAAA,QAACM,cAAAN,cAAAA,QAAMwE,UAAQ,MACZnC,GAAMoC,IAAKtE,CAAAA,OACVH,cAAAA,QAAAM,cAACoE,KAAW,EAACvE,IAAIA,IAAIwE,KAAKxE,GAAAA,CAAAA,CAAAA,CAAAA;AAMlC,UAAM2D,KAAS9D,cAAAA,QAAMM,cAAcyD,GAAMG,IAAO7D,EAAAA;AAEhD,WAAmB,YAAA,OAAR0D,IACF/D,cAAAA,QAAAA,cAAC6D,KAAa,EAACC,QAAQA,GAAAA,CAAAA,IAGzBA;EAAM,GAEZ,CAACC,GAAMG,IAAOC,IAAoB9B,EAAAA,CAAAA;AAAO;ADnCvC,IEOMuC,MAAsB,CAAA,EAAGd,QAAAA,EAAAA,MAAAA;AACpC,QAAA,EAAMe,QAAEA,GAAAA,IAAW7C,IAAiBoC,CAAAA,QAAU,EAC5CS,QAAQT,GAAKC,KAAKQ,OAAAA,EAAAA,GAAAA,EAGdC,UAAEA,GAAAA,IAAajE,IAAmBuB,CAAAA,QAAW,EACjD0C,UAAU1C,GAAM2C,QAAQD,SAAAA,EAAAA;AAI1B,SAAID,KACK,OAGF7E,cAAAA,QAAMM,cAAcwE,IAAU,EAAEhB,QAAQA,KAAU9D,cAAAA,QAACM,cAAA2D,KAAgB,IAAA,EAAA,CAAA;AAAG;AFrBxE,IGQMS,MAAc,CAAA,EAAGvE,IAAAA,GAAI2D,QAAAA,GAAAA,MAE9B9D,cAAAA,QAACM,cAAAJ,IAAa,EAAAC,IAAIA,EAAAA,GAChBH,cAAAA,QAACM,cAAAsE,KAAAA,EAAoBd,QAAQA,GAAAA,CAAAA,CAAAA;AHX5B,IIMMkB,MAAsB,EACjCC,IAAI,OACJC,QAAAA,OACAC,QAAQ,CAAE,GACVN,QAAAA,MAAQ;AJVH,IIaMO,MAAwB,EACnCH,IAAI,QACJC,QAAQ,WAAA;AAYJ,SAAUG,GAAAA,EAAqClF,IACnDA,GAAEE,UACFA,IAAAA,GACGiF,GAAAA,GAAAA;AAEH,QAAA,EAAML,IAAEA,GAAAA,IAAO,EAAA,GACVD,KAAAA,GACAM,GAAAA,GAAAA,EAGCC,OAAEA,IAAKhD,SAAEA,GAAAA,IAAY1B,IAAAA,GAAAA,EACnBV,IAAIqF,IAAMjC,eAAEA,GAAAA,IAAkBvB,IAAAA,GAAAA,CAE/ByD,EAAAA,QAAgBC,cAAAA,UAAwB,MAAA;AAC7CzE,cAAAA,CAAAA,CAAYd,GAAIwF,EAAAA;AAChB,UAAMvB,KAAOmB,GAAMnB,KAAKoB,EAAAA,EAAQI,IAAAA;AAEhC,QAAIrC,IAAe;AACjB,YAAMsC,KAAezB,GAAKC,KAAKyB,YAAY3F,CAAAA,IACvCoF,GAAMnB,KAAKA,GAAKC,KAAKyB,YAAY3F,CAAAA,CAAAA,EAAKyF,IAAAA,IACtC;AAGJ,UAAIC,MAAgBA,GAAaxB,KAAKN,SAASkB;AAC7C,eAAOY,GAAa1F;AAItB,YAAM4F,KAAgB/F,cAAAA,QAAMM,cAC1B+E,IACAC,IACAjF,EAAAA,GAGI2F,KAAOT,GAAMU,kBAAkBF,EAAAA,EAAeG,WAAAA;AAGpD,aADA3D,GAAQW,QAAQiD,OAAAA,EAASC,sBAAsBJ,IAAMR,IAAQrF,CAAAA,GACtD6F,GAAKK;IACb;AACD,WAAO;EAAI,CAAA;AAGb,SAAOZ,KAAezF,cAAAA,QAAAA,cAAC0E,KAAW,EAACvE,IAAIsF,GAAAA,CAAAA,IAAmB;AAC5D;ACnEa,IAAAa,KAA2B,MACtC3C,GAAmB,cAAc,EAC/BC,SAAS,4BAAA,CAAA;AAAA,SAGG2C,OAAAA,EAAAA,GACXrC,EAAAA,GAAAA;AAIH,aAFA1C,cAAAA,WAAU,MAAM8E,GAAAA,GAA4B,CAAA,CAAA,GAErCtG,cAAAA,QAAAM,cAAC+E,IAAY,EAAA,GAAAnB,GAAOgB,QAAAA,KAAQ,CAAA;AACrC;ACLA,IAAMsB,MAAiB,MAAA;AACrB,QAAA,EAAMC,WAAEA,EAAAA,IAAc5F,IAAmBuB,CAAAA,QAAW,EAClDqE,WACErE,GAAMC,MAAMqE,EAAAA,KAActE,GAAMC,MAAMqE,EAAAA,EAAWpC,oBAAAA,EAAAA;AAGrD,SAAKmC,IAIEzG,cAAAA,QAAAM,cAACoE,KAAW,EAACvE,IAAIuG,IAAW/B,KAAK8B,EAAAA,CAAAA,IAH/B;AAG4C;AAVvD,IAgBaE,KAAQ,CAAA,EAAGtG,UAAAA,GAAUuG,MAAAA,IAAMvC,MAAAA,GAAAA,MAAAA;AACtC,QAAA,EAAM9B,SAAEA,IAAOgD,OAAEA,GAAAA,IAAU1E,IAAAA;AAErB+F,EAAAA,MACJjD,GAAmB,wBAAwB,EACzCC,SAAS,uBAAA,CAAA;AAIb,QAAMiD,SAAWC,cAAAA,QAAAA,KAAO;AAExB,MAAA,CAAKD,GAASE,SAAS;AACrB,UAAMC,KAAc3C,MAAQuC;AAE5B,QAAII;AACFzE,MAAAA,GAAQW,QAAQiD,OAAAA,EAASc,YAAYD,EAAAA;aAC5B3G,GAAU;AACnB,YAAM6G,KAAWlH,cAAAA,QAAMmH,SAASC,KAAK/G,CAAAA,GAE/B+D,KAAOmB,GAAMU,kBAAkBiB,EAAAA,EAAUhB,WAAW,CAAC9B,IAAMiD,QAC3DA,OAAQH,OACV9C,GAAKjE,KAAKuG,KAELtC,GAAAA;AAGT7B,MAAAA,GAAQW,QAAQiD,OAAAA,EAASmB,YAAYlD,EAAAA;IACtC;AAEDyC,IAAAA,GAASE,UAAAA;EACV;AAED,SAAO/G,cAAAA,QAAAM,cAACkG,KAAc,IAAA;AAAG;AAAA,IC4Cfe;AAAAA,CAAZ,SAAYA,GAAAA;AACVA,IAAAA,EAAA,MAAA,CAAA,IAAA,OACAA,EAAAA,EAAA,KAAA,CAAA,IAAA,MACAA,EAAAA,EAAA,MAAA,CAAA,IAAA;AACD,EAJWA,OAAAA,KAIX,CAAA,EAAA;AC7FD,IAAMC,KAAoBjF,OAAAA;AACxB,QAAA,EAAM6D,uBACJA,IAAqBqB,QACrBA,IAAMC,cACNA,IAAYC,cACZA,IAAYC,OACZA,IAAAA,GACGtF,GAAAA,IACDC;AAEJ,SAAOD;AAAa;AAuChB,SAAUuF,IAAa5F,GAAAA;AAC3B,QAAA,EAAMN,YACJA,IACAY,SAASuF,IAAevC,OACxBA,IAAKvE,OACLA,IAAAA,GACGG,GAAAA,IACDN,IAAkBoB,CAAAA,GAEhBK,KAAgBkF,GAAiBM,EAAAA;AAevC,SAAO,EACLnG,YAAAA,IACAY,aAfcjB,cAAAA,SAAQ,OACf,EAAA,GACFgB,IACHY,SAAS,EAAA,GACJZ,GAAcY,SACjBiD,QAAQ,IAAI4B,OACVP,GAAiBlF,GAAcY,QAAQiD,OAAAA,GAAU4B,EAAAA,CAAAA,GACnD5E,UAAU,IAAI4E,OACZP,GAAiBlF,GAAcY,QAAQC,SAAAA,GAAY4E,EAAAA,CAAAA,EAAAA,EAAAA,IAGxD,CAACzF,EAAAA,CAAAA,GAKFiD,OAAAA,IACAvE,OAAAA,IAAAA,GACIG,GAAAA;AAER;AC3FM,SAAU6G,IAAiB/F,GAAAA;AAC/B,SAAQgG,CAAAA,OACE/D,CAAAA,OAAAA;AACN,UAAMgE,KAASjG,IAAU4F,IAAU5F,CAAAA,IAAW4F,IAAAA;AAC9C,WAAO7H,cAAAA,QAAAA,cAACiI,IAAgB,EAAA,GAAKC,IAAAA,GAAYhE,GAAAA,CAAAA;EAAS;AAGxD;ACPM,SAAUiE,GAAelG,GAAAA;AAC7B,SAAO,SAAUgG,IAAAA;AACf,WAAQ/D,CAAAA,OAAAA;AACN,YAAME,KAAOX,GAAQxB,CAAAA;AACrB,aAAOjC,cAAAA,QAAAA,cAACiI,IAAgB,EAAA,GAAK7D,IAAAA,GAAUF,GAAAA,CAAAA;IAAS;EAEpD;AACF;ACZO,IAAMkE,KAAc,SAACC,GAAAA;AAC1B,SAAIC,OAAOF,cACFE,OAAOF,YAAYC,CAAAA,IAErBA,EAAME,OACX,SAACC,IAAKC,IAAAA;AAAA,QAAAC,KAAAC,IAAAF,IAAA,CAAA,GAAGtI,KAAEuI,GAAA,CAAA,GAAElI,KAAKkI,GAAA,CAAA;AAAA,WAAA7G,GAAAA,GAAA,CAAA,GACb2G,EAAAA,GAAK,CAAA,GAAAI,IAAA,CAAA,GACPzI,IAAKK,EAAAA,CAAAA;EACN,GACF,CAAE,CAAA;AAEN;AAXO,ICMMqI,KAAuB,SAClCxG,GACAyG,IACAC,IAAAA;AAEA,MAAMC,KAAQC,MAAMC,QAAQJ,EAAAA,IAAYA,KAAW,CAACA,EAAAA,GAE9CK,KAAYtH,GAAA,EAChBuH,WAAAA,OACAC,QAAAA,MAAQ,GACJN,MAAU,CAAA,CAAA,GAGVO,KAAgBN,GACnBO,OAAO,SAACC,IAAAA;AAAI,WAAA,CAAA,CAAOA;EAAI,CAAA,EACvB/E,IAAI,SAAC+E,IAAAA;AACJ,WAAoB,YAAA,OAATA,KACF,EACLpF,MAAM/B,EAAMmH,EAAAA,GACZC,QAAAA,CAAAA,CAAUpH,EAAMmH,EAAAA,EAAAA,IAIA,aAAhBE,GAAOF,EAAAA,KAAsBL,GAAaE,SAQvC,EACLjF,MAAM,MACNqF,QAAAA,MAAQ,IARD,EACLrF,MAFWoF,IAGXC,QAAAA,CAAAA,CAAUpH,EAHCmH,GAGUrJ,EAAAA,EAAAA;EAQ3B,CAAA;AASF,SAPIgJ,GAAaC,aACfnI,UACkE,MAAhEqI,GAAcC,OAAO,SAACT,IAAAA;AAAQ,WAAA,CAAMA,GAASW;EAAM,CAAA,EAAElF,QACrDoF,EAAAA,GAIGL;AACT;ADnDO,ICmDP,KAAA,CAAA,SAAA;ADnDO,IEaHM,KAAkD;AFb/C,IE4CMC,KAAmB,SAC9BC,GACAC,IAAAA;AAEA,MAAoB,YAAA,OAATA;AACT,WAAOA;AAGT,MApBwBC,IAoBlBC,KAhB0B,SAChCH,IACAC,IAAAA;AAEA,QAAMG,KAzBoB,SAACJ,IAAAA;AAC3B,UAAIF,MAAwBA,GAAqBE,aAAaA;AAC5D,eAAOF,GAAqBO;AAG9BP,WAAuB,EACrBE,UAAAA,IACAK,UAAU,oBAAIC,MAAAA;AAGhB,eAAAC,KAAA,GAAAC,KAA2BhC,OAAOiC,QAAQT,EAAAA,GAAWO,KAAAC,GAAA/F,QAAA8F,MAAA;AAAhD,YAAAG,KAAA7B,IAAA2B,GAAAD,EAAAA,GAAA,CAAA;AACHT,WAAqBO,SAASM,IADVD,GAAA,CAAA,GAANA,GAAA,CAAA,CAAA;MAEhB;AAEA,aAAOZ,GAAqBO;IAC9B,EAUmCL,EAAAA,EAAUlE,IAAImE,EAAAA;AAC/C,WAAA,WAAOG,KAAqBA,KAAO;EACrC,EAUiDJ,GAAUC,EAAAA;AAOzD,SALA9I,UACEgJ,IACAS,GAAsBC,QAAQ,gBAxBRX,KAwBwCD,IAvBtCG,QAASF,GAAkBY,WAAAA,CAAAA,GA0B9CX;AACT;ACrDA,IAAMY,KAAa,CAAC9G,GAAkC+F,OAChC,YAAA,OAAT/F,IACFA,IAEF,EAAEkG,cAAcJ,GAAiBC,IAAU/F,CAAAA,EAAAA;AAJpD,IAOa+G,KAAgB,CAC3BzG,GACAyF,OAAAA;AAEA,MAAA,EAAI/F,MAAEA,IAAIgH,UAAEA,IAAQ7G,OAAEA,GAAAA,IAAUG;AAuBhC,SAtBAH,KAAQoE,OAAO0C,KAAK9G,EAAAA,EAAOqE,OAAO,CAAC0C,IAA6BtG,OAAAA;AAC9D,UAAMuG,KAAOhH,GAAMS,EAAAA;AAEnB,WAAIuG,QAAAA,MAAuD,cAAA,OAATA,OAKhDD,GAAOtG,EAAAA,IADG,eAARA,MAAsC,YAAA,OAATuG,KACjB/D,cAAAA,SAAS1C,IAAIyG,IAAOC,CAAAA,OACX,YAAA,OAAVA,KACFA,KAEFL,GAAcK,IAAOrB,EAAAA,CAAAA,IAEA,cAAA,OAAdoB,GAAKnH,OACP+G,GAAcI,IAAMpB,EAAAA,IAEpBoB,KAbPD;EAeI,GACZ,CAAE,CAAA,GAEE,EACLlH,MAAM8G,GAAW9G,IAAM+F,EAAAA,GACvBiB,UAAAA,CAAAA,CAAYA,IACZ7G,OAAAA,GAAAA;AACD;AAtCH,IAyCakH,KAAgB,CAC3B/G,GACAyF,OAAAA;AAEA,QAAA,EAAM/F,MAAEA,IAAIG,OAAEA,IAAK6G,UAAEA,IAAQb,MAAEA,IAAAA,GAASmB,GAAAA,IAAahH;AAIrD,SAAO,EAAA,GAFayG,GAAc,EAAE/G,MAAAA,IAAMgH,UAAAA,IAAU7G,OAAAA,GAAAA,GAAS4F,EAAAA,GAAAA,GAIxDuB,GAAAA;AACJ;ACvCa,SAAAC,GAAYlJ,GAAoBjC,IAAAA;AAC9Cc,YAAuB,YAAA,OAANd,IAAgBoL,EAAAA;AAEjC,MAAMnH,KAAOhC,EAAMC,MAAMlC,EAAAA,GAEnBqL,KAAc,SAACrL,IAAAA;AAAE,WAAKmL,GAAYlJ,GAAOjC,EAAAA;EAAG;AAElD,SAAO,EACL4K,UAAQ,WAAA;AACN,WAAA,CAAA,CAAS3G,GAAKC,KAAK0G;EACpB,GACDU,QAAM,WAAA;AACJ,WAAOrH,GAAKjE,OAAOuG;EACpB,GACDgF,cAAY,WAAA;AACV,WACEtH,GAAKC,KAAKsH,UACVH,GAAYpH,GAAKC,KAAKsH,MAAAA,EAAQ7F,YAAAA,EAAc8F,SAASxH,GAAKjE,EAAAA;EAE7D,GACD0L,gBAAc,WAAA;AACZ,WAAOC,KAAKL,OAAAA,KAAYK,KAAKJ,aAAAA;EAC9B,GACDK,aAAW,WAAA;AACT,WAAA,CAAQD,KAAKD,eAAAA;EACd,GACDG,yBAAyB,WAAA;AAAA,WACvB5H,GAAKC,KAAKyB,eAAewC,OAAO0C,KAAK5G,GAAKC,KAAKyB,WAAAA,EAAavB,SAAS;EAAC,GACxE0H,0BAAwB,WAAA;AAItB,WAHAtI,GAAmB,2CAA2C,EAC5DC,SAAS,yCAAA,CAAA,GAEJkI,KAAKE,wBAAAA;EACb,GACDE,YAAU,WAAA;AACR,WAAO9J,EAAM+J,OAAOC,SAASC,IAAIlM,EAAAA;EAClC,GACDmM,WAAS,WAAA;AACP,WAAOlK,EAAM+J,OAAOI,QAAQF,IAAIlM,EAAAA;EACjC,GACDqM,WAAS,WAAA;AACP,WAAOpK,EAAM+J,OAAOM,QAAQJ,IAAIlM,EAAAA;EACjC,GACDyF,KAAG,WAAA;AACD,WAAOxB;EACR,GACDsI,WAAsB,WAAA;AAAA,QAAZC,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,KAAAA,UAAAA,CAAAA;AAsBR,WArBA,SAASC,GACPzM,IAAAA;AAEiB,UADjBuM,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,IAAAA,UAAAA,CAAAA,IAAsB,CAAA,GACtBG,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,IAAAA,UAAAA,CAAAA,IAAgB,GAEVzI,KAAOhC,EAAMC,MAAMlC,EAAAA;AACzB,aAAKiE,MAILsI,GAAUI,KAAK3M,EAAAA,GAEViE,GAAKC,KAAKsH,WAIXgB,MAAAA,CAAUA,MAAkB,MAAVE,QACpBH,KAAYE,GAAiBxI,GAAKC,KAAKsH,QAAQe,IAAWG,KAAQ,CAAA,IAE7DH,MANEA,MANAA;IAaX,EACwBtI,GAAKC,KAAKsH,MAAAA;EACnC,GACDoB,aAE4C,WAAA;AAAA,QAD1CJ,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,KAAAA,UAAAA,CAAAA,GACAK,KAA0CC,UAAA1I,SAAA,IAAA0I,UAAA,CAAA,IAAA;AAqC1C,WAnCA,SAASC,GACP/M,IAAAA;AAEiB,UADjB4M,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,IAAAA,UAAAA,CAAAA,IAAwB,CAAA,GACxBF,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,IAAAA,UAAAA,CAAAA,IAAgB;AAEhB,cAAIF,MAAAA,CAAUA,MAAkB,MAAVE,OACPzK,EAAMC,MAAMlC,EAAAA,KAML,iBAAhB6M,MAEkBxB,GAAYrL,EAAAA,EAAI2F,YAAAA,EAExBqH,QAAQ,SAAC3H,IAAAA;AACnBuH,QAAAA,GAAYD,KAAKtH,EAAAA,GACjBuH,KAAcG,GAAgB1H,IAAQuH,IAAaF,KAAQ,CAAA;MAC7D,CAAA,GAGkB,kBAAhBG,MACiBxB,GAAYrL,EAAAA,EAAIiN,WAAAA,EAExBD,QAAQ,SAAC3H,IAAAA;AAClBuH,QAAAA,GAAYD,KAAKtH,EAAAA,GACjBuH,KAAcG,GAAgB1H,IAAQuH,IAAaF,KAAQ,CAAA;MAC7D,CAAA,GAGKE,MAEFA;IACT,EACuB5M,EAAAA;EACxB,GACD2F,aAAW,WAAA;AACT,WAAOwC,OAAO+E,OAAOjJ,GAAKC,KAAKyB,eAAe,CAAA,CAAA;EAC/C,GACDsH,YAAU,WAAA;AACR,WAAOhJ,GAAKC,KAAKhC,SAAS,CAAA;EAC3B,GACDiL,aAAW,SAACC,IAAAA;AACV,QAAA;AACE,UAAMC,KAAapJ;AAUnB,aATAnD,UAAAA,CAAW6K,KAAKD,eAAAA,GAAkB4B,EAAAA,GAClCxM,UACEqK,GAAYlJ,GAAOoL,GAAWnJ,KAAKsH,MAAAA,EAAQZ,SAAAA,GAC3C2C,EAAAA,GAEFzM,UACEuM,GAAWG,MAAMC,QAAQJ,IAAYhC,EAAAA,GACrCqC,EAAAA,GAAAA;IAQJ,SALSC,IAAAA;AAIP,aAHIP,MACFA,GAAQO,EAAAA,GAAAA;IAGZ;EACD,GACDC,aAAYjF,SAAAA,IAAwByE,IAAAA;AAClC,QAAMS,KAAUnF,GAAqBzG,EAAMC,OAAOyG,EAAAA,GAE5CmF,KAAgB7J;AAEtB,QAAA;AACEnD,gBAAU6K,KAAKf,SAAAA,GAAYmD,EAAAA,GAC3BjN,UACEgN,GAAcN,MAAMQ,UAClBH,GAAQvJ,IAAI,SAACqE,IAAAA;AAAQ,eAAKA,GAAS1E;MAAI,CAAA,GACvC6J,IACAzC,EAAAA,GAEF4C,EAAAA;AAGF,UAAMC,KAAc,CAAA;AA2DpB,aAzDAL,GAAQb,QAAQ,SAAiC1E,IAAAA;AAAA,YAAxB+E,KAAAA,GAANpJ,MAAkBqF,KAAAA,GAAAA;AAOnC,YANAxI,UACEuM,GAAWG,MAAMW,QAAQL,IAAeT,IAAYhC,EAAAA,GACpD+C,EAAAA,GAIG9E,IAAL;AAIAxI,oBAAAA,CACGuK,GAAYgC,GAAWrN,EAAAA,EAAI0L,eAAAA,GAC5B4B,EAAAA;AAGF,cAAMe,KAAkBhD,GAAYgC,GAAWrN,EAAAA,EAAI4M,YAAAA,IAAY;AAE/D9L,oBAAAA,CACGuN,GAAgB5C,SAASqC,GAAc9N,EAAAA,KACtC8N,GAAc9N,OAAOqN,GAAWrN,IAClCsO,EAAAA;AAGF,cAAMC,KACJlB,GAAWnJ,KAAKsH,UAAUvJ,EAAMC,MAAMmL,GAAWnJ,KAAKsH,MAAAA;AAExD1K,oBACEyN,GAAkBrK,KAAK0G,UACvB2C,EAAAA,GAGFzM,UACEyN,MAAAA,CACIA,MAAAA,CAAsBtM,EAAMC,MAAMmL,GAAWrN,EAAAA,GACjDwO,EAAAA,GAGED,GAAkBvO,OAAO8N,GAAc9N,OACpCkO,GAAYK,GAAkBvO,EAAAA,MACjCkO,GAAYK,GAAkBvO,EAAAA,IAAM,CAAA,IAGtCkO,GAAYK,GAAkBvO,EAAAA,EAAI2M,KAAKU,EAAAA;QAlCzC;MAoCF,CAAA,GAEAlF,OAAO0C,KAAKqD,EAAAA,EAAalB,QAAQ,SAACyB,IAAAA;AAChC,YACMC,KAAazM,EAAMC,MAAMuM,EAAAA;AAE/B3N,kBACE4N,GAAWlB,MAAMmB,WAJAT,GAAYO,EAAAA,GAIWC,IAAYrD,EAAAA,GACpDuD,EAAAA;MAEJ,CAAA,GAAA;IAQF,SALSjB,IAAAA;AAIP,aAHIP,MACFA,GAAQO,EAAAA,GAAAA;IAGZ;EACD,GACDkB,kBAAgB,WAAA;AACd,WAAO5D,GAAchH,GAAKC,MAAMjC,EAAM2C,QAAQ+E,QAAAA;EAC/C,GACD5D,YAAU,SAAC8G,IAAAA;AACT,QAAM3K,KAAQ,CAAClC,EAAAA,EAAAA,OAAAA,IAAO2L,KAAKiB,YAAAA,MAAkBC,EAAAA,CAAAA,CAAAA,EAAczE,OACzD,SAACC,IAAOyG,IAAAA;AAEN,aADAzG,GAAMyG,EAAAA,IAAgBzD,GAAYyD,EAAAA,EAAcrJ,IAAAA,GACzC4C;IACR,GACD,CAAE,CAAA;AAGJ,WAAO,EACLnC,YAAYlG,IACZkC,OAAAA,GAAAA;EAEH,GAMD6M,YAAuB,WAAA;AAAA,QAAZvC,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,KAAAA,UAAAA,CAAAA;AAIT,WAHAhJ,GAAmB,6BAA6B,EAC9CC,SAAS,6BAAA,CAAA,GAEJkI,KAAKiB,YAAYJ,EAAAA;EACzB,GACDwC,kBAAgB,WAAA;AACd,WAAA,CAAQrD,KAAKL,OAAAA,KAAAA,CAAarH,GAAKC,KAAKsH;EACtC,EAAA;AAEJ;AC9Qc,SAAUyD,GACtBzD,GACA0D,IACAC,IACAC,IAAAA;AAiBA,WAfItE,KAAuB,EACzBU,QAAAA,GACA6D,OAAO,GACPC,OAAO,SAAA,GAGLC,KAAY,GACdC,KAAS,GAETC,KAAS,GACTC,KAAU,GACVC,KAAU,GACVC,KAAU,GAGHC,KAAI,GAAGC,KAAMZ,GAAK9K,QAAQyL,KAAIC,IAAKD,MAAK;AAC/C,QAAME,KAAMb,GAAKW,EAAAA;AAWjB,QANAD,KAAUG,GAAIC,MAAMD,GAAIE,aAExBP,KAAUK,GAAIG,OAAOH,GAAII,aAAa,GAEtCR,KAAUI,GAAIC,MAAMD,GAAIE,cAAc,GAAA,EAGnCT,MAAUO,GAAIG,OAAOV,MACrBC,MAAUE,MAAWF,MACrBF,MAXQQ,GAAIG,OAAOH,GAAII,aAWCZ;AAM3B,UAFAzE,GAAOuE,QAAQQ,IAEVE,GAAIK,QAUF;AAEL,YAAIhB,KAAOO,IAAS;AAClB7E,UAAAA,GAAOwE,QAAQ;AACf;QACF;AAAOxE,QAAAA,GAAOwE,QAAQ;MACxB;AAfMF,QAAAA,KAAOQ,OAASH,KAASG,KAEzBT,KAAOO,MACTF,KAASE,IACT5E,GAAOwE,QAAQ,aAEfC,KAAYG,IACZ5E,GAAOwE,QAAQ;EASrB;AAEA,SAAOxE;AACT;ACnDA,IAAMuF,KAAkB,SAACzM,GAAAA;AAA+B,SACvC,YAAA,OAARA,IAAmBA,IAAOA,EAAKmG;AAAI;AAE5B,SAAAuG,GACdC,GACAC,IAAAA;AAEA,MAAIC,KAAaF,EAAQrM,KAAKN,MAGxBK,KAAa,EACjBjE,IAHOuQ,EAAQvQ,MAAM0Q,GAAAA,GAIrBvM,qBAAqBwM,KAAKC,IAAAA,GAC1B1M,MAAIxC,GAAA,EACFkC,MAAM6M,IACN1G,MAAMsG,GAAgBI,EAAAA,GACtBhG,aAAa4F,GAAgBI,EAAAA,GAC7B1M,OAAO,CAAE,GACTiB,QAAQ,CAAE,GACVwG,QAAQ,MACRZ,UAAAA,OACAlG,QAAAA,OACAxC,OAAO,CAAA,GACPyD,aAAa,CAAE,EAAA,GACZ4K,EAAQrM,IAAAA,GAEb2M,MAAM,CAAE,GACR5Q,SAAS,CAAE,GACX+L,QAAQ,EACNC,UAAAA,OACAK,SAAAA,OACAF,SAAAA,MAAS,GAEXoB,OAAO,EACLC,SAAS,WAAA;AAAA,WAAA;EAAU,GACnBU,SAAS,WAAA;AAAA,WAAA;EAAU,GACnBH,WAAW,WAAA;AAAA,WAAA;EAAU,GACrBW,YAAY,WAAA;AAAA,WAAA;EAAU,EAAA,GAExBjM,KAAK,KAAA;AAIP,MAAIuB,GAAKC,KAAKN,SAASsB,MAAWjB,GAAKC,KAAKN,SAASwC,QAAQ;AAC3D,QAAM0K,KAAAA,GAAAA,GAAAA,CAAAA,GACDjM,GAAAA,GACAZ,GAAKC,KAAKH,KAAAA;AAGfE,IAAAA,GAAKC,KAAKH,QAAQoE,OAAO0C,KAAK5G,GAAKC,KAAKH,KAAAA,EAAOqE,OAAO,SAACrE,IAAOS,IAAAA;AAU5D,aATI2D,OAAO0C,KAAKhG,GAAAA,EAAqB4G,SAASjH,EAAAA,IAG5CP,GAAKC,KAAKe,IAAsBT,EAAAA,KAAQA,EAAAA,IAAOsM,GAAYtM,EAAAA,IAG3DT,GAAMS,EAAAA,IAAOP,GAAKC,KAAKH,MAAMS,EAAAA,GAGxBT;IACR,GAAE,CAAE,CAAA,GAGLE,GAAKC,KAAK6F,OAAOsG,GADjBI,KAAaxM,GAAKC,KAAKN,IAAAA,GAEvBK,GAAKC,KAAKuG,cAAc4F,GAAgBI,EAAAA,GAEVxM,GAAKC,KAAKN,SAASwC,WAE/CnC,GAAKC,KAAK0G,WAAAA,MACVzE,GAAAA;EAEJ;AAEIqK,EAAAA,MACFA,GAAUvM,EAAAA;AAIZ,MAAM8M,KAAsBN,GAAWO;AAEvC,MAAID,IAAqB;AA+BvB,QA9BA9M,GAAKC,KAAKuG,cACRsG,GAAoBtG,eACpBsG,GAAoBhH,QACpB9F,GAAKC,KAAKuG,aAEZxG,GAAKC,KAAKH,QAAAA,GAAAA,GAAAA,CAAAA,GACJgN,GAAoBhN,SAASgN,GAAoBE,gBAAgB,CAAE,CAAA,GACpEhN,GAAKC,KAAKH,KAAAA,GAGfE,GAAKC,KAAKc,SAAMtD,GAAAA,GAAA,CAAA,GACVqP,GAAoB/L,UAAU,CAAA,CAAA,GAC/Bf,GAAKC,KAAKc,MAAAA,GAIb+L,QAAAA,GAAoBnG,aAGpB3G,GAAKC,KAAK0G,WAAWmG,GAAoBnG,WAGvCmG,GAAoBvD,SACtBrF,OAAO0C,KAAKkG,GAAoBvD,KAAAA,EAAOR,QAAQ,SAACxI,IAAAA;AAC1C,OAAC,WAAW,WAAW,aAAa,YAAA,EAAciH,SAASjH,EAAAA,MAC7DP,GAAKuJ,MAAMhJ,EAAAA,IAAOuM,GAAoBvD,MAAMhJ,EAAAA;IAEhD,CAAA,GAGEuM,GAAoB9Q,SAAS;AAC/B,UAAMiR,KAAqB,EACzBlR,IAAIiE,GAAKjE,IACTC,SAAAA,KAAS;AAGXkI,aAAO0C,KAAKkG,GAAoB9Q,OAAAA,EAAS+M,QAAQ,SAACpD,IAAAA;AAChD3F,QAAAA,GAAKhE,QAAQ2J,EAAAA,IAAQ,SAAC7F,IAAAA;AAAK,iBACzBlE,cAAAA,QAAMM,cACJJ,IACAmR,IACArR,cAAAA,QAAMM,cAAc4Q,GAAoB9Q,QAAQ2J,EAAAA,GAAO7F,EAAAA,CAAAA;QACxD;MACL,CAAA;IACF;AAEIgN,IAAAA,GAAoBF,SACtB5M,GAAK4M,OAAOE,GAAoBF;EAEpC;AAEA,SAAO5M;AACT;ACjIA,IASakN,KAAkB,CAC7BjN,GACAyF,IACA0F,OAAAA;AAEA,MAAA,EAAIzL,MAAEA,IAAIG,OAAEA,GAAAA,IAAUG;AAEtB,QAAMkN,MAhBY,CAACxN,IAAsB+F,OACzB,YAAA,OAAT/F,MAAqBA,GAAKkG,eACP,aAAtBlG,GAAKkG,eACH1D,SACAuD,GAAS/F,GAAKkG,YAAAA,IACA,YAAA,OAATlG,KACPA,KACA,MASqBA,IAAM+F,EAAAA;AAE/B,MAAA,CAAKyH;AACH;AAGFrN,EAAAA,KAAQoE,OAAO0C,KAAK9G,EAAAA,EAAOqE,OAAO,CAAC0C,IAA6BtG,OAAAA;AAC9D,UAAMuG,KAAOhH,GAAMS,EAAAA;AAenB,WAbEsG,GAAOtG,EAAAA,IADLuG,QAAAA,KACY,OACW,YAAA,OAATA,MAAqBA,GAAKjB,eAC5BqH,GAAgBpG,IAAMpB,EAAAA,IACnB,eAARnF,MAAsBsE,MAAMC,QAAQgC,EAAAA,IAC/BA,GAAKzG,IAAK0G,CAAAA,OACD,YAAA,OAAVA,KACFA,KAEFmG,GAAgBnG,IAAOrB,EAAAA,CAAAA,IAGlBoB,IAETD;EAAM,GACZ,CAAE,CAAA,GAEDuE,OACFtL,GAAMS,MAAM6K;AAGd,QAAMnI,KAAM,EAAA,GACPrH,cAAAA,QAAMM,cAAciR,IAAM,EAAA,GACxBrN,GAAAA,CAAAA,EAAAA;AAIP,SAAO,EAAA,GACFmD,IACH6C,MAAML,GAAiBC,IAAUzC,GAAItD,IAAAA,EAAAA;AACtC;AAtDH,IAyDayN,KAAkB,CAC7BnN,GACAyF,OAAAA;AAEA,QAAA,EAAQ/F,MAAM0N,IAAMvN,OAAOwN,IAAAA,GAAUrG,GAAAA,IAAahH;AAOlDpD,YAAAA,WAL4BwQ,MAAsC,YAAA,OAATA,MAAAA,WAEvDA,MAAAA,WACCA,GAAmCxH,cAIpC0H,GAA4ChH,QAC1C,iBACAtG,EAAKuG,WAAAA,EACLD,QAAQ,yBAAyBrC,OAAO0C,KAAKlB,EAAAA,EAAU8H,KAAK,IAAA,CAAA,CAAA;AAGhE,QAAA,EAAM7N,MAAEA,IAAImG,MAAEA,IAAIhG,OAAEA,GAAAA,IAAWoN,GAC7BjN,GACAyF,EAAAA,GAAAA,EAGI6B,QAAEA,IAAMxG,QAAEA,IAAMyF,aAAEA,IAAWG,UAAEA,IAAQ1I,OAAEA,IAAKwC,QAAEA,GAAAA,IAAWwG;AAIjE,SAAO,EACLtH,MAAAA,IACAmG,MAAAA,IACAU,aAAaA,MAAeV,IAC5BhG,OAAAA,IACAiB,QAAQA,MAAU,CAAE,GACpB4F,UAAAA,CAAAA,CAAYA,IACZlG,QAAAA,CAAAA,CAAUA,IACV8G,QAAAA,IACA7F,aAXkBuF,GAASvF,eAAeuF,GAASwG,gBAWvB,CAAE,GAC9BxP,OAAOA,MAAS,CAAA,EAAA;AACjB;AAhGH,ICfMyP,KAAa,CAAC5K,GAAgB6K,OAAAA;AAClC,MAAIA,GAAcxN,SAAS;AACzB,WAAO,EAAE,CAAC2C,EAAS/G,EAAAA,GAAK+G,EAAAA;AAE1B,QAAM7E,KAAQ0P,GAActN,IAAI,CAAA,EAAG4B,YAAAA,GAAAA,MAAiBA,EAAAA,GAC9C2L,KAAmB,EAAA,GAAK9K,GAAU7C,MAAM,EAAA,GAAK6C,EAAS7C,MAAMhC,OAAAA,GAAAA,EAAAA;AAElE,SAAO0P,GAAcxJ,OAAO,CAACC,IAAOxC,OAAAA;AAClC,UAAMiM,KAAcjM,GAAK3D,MAAM2D,GAAKK,UAAAA;AACpC,WAAO,EAAA,GACFmC,IAAAA,GACAxC,GAAK3D,OAER,CAAC4P,GAAY9R,EAAAA,GAAK,EAAA,GACb8R,IACH5N,MAAM,EAAA,GACD4N,GAAY5N,MACfsH,QAAQzE,EAAS/G,GAAAA,EAAAA,EAAAA;EAGtB,GAde,EAAE,CAAC+G,EAAS/G,EAAAA,GAAK6R,GAAAA,CAAAA;AAetB;ADNf,ICSaE,KAAa,CACxBhL,GACA6K,QACc,EACd1L,YAAYa,EAAS/G,IACrBkC,OAAOyP,GAAW5K,GAAU6K,EAAAA,EAAAA;ACMxB,SAAUI,GAAa/P,GAAAA;AAC3B,QAAM2C,KAAU3C,KAASA,EAAM2C,SAEzBqN,KAAkD,MACtDD,GAAa/P,CAAAA;AAEf,SAAO,EAMLiQ,oBAAoB,CAClBC,IACAC,IACAC,IACAC,KAA2CrO,CAAAA,OACzChC,EAAMC,MAAM+B,GAAKjE,EAAAA,EAAI0C,QAAAA;AAEvB,UAAM2K,KAAapL,EAAMC,MAAMkQ,EAAAA,GAGzBG,KAFaN,GAAAA,EAAIhO,KAAKoJ,GAAWrN,EAAAA,EAAI4K,SAAAA,IAGvCyC,KACApL,EAAMC,MAAMmL,GAAWnJ,KAAKsH,MAAAA;AAEhC,QAAA,CAAK+G;AAAc;AAEnB,UAAMC,KAAoBD,GAAarO,KAAKhC,SAAS,CAAA,GAiB/CuQ,KAAaxD,GACjBsD,IAhB4BC,KAC1BA,GAAkBpK,OAAO,CAAC0C,IAAQ9K,OAAAA;AAChC,YAAM0C,KAAM4P,GAAWrQ,EAAMC,MAAMlC,EAAAA,CAAAA;AACnC,UAAI0C,IAAK;AACP,cAAMmO,KAAiB,EACrB7Q,IAAAA,IAAAA,GACG0S,GAAWhQ,EAAAA,EAAAA;AAGhBoI,QAAAA,GAAO6B,KAAKkE,EAAAA;MACb;AACD,aAAO/F;IAAM,GACZ,CAAA,CAAA,IACH,CAAA,GAKFuH,GAAIM,GACJN,GAAIO,CAAAA,GAEAd,KACJU,GAAkBpO,UAClBnC,EAAMC,MAAMsQ,GAAkBC,GAAWpD,KAAAA,CAAAA,GAErCwD,KAAoB,EACxBC,WAAW,EAAA,GACNL,IACHX,aAAAA,GAAAA,GAEFiB,OAAO,KAAA;AAmBT,WAhBoBrK,GAAqBzG,EAAMC,OAAOiQ,EAAAA,EAE1CnF,QAAQ,CAAA,EAAG/I,MAAAA,IAAMqF,QAAAA,GAAAA,MAAAA;AAEvBA,MAAAA,MACF2I,GAAAA,EACGhO,KAAKA,GAAKjE,EAAAA,EACVmN,YAAaQ,CAAAA,OAASkF,GAAOE,QAAQpF,EAAAA;IACzC,CAAA,GAIHsE,GAAAA,EACGhO,KAAKsO,GAAavS,EAAAA,EAClB4N,YAAYuE,IAASxE,CAAAA,OAASkF,GAAOE,QAAQpF,EAAAA,GAEzCkF;EAAM,GAMfG,YAAU,MACDpO,IAGTqO,UAAQ,MACChR,EAAMC,OAOf+B,MAAKjE,CAAAA,OACImL,GAAYlJ,GAAOjC,EAAAA,GAM5BkT,qBAAAA;AACE,UAAMC,KAAYhL,OAAO0C,KAAK5I,EAAMC,KAAAA,EAAOoC,IAAKtE,CAAAA,OAAe,CAC7DA,IACA2L,KAAK1H,KAAKjE,EAAAA,EAAI6O,iBAAAA,CAAAA,CAAAA;AAEhB,WAAO5G,GAAYkL,EAAAA;EACpB,GAEDC,UAASC,CAAAA,OCnJG,SAAapR,IAAoBoR,IAAAA;AAC/C,QAAMC,KAAQrR,GAAM+J,OAAOqH,EAAAA;AAC3B,WAAO,EACLE,UAAQ,SAACvT,IAAAA;AACP,aAAOsT,GAAMpH,IAAIlM,EAAAA;IAClB,GACDwT,SAAO,WAAA;AACL,aAA6B,MAAtB7H,KAAK8H,IAAAA,EAAMrP;IACnB,GACDsP,OAAK,WAAA;AAEH,aADe/H,KAAK8H,IAAAA,EACN,CAAA;IACf,GACDE,MAAI,WAAA;AACF,UAAMzG,KAASvB,KAAK8H,IAAAA;AACpB,aAAOvG,GAAOA,GAAO9I,SAAS,CAAA;IAC/B,GACDqP,KAAG,WAAA;AACD,aAAO3K,MAAM8K,KAAKN,EAAAA;IACnB,GACDO,MAAI,WAAA;AACF,aAAOlI,KAAK8H,IAAAA,EAAMrP;IACnB,GACD0P,IAAE,SAACjE,IAAAA;AACD,aAAOlE,KAAK8H,IAAAA,EAAM5D,EAAAA;IACnB,GACDkE,KAAG,WAAA;AACD,aAAOT;IACT,EAAA;EAEJ,EDsH0BrR,GAAOoR,EAAAA,GAM7BW,YAAAA;AACE,WAAOC,KAAKC,UAAUvI,KAAKuH,mBAAAA,CAAAA;EAC5B,GAEDpN,mBAAoBqO,CAAAA,QAA2C,EAC7DpO,WACEyK,IAAAA;AAEA,QAAIvM,KE9JI,SACdiD,IACAsJ,IAAAA;AAEA,UAAI4D,KAAUlN;AAUd,aARuB,YAAA,OAAZkN,OACTA,KAAUvU,cAAAA,QAAMM,cAAckE,cAAAA,UAAU,CAAE,GAAE+P,EAAAA,IAOvC9D,GACL,EACEpM,MAAM,EACJN,MALWwQ,GAAQxQ,MAMnBG,OAAO,EAAA,GAAKqQ,GAAQrQ,MAAAA,EAAAA,EAAAA,GAGvBE,CAAAA,OAAAA;AACKuM,QAAAA,MACFA,GAAUvM,IAAMmQ,EAAAA;MACjB,CAAA;IAGP,EFmIoCD,IAAc,CAAClQ,IAAMiD,OAAAA;AAC/C,YAAM6C,KAAOL,GAAiBzH,EAAM2C,QAAQ+E,UAAU1F,GAAKC,KAAKN,IAAAA;AAEhEK,MAAAA,GAAKC,KAAKuG,cAAcxG,GAAKC,KAAKuG,eAAeV,IACjD9F,GAAKC,KAAK6F,OAAOA,IAEbyG,MACFA,GAAUvM,IAAMiD,EAAAA;IACjB,CAAA,GAGC0K,KAA4B,CAAA;AAahC,WAXIuC,GAAapQ,SAASoQ,GAAapQ,MAAM7D,aAC3C0R,KAAgB/R,cAAAA,QAAMmH,SAASqN,QAC7BF,GAAapQ,MAAM7D,QAAAA,EACnBkI,OAAmB,CAACC,IAAO2C,QACvBnL,cAAAA,QAAMyU,eAAetJ,EAAAA,KACvB3C,GAAMsE,KAAKsF,GAAAA,EAAInM,kBAAkBkF,EAAAA,EAAOjF,WAAWyK,EAAAA,CAAAA,GAE9CnI,KACN,CAAA,CAAA,IAGE0J,GAAW9N,IAAM2N,EAAAA;EACzB,EAAA,IAGH2C,qBAAsBC,CAAAA,QAAoC,EACxDC,OAAOjE,IAAAA;AACL,UAAMtM,KAAOmN,GAAgBmD,IAAgBvS,EAAM2C,QAAQ+E,QAAAA;AAC3D7I,cAAUoD,GAAKN,MAAM2G,EAAAA;AAErB,UAAMvK,KAA0B,YAAA,OAAdwQ,MAA0BA;AAQ5C,WANIxQ,MACFwD,GAAmB,6CAA6C,EAC9DC,SAAS,8DAAA,CAAA,GAINwO,GAAAA,EACJyC,eAAe,EAAA,GACV1U,KAAK,EAAEA,IAAAA,GAAAA,IAAO,CAAA,GAClBkE,MAAAA,GAAAA,CAAAA,EAEDuQ,OAAAA,CAAQzU,MAAMwQ,EAAAA;EAClB,EAAA,IAGHkE,gBAAiBzQ,CAAAA,QAAqB,EACpCwQ,QAAOjE,CAAAA,OACEF,GAAWrM,IAAOA,CAAAA,OAAAA;AACnBA,IAAAA,GAAKC,KAAKsH,WAAWmJ,OACvB1Q,GAAKC,KAAKsH,SAASjF;AAGrB,UAAMwD,KAAOL,GAAiBzH,EAAM2C,QAAQ+E,UAAU1F,GAAKC,KAAKN,IAAAA;AAChE9C,cAAmB,SAATiJ,IAAeQ,EAAAA,GACzBtG,GAAKC,KAAKuG,cAAcxG,GAAKC,KAAKuG,eAAeV,IACjD9F,GAAKC,KAAK6F,OAAOA,IAEbyG,MACFA,GAAUvM,EAAAA;EACX,CAAA,EAAA,IAKPqM,WAAW6D,IAAkCS,IAAAA;AAC3CpR,OAAmB,oBAAoB2Q,EAAAA,KAAiB,EACtD1Q,SAAS,2BAA2B0Q,EAAAA,iBAAAA,CAAAA;AAGtC,UAAMtO,KAAO8F,KAAK7F,kBAAkBqO,EAAAA,EAAcpO,WAAAA,GAE5C9B,KAAO4B,GAAK3D,MAAM2D,GAAKK,UAAAA;AAE7B,WAAK0O,MAIDA,GAAO5U,OACTiE,GAAKjE,KAAK4U,GAAO5U,KAGf4U,GAAO1Q,SACTD,GAAKC,OAAO,EAAA,GACPD,GAAKC,MAAAA,GACL0Q,GAAO1Q,KAAAA,IAIPD,MAdEA;EAeV,GAED4Q,UAAQ,MACC5S,EAAAA;AAGb;AG/PA,IAAa6S,KAA0B,SAAAC,GAAAA;AAAAC,EAAAA,IAAAF,IAAQG,EAAAA;AAAR,MAAAC,KAAAC,IAAAL,EAAAA;AAAA,WAAAA,KAAAA;AAAA,WAAAM,GAAAzJ,MAAAmJ,EAAAA,GAAAI,GAAAG,MAAA1J,MAAAmB,SAAAA;EAAA;AAgBpC,SAhBoCwI,GAAAR,IAAA,CAAA,EAAAtQ,KAAA,YAAAnE,OAGrC,WAAA;AACE,WAAO,EACLoC,SAAS,SAAC8S,IAAiBvV,IAAAA;IAAiB,GAC5CwV,QAAQ,SAACD,IAAiBvV,IAAAA;IAAiB,GAC3CyV,OAAO,SAACF,IAAiBvV,IAAAA;IAAiB,GAC1C2C,MAAM,SAAC4S,IAAiBvV,IAAAA;IAAiB,GACzC0V,MAAM,SAACH,IAAiBvV,IAAAA;IAAiB,GACzC2V,QAAQ,SACNJ,IACAK,IACAhR,IAAAA;IACI,EAAA;EAEV,EAAA,CAAA,CAAA,GAACkQ;AAAA,EAhBoC;AAAvC,IAmBsBe,KAEpB,SAAAC,GAAAA;AAAAd,EAAAA,IAAAa,IAAQE,EAAAA;AAAR,MAAAC,KAAAb,IAAAU,EAAAA;AAAA,WAAAA,KAAAA;AAAA,WAAAT,GAAAzJ,MAAAkK,EAAAA,GAAAG,GAAAX,MAAA1J,MAAAmB,SAAAA;EAAA;AAAA,SAAAwI,GAAAO,EAAAA;AAAA,EAAA;AArBF,ICSMI,KAA+B,SAACC,GAAAA;AACpCA,IAAEC,eAAAA;AACJ;ADXA,ICgBaC,KAAU,WAAA;AAkBrB,WAAqBvV,EAAAA,IAA6BwV,IAAAA;AAAsBjB,IAAAA,GAAAzJ,MAAAyK,CAAAA,GAAA3N,IAAAkD,MAAA,SAAA,MAAA,GAAAlD,IAAAkD,MAAA,cAAA,MAAA,GAAAlD,IAAAkD,MAAA,uBAAA,MAAA,GAAAlD,IAAAkD,MAAA,qCAAA,MAAA,GAAAlD,IAAAkD,MAAA,oBAV3B,IAAA,GAAIlD,IAAAkD,MAAA,mBAAA,MAAA,GAAAlD,IAAAkD,MAAA,gCAAA,MAAA,GAAAlD,IAAAkD,MAAA,aAAA,MAAA,GAAAlD,IAAAkD,MAAA,gBAAA,MAAA,GAAAlD,IAAAkD,MAAA,oBAAA,MAAA,GAU5BA,KAAK9K,QAALA,IAA6B8K,KAAU0K,aAAVA,IAChD1K,KAAK2K,sBAAsB,MAC3B3K,KAAK4K,oCAAoC,MAEzC5K,KAAK6K,kBAAkB,MACvB7K,KAAK8K,+BAA+B,MAEpC9K,KAAK+K,mBAAmB,MAExB/K,KAAKgL,YAAY,MACjBhL,KAAKiL,eAAejL,KAAKkL,gBAAAA,GAEzBlL,KAAKmL,qBAAAA,GAELnL,KAAKoL,mBAAmBpL,KAAKqL,SAASC,KAAKtL,IAAAA,GAC3CuL,OAAOC,iBAAiB,UAAUxL,KAAKoL,kBAAAA,IAAkB,GACzDG,OAAOC,iBAAiB,YAAYlB,IAAAA,KAA8B;EACpE;AAuOC,SAvOAX,GAAAc,GAAA,CAAA,EAAA5R,KAAA,WAAAnE,OAED,WAAA;AACE6W,WAAOE,oBAAoB,UAAUzL,KAAKoL,kBAAAA,IAAkB,GAC5DG,OAAOE,oBAAoB,YAAYnB,IAAAA,KAA8B;EACvE,EAAA,GAAC,EAAAzR,KAAA,YAAAnE,OAEO,SAAS6V,IAAAA;AACf,QAAMmB,KAAanB,GAAE9D,QACfrL,KAAW4E,KAAK9K,MAAMuE,MAAMnB,KAAKsC,EAAAA,EAAWd,IAAAA;AAKhD4R,IAAAA,cAAsBnS,WACtB6B,MACAA,GAASrE,OACT2U,GAAW9D,SAASxM,GAASrE,GAAAA,MAM/BiJ,KAAK8K,+BAA+B;EACtC,EAAA,GAAC,EAAAjS,KAAA,mBAAAnE,OAEO,WAAA;AACN,WACSqI,GACLiD,KAAK9K,MAAMuE,MAAM6N,SAAAA,GAFQ,UAAzBtH,KAAK0K,WAAWzS,OAGhB+H,KAAK0K,WAAWxQ,KAAK3D,MAAMyJ,KAAK0K,WAAWxQ,KAAKK,UAAAA,IAMlDyF,KAAK0K,WAAWnU,KAAAA;EAEpB,EAAA,GAEA,EAAAsC,KAAA,wBAAAnE,OACQ,WAAA;AAAoB,QAAAiX,KAAA3L;AAEG,cAAzBA,KAAK0K,WAAWzS,QAIpB+H,KAAKiL,aAAa5J,QAAQ,SAAqB1E,IAAAA;AAAAA,MAAAA,GAAZgB,UAKjCgO,GAAKzW,MAAMuE,MAAMnB,KAAAA,GALUA,KAKAjE,EAAAA,EAAImN,YAAY,SAACQ,IAAAA;AAC1C2J,QAAAA,GAAKX,YAAYhJ;MACnB,CAAA;IACF,CAAA;EACF,EAAA,GAAC,EAAAnJ,KAAA,iBAAAnE,OAEO,SACNkX,IACA5E,IACAC,IAAAA;AAIA,WAFqC2E,GAA7BvH,MAGAoG,EAAWoB,gBAAgB5E,MAHE2E,GAAxBE,SAIFrB,EAAWoB,gBAAgB5E,MAJD2E,GAAhBrH,OAKZkG,EAAWoB,gBAAgB7E,MALC4E,GAAVG,QAMjBtB,EAAWoB,gBAAgB7E;EAMvC,EAAA,GAAC,EAAAnO,KAAA,UAAAnE,OAEO,SAAOsX,IAAAA;AACb,WAAA,CACEhM,KAAK+K,oBACL/K,KAAK+K,iBAAiB5D,UAAUtH,OAAOxL,OAAO2X,GAAYnM,OAAOxL,MACjE2L,KAAK+K,iBAAiB5D,UAAUzD,UAAUsI,GAAYtI,SACtD1D,KAAK+K,iBAAiB5D,UAAUxD,UAAUqI,GAAYrI;EAM1D,EAAA,GAEA,EAAA9K,KAAA,sBAAAnE,OAGQ,SAAmByN,IAAAA;AAAmB,QAAA8J,KAAAjM,MAEtCkM,KAAgClM,KAAK8K;AAC3C,WACE9K,KAAK6K,oBAAoB1I,GAAc9N,MACvC6X,KAEOA,KAGF/J,GAAc5J,KAAKhC,MAAMkG,OAAO,SAAC0C,IAAQ9K,IAAAA;AAC9C,UAAM0C,KAAMkV,GAAK/W,MAAMuE,MAAMnB,KAAKjE,EAAAA,EAAIyF,IAAAA,EAAM/C;AAS5C,aAPIA,MACFoI,GAAO6B,KAAIjL,GAAA,EACT1B,IAAAA,GAAAA,GACG0S,GAAWhQ,EAAAA,CAAAA,CAAAA,GAIXoI;IACR,GAAE,CAAA,CAAA;EACL,EAAA,GAEA,EAAAtG,KAAA,qBAAAnE,OASQ,SAAkByX,IAAAA;AAAoB,QAAAC,KAAApM;AAG5C,QACEmM,OAAiBnM,KAAK2K,uBACtB3K,KAAK4K,mCACL;AACA,UAAMtS,KAAO0H,KAAK9K,MAAMuE,MACrBnB,KAAK0H,KAAK4K,iCAAAA,EACV9Q,IAAAA;AAEH,UAAIxB;AACF,eAAOA;IAEX;AAgBA,WAdkB,SAAZ+T,GAAa3S,IAAAA;AACjB,UAAMpB,KAAO8T,GAAKlX,MAAMuE,MAAMnB,KAAKoB,EAAAA,EAAQI,IAAAA;AAE3C,aAAIxB,MAAQA,GAAKC,KAAK0G,WACb3G,KAGJA,GAAKC,KAAKsH,SAIRwM,GAAU/T,GAAKC,KAAKsH,MAAAA,IAHlB;IAAA,EAMMsM,EAAAA;EACnB,EAAA,GAEA,EAAAtT,KAAA,oBAAAnE,OAIA,SAAiByX,IAAsBnF,IAAWC,IAAAA;AAChD,QAAI9E,KAAgBnC,KAAKsM,kBAAkBH,EAAAA;AAE3C,QAAKhK,OAILnC,KAAK2K,sBAAsBwB,IAC3BnM,KAAK4K,oCAAoCzI,GAAc9N,IAIrD8N,GAAc5J,KAAKsH,UACnBG,KAAKuM,cAAcxF,GAAW5E,GAAcpL,GAAAA,GAAMiQ,IAAGC,EAAAA,KAAAA,CAEpDjH,KAAK9K,MAAMuE,MAAMnB,KAAK6J,GAAc9N,EAAAA,EAAIuL,aAAAA,MAEzCuC,KAAgBnC,KAAK9K,MAAMuE,MAAMnB,KAAK6J,GAAc5J,KAAKsH,MAAAA,EAAQ/F,IAAAA,IAG9DqI,KAAL;AAIAnC,WAAK8K,+BAA+B9K,KAAKwM,mBAAmBrK,EAAAA,GAC5DnC,KAAK6K,kBAAkB1I,GAAc9N;AAErC,UAAMoY,KAAWnJ,GACfnB,IACAnC,KAAK8K,8BACL9D,IACAC,EAAAA;AAIF,UAAKjH,KAAK0M,OAAOD,EAAAA,GAAjB;AAIA,YAAIrF,KAAQpH,KAAKgL;AAGZ5D,QAAAA,MACHpH,KAAK9K,MAAMuE,MAAMnB,KAAK6J,GAAc9N,EAAAA,EAAI4N,YACtCjC,KAAKiL,aAAatS,IAAI,SAACgU,IAAAA;AAAU,iBAAKA,GAAWrU;QAAAA,CAAAA,GACjD,SAACsU,IAAAA;AACCxF,UAAAA,KAAQwF;QACV,CAAA;AAIJ,YAAMC,KAAgB1K,GAAc5J,KAAKhC,MAAMkW,GAAS/I,KAAAA,GAClDyC,KACJ0G,MAAiB7M,KAAK9K,MAAMuE,MAAMnB,KAAKuU,EAAAA,EAAe/S,IAAAA;AAUxD,eARAkG,KAAK+K,mBAAmB,EACtB5D,WAAAA,GAAAA,GAAAA,CAAAA,GACKsF,EAAAA,GAAQ,CAAA,GAAA,EACXtG,aAAAA,GAAAA,CAAAA,GAEFiB,OAAAA,GAAAA,GAGKpH,KAAK+K;MA1BZ;IAfA;EA0CF,EAAA,GAAC,EAAAlS,KAAA,gBAAAnE,OAED,WAAA;AACE,WAAOsL,KAAK+K;EACd,EAAA,CAAA,CAAA,GAACN;AAAA,EA1QoB;AAAAqC,IAAVrC,IAAAA,iBACY,EAAA;ACxBlB,IAAMsC,KAAe,SAC1BxC,GACAyC,IAAAA;AAGA,MAA+B,MAA3BA,GAAgBvU,UAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,KAAAA,UAAAA,CAAAA,GAAmC;AACrD,QAAAwU,KAA0BD,GAAgB,CAAA,EAAGE,sBAAAA,GAArCC,KAAAA,GAAAA,OAAOC,KAAAA,GAAAA,QACTC,KAASL,GAAgB,CAAA,EAAGM,UAAAA,IAAU;AAa5C,WAXAD,GAAOE,MAAMd,WAAqB,YAClCY,GAAOE,MAAMhJ,OAAc,SAC3B8I,GAAOE,MAAMlJ,MAAa,SAC1BgJ,GAAOE,MAAMJ,QAAK,GAAAK,OAAML,IAAS,IAAA,GACjCE,GAAOE,MAAMH,SAAM,GAAAI,OAAMJ,IAAU,IAAA,GACnCC,GAAOE,MAAME,gBAAgB,QAC7BJ,GAAOK,UAAUC,IAAI,aAAA,GAErBC,SAASC,KAAKC,YAAYT,EAAAA,GAC1B9C,EAAEwD,aAAaC,aAAaX,IAAQ,GAAG,CAAA,GAEhCA;EACT;AAMA,MAAMY,KAAYL,SAASpZ,cAAc,KAAA;AA0BzC,SAzBAyZ,GAAUV,MAAMd,WAAW,YAC3BwB,GAAUV,MAAMhJ,OAAO,SACvB0J,GAAUV,MAAMlJ,MAAa,SAC7B4J,GAAUV,MAAMJ,QAAQ,QACxBc,GAAUV,MAAMH,SAAS,QACzBa,GAAUV,MAAME,gBAAgB,QAChCQ,GAAUP,UAAUC,IAAI,uBAAA,GAExBX,GAAgB3L,QAAQ,SAACtK,IAAAA;AACvB,QAAqCA,KAAAA,GAAImW,sBAAAA,GAAjCC,KAAAA,GAAAA,OAAOC,KAAAA,GAAAA,QAAQ/I,KAAAA,GAAAA,KAAKE,KAAAA,GAAAA,MACtB8I,KAAStW,GAAIuW,UAAAA,IAAU;AAE7BD,IAAAA,GAAOE,MAAMd,WAAqB,YAClCY,GAAOE,MAAMhJ,OAAI,GAAAiJ,OAAMjJ,IAAQ,IAAA,GAC/B8I,GAAOE,MAAMlJ,MAAG,GAAAmJ,OAAMnJ,IAAO,IAAA,GAC7BgJ,GAAOE,MAAMJ,QAAK,GAAAK,OAAML,IAAS,IAAA,GACjCE,GAAOE,MAAMH,SAAM,GAAAI,OAAMJ,IAAU,IAAA,GACnCC,GAAOK,UAAUC,IAAI,aAAA,GAErBM,GAAUH,YAAYT,EAAAA;EACxB,CAAA,GAEAO,SAASC,KAAKC,YAAYG,EAAAA,GAC1B1D,EAAEwD,aAAaC,aAAaC,IAAW1D,EAAE2D,SAAS3D,EAAE4D,OAAAA,GAE7CF;AACT;AAtDO,ICgBMG,KAA6B,SAAAC,GAAAA;AAAAhF,EAAAA,IAAA+E,IAAQjF,EAAAA;AAAR,MAAAI,KAAAC,IAAA4E,EAAAA;AAAA,WAAAA,KAAAA;AAAA,QAAAzC;AAAAlC,IAAAA,GAAAzJ,MAAAoO,EAAAA;AAAA,aAAAE,KAAAnN,UAAA1I,QAAAwD,KAAA,IAAAkB,MAAAmR,EAAAA,GAAAC,KAAA,GAAAA,KAAAD,IAAAC;AAAAtS,MAAAA,GAAAsS,EAAAA,IAAApN,UAAAoN,EAAAA;AAaV,WAbUzR,IAAA0R,IAAA7C,KAAApC,GAAAkF,KAAA/E,MAAAH,IAAA,CAAAvJ,IAAAA,EAAAwN,OAAAvR,EAAAA,CAAAA,CAAAA,GAAA,wBAAA,MAAA,GAAAa,IAAA0R,IAAA7C,EAAAA,GAAA,cAAA,MAAA,GAAA7O,IAAA0R,IAAA7C,EAAAA,GAAA,cAYR,IAAA,GAAI7O,IAAA0R,IAAA7C,EAAAA,GAAA,6BACR,CAAA,CAAA,GAAEA;EAAA;AAgW7B,SAhW6BhC,GAAAyE,IAAA,CAAA,EAAAvV,KAAA,aAAAnE,OAE9B,WAAA;AACEsL,SAAK/G,QAAQ/D,MAAMuB,QAAQiY,YAAAA;EAC7B,EAAA,GAAC,EAAA7V,KAAA,YAAAnE,OAED,WAAA;AAAQ,QAAAuX,KAAAjM,MACA9K,KAAQ8K,KAAK/G,QAAQ/D;AAE3B,WAAO,EACL4B,SAAS,SAAC8S,IAAiBvV,IAAAA;AAGzB,aAFAa,GAAMuB,QAAQkF,OAAOtH,IAAIuV,EAAAA,GAElBqC,GAAK0C,QAAQ,SAAC9Y,IAAAA;AACnBA,QAAAA,GAAWgU,OAAOD,IAAIvV,EAAAA,GACtBwB,GAAWiU,MAAMF,IAAIvV,EAAAA,GACrBwB,GAAWkU,KAAKH,IAAIvV,EAAAA;MACtB,CAAA;IACD,GACDwV,QAAQ,SAACD,IAAiBvV,IAAAA;AACxB,UAAMua,KAAoB3C,GAAK4C,sBAC7BjF,IACA,aACA,SAACW,IAAAA;AACCA,QAAAA,GAAElF,MAAMyJ,gBAAAA;AAER,YAAIC,KAAwB,CAAA;AAE5B,YAAI1a,IAAI;AACN,cAAQoF,KAAUvE,GAAVuE,OACFuV,KAAqBvV,GAAMgO,SAAS,UAAA,EAAYK,IAAAA;AAAAA,WAChCmE,GAAKhT,QAAQgW,qBAAqB1E,EAAAA,KASnCyE,GAAmBlP,SAASzL,EAAAA,OAC/C0a,KAAwBC,GAAmBvR,OACzC,SAACyR,IAAAA;AACC,gBAAMjO,KAAcxH,GACjBnB,KAAK4W,EAAAA,EACLjO,YAAAA,IAAY,GACTL,KAAYnH,GAAMnB,KAAK4W,EAAAA,EAAYtO,UAAAA,IAAU;AAGnD,mBAAA,CAAIK,GAAYnB,SAASzL,EAAAA,KAAAA,CAAOuM,GAAUd,SAASzL,EAAAA;UAKrD,CAAA,IAIC0a,GAAsBjP,SAASzL,EAAAA,KAClC0a,GAAsB/N,KAAK3M,EAAAA;QAE/B;AAEAa,QAAAA,GAAMuB,QAAQmF,aAAa,YAAYmT,EAAAA;MACzC,CAAA,GAGII,KAAgBlD,GAAK4C,sBAAsBjF,IAAI,SAAS,SAACW,IAAAA;AAC7DA,QAAAA,GAAElF,MAAMyJ,gBAAAA;AAER,YACME,KADY9Z,GAAVuE,MACyBgO,SAAS,UAAA,EAAYK,IAAAA,GAEhDsH,KAAgBnD,GAAKhT,QAAQgW,qBAAqB1E,EAAAA,GAClD8E,KAAwBpD,GAAKqD,0BAA0BxP,SAC3DzL,EAAAA,GAGE0a,KAA4BC,IAAAA,EAAAA;AAE5BI,QAAAA,MAAiBC,MACnBN,GAAsBQ,OAAOR,GAAsBS,QAAQnb,EAAAA,GAAK,CAAA,GAChEa,GAAMuB,QAAQmF,aAAa,YAAYmT,EAAAA,KAAAA,CAC7BK,MAAiBJ,GAAmBvW,SAAS,KAEvDvD,GAAMuB,QAAQmF,aAAa,YAD3BmT,KAAwB,CAAC1a,EAAAA,CAAAA,GAI3B4X,GAAKqD,4BAA4BP;MACnC,CAAA;AAEA,aAAO,WAAA;AACLH,QAAAA,GAAAA,GACAO,GAAAA;MAAAA;IAEH,GACDrF,OAAO,SAACF,IAAiBvV,IAAAA;AACvB,UAAMob,KAAkBxD,GAAK4C,sBAC3BjF,IACA,aACA,SAACW,IAAAA;AACCA,QAAAA,GAAElF,MAAMyJ,gBAAAA,GACR5Z,GAAMuB,QAAQmF,aAAa,WAAWvH,EAAAA;MACxC,CAAA,GAGEqb,KAAwC;AAa5C,aAXIzD,GAAKhT,QAAQ0W,4BACfD,KAAmBzD,GAAK4C,sBACtBjF,IACA,cACA,SAACW,IAAAA;AACCA,QAAAA,GAAElF,MAAMyJ,gBAAAA,GACR5Z,GAAMuB,QAAQmF,aAAa,WAAW,IAAA;MACxC,CAAA,IAIG,WAAA;AACL6T,QAAAA,GAAAA,GAEKC,MAILA,GAAAA;MAAAA;IAEH,GACD3F,MAAM,SAACH,IAAiBgG,IAAAA;AACtB,UAAMC,KAAiB5D,GAAK4C,sBAC1BjF,IACA,YACA,SAACW,IAAAA;AAIC,YAHAA,GAAElF,MAAMyJ,gBAAAA,GACRvE,GAAEC,eAAAA,GAEGyB,GAAK6D,YAAV;AAIA,cAAMC,KAAY9D,GAAK6D,WAAWE,iBAChCJ,IACArF,GAAE2D,SACF3D,GAAE4D,OAAAA;AAGC4B,UAAAA,MAIL7a,GAAMuB,QAAQwZ,aAAaF,EAAAA;QAZ3B;MAaF,CAAA,GAGIG,KAAkBjE,GAAK4C,sBAC3BjF,IACA,aACA,SAACW,IAAAA;AACCA,QAAAA,GAAElF,MAAMyJ,gBAAAA,GACRvE,GAAEC,eAAAA;MACJ,CAAA;AAGF,aAAO,WAAA;AACL0F,QAAAA,GAAAA,GACAL,GAAAA;MAAAA;IAEH,GACD7Y,MAAM,SAAC4S,IAAiBvV,IAAAA;AACtB,UAAA,CAAKa,GAAMuE,MAAMnB,KAAKjE,EAAAA,EAAImN,YAAAA;AACxB,eAAO,WAAA;QAAO;AAGhBoI,MAAAA,GAAGuG,aAAa,aAAa,MAAA;AAE7B,UAAMC,KAAkBnE,GAAK4C,sBAC3BjF,IACA,aACA,SAACW,IAAAA;AACCA,QAAAA,GAAElF,MAAMyJ,gBAAAA;AAER,YAAQrV,KAAmBvE,GAAnBuE,OAAOhD,KAAYvB,GAAZuB,SAEXuY,KAAqBvV,GAAMgO,SAAS,UAAA,EAAYK,IAAAA,GAE9CsH,KAAgBnD,GAAKhT,QAAQgW,qBAAqB1E,EAAAA;AAC1B0B,QAAAA,GAAKqD,0BAA0BxP,SAC3DzL,EAAAA,MAKE2a,KADEI,KACuBJ,CAAAA,EAAAA,OAAAA,IAAAA,EAAAA,GAAoB3a,CAAAA,EAAAA,CAAAA,IAExB,CAACA,EAAAA,GAExBa,GAAMuB,QAAQmF,aAAa,YAAYoT,EAAAA,IAGzCvY,GAAQmF,aAAa,WAAWoT,EAAAA;AAEhC,YAAMqB,KAAerB,GAAmBrW,IACtC,SAACtE,IAAAA;AAAE,iBAAKoF,GAAMnB,KAAKjE,EAAAA,EAAIyF,IAAAA,EAAM/C;QAAAA,CAAAA;AAG/BkV,QAAAA,GAAKqE,uBAAuBvD,GAC1BxC,IACA8F,IACAjC,GAAqBmC,qBAAAA,GAGvBtE,GAAKvB,aAAa,EAChBzS,MAAM,YACN1B,OAAOyY,GAAAA,GAGT/C,GAAK6D,aAAa,IAAIrF,GACpBwB,GAAKhT,QAAQ/D,OACb+W,GAAKvB,UAAAA;MAET,CAAA,GAGI8F,KAAgBvE,GAAK4C,sBAAsBjF,IAAI,WAAW,SAACW,IAAAA;AAC/DA,QAAAA,GAAElF,MAAMyJ,gBAAAA,GAER7C,GAAKwE,YAAY,SAAC/F,IAAYqF,IAAAA;AACJ,oBAApBrF,GAAWzS,QAQf/C,GAAMuB,QAAQia,KACZhG,GAAWnU,OACXwZ,GAAU5I,UAAUtH,OAAOxL,IAL3B0b,GAAU5I,UAAUzD,SACW,YAA9BqM,GAAU5I,UAAUxD,QAAoB,IAAI,EAAA;QAOjD,CAAA;MACF,CAAA;AAEA,aAAO,WAAA;AACLiG,QAAAA,GAAGuG,aAAa,aAAa,OAAA,GAC7BC,GAAAA,GACAI,GAAAA;MAAAA;IAEH,GACDxG,QAAQ,SACNJ,IACA+G,IACA1X,IAAAA;AAEA2Q,MAAAA,GAAGuG,aAAa,aAAa,MAAA;AAE7B,UAAMC,KAAkBnE,GAAK4C,sBAC3BjF,IACA,aACA,SAACW,IAAAA;AAEC,YAAIrQ;AACJ,YAFAqQ,GAAElF,MAAMyJ,gBAAAA,GAEmB,cAAA,OAAhB6B,IAA4B;AACrC,cAAMxR,KAASwR,GAAAA;AAEbzW,UAAAA,KADEhG,cAAAA,QAAMyU,eAAexJ,EAAAA,IAChBjK,GAAMuE,MAAMU,kBAAkBgF,EAAAA,EAAQ/E,WAAAA,IAEtC+E;QAEX;AACEjF,UAAAA,KAAOhF,GAAMuE,MAAMU,kBAAkBwW,EAAAA,EAAavW,WAAAA;AAIpD6R,QAAAA,GAAKqE,uBAAuBvD,GAC1BxC,IACA,CAHUA,GAAEqG,aAAAA,GAIZxC,GAAqBmC,qBAAAA,GAEvBtE,GAAKvB,aAAa,EAChBzS,MAAM,OACNiC,MAAAA,GAAAA,GAGF+R,GAAK6D,aAAa,IAAIrF,GACpBwB,GAAKhT,QAAQ/D,OACb+W,GAAKvB,UAAAA;MAET,CAAA,GAGI8F,KAAgBvE,GAAK4C,sBAAsBjF,IAAI,WAAW,SAACW,IAAAA;AAC/DA,QAAAA,GAAElF,MAAMyJ,gBAAAA,GACR7C,GAAKwE,YAAY,SAAC/F,IAAYqF,IAAAA;AACJ,yBAApBrF,GAAWzS,SAOf/C,GAAMuB,QAAQ+E,YACZkP,GAAWxQ,MACX6V,GAAU5I,UAAUtH,OAAOxL,IAJ3B0b,GAAU5I,UAAUzD,SACW,YAA9BqM,GAAU5I,UAAUxD,QAAoB,IAAI,EAAA,GAO3C1K,UAAW4X,kBAAAA,SAAW5X,GAAQ6X,QAAAA,KAChC7X,GAAQ6X,SAASpG,GAAWxQ,IAAAA;QAEhC,CAAA;MACF,CAAA;AAEA,aAAO,WAAA;AACL0P,QAAAA,GAAGmH,gBAAgB,WAAA,GACnBX,GAAAA,GACAI,GAAAA;MAAAA;IAEJ,EAAA;EAEJ,EAAA,GAAC,EAAA3X,KAAA,eAAAnE,OAEO,SACNsc,IAAAA;AAEA,QAAM9b,KAAQ8K,KAAK/G,QAAQ/D;AAE3B,QAAK8K,KAAK8P,YAAV;AAIA,UAAMQ,KAAuBtQ,KAAKsQ,sBAE5BP,KAAY/P,KAAK8P,WAAWmB,aAAAA;AAE9BjR,WAAK0K,cAAcqF,MAAAA,CAAcA,GAAU3I,SAC7C4J,GAAWhR,KAAK0K,YAAYqF,EAAAA,GAG1BO,OACFA,GAAqBvN,WAAWmO,YAAYZ,EAAAA,GAC5CtQ,KAAKsQ,uBAAuB,OAG9BtQ,KAAK0K,aAAa,MAElBxV,GAAMuB,QAAQwZ,aAAa,IAAA,GAC3B/a,GAAMuB,QAAQmF,aAAa,WAAW,IAAA,GACtCoE,KAAK8P,WAAWla,QAAAA,GAEhBoK,KAAK8P,aAAa;IArBlB;EAsBF,EAAA,CAAA,CAAA,GAAC1B;AAAA,EA7WuC;AChBlB,SAAA+C,GACtBzK,GACA0K,IACAC,IAAAA;AACqB,MAArBC,KAAAA,UAAAA,SAAAA,KAAAA,WAAAA,UAAAA,CAAAA,IAAAA,UAAAA,CAAAA,IAAoB,GAEhBC,KAAI,GACNC,KAAI,GACJC,KAAI,GACJC,KAAI,GACJ/N,KAAQ+C,EAAI/C;AA8Bd,SA5Bc0N,KAAAA,GAID5M,UAMTgN,KAVUJ,GAUA7M,YACVkN,KAAIJ,IACJC,KAAc,aAAV5N,KAZM0N,GAYqBhN,MAZrBgN,GAYiCvF,QAC3C0F,KAbUH,GAaA9M,SARVkN,KAAIH,IACJI,KANUL,GAMA/M,aACViN,KAPUF,GAOAhN,KACVmN,KAAc,aAAV7N,KARM0N,GAQqB9M,OARrB8M,GAQkC9M,OARlC8M,GAQ+C7M,cAQvD4M,OACFG,KAAIH,GAAc/M,MAAM+M,GAAcO,QAAQtN,KAC9CmN,KAAIJ,GAAc7M,OAAO6M,GAAcO,QAAQpN,MAC/CkN,KACEL,GAAc5M,aACd4M,GAAcO,QAAQ5F,QACtBqF,GAAcO,QAAQpN,OACtB6M,GAAcQ,OAAOrN,OACrB6M,GAAcQ,OAAO7F,OACvB2F,KAAIJ,KAGD,EACLjN,KAAG,GAAAmJ,OAAK+D,IAAK,IAAA,GACbhN,MAAI,GAAAiJ,OAAKgE,IAAK,IAAA,GACdrE,OAAK,GAAAK,OAAKiE,IAAK,IAAA,GACfrE,QAAAA,GAAAA,OAAWsE,IAAC,IAAA,EAAA;AAEhB;AAAA5E,ID9BasB,IAQoByD,yBAAAA,GAAAA,KAAgBC,GAAAA,CAAAA;AElB1C,IAAMC,KAAwB,MAAA;AACnC,QAAA,EAAMhC,WAAEA,GAASiC,kBAAEA,IAAgBC,SAAEA,GAAAA,IAAYld,IAC9CuB,CAAAA,QAAW,EACVyZ,WAAWzZ,GAAMyZ,WACjBiC,kBAAkB1b,GAAM2C,QAAQ8W,WAChCkC,SAAS3b,GAAM2C,QAAQgZ,QAAAA,EAAAA,GAIrBhd,KAAUJ,IAAAA;AAehB,aAbAa,cAAAA,WAAU,MAAA;AACHT,IAAAA,OAIAgd,KAKLhd,GAAQid,OAAAA,IAJNjd,GAAQkd,QAAAA;EAIM,GACf,CAACF,IAAShd,EAAAA,CAAAA,GAER8a,IAIE7b,cAAAA,QAAMM,cAAc4d,IAAiB,EAC1CC,WAAWL,GAAiBK,WAC5B9E,OAAO,EAAA,GACF4D,GACDpB,EAAU5I,WACVJ,GAAWgJ,EAAU5I,UAAUtH,OAAO9I,GAAAA,GACtCgZ,EAAU5I,UAAUhB,eAClBY,GAAWgJ,EAAU5I,UAAUhB,YAAYpP,GAAAA,GAC7Cib,GAAiBV,SAAAA,GAEnBgB,iBAAiBvC,EAAU3I,QACvB4K,GAAiB5K,QACjB4K,GAAiBO,SACrBC,YAAYR,GAAiBQ,cAAc,gBAAA,GACvCR,GAAiBzE,SAAS,CAAA,EAAA,GAEhCkF,WAAW1C,EAAU5I,UAAUtH,OAAO9I,IAAAA,CAAAA,IAnB/B;AAoBP;AA7CG,ICGM2b,KAAS,CAAA,EAAGne,UAAAA,EAAAA,MAAAA;AACvB,QAAMW,SAAQJ,cAAAA,YAAWH,GAAAA,GAEnBM,SAAUO,cAAAA,SAAQ,MAAMN,GAAMuE,MAAM4N,WAAAA,EAAasL,SAASzd,EAAAA,GAAQ,CACtEA,EAAAA,CAAAA;AAGF,SAAKD,KAKHf,cAAAA,QAAAA,cAACU,IAAoBH,UAAS,EAAAC,OAAOO,GAAAA,GACnCf,cAAAA,QAAAM,cAACud,IAAwB,IAAA,GACxBxd,CAAAA,IANI;AAQP;ADnBG,IEIMqe,KAAkC,EAC7Crc,OAAO,CAAE,GACT8J,QAAQ,EACNM,SAAS,oBAAIkS,OACbvS,UAAU,oBAAIuS,OACdpS,SAAS,oBAAIoS,MAAAA,GAEf9C,WAAW,MACX9W,SAAS,EACP6Z,eAAe,MAAM,MACrB9Z,UAAU,CAAA,EAAGhB,QAAAA,EAAAA,MAAaA,GAC1B+a,iBAAiB,MAAM,MACvB/U,UAAU,CAAE,GACZiU,SAAAA,MACAlC,WAAW,EACT3I,OAAO,OACPmL,SAAS,mBAAA,GAEXI,UAAWzd,OACT,IAAIkZ,GAAqB,EACvBlZ,OAAAA,GACAya,yBAAAA,OACAV,sBAAuB1E,CAAAA,OAAAA,CAAAA,CAAoBA,GAAEyI,QAAAA,CAAAA,GAEjDC,gBAAgB,MAAA;AAAA,EAAA,EAAA;AF5Bb,IEgCMC,KAA0B,EACrCC,SC4Z2B,SAC3B7c,GACAmD,IAAAA;AAEA,SAAA1D,GAAAA,GAAA,CAAA,GAxac,SACdO,IACAmD,IAAAA;AAGA,QAAM2Z,KAAsB,SAC1BlZ,IACAmZ,IACAC,IAAAA;AA6CA,UAnCwB,SAAlBC,GAAmBlf,IAAYgf,IAAAA;AACnC,YAAM/a,KAAO4B,GAAK3D,MAAMlC,EAAAA;AAEM,oBAAA,OAAnBiE,GAAKC,KAAKN,QACnB9C,UACEmB,GAAM2C,QAAQ+E,SAAS1F,GAAKC,KAAK6F,IAAAA,GACjCQ,GAAsBC,QACpB,eAAa,GAAA2O,OACTlV,GAAKC,KAAKN,KAAamG,IAAAA,CAAAA,CAAAA,GAKjC9H,GAAMC,MAAMlC,EAAAA,IAAAA,GAAAA,GAAAA,CAAAA,GACPiE,EAAAA,GAAI,CAAA,GAAA,EACPC,MAAIxC,GAAAA,GAAA,CAAA,GACCuC,GAAKC,IAAAA,GAAI,CAAA,GAAA,EACZsH,QAAQwT,GAAAA,CAAAA,EAAAA,CAAAA,GAIR/a,GAAKC,KAAKhC,MAAMkC,SAAS,MAAA,OACpBnC,GAAMC,MAAMlC,EAAAA,EAAIkE,KAAKH,MAAM7D,UAClC+D,GAAKC,KAAKhC,MAAM8K,QAAQ,SAACmS,IAAAA;AAAW,iBAClCD,GAAgBC,IAAalb,GAAKjE,EAAAA;QAAAA,CAAAA,IAItCmI,OAAO+E,OAAOjJ,GAAKC,KAAKyB,WAAAA,EAAaqH,QAAQ,SAAC1H,IAAAA;AAAY,iBACxD4Z,GAAgB5Z,IAAcrB,GAAKjE,EAAAA;QAAAA,CAAAA;MAAAA,EAIvB6F,GAAKK,YAAY8Y,EAAAA,GAE5BA,MAAYnZ,GAAKK,eAAeK,IAArC;AAIA,YAAMiF,KAAS4T,GAAqBJ,EAAAA;AAEpC,YAAyB,YAArBC,GAAYrb;AAYhB4H,UAAAA,GAAOtH,KAAKyB,YAAYsZ,GAAYjf,EAAAA,IAAM6F,GAAKK;aAZ/C;AACE,cAAMmJ,KAAQ4P,GAAY5P;AAEb,kBAATA,KACF7D,GAAOtH,KAAKhC,MAAMgZ,OAAO7L,IAAO,GAAGxJ,GAAKK,UAAAA,IAExCsF,GAAOtH,KAAKhC,MAAMyK,KAAK9G,GAAKK,UAAAA;QAIhC;MAdA;IAAA,GAmBIkZ,KAAuB,SAACJ,IAAAA;AAC5Ble,gBAAUke,IAAUK,EAAAA;AACpB,UAAM7T,KAASvJ,GAAMC,MAAM8c,EAAAA;AAE3B,aADAle,UAAU0K,IAAQhC,EAAAA,GACXgC;IAAAA,GAGH8T,KAAa,SAAbA,GAActf,IAAAA;AAClB,UAAMqN,KAAapL,GAAMC,MAAMlC,EAAAA,GAC7B0O,KAAazM,GAAMC,MAAMmL,GAAWnJ,KAAKsH,MAAAA;AAgB3C,UAdI6B,GAAWnJ,KAAKhC,SAGlBqd,IAAIlS,GAAWnJ,KAAKhC,KAAAA,EAAO8K,QAAQ,SAACwS,IAAAA;AAAO,eAAKF,GAAWE,EAAAA;MAAAA,CAAAA,GAGzDnS,GAAWnJ,KAAKyB,eAClBwC,OAAO+E,OAAOG,GAAWnJ,KAAKyB,WAAAA,EAAarB,IAAI,SAACgB,IAAAA;AAAY,eAC1Dga,GAAWha,EAAAA;MAAAA,CAAAA,GAIKoJ,GAAWxK,KAAKhC,MAAMuJ,SAASzL,EAAAA,GAElC;AACf,YAAMyf,KAAiB/Q,GAAWxK,KAAKhC;AACvCud,QAAAA,GAAevE,OAAOuE,GAAetE,QAAQnb,EAAAA,GAAK,CAAA;MACpD,OAAO;AACL,YAAM0f,KAAWvX,OAAO0C,KAAK6D,GAAWxK,KAAKyB,WAAAA,EAAaga,KACxD,SAAC3f,IAAAA;AAAE,iBAAK0O,GAAWxK,KAAKyB,YAAY3F,EAAAA,MAAQA;QAAAA,CAAAA;AAE1C0f,QAAAA,MAAAA,OACKhR,GAAWxK,KAAKyB,YAAY+Z,EAAAA;MAEvC;AAAA,OC5IgC,SAACzd,IAAoBoD,IAAAA;AACvD8C,eAAO0C,KAAK5I,GAAM+J,MAAAA,EAAQgB,QAAQ,SAACxI,IAAAA;AACjC,cAAMob,KAAW3d,GAAM+J,OAAOxH,EAAAA;AAC1Bob,UAAAA,MAAYA,GAAS1T,OAAO0T,GAAS1T,IAAI7G,EAAAA,MAC3CpD,GAAM+J,OAAOxH,EAAAA,IAAO,IAAIga,IACtB1V,MAAM8K,KAAKgM,EAAAA,EAAUxW,OAAO,SAACpJ,IAAAA;AAAE,mBAAKqF,OAAWrF;UAAE,CAAA,CAAA;QAGvD,CAAA;MAAE,EDsIqBiC,IAAOjC,EAAAA,GAAAA,OACrBiC,GAAMC,MAAMlC,EAAAA;IAAAA;AAGrB,WAAO,EAULiG,uBAAAA,SAAsBJ,IAAgBmZ,IAAkBhf,IAAAA;AACtD,UAEM6f,KAFST,GAAqBJ,EAAAA,EAEF9a,KAAKyB,YAAY3F,EAAAA;AAE/C6f,MAAAA,MACFP,GAAWO,EAAAA,GAGbd,GAAoBlZ,IAAMmZ,IAAU,EAAEpb,MAAM,UAAU5D,IAAAA,GAAAA,CAAAA;IACvD,GASDsZ,KAAAA,SAAIwG,IAA0Bd,IAAmB3P,IAAAA;AAE/C,UAAInN,KAAQ,CAAC4d,EAAAA;AACThX,YAAMC,QAAQ+W,EAAAA,MAChBtc,GAAmB,6BAA6B,EAC9CC,SAAS,0BAAA,CAAA,GAEXvB,KAAQ4d,KAEV5d,GAAM8K,QAAQ,SAAC/I,IAAAA;AACb8a,QAAAA,GACE,EACE7c,OAAAA,IAAAA,CAAAA,GACG+B,GAAKjE,IAAKiE,EAAAA,GAEbiC,YAAYjC,GAAKjE,GAAAA,GAEnBgf,IACA,EAAEpb,MAAM,SAASyL,OAAAA,GAAAA,CAAAA;MAErB,CAAA;IACD,GASDlI,aAAAA,SAAYtB,IAAgBmZ,IAAmB3P,IAAAA;AAC7C0P,MAAAA,GAAoBlZ,IAAMmZ,IAAU,EAAEpb,MAAM,SAASyL,OAAAA,GAAAA,CAAAA;IACtD,GAMD0Q,QAAM,SAACpX,IAAAA;AACWD,SAAqBzG,GAAMC,OAAOyG,IAAU,EAC1DM,WAAAA,MACAC,QAAAA,KAAQ,CAAA,EAGF8D,QAAQ,SAAa1E,IAAAA;AAAA,YAAVrE,KAAAA,GAAAA;AACjBnD,kBAAAA,CACGsE,GAAMnB,KAAKA,GAAKjE,EAAAA,EAAI0L,eAAAA,GACrBsU,EAAAA,GAEFV,GAAWrb,GAAKjE,EAAAA;MAClB,CAAA;IACD,GAED8G,aAAW,SAACmZ,IAAAA;AACV,UAAMC,KACY,YAAA,OAATD,KAAoBhM,KAAKkM,MAAMF,EAAAA,IAASA,IAE3C9M,KAAYhL,OAAO0C,KAAKqV,EAAAA,EAAiB5b,IAAI,SAACtE,IAAAA;AAClD,YAAIqF,KAASrF;AAMb,eAJIA,OAAO2U,OACTtP,KAASkB,KAGJ,CACLlB,IACAD,GACGmP,oBAAoB2L,GAAgBlgB,EAAAA,CAAAA,EACpCyU,OAAO,SAACxQ,IAAAA;AAAI,iBAAMA,GAAKjE,KAAKqF;QAAO,CAAA,CAAA;MAE1C,CAAA;AAEAsG,WAAKnE,aAAaS,GAAYkL,EAAAA,CAAAA;IAC/B,GAQDkJ,MAAAA,SAAK1T,IAAwByX,IAAqB/Q,IAAAA;AAChD,UAAMxB,KAAUnF,GAAqBzG,GAAMC,OAAOyG,IAAU,EAC1DM,WAAAA,KAAW,CAAA,GAGPoX,KAAYpe,GAAMC,MAAMke,EAAAA,GAExBE,KAAoB,oBAAI9B;AAE9B3Q,MAAAA,GAAQb,QAAQ,SAAAzE,IAAuBsH,IAAAA;AAAK,YAAnBxC,KAAAA,GAANpJ,MACXsX,KAAWlO,GAAWrN,IACtBugB,KAAkBlT,GAAWnJ,KAAKsH;AAExCpG,QAAAA,GAAMnB,KAAKmc,EAAAA,EAAaxS,YAAY,CAAC2N,EAAAA,GAAW,SAAC5N,IAAAA;AAC/C,gBAAM,IAAI6S,MAAM7S,EAAAA;QAClB,CAAA,GAGA1L,GAAM2C,QAAQ8Z,gBACZrR,IACAgT,IACApe,GAAMC,MAAMqe,EAAAA,CAAAA;AAGd,YACME,KADgBxe,GAAMC,MAAMqe,EAAAA,EACOrc,KAAKhC;AAE9Coe,QAAAA,GAAkBhH,IAAImH,EAAAA;AAEtB,YAAMC,KAAWD,GAAmBtF,QAAQI,EAAAA;AAC5CkF,QAAAA,GAAmBC,EAAAA,IAAY,MAE/BL,GAAUnc,KAAKhC,MAAMgZ,OAAO7L,KAAQQ,IAAG,GAAG0L,EAAAA,GAE1CtZ,GAAMC,MAAMqZ,EAAAA,EAAUrX,KAAKsH,SAAS4U;MACtC,CAAA,GAEAE,GAAkBtT,QAAQ,SAAC9K,IAAAA;AACzB,YAAMkC,KAASlC,GAAMkC;AAErBmb,QAAAA,IAAIrd,EAAAA,EAAOye,QAAAA,EAAU3T,QAAQ,SAAC3M,IAAOgP,IAAAA;AACrB,mBAAVhP,MAIJ6B,GAAMgZ,OAAO9W,KAAS,IAAIiL,IAAO,CAAA;QACnC,CAAA;MACF,CAAA;IACD,GAED7H,cAAY,SAACtF,IAAAA;AACXyJ,WAAK0O,YAAAA,GACLpY,GAAMC,QAAQA;IACf,GAEDmY,aAAW,WAAA;AACT1O,WAAKpE,aAAa,YAAY,IAAA,GAC9BoE,KAAKpE,aAAa,WAAW,IAAA,GAC7BoE,KAAKpE,aAAa,WAAW,IAAA,GAC7BoE,KAAKiQ,aAAa,IAAA;IACnB,GAKDnU,OAAK,WAAA;AACHkE,WAAK0O,YAAAA,GACL1O,KAAKnE,aAAa,CAAA,CAAA;IACnB,GAODoZ,YAAU,SAAC/d,IAAAA;AACTA,MAAAA,GAAGZ,GAAM2C,OAAAA;IACV,GAED2C,cACE8L,SAAAA,IACAwN,IAAAA;AAUA,UARA5e,GAAM+J,OAAOqH,EAAAA,EAAWrG,QAAQ,SAAChN,IAAAA;AAC3BiC,QAAAA,GAAMC,MAAMlC,EAAAA,MACdiC,GAAMC,MAAMlC,EAAAA,EAAIgM,OAAOqH,EAAAA,IAAAA;MAE3B,CAAA,GAEApR,GAAM+J,OAAOqH,EAAAA,IAAa,oBAAImL,OAEzBqC,IAAL;AAIA,YAAMhT,KAAUnF,GAAqBzG,GAAMC,OAAO2e,IAAgB,EAChE3X,QAAAA,MACAD,WAAAA,KAAW,CAAA,GAGP6X,KAAuB,IAAItC,IAAI3Q,GAAQvJ,IAAI,SAAAyc,IAAAA;AAAO,iBAAA7K,GAAJjS,KAAgBjE;QAAE,CAAA,CAAA;AACtE8gB,QAAAA,GAAQ9T,QAAQ,SAAChN,IAAAA;AACfiC,UAAAA,GAAMC,MAAMlC,EAAAA,EAAIgM,OAAOqH,EAAAA,IAAAA;QACzB,CAAA,GACApR,GAAM+J,OAAOqH,EAAAA,IAAayN;MAX1B;IAYD,GAOD7d,WACE0F,SAAAA,IACA9F,IAAAA;AAEgB6F,SAAqBzG,GAAMC,OAAOyG,IAAU,EAC1DO,QAAAA,MACAD,WAAAA,KAAW,CAAA,EAGL+D,QAAQ,SAAAgU,IAAAA;AAAO,eAAOne,GAAGZ,GAAMC,MAAAA,GAApB+B,KAA+BjE,EAAAA,EAAIkE,KAAKc,MAAAA;MAAAA,CAAAA;IAC5D,GAQDsC,QAAOtH,SAAAA,IAAY0C,IAAAA;AACZT,MAAAA,GAAMC,MAAMlC,EAAAA,MAIjBiC,GAAMC,MAAMlC,EAAAA,EAAI0C,MAAMA;IACvB,GAEDkZ,cAAY,SAACF,IAAAA;AAETA,MAAAA,OAAAA,CACEA,GAAU5I,UAAUtH,OAAO9I,OAC1BgZ,GAAU5I,UAAUhB,eAAAA,CAClB4J,GAAU5I,UAAUhB,YAAYpP,SAGvCT,GAAMyZ,YAAYA;IACnB,GAODxY,WAAUlD,SAAAA,IAAYmD,IAAAA;AACpBlB,MAAAA,GAAMC,MAAMlC,EAAAA,EAAIkE,KAAKQ,SAASvB;IAC/B,GAODP,SACE+F,SAAAA,IACA9F,IAAAA;AAEgB6F,SAAqBzG,GAAMC,OAAOyG,IAAU,EAC1DO,QAAAA,MACAD,WAAAA,KAAW,CAAA,EAGL+D,QAAQ,SAAAiU,IAAAA;AAAO,eAAOpe,GAAGZ,GAAMC,MAAAA,GAApB+B,KAA+BjE,EAAAA,EAAIkE,KAAKH,KAAAA;MAAAA,CAAAA;IAC5D,GAEDmd,YAAU,SAACL,IAAAA;AACT,UAAIA,IAAgB;AAClB,YAAMhT,KAAUnF,GAAqBzG,GAAMC,OAAO2e,IAAgB,EAChE3X,QAAAA,MACAD,WAAAA,KAAW,CAAA;AAGb0C,aAAKpE,aACH,YACAsG,GAAQvJ,IAAI,SAAA6c,IAAAA;AAAO,iBAAAjL,GAAJjS,KAAgBjE;QAAE,CAAA,CAAA;MAErC;AACE2L,aAAKpE,aAAa,YAAY,IAAA;AAGhCoE,WAAKpE,aAAa,WAAW,IAAA;IAC/B,EAAA;EAEJ,EAOetF,GAAOmD,EAAAA,CAAAA,GAAM,CAAA,GAAA,EAGxBgc,UAAQ,SACNve,IAAAA;AAKoBT,QAAAA,KAAAA,IAAYuJ,MAAInJ,EAAAA;AAGpCK,IAAAA,GAAGZ,GAAOG,EAAAA;EACZ,EAAA,CAAA;AAEJ,GD/aEif,yBAAyB,CACvB,UACA,gBACA,cACA,eACA,cACA,cAAA,GAEFC,kBAAmBrf,OAAAA;AAIjBkG,SAAO0C,KAAK5I,EAAM+J,MAAAA,EAAQgB,QAASuU,CAAAA,OAAAA;AACjBzY,UAAM8K,KAAK3R,EAAM+J,OAAOuV,EAAAA,KAAc,CAAA,CAAA,EAE9CvU,QAAShN,CAAAA,OAAAA;AACViC,QAAMC,MAAMlC,EAAAA,KACfiC,EAAM+J,OAAOuV,EAAAA,EAAWxB,OAAO/f,EAAAA;IAChC,CAAA;EACD,CAAA,GAMJmI,OAAO0C,KAAK5I,EAAMC,KAAAA,EAAO8K,QAAShN,CAAAA,OAAAA;AAChC,UAAMiE,KAAOhC,EAAMC,MAAMlC,EAAAA;AAEzBmI,WAAO0C,KAAK5G,GAAK+H,MAAAA,EAAQgB,QAASuU,CAAAA,OAAAA;AACRtd,MAAAA,GAAK+H,OAAOuV,EAAAA,KAIlCtf,EAAM+J,OAAOuV,EAAAA,KAAAA,CACZtf,EAAM+J,OAAOuV,EAAAA,EAAWrV,IAAIjI,GAAKjE,EAAAA,MAElCiE,GAAK+H,OAAOuV,EAAAA,IAAAA;IACb,CAAA;EACD,CAAA;AACF,EAAA;AFzEC,IEkFMC,KAAiB,CAC5B5c,GACA6c,OAOOC,GACL7C,IACA,EAAA,GACKN,IACH3Z,SAAS,EAAA,GACJ2Z,GAAmB3Z,SAAAA,GACnBA,EAAAA,EAAAA,GAGPoN,IACAyP,EAAAA;AFrGG,IKSM1Z,KAAS,CAAA,EAAG7H,UAAAA,GAAAA,GAAa0E,GAAAA,MAAAA;AAAAA,aAEhCA,GAAQ+E,YACV7I,UAC8B,YAAA,OAArB8D,GAAQ+E,YAAAA,CACZb,MAAMC,QAAQnE,GAAQ+E,QAAAA,KACF,SAArB/E,GAAQ+E,UACVgY,EAAAA;AAIJ,QAAMC,KAAmBjb,UAAO/B,EAAAA,GAE1B7C,KAAUyf,GACdI,GAAWhb,SACX,CAAC3E,IAAO4f,IAAeC,IAA4B1c,IAAO2c,OAAAA;AACxD,QAAA,CAAKD;AACH;AAGF,UAAA,EAAME,SAAEA,IAAAA,GAAYC,GAAAA,IAAoBH;AAExC,aAASjS,KAAI,GAAGA,KAAImS,GAAQ5d,QAAQyL,MAAK;AACvC,YAAA,EAAMqS,MAAEA,GAAAA,IAASF,GAAQnS,EAAAA,GACnBsS,KACJD,GAAK9d,SAAS,KAAiB,YAAZ8d,GAAK,CAAA,KAA8B,WAAZA,GAAK,CAAA;AAajD,UARE,CAACE,GAAgBC,QAAQD,GAAgBE,QAAAA,EAAU7W,SAHpCwW,GAAgBre,IAAAA,KAM/Bqe,GAAgBM,WAEhBN,GAAgBre,OAAOqe,GAAgBM,OAAO,CAAA,IAI9C,CAAC,YAAY,aAAA,EAAe9W,SAASwW,GAAgBre,IAAAA,KACrDue,IACA;AACAJ,QAAAA,GAAYS,CAAAA,OAAAA;AACNvgB,UAAAA,GAAM2C,QAAQga,kBAChB3c,GAAM2C,QAAQga,eACZ4D,IACAX,IACAI,IACA7c,EAAAA;QAEH,CAAA;AAEH;MACD;IACF;EAAA,CAAA;AAiCL,SA5BM/D,aAAU,MAAA;AACTU,IAAAA,MAAAA,WAKH6C,GAAQgZ,WACR7b,GAAQqD,MAAM4N,WAAAA,EAAa4K,YAAYhZ,GAAQgZ,WAKjD7b,GAAQK,QAAQwe,WAAY6B,CAAAA,OAAAA;AAC1BA,MAAAA,GAAc7E,UAAUhZ,GAAQgZ;IAAO,CAAA;EACvC,GACD,CAAC7b,IAAS6C,GAAQgZ,OAAAA,CAAAA,GAEfvc,aAAU,MAAA;AACdU,IAAAA,GAAQ2gB,UACLzQ,CAAAA,QAAO,EACNxL,MAAM1E,GAAQqD,MAAM4O,UAAAA,EAAAA,IAEtB,MAAA;AACEjS,MAAAA,GAAQqD,MAAM4N,WAAAA,EAAayL,cAAc1c,GAAQqD,KAAAA;IAAM,CAAA;EAE1D,GACA,CAACrD,EAAAA,CAAAA,GAECA,KAKHlC,iBAACS,IAAcF,UAAS,EAAAC,OAAO0B,GAAAA,GAC7B5B,iBAACke,IAAQ,MAAAne,CAAAA,CAAAA,IALJ;AAOP;AAAA,IAAA,KAAA,CAAA,UAAA,MAAA;AAAA,IAAA,KAAA,CAAA,OAAA;AAAA,IAAA,KAAA,CAAA,OAAA;AAAA,IAAA,KAAA,CAAA,uBAAA,OAAA;AAAA,IAAA,KAAA,CAAA,uBAAA,OAAA;AAAA,ICzGEyiB,KAAc,SAACjU,GAAAA;AACnB,MACE1C,KAGE0C,EAHF1C,QAAM4W,KAGJlU,EAFFxK,MAAe+I,KAAAA,GAAP/K,OAAmByD,KAAAA,GAAAA,aACxBkd,KAAcvgB,IACfoM,GADelM,EAAAA,GAEbsgB,KAAkBxS,OAAWyS,iBAAAA,SAAUrU,CAAAA,CAAAA;AAW7C,SAAO,EACLzK,MAXFyK,IAAUhN,GAAAA,GAAAA,GAAA,CAAA,GACLohB,EAAAA,GACAD,EAAAA,GAAc,CAAA,GAAA,EACjB7W,QAAAA,GAAAA,GAAAA,CAAAA,GACK8W,GAAgB9W,MAAAA,GAChBA,EAAAA,GAELtJ,KAAKgM,EAAWhM,OAAOogB,GAAgBpgB,IAAAA,CAAAA,GAKvCuK,YAAAA,IACAtH,aAAAA,GAAAA;AAEJ;ADmFI,ICjFSqd,KAAoB,SAACC,GAAKC,IAAAA;AACrC,MAAeC,KAAyBD,GAAhChhB,OAAoBkhB,KAAAA,IAAYF,IAAGG,EAAAA,GAC5BC,KAAyBL,EAAhC/gB,OAAoBqhB,KAAAA,IAAYN,GAAGO,EAAAA;AAC3CC,SAAOF,EAAAA,EAASG,QAAQN,EAAAA;AAExB,MAAMO,KAAqBxb,OAAO0C,KAAKsY,EAAAA,EAAU/a,OAAO,SAACC,IAAOrI,IAAAA;AACdmjB,QAATlf,KAAI3B,IAAK6gB,GAASnjB,EAAAA,GAAd4jB,EAAAA;AAE3C,WADAvb,GAAMrI,EAAAA,IAAMiE,IACLoE;EACR,GAAE,CAAE,CAAA,GAECwb,KAAqB1b,OAAO0C,KAAKyY,EAAAA,EAAUlb,OAAO,SAACC,IAAOrI,IAAAA;AACdsjB,QAATrf,KAAI3B,IAAKghB,GAAStjB,EAAAA,GAAd8jB,EAAAA;AAE3C,WADAzb,GAAMrI,EAAAA,IAAMiE,IACLoE;EACR,GAAE,CAAE,CAAA;AAELob,SAAOI,EAAAA,EAAoBH,QAAQC,EAAAA;AACrC;AD+DI,IC7DSI,KAAkB,SAAChd,GAAAA;AAC9B,MAAM7E,KAAQ,CAAA;AAmDd,SAlDqB,SAAf8hB,GAAgBC,IAAAA;AACpB,QAAsDtB,KAAAA,GAAYsB,EAAAA,GAApDvV,KAAAA,GAANzK,MAAkBgJ,KAAAA,GAAAA,YAAYtH,KAAAA,GAAAA;AACtCzD,IAAAA,GAAMwM,GAAW1O,EAAAA,IAAM0O,IAEnBzB,MACFA,GAAWD,QAAQ,SAACkX,IAAerU,IAAAA;AACjC,UAII8S,KAAAA,GAAYuB,EAAAA,GAHRC,KAAAA,GAANlgB,MACYmgB,KAAAA,GAAZnX,YACaoX,KAAAA,GAAb1e;AAEFwe,MAAAA,GAAUjgB,KAAKsH,SAASkD,GAAW1O,IACnCkC,GAAMiiB,GAAUnkB,EAAAA,IAAMmkB,IACtBzV,GAAWxK,KAAKhC,MAAM2N,EAAAA,IAAKsU,GAAUnkB,IACrCgkB,GAAAA,GAAAA,GAAAA,CAAAA,GACKG,EAAAA,GAAS,CAAA,GAAA,EACZjgB,MAAIxC,GAAAA,GAAA,CAAA,GACCyiB,GAAUjgB,IAAAA,GAAI,CAAA,GAAA,EACjBhC,OAAOkiB,MAAmB,CAAA,GAC1Bze,aAAa0e,MAAyB,CAAE,EAAA,CAAA,EAAA,CAAA,CAAA;IAG9C,CAAA,GAGE1e,MACFwC,OAAO0C,KAAKlF,EAAAA,EAAaqH,QAAQ,SAAC0S,IAAAA;AAChC,UAAA4E,KAII3B,GAAYhd,GAAY+Z,EAAAA,CAAAA,GAHpByE,KAAAA,GAANlgB,MACYmgB,KAAAA,GAAZnX,YACaoX,KAAAA,GAAb1e;AAEF+I,MAAAA,GAAWxK,KAAKyB,YAAY+Z,EAAAA,IAAYyE,GAAUnkB,IAElDmkB,GAAUjgB,KAAKsH,SAASkD,GAAW1O,IACnCkC,GAAMiiB,GAAUnkB,EAAAA,IAAMmkB,IACtBH,GAAAA,GAAAA,GAAAA,CAAAA,GACKG,EAAAA,GAAS,CAAA,GAAA,EACZjgB,MAAIxC,GAAAA,GAAA,CAAA,GACCyiB,GAAUjgB,IAAAA,GAAI,CAAA,GAAA,EACjBhC,OAAOkiB,MAAmB,CAAA,GAC1Bze,aAAa0e,MAAyB,CAAE,EAAA,CAAA,EAAA,CAAA,CAAA;IAG9C,CAAA;EAAA,EAIStd,CAAAA,GAEN7E;AACT;ADQI,ICNSqiB,KAAkB,WAAA;AAAsB,MAArBtiB,IAAQ6K,UAAA1I,SAAA,KAAA,WAAA0I,UAAA,CAAA,IAAAA,UAAA,CAAA,IAAA,CAAA,GACvB/F,KAAqB9E,EAA5BC,OAAiB8J,KAAW/J,EAAX+J;AAEzB,SACKuS,GAAAA,GAAAA,GAAAA,CAAAA,GAAAA,EAAAA,GACAtc,CAAAA,GAAK,CAAA,GAAA,EACRC,OAAO6E,KAAWgd,GAAgBhd,EAAAA,IAAY,CAAE,GAChDiF,QAAMtK,GAAAA,GAAA,CAAA,GACD6c,GAAmBvS,MAAAA,GAClBA,MAAU,CAAA,CAAA,EAAA,CAAA;AAGpB;", "names": ["Symbol", "Symbol", "Symbol", "Map", "Map", "Map", "othValue", "Uint8Array", "Symbol", "Uint8Array", "n", "Promise", "Set", "WeakMap", "Map", "Promise", "Set", "WeakMap", "Uint8Array", "Symbol", "key", "die", "error", "args", "e", "errors", "msg", "apply", "Error", "length", "map", "s", "join", "isDraft", "value", "DRAFT_STATE", "isDraftable", "proto", "Object", "getPrototypeOf", "Ctor", "hasOwnProperty", "call", "constructor", "Function", "toString", "objectCtorString", "Array", "isArray", "DRAFTABLE", "_value$constructor", "isMap", "isSet", "each", "obj", "iter", "enumerableOnly", "getArchtype", "Object", "keys", "ownKeys", "for<PERSON>ach", "key", "entry", "index", "thing", "state", "DRAFT_STATE", "type_", "Array", "isArray", "isMap", "isSet", "has", "prop", "prototype", "hasOwnProperty", "call", "get", "set", "propOrOldValue", "value", "t", "add", "is", "x", "y", "target", "hasMap", "Map", "hasSet", "Set", "latest", "copy_", "base_", "shallowCopy", "base", "slice", "descriptors", "getOwnPropertyDescriptors", "i", "length", "desc", "writable", "configurable", "enumerable", "create", "getPrototypeOf", "freeze", "deep", "isFrozen", "isDraft", "isDraftable", "clear", "delete", "dontMutateFrozenCollections", "die", "getPlugin", "pluginKey", "plugin", "plugins", "loadPlugin", "implementation", "getCurrentScope", "currentScope", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "drafts_", "revokeDraft", "parent_", "enterScope", "immer", "immer_", "canAutoFreeze_", "unfinalizedDrafts_", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "useProxies_", "willFinalizeES5_", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "NOTHING", "undefined", "rootScope", "path", "childValue", "finalizeProperty", "scope_", "finalized_", "draft_", "resultEach", "generatePatches_", "parentState", "targetObject", "rootPath", "targetIsSet", "res", "assigned_", "concat", "autoFreeze_", "peek", "getDescriptorFromProto", "source", "proto", "getOwnPropertyDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "prepareCopy", "createProxy", "parent", "proxyMap_", "proxySet_", "isManual_", "traps", "objectTraps", "arrayTraps", "Proxy", "revocable", "revoke", "proxy", "createES5Proxy_", "push", "current", "currentImpl", "copy", "archType", "hasChanges_", "copyHelper", "from", "state", "enablePatches", "deepClonePatchValue", "obj", "isDraftable", "Array", "isArray", "map", "isMap", "Map", "from", "entries", "n", "isSet", "Set", "cloned", "Object", "create", "getPrototypeOf", "key", "has", "immerable", "clonePatchValueIfNeeded", "isDraft", "ADD", "loadPlugin", "applyPatches_", "draft", "patches", "for<PERSON>ach", "patch", "path", "op", "base", "i", "length", "parentType", "getArchtype", "p", "die", "get", "join", "type", "value", "set", "push", "splice", "add", "delete", "generatePatches_", "basePath", "inversePatches", "type_", "base_", "copy_", "each", "assigned_", "assignedValue", "origValue", "concat", "a", "unshift", "generateReplacementPatches_", "baseValue", "replacement", "NOTHING", "enableMapSet", "__extends", "d", "b", "__", "constructor", "extendStatics", "prototype", "prepareMapCopy", "prepareSetCopy", "createProxy", "scope_", "immer_", "drafts_", "assertUnrevoked", "revoked_", "JSON", "stringify", "latest", "setPrototypeOf", "__proto__", "hasOwnProperty", "DraftMap", "target", "parent", "DRAFT_STATE", "parent_", "getCurrentScope", "modified_", "finalized_", "draft_", "this", "isManual_", "defineProperty", "size", "<PERSON><PERSON><PERSON><PERSON>", "clear", "cb", "thisArg", "_value", "call", "_this", "keys", "values", "r", "iterator", "iteratorSymbol", "_this2", "next", "done", "_this3", "DraftSet", "result", "proxyMap_", "proxySet_", "value", "currentScope", "hasSymbol", "Symbol", "hasMap", "Map", "hasSet", "Set", "hasProxies", "Proxy", "revocable", "Reflect", "NOTHING", "for", "DRAFTABLE", "DRAFT_STATE", "iteratorSymbol", "iterator", "errors", "data", "path", "op", "plugin", "thing", "objectCtorString", "Object", "prototype", "constructor", "ownKeys", "getOwnPropertySymbols", "obj", "getOwnPropertyNames", "concat", "getOwnPropertyDescriptors", "target", "res", "for<PERSON>ach", "key", "getOwnPropertyDescriptor", "plugins", "objectTraps", "get", "state", "prop", "source", "latest", "has", "desc", "getDescriptorFromProto", "_desc$get", "call", "draft_", "undefined", "finalized_", "isDraftable", "peek", "base_", "prepareCopy", "copy_", "createProxy", "scope_", "immer_", "set", "modified_", "current", "currentState", "assigned_", "is", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "deleteProperty", "owner", "writable", "configurable", "type_", "enumerable", "defineProperty", "die", "getPrototypeOf", "setPrototypeOf", "arrayTraps", "each", "fn", "arguments", "apply", "this", "parseInt", "Immer", "config", "base", "recipe", "patchListener", "defaultBase", "self", "_this", "args", "produce", "draft", "_this2", "result", "scope", "enterScope", "proxy", "<PERSON><PERSON><PERSON><PERSON>", "revokeScope", "leaveScope", "Promise", "then", "usePatchesInScope", "processResult", "error", "autoFreeze_", "freeze", "p", "ip", "getPlugin", "generateReplacementPatches_", "produceWithPatches", "patches", "inversePatches", "nextState", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "isDraft", "isManual_", "finishDraft", "useProxies_", "applyPatches", "i", "length", "patch", "slice", "applyPatchesImpl", "applyPatches_", "immer", "bind", "ROOT_NODE", "DEPRECATED_ROOT_NODE", "ERROR_NOPARENT", "ERROR_DUPLICATE_NODEID", "ERROR_INVALID_NODEID", "ERROR_TOP_LEVEL_ELEMENT_NO_ID", "ERROR_MOVE_CANNOT_DROP", "ERROR_MOVE_INCOMING_PARENT", "ERROR_MOVE_OUTGOING_PARENT", "ERROR_MOVE_NONCANVAS_CHILD", "ERROR_MOVE_TO_NONCANVAS_PARENT", "ERROR_MOVE_TOP_LEVEL_NODE", "ERROR_MOVE_TO_DESCENDANT", "ERROR_NOT_IN_RESOLVER", "ERROR_CANNOT_DRAG", "ERROR_INVALID_NODE_ID", "ERROR_DELETE_TOP_LEVEL_NODE", "ERROR_RESOLVER_NOT_AN_OBJECT", "ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER", "ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT", "ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT", "G", "t", "n", "r", "Y", "W", "z", "e", "Q", "V", "i", "X", "o", "a", "c", "s", "u", "Z", "HISTORY_ACTIONS", "UNDO", "REDO", "THROTTLE", "IGNORE", "MERGE", "CLEAR", "History", "_classCallCheck", "this", "_defineProperty", "_createClass", "key", "value", "patches", "inversePatches", "length", "pointer", "timeline", "timestamp", "Date", "now", "throttleRate", "_this$timeline$this$p", "currPatches", "currInversePatches", "getTime", "concat", "_toConsumableArray", "add", "_this$timeline$this$p2", "state", "canUndo", "applyPatches", "canRedo", "useMethods", "methodsOrOptions", "initialState", "queryMethods", "patchListener", "methodsFactory", "history", "useMemo", "ignoreHistoryForActionsRef", "useRef", "normalizeHistoryRef", "methods", "current", "ignoreHistoryForActions", "normalizeHistory", "patchListenerRef", "stateRef", "reducer", "action", "finalState", "query", "createQuery", "_produceWithPatches2", "_slicedToArray", "produceWithPatches", "draft", "_methodsFactory2", "type", "undo", "redo", "clear", "_objectSpread", "_methodsFactory", "payload", "_action$payload", "params", "slice", "apply", "nextState", "cb", "normalizedDraft", "includes", "produce", "throttleAdd", "config", "rate", "merge", "getState", "useCallback", "watcher", "Watcher", "dispatch", "newState", "notify", "useEffect", "actions", "actionTypes", "Object", "keys", "reduce", "accum", "_len", "arguments", "Array", "_key", "throttle", "filter", "_len2", "_key2", "ignore", "_len3", "_key3", "_len4", "_key4", "subscribe", "collector", "collectOnCreate", "queries", "_queryMethods", "enableMapSet", "enablePatches", "onChange", "_this", "subscriber", "Subscriber", "subscribers", "push", "unsubscribe", "bind", "index", "indexOf", "splice", "for<PERSON>ach", "collect", "recollect", "isEqualWith", "collected", "err", "console", "warn", "getDOMInfo", "el", "getBoundingClientRect", "x", "y", "top", "left", "bottom", "right", "width", "height", "style", "window", "getComputedStyle", "margin", "parseInt", "marginLeft", "marginRight", "marginBottom", "marginTop", "padding", "paddingLeft", "paddingRight", "paddingBottom", "paddingTop", "outerWidth", "Math", "round", "outerHeight", "inFlow", "parentElement", "parent", "parentStyle", "overflow", "float", "display", "position", "tagName", "useCollector", "store", "initial", "collectorRef", "onCollect", "renderCollected", "setRenderCollected", "useState", "getRandomId", "EventHandlerUpdates", "nanoid", "ConnectorRegistry", "WeakMap", "Map", "element", "existingId", "elementIdMap", "get", "newId", "set", "connectorName", "elementId", "getElementId", "connectorPayload", "existingConnector", "getByElement", "name", "isEqual", "required", "disable", "cleanup", "id", "getConnectorId", "registry", "enable", "connector", "options", "remove", "isEnabled", "delete", "connectors", "EventHandlers", "Set", "onDisable", "listener", "HandlerDisabled", "onEnable", "Handler<PERSON><PERSON>bled", "eventName", "bindedListener", "craft", "stopPropagation", "blockedEvents", "blockingElements", "blockingElement", "contains", "addEventListener", "removeEventListener", "_this2", "handlers", "activeConnectorIds", "canRegisterConnectors", "connectorsToRegister", "entries", "_ref", "_ref2", "handler", "registerConnector", "register", "connectorId", "opts", "instance", "connectorsToCleanup", "proxiedHandlers", "Proxy", "target", "receiver", "Reflect", "args", "createProxyHandlers", "DerivedEventHandlers", "_EventHandlers", "_super", "derived", "_assertThisInitialized", "call", "unsubscribeParentHandlerListener", "listen", "msg", "_get", "_getPrototypeOf", "prototype", "setRef", "ref", "node", "cloneWithRef", "newRef", "previousRef", "invariant", "cloneElement", "wrapHookToRecognizeElement", "hook", "elementOrNode", "isValidElement", "Error", "wrapConnectorHooks", "RenderIndicator", "className", "parentDom", "indicator", "React", "createElement", "opacity", "borderStyle", "borderWidth", "borderColor", "zIndex", "ownerDocument", "document", "ReactDOM", "createPortal", "body", "deprecationWarning", "name", "payload", "message", "suggest", "doc", "concat", "console", "warn", "isClientSide", "window", "isLinux", "test", "navigator", "userAgent", "isChromium", "NodeContext", "React", "createContext", "NodeProvider", "id", "related", "children", "createElement", "Provider", "value", "EditorContext", "EventHandlerContext", "useEventHandler", "useContext", "useInternalEditor", "collector", "handler", "store", "invariant", "ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT", "collected", "useCollector", "connectorsUsage", "useMemo", "createConnectorsUsage", "useEffect", "register", "cleanup", "connectors", "wrapConnectorHooks", "_objectSpread", "inContext", "ye", "useInternalNode", "collect", "context", "ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT", "state", "nodes", "EditorActions", "actions", "editorConnectors", "_objectWithoutProperties", "_useInternalEditor", "_excluded", "connect", "dom", "drag", "setProp", "cb", "throttleRate", "history", "throttle", "setCustom", "setHidden", "bool", "inNodeContext", "me", "useNode", "_useInternalNode", "deprecationWarning", "suggest", "SimpleElement", "render", "type", "cloneElement", "De<PERSON>ult<PERSON>ender", "props", "hydrationTimestamp", "node", "data", "_hydrationTimestamp", "length", "Fragment", "map", "NodeElement", "key", "RenderNodeToElement", "hidden", "onRender", "options", "defaultElementProps", "is", "canvas", "custom", "elementPropToNodeData", "Element", "elementProps", "query", "nodeId", "linkedNodeId", "useState", "ERROR_TOP_LEVEL_ELEMENT_NO_ID", "get", "existingNode", "linkedNodes", "linkedElement", "tree", "parseReactElement", "toNodeTree", "ignore", "addLinkedNodeFromTree", "rootNodeId", "deprecateCanvasComponent", "<PERSON><PERSON>", "RenderRootNode", "timestamp", "ROOT_NODE", "<PERSON>ame", "json", "isLoaded", "useRef", "current", "initialData", "deserialize", "rootNode", "Children", "only", "jsx", "addNodeTree", "NodeSelectorType", "getPublicActions", "setDOM", "setNodeEvent", "replaceNodes", "reset", "useEditor", "internalActions", "args", "connectEditor", "WrappedComponent", "Editor", "connectNode", "fromEntries", "pairs", "Object", "reduce", "accum", "_ref", "_ref2", "_slicedToArray", "_defineProperty", "getNodesFromSelector", "selector", "config", "items", "Array", "isArray", "mergedConfig", "existOnly", "idOnly", "nodeSelectors", "filter", "item", "exists", "_typeof", "ERROR_INVALID_NODEID", "CACHED_RESOLVER_DATA", "resolveComponent", "resolver", "comp", "component", "resolvedName", "name", "reversed", "Map", "_i", "_Object$entries", "entries", "_Object$entries$_i", "set", "ERROR_NOT_IN_RESOLVER", "replace", "displayName", "reduceType", "serializeComp", "isCanvas", "keys", "result", "prop", "child", "serializeNode", "nodeData", "NodeHelpers", "ERROR_INVALID_NODE_ID", "nodeHelpers", "isRoot", "isLinkedNode", "parent", "includes", "isTopLevelNode", "this", "isDeletable", "isParentOfTopLevelNodes", "isParentOfTopLevelCanvas", "isSelected", "events", "selected", "has", "isHovered", "hovered", "isDragged", "dragged", "ancestors", "deep", "appendParentNode", "depth", "push", "descendants", "include<PERSON>nly", "arguments", "appendChildNode", "for<PERSON>ach", "childNodes", "values", "isDraggable", "onError", "targetNode", "ERROR_MOVE_TOP_LEVEL_NODE", "ERROR_MOVE_NONCANVAS_CHILD", "rules", "canDrag", "ERROR_CANNOT_DRAG", "err", "isDroppable", "targets", "newParentNode", "ERROR_MOVE_TO_NONCANVAS_PARENT", "canMoveIn", "ERROR_MOVE_INCOMING_PARENT", "parentNodes", "canDrop", "ERROR_MOVE_CANNOT_DROP", "targetDeepNodes", "ERROR_MOVE_TO_DESCENDANT", "currentParentNode", "ERROR_DUPLICATE_NODEID", "parentNodeId", "parentNode", "canMoveOut", "ERROR_MOVE_OUTGOING_PARENT", "toSerializedNode", "descendantId", "decendants", "isTopLevelCanvas", "findPosition", "dims", "posX", "posY", "index", "where", "leftLimit", "xLimit", "yLimit", "xCenter", "yCenter", "dimDown", "i", "len", "dim", "top", "outerHeight", "left", "outerWidth", "inFlow", "getNodeTypeName", "createNode", "newNode", "normalize", "actualType", "getRandomNodeId", "Date", "now", "info", "mergedProps", "userComponentConfig", "craft", "defaultProps", "relatedNodeContext", "deserializeComp", "main", "deserializeNode", "Comp", "Props", "ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER", "join", "_child<PERSON><PERSON><PERSON>", "mergeNodes", "childrenNodes", "nodeWithChildren", "currentNode", "mergeTrees", "QueryMethods", "_", "getDropPlaceholder", "source", "target", "pos", "nodesToDOM", "targetParent", "targetParentNodes", "dropAction", "getDOMInfo", "x", "y", "output", "placement", "error", "getOptions", "getNodes", "getSerializedNodes", "nodePairs", "getEvent", "eventType", "event", "contains", "isEmpty", "all", "first", "last", "from", "size", "at", "raw", "serialize", "JSON", "stringify", "reactElement", "element", "toArray", "isValidElement", "parseSerializedNode", "serializedNode", "toNode", "parseFreshNode", "DEPRECATED_ROOT_NODE", "extras", "getState", "CoreEventHandlers", "_EventHandlers", "_inherits", "EventHandlers", "_super", "_createSuper", "_classCallCheck", "apply", "_createClass", "el", "select", "hover", "drop", "create", "UserElement", "DerivedCoreEventHandlers", "_DerivedEventHandlers", "DerivedEventHandlers", "_super2", "documentDragoverEventHandler", "e", "preventDefault", "Positioner", "dragTarget", "currentDropTargetId", "currentDropTargetCanvasAncestorId", "currentTargetId", "currentTargetChildDimensions", "currentIndicator", "dragError", "draggedNodes", "getDraggedNodes", "validateDraggedNodes", "onScrollListener", "onScroll", "bind", "window", "addEventListener", "removeEventListener", "scrollBody", "_this", "domInfo", "BORDER_OFFSET", "bottom", "right", "newPosition", "_this2", "existingTargetChildDimensions", "dropTargetId", "_this3", "get<PERSON>anvas", "getCanvasAncestor", "isNearBorders", "getChildDimensions", "position", "isDiff", "sourceNode", "dropError", "currentNodeId", "ee", "createShadow", "shadowsToCreate", "_shadowsToCreate$0$ge", "getBoundingClientRect", "width", "height", "shadow", "cloneNode", "style", "concat", "pointerEvents", "classList", "add", "document", "body", "append<PERSON><PERSON><PERSON>", "dataTransfer", "setDragImage", "container", "clientX", "clientY", "DefaultEventHandlers", "_CoreEventHandlers", "_len", "_key", "_assertThisInitialized", "call", "clearEvents", "reflect", "unbindOnMouseDown", "addCraftEventListener", "stopPropagation", "newSelectedElementIds", "selectedElementIds", "isMultiSelectEnabled", "selectedId", "unbindOnClick", "isMultiSelect", "isNodeAlreadySelected", "currentSelectedElementIds", "splice", "indexOf", "unbindMouseover", "unbindMouseleave", "removeHoverOnMouseleave", "targetId", "unbindDragOver", "positioner", "indicator", "computeIndicator", "setIndicator", "unbindDragEnter", "setAttribute", "unbindDragStart", "selectedDOMs", "draggedElementShadow", "forceSingleDragShadow", "unbindDragEnd", "dropElement", "move", "userElement", "currentTarget", "isFunction", "onCreate", "removeAttribute", "onDropNode", "getIndicator", "<PERSON><PERSON><PERSON><PERSON>", "movePlaceholder", "canvasDOMInfo", "bestTargetDomInfo", "thickness", "t", "l", "w", "h", "padding", "margin", "isChromium", "isLinux", "RenderEditorIndicator", "indicatorOptions", "enabled", "enable", "disable", "RenderIndicator", "className", "backgroundColor", "success", "transition", "parentDom", "Events", "handlers", "editorInitialState", "Set", "onNodesChange", "onBeforeMoveEnd", "metaKey", "normalizeNodes", "ActionMethodsWithConfig", "methods", "addNodeTreeToParent", "parentId", "addNodeType", "iterateChildren", "childNodeId", "getParentAndValidate", "ERROR_NOPARENT", "deleteNode", "_toConsumableArray", "childId", "parent<PERSON><PERSON><PERSON><PERSON>", "linkedId", "find", "eventSet", "existingLinkedNode", "nodeToAdd", "delete", "ERROR_DELETE_TOP_LEVEL_NODE", "input", "dehydratedNodes", "parse", "newParentId", "newParent", "nodesArrToCleanup", "currentParentId", "Error", "currentParentNodes", "oldIndex", "reverse", "setOptions", "nodeIdSelector", "nodeIds", "_ref3", "_ref4", "_ref5", "selectNode", "_ref6", "setState", "ignoreHistoryForActions", "normalizeHistory", "eventName", "useEditorStore", "patchListener", "useMethods", "ERROR_RESOLVER_NOT_AN_OBJECT", "optionsRef", "previousState", "actionPerformedWithPatches", "normalizer", "patches", "actionPerformed", "path", "isModifyingNodeData", "HISTORY_ACTIONS", "IGNORE", "THROTTLE", "params", "draft", "editorOptions", "subscribe", "getTestNode", "_parentNode$data", "restParentNode", "validParentNode", "cloneDeep", "expectEditorState", "lhs", "rhs", "nodesRhs", "restRhs", "_excluded2", "nodesLhs", "restLhs", "_excluded3", "expect", "toEqual", "nodesRhsSimplified", "_excluded4", "nodesLhsSimplified", "_excluded5", "createTestNodes", "iterateNodes", "testNode", "childTestNode", "childNode", "grandChildNodes", "grandChildLinkedNodes", "_getTestNode3", "createTestState"]}