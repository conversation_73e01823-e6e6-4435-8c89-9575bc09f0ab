import { useState, useEffect } from 'react'
import { ArrowLeft, Download, Users, CreditCard, Clock, CheckCircle } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { templateAPI, studentAPI, cardAPI } from '../services/api'
import toast from 'react-hot-toast'

export default function BulkCardGenerator() {
  const navigate = useNavigate()
  const [templates, setTemplates] = useState([])
  const [students, setStudents] = useState([])
  const [selectedTemplate, setSelectedTemplate] = useState('')
  const [generating, setGenerating] = useState(false)
  const [bulkJobs, setBulkJobs] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchData()
    fetchBulkJobs()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch templates
      const templatesResponse = await templateAPI.getAll()
      setTemplates(templatesResponse.data)
      
      // Fetch active students
      const studentsResponse = await studentAPI.getAll({ limit: 1000 })
      const activeStudents = studentsResponse.data.students?.filter(s => s.status === 'active') || []
      setStudents(activeStudents)
      
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const fetchBulkJobs = async () => {
    try {
      const response = await fetch('http://localhost:5001/api/cards/bulk-jobs')
      const jobs = await response.json()
      setBulkJobs(jobs)
    } catch (error) {
      console.error('Error fetching bulk jobs:', error)
    }
  }

  const handleBulkGenerate = async () => {
    if (!selectedTemplate) {
      toast.error('Please select a template')
      return
    }

    if (students.length === 0) {
      toast.error('No active students found')
      return
    }

    try {
      setGenerating(true)
      
      const response = await fetch(`http://localhost:5001/api/cards/bulk-generate/${selectedTemplate}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const result = await response.json()
      
      if (response.ok) {
        toast.success(`Bulk generation started! Processing ${result.totalStudents} students`)
        fetchBulkJobs() // Refresh jobs list
      } else {
        toast.error(result.error || 'Failed to start bulk generation')
      }
      
    } catch (error) {
      console.error('Error starting bulk generation:', error)
      toast.error('Failed to start bulk generation')
    } finally {
      setGenerating(false)
    }
  }

  const getJobStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600'
      case 'processing': return 'text-blue-600'
      case 'failed': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getJobStatusIcon = (status) => {
    switch (status) {
      case 'completed': return CheckCircle
      case 'processing': return Clock
      default: return Clock
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="card p-6">
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate(-1)}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Bulk Card Generator</h1>
          <p className="text-gray-600">Generate ID cards for all students at once</p>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6 text-center">
          <Users className="mx-auto h-8 w-8 text-blue-600 mb-2" />
          <div className="text-2xl font-bold text-gray-900">{students.length}</div>
          <div className="text-sm text-gray-600">Active Students</div>
        </div>
        <div className="card p-6 text-center">
          <CreditCard className="mx-auto h-8 w-8 text-green-600 mb-2" />
          <div className="text-2xl font-bold text-gray-900">{templates.length}</div>
          <div className="text-sm text-gray-600">Available Templates</div>
        </div>
        <div className="card p-6 text-center">
          <Download className="mx-auto h-8 w-8 text-purple-600 mb-2" />
          <div className="text-2xl font-bold text-gray-900">{bulkJobs.length}</div>
          <div className="text-sm text-gray-600">Generation Jobs</div>
        </div>
      </div>

      {/* Bulk Generation Form */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Generate Cards for All Students</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Template
            </label>
            <select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value)}
              className="input"
            >
              <option value="">Choose a template...</option>
              {templates.map(template => (
                <option key={template.id} value={template.id}>
                  {template.name} {template.is_default ? '(Default)' : ''}
                </option>
              ))}
            </select>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Users className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">Bulk Generation Info</h4>
                <p className="text-sm text-blue-700 mt-1">
                  This will generate ID cards for all {students.length} active students using the selected template.
                  The process runs in the background and you can monitor progress below.
                </p>
              </div>
            </div>
          </div>

          <button
            onClick={handleBulkGenerate}
            disabled={!selectedTemplate || generating || students.length === 0}
            className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {generating ? 'Starting Generation...' : `Generate Cards for ${students.length} Students`}
          </button>
        </div>
      </div>

      {/* Recent Jobs */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Recent Generation Jobs</h3>
            <button
              onClick={fetchBulkJobs}
              className="btn-secondary btn-sm"
            >
              Refresh
            </button>
          </div>
        </div>
        <div className="card-content">
          {bulkJobs.length > 0 ? (
            <div className="space-y-4">
              {bulkJobs.map((job) => {
                const StatusIcon = getJobStatusIcon(job.status)
                const progress = job.total_students > 0 ? (job.completed_students / job.total_students) * 100 : 0
                
                return (
                  <div key={job.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <StatusIcon className={`h-5 w-5 ${getJobStatusColor(job.status)}`} />
                        <span className="font-medium text-gray-900">{job.template_name}</span>
                        <span className={`text-sm ${getJobStatusColor(job.status)} capitalize`}>
                          {job.status}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {new Date(job.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    
                    <div className="mb-2">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Progress: {job.completed_students} / {job.total_students}</span>
                        <span>{Math.round(progress)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                    
                    {job.status === 'completed' && (
                      <div className="text-sm text-green-600">
                        ✓ Completed on {new Date(job.completed_at).toLocaleString()}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No generation jobs yet</h3>
              <p className="mt-1 text-sm text-gray-500">
                Start your first bulk generation above.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
