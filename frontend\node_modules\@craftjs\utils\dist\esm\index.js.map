{"version": 3, "file": "index.js", "sources": ["../../src/constants.ts", "../../src/History.ts", "../../src/useMethods.ts", "../../src/getDOMInfo.ts", "../../src/useCollector.tsx", "../../src/getRandomId.ts", "../../src/EventHandlers/interfaces.ts", "../../src/EventHandlers/ConnectorRegistry.ts", "../../src/EventHandlers/EventHandlers.ts", "../../src/EventHandlers/isEventBlockedByDescendant.ts", "../../src/EventHandlers/DerivedEventHandlers.ts", "../../src/EventHandlers/wrapConnectorHooks.tsx", "../../src/RenderIndicator.tsx", "../../src/useEffectOnce.tsx", "../../src/deprecate.ts", "../../src/platform.ts"], "sourcesContent": ["export const ROOT_NODE = 'ROOT';\nexport const DEPRECATED_ROOT_NODE = 'canvas-ROOT';\n\n// TODO: Use a better way to store/display error messages\nexport const ERROR_NOPARENT = 'Parent id cannot be ommited';\nexport const ERROR_DUPLICATE_NODEID =\n  'Attempting to add a node with duplicated id';\nexport const ERROR_INVALID_NODEID =\n  'Node does not exist, it may have been removed';\nexport const ERROR_TOP_LEVEL_ELEMENT_NO_ID =\n  'A <Element /> that is used inside a User Component must specify an `id` prop, eg: <Element id=\"text_element\">...</Element> ';\nexport const ERROR_MISSING_PLACEHOLDER_PLACEMENT =\n  'Placeholder required placement info (parent, index, or where) is missing';\nexport const ERROR_MOVE_CANNOT_DROP =\n  'Node cannot be dropped into target parent';\nexport const ERROR_MOVE_INCOMING_PARENT = 'Target parent rejects incoming node';\nexport const ERROR_MOVE_OUTGOING_PARENT =\n  'Current parent rejects outgoing node';\nexport const ERROR_MOVE_NONCANVAS_CHILD =\n  'Cannot move node that is not a direct child of a Canvas node';\nexport const ERROR_MOVE_TO_NONCANVAS_PARENT =\n  'Cannot move node into a non-Canvas parent';\nexport const ERROR_MOVE_TOP_LEVEL_NODE = 'A top-level Node cannot be moved';\nexport const ERROR_MOVE_ROOT_NODE = 'Root Node cannot be moved';\n\nexport const ERROR_MOVE_TO_DESCENDANT = 'Cannot move node into a descendant';\nexport const ERROR_NOT_IN_RESOLVER =\n  'The component type specified for this node (%node_type%) does not exist in the resolver';\nexport const ERROR_INFINITE_CANVAS =\n  \"The component specified in the <Canvas> `is` prop has additional Canvas specified in it's render template.\";\nexport const ERROR_CANNOT_DRAG =\n  'The node has specified a canDrag() rule that prevents it from being dragged';\nexport const ERROR_INVALID_NODE_ID = 'Invalid parameter Node Id specified';\nexport const ERROR_DELETE_TOP_LEVEL_NODE =\n  'Attempting to delete a top-level Node';\n\nexport const ERROR_RESOLVER_NOT_AN_OBJECT = `Resolver in <Editor /> has to be an object. For (de)serialization Craft.js needs a list of all the User Components. \n    \nMore info: https://craft.js.org/r/docs/api/editor#props`;\n\nexport const ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER = `An Error occurred while deserializing components: Cannot find component <%displayName% /> in resolver map. Please check your resolver in <Editor />\n\nAvailable components in resolver: %availableComponents%\n\nMore info: https://craft.js.org/r/docs/api/editor#props`;\n\nexport const ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT = `You can only use useEditor in the context of <Editor />. \n\nPlease only use useEditor in components that are children of the <Editor /> component.`;\n\nexport const ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT = `You can only use useNode in the context of <Editor />. \n\nPlease only use useNode in components that are children of the <Editor /> component.`;\n", "import { Patch, applyPatches } from 'immer';\n\ntype Timeline = Array<{\n  patches: Patch[];\n  inversePatches: Patch[];\n  timestamp: number;\n}>;\n\nexport const HISTORY_ACTIONS = {\n  UNDO: 'HISTORY_UNDO',\n  REDO: 'HISTORY_REDO',\n  THROTTLE: 'HISTORY_THROTTLE',\n  IGNORE: 'HISTORY_IGNORE',\n  MERGE: 'HISTORY_MERGE',\n  CLEAR: 'HISTORY_CLEAR',\n};\n\nexport class History {\n  timeline: Timeline = [];\n  pointer = -1;\n\n  add(patches: Patch[], inversePatches: Patch[]) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    this.pointer = this.pointer + 1;\n    this.timeline.length = this.pointer;\n    this.timeline[this.pointer] = {\n      patches,\n      inversePatches,\n      timestamp: Date.now(),\n    };\n  }\n\n  throttleAdd(\n    patches: Patch[],\n    inversePatches: Patch[],\n    throttleRate: number = 500\n  ) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    if (this.timeline.length && this.pointer >= 0) {\n      const {\n        patches: currPatches,\n        inversePatches: currInversePatches,\n        timestamp,\n      } = this.timeline[this.pointer];\n\n      const now = new Date();\n      const diff = now.getTime() - timestamp;\n\n      if (diff < throttleRate) {\n        this.timeline[this.pointer] = {\n          timestamp,\n          patches: [...currPatches, ...patches],\n          inversePatches: [...inversePatches, ...currInversePatches],\n        };\n        return;\n      }\n    }\n\n    this.add(patches, inversePatches);\n  }\n\n  merge(patches: Patch[], inversePatches: Patch[]) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    if (this.timeline.length && this.pointer >= 0) {\n      const {\n        patches: currPatches,\n        inversePatches: currInversePatches,\n        timestamp,\n      } = this.timeline[this.pointer];\n\n      this.timeline[this.pointer] = {\n        timestamp,\n        patches: [...currPatches, ...patches],\n        inversePatches: [...inversePatches, ...currInversePatches],\n      };\n      return;\n    }\n\n    this.add(patches, inversePatches);\n  }\n\n  clear() {\n    this.timeline = [];\n    this.pointer = -1;\n  }\n\n  canUndo() {\n    return this.pointer >= 0;\n  }\n\n  canRedo() {\n    return this.pointer < this.timeline.length - 1;\n  }\n\n  undo(state) {\n    if (!this.canUndo()) {\n      return;\n    }\n\n    const { inversePatches } = this.timeline[this.pointer];\n    this.pointer = this.pointer - 1;\n    return applyPatches(state, inversePatches);\n  }\n\n  redo(state) {\n    if (!this.canRedo()) {\n      return;\n    }\n\n    this.pointer = this.pointer + 1;\n    const { patches } = this.timeline[this.pointer];\n    return applyPatches(state, patches);\n  }\n}\n", "// https://github.com/pelotom/use-methods\nimport produce, {\n  Patch,\n  produceWithPatches,\n  enableMapSet,\n  enablePatches,\n} from 'immer';\nimport isEqualWith from 'lodash/isEqualWith';\nimport { useMemo, useEffect, useRef, useCallback } from 'react';\n\nimport { History, HISTORY_ACTIONS } from './History';\nimport { Delete } from './utilityTypes';\n\nenableMapSet();\nenablePatches();\n\nexport type SubscriberAndCallbacksFor<\n  M extends MethodsOrOptions,\n  Q extends QueryMethods = any\n> = {\n  subscribe: Watcher<StateFor<M>>['subscribe'];\n  getState: () => { prev: StateFor<M>; current: StateFor<M> };\n  actions: CallbacksFor<M>;\n  query: QueryCallbacksFor<Q>;\n  history: History;\n};\n\nexport type StateFor<M extends MethodsOrOptions> = M extends MethodsOrOptions<\n  infer S,\n  any\n>\n  ? S\n  : never;\n\nexport type CallbacksFor<\n  M extends MethodsOrOptions\n> = M extends MethodsOrOptions<any, infer R>\n  ? {\n      [T in ActionUnion<R>['type']]: (\n        ...payload: ActionByType<ActionUnion<R>, T>['payload']\n      ) => void;\n    } & {\n      history: {\n        undo: () => void;\n        redo: () => void;\n        clear: () => void;\n        throttle: (\n          rate?: number\n        ) => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n        merge: () => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n        ignore: () => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n      };\n    }\n  : {};\n\nexport type Methods<S = any, R extends MethodRecordBase<S> = any, Q = any> = (\n  state: S,\n  query: Q\n) => R;\n\nexport type Options<S = any, R extends MethodRecordBase<S> = any, Q = any> = {\n  methods: Methods<S, R, Q>;\n  ignoreHistoryForActions: ReadonlyArray<keyof MethodRecordBase>;\n  normalizeHistory?: (state: S) => void;\n};\n\nexport type MethodsOrOptions<\n  S = any,\n  R extends MethodRecordBase<S> = any,\n  Q = any\n> = Methods<S, R, Q> | Options<S, R, Q>;\n\nexport type MethodRecordBase<S = any> = Record<\n  string,\n  (...args: any[]) => S extends object ? S | void : S\n>;\n\nexport type Action<T = any, P = any> = {\n  type: T;\n  payload?: P;\n  config?: Record<string, any>;\n};\n\nexport type ActionUnion<R extends MethodRecordBase> = {\n  [T in keyof R]: { type: T; payload: Parameters<R[T]> };\n}[keyof R];\n\nexport type ActionByType<A, T> = A extends { type: infer T2 }\n  ? T extends T2\n    ? A\n    : never\n  : never;\n\nexport type QueryMethods<\n  S = any,\n  O = any,\n  R extends MethodRecordBase<S> = any\n> = (state?: S, options?: O) => R;\nexport type QueryCallbacksFor<M extends QueryMethods> = M extends QueryMethods<\n  any,\n  any,\n  infer R\n>\n  ? {\n      [T in ActionUnion<R>['type']]: (\n        ...payload: ActionByType<ActionUnion<R>, T>['payload']\n      ) => ReturnType<R[T]>;\n    } & {\n      history: {\n        canUndo: () => boolean;\n        canRedo: () => boolean;\n      };\n    }\n  : {};\n\nexport type PatchListenerAction<M extends MethodsOrOptions> = {\n  type: keyof CallbacksFor<M>;\n  params: any;\n  patches: Patch[];\n};\n\nexport type PatchListener<\n  S,\n  M extends MethodsOrOptions,\n  Q extends QueryMethods\n> = (\n  newState: S,\n  previousState: S,\n  actionPerformedWithPatches: PatchListenerAction<M>,\n  query: QueryCallbacksFor<Q>,\n  normalizer: (cb: (draft: S) => void) => void\n) => void;\n\nexport function useMethods<S, R extends MethodRecordBase<S>>(\n  methodsOrOptions: MethodsOrOptions<S, R>, // methods to manipulate the state\n  initialState: any\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods\n>(\n  methodsOrOptions: MethodsOrOptions<S, R, QueryCallbacksFor<Q>>, // methods to manipulate the state\n  initialState: any,\n  queryMethods: Q\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods\n>(\n  methodsOrOptions: MethodsOrOptions<S, R, QueryCallbacksFor<Q>>, // methods to manipulate the state\n  initialState: any,\n  queryMethods: Q,\n  patchListener: PatchListener<\n    S,\n    MethodsOrOptions<S, R, QueryCallbacksFor<Q>>,\n    Q\n  >\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods = null\n>(\n  methodsOrOptions: MethodsOrOptions<S, R>,\n  initialState: any,\n  queryMethods?: Q,\n  patchListener?: any\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q> {\n  const history = useMemo(() => new History(), []);\n\n  let methodsFactory: Methods<S, R>;\n  let ignoreHistoryForActionsRef = useRef([]);\n  let normalizeHistoryRef = useRef<any>(() => {});\n\n  if (typeof methodsOrOptions === 'function') {\n    methodsFactory = methodsOrOptions;\n  } else {\n    methodsFactory = methodsOrOptions.methods;\n    ignoreHistoryForActionsRef.current = methodsOrOptions.ignoreHistoryForActions as any;\n    normalizeHistoryRef.current = methodsOrOptions.normalizeHistory;\n  }\n\n  const patchListenerRef = useRef(patchListener);\n  patchListenerRef.current = patchListener;\n\n  const stateRef = useRef(initialState);\n\n  const reducer = useMemo(() => {\n    const { current: normalizeHistory } = normalizeHistoryRef;\n    const { current: ignoreHistoryForActions } = ignoreHistoryForActionsRef;\n    const { current: patchListener } = patchListenerRef;\n\n    return (state: S, action: Action) => {\n      const query =\n        queryMethods && createQuery(queryMethods, () => state, history);\n\n      let finalState;\n      let [nextState, patches, inversePatches] = (produceWithPatches as any)(\n        state,\n        (draft: S) => {\n          switch (action.type) {\n            case HISTORY_ACTIONS.UNDO: {\n              return history.undo(draft);\n            }\n            case HISTORY_ACTIONS.REDO: {\n              return history.redo(draft);\n            }\n            case HISTORY_ACTIONS.CLEAR: {\n              history.clear();\n              return {\n                ...draft,\n              };\n            }\n\n            // TODO: Simplify History API\n            case HISTORY_ACTIONS.IGNORE:\n            case HISTORY_ACTIONS.MERGE:\n            case HISTORY_ACTIONS.THROTTLE: {\n              const [type, ...params] = action.payload;\n              methodsFactory(draft, query)[type](...params);\n              break;\n            }\n            default:\n              methodsFactory(draft, query)[action.type](...action.payload);\n          }\n        }\n      );\n\n      finalState = nextState;\n\n      if (patchListener) {\n        patchListener(\n          nextState,\n          state,\n          { type: action.type, params: action.payload, patches },\n          query,\n          (cb) => {\n            let normalizedDraft = produceWithPatches(nextState, cb);\n            finalState = normalizedDraft[0];\n\n            patches = [...patches, ...normalizedDraft[1]];\n            inversePatches = [...normalizedDraft[2], ...inversePatches];\n          }\n        );\n      }\n\n      if (\n        [HISTORY_ACTIONS.UNDO, HISTORY_ACTIONS.REDO].includes(\n          action.type as any\n        ) &&\n        normalizeHistory\n      ) {\n        finalState = produce(finalState, normalizeHistory);\n      }\n\n      if (\n        ![\n          ...ignoreHistoryForActions,\n          HISTORY_ACTIONS.UNDO,\n          HISTORY_ACTIONS.REDO,\n          HISTORY_ACTIONS.IGNORE,\n          HISTORY_ACTIONS.CLEAR,\n        ].includes(action.type as any)\n      ) {\n        if (action.type === HISTORY_ACTIONS.THROTTLE) {\n          history.throttleAdd(\n            patches,\n            inversePatches,\n            action.config && action.config.rate\n          );\n        } else if (action.type === HISTORY_ACTIONS.MERGE) {\n          history.merge(patches, inversePatches);\n        } else {\n          history.add(patches, inversePatches);\n        }\n      }\n\n      return finalState;\n    };\n  }, [history, methodsFactory, queryMethods]);\n\n  const getState = useCallback(() => stateRef.current, []);\n  const watcher = useMemo(() => new Watcher<S>(getState), [getState]);\n\n  const dispatch = useCallback(\n    (action: any) => {\n      const newState = reducer(stateRef.current, action);\n      stateRef.current = newState;\n      watcher.notify();\n    },\n    [reducer, watcher]\n  );\n\n  useEffect(() => {\n    watcher.notify();\n  }, [watcher]);\n\n  const query = useMemo(\n    () =>\n      !queryMethods\n        ? []\n        : createQuery(queryMethods, () => stateRef.current, history),\n    [history, queryMethods]\n  );\n\n  const actions = useMemo(() => {\n    const actionTypes = Object.keys(methodsFactory(null, null));\n\n    const { current: ignoreHistoryForActions } = ignoreHistoryForActionsRef;\n\n    return {\n      ...actionTypes.reduce((accum, type) => {\n        accum[type] = (...payload) => dispatch({ type, payload });\n        return accum;\n      }, {} as any),\n      history: {\n        undo() {\n          return dispatch({\n            type: HISTORY_ACTIONS.UNDO,\n          });\n        },\n        redo() {\n          return dispatch({\n            type: HISTORY_ACTIONS.REDO,\n          });\n        },\n        clear: () => {\n          return dispatch({\n            type: HISTORY_ACTIONS.CLEAR,\n          });\n        },\n        throttle: (rate) => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.THROTTLE,\n                    payload: [type, ...payload],\n                    config: {\n                      rate: rate,\n                    },\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n        ignore: () => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.IGNORE,\n                    payload: [type, ...payload],\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n        merge: () => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.MERGE,\n                    payload: [type, ...payload],\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n      },\n    };\n  }, [dispatch, methodsFactory]);\n\n  return useMemo(\n    () => ({\n      getState,\n      subscribe: (collector, cb, collectOnCreate) =>\n        watcher.subscribe(collector, cb, collectOnCreate),\n      actions,\n      query,\n      history,\n    }),\n    [actions, query, watcher, getState, history]\n  ) as any;\n}\n\nexport function createQuery<Q extends QueryMethods>(\n  queryMethods: Q,\n  getState,\n  history: History\n) {\n  const queries = Object.keys(queryMethods()).reduce((accum, key) => {\n    return {\n      ...accum,\n      [key]: (...args: any) => {\n        return queryMethods(getState())[key](...args);\n      },\n    };\n  }, {} as QueryCallbacksFor<typeof queryMethods>);\n\n  return {\n    ...queries,\n    history: {\n      canUndo: () => history.canUndo(),\n      canRedo: () => history.canRedo(),\n    },\n  };\n}\n\nclass Watcher<S> {\n  getState;\n  subscribers: Subscriber[] = [];\n\n  constructor(getState) {\n    this.getState = getState;\n  }\n\n  /**\n   * Creates a Subscriber\n   * @returns {() => void} a Function that removes the Subscriber\n   */\n  subscribe<C>(\n    collector: (state: S) => C,\n    onChange: (collected: C) => void,\n    collectOnCreate?: boolean\n  ): () => void {\n    const subscriber = new Subscriber(\n      () => collector(this.getState()),\n      onChange,\n      collectOnCreate\n    );\n    this.subscribers.push(subscriber);\n    return this.unsubscribe.bind(this, subscriber);\n  }\n\n  unsubscribe(subscriber) {\n    if (this.subscribers.length) {\n      const index = this.subscribers.indexOf(subscriber);\n      if (index > -1) return this.subscribers.splice(index, 1);\n    }\n  }\n\n  notify() {\n    this.subscribers.forEach((subscriber) => subscriber.collect());\n  }\n}\n\nclass Subscriber {\n  collected: any;\n  collector: () => any;\n  onChange: (collected: any) => void;\n  id;\n\n  /**\n   * Creates a Subscriber\n   * @param collector The method that returns an object of values to be collected\n   * @param onChange A callback method that is triggered when the collected values has changed\n   * @param collectOnCreate If set to true, the collector/onChange will be called on instantiation\n   */\n  constructor(collector, onChange, collectOnCreate = false) {\n    this.collector = collector;\n    this.onChange = onChange;\n\n    // Collect and run onChange callback when Subscriber is created\n    if (collectOnCreate) this.collect();\n  }\n\n  collect() {\n    try {\n      const recollect = this.collector();\n      if (!isEqualWith(recollect, this.collected)) {\n        this.collected = recollect;\n        if (this.onChange) this.onChange(this.collected);\n      }\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.warn(err);\n    }\n  }\n}\n", "export const getDOMInfo = (el: HTMLElement) => {\n  const {\n    x,\n    y,\n    top,\n    left,\n    bottom,\n    right,\n    width,\n    height,\n  } = el.getBoundingClientRect() as DOMRect;\n\n  const style = window.getComputedStyle(el);\n\n  const margin = {\n    left: parseInt(style.marginLeft),\n    right: parseInt(style.marginRight),\n    bottom: parseInt(style.marginBottom),\n    top: parseInt(style.marginTop),\n  };\n\n  const padding = {\n    left: parseInt(style.paddingLeft),\n    right: parseInt(style.paddingRight),\n    bottom: parseInt(style.paddingBottom),\n    top: parseInt(style.paddingTop),\n  };\n\n  const styleInFlow = (parent: HTMLElement) => {\n    const parentStyle: any = getComputedStyle(parent);\n\n    if (style.overflow && style.overflow !== 'visible') {\n      return;\n    }\n\n    if (parentStyle.float !== 'none') {\n      return;\n    }\n\n    if (parentStyle.display === 'grid') {\n      return;\n    }\n\n    if (\n      parentStyle.display === 'flex' &&\n      parentStyle['flex-direction'] !== 'column'\n    ) {\n      return;\n    }\n\n    switch (style.position) {\n      case 'static':\n      case 'relative':\n        break;\n      default:\n        return;\n    }\n\n    switch (el.tagName) {\n      case 'TR':\n      case 'TBODY':\n      case 'THEAD':\n      case 'TFOOT':\n        return true;\n    }\n\n    switch (style.display) {\n      case 'block':\n      case 'list-item':\n      case 'table':\n      case 'flex':\n      case 'grid':\n        return true;\n    }\n\n    return;\n  };\n\n  return {\n    x,\n    y,\n    top,\n    left,\n    bottom,\n    right,\n    width,\n    height,\n    outerWidth: Math.round(width + margin.left + margin.right),\n    outerHeight: Math.round(height + margin.top + margin.bottom),\n    margin,\n    padding,\n    inFlow: el.parentElement && !!styleInFlow(el.parentElement),\n  };\n};\n", "import { useState, useCallback, useRef, useEffect } from 'react';\n\nimport { SubscriberAndCallbacksFor } from './useMethods';\nimport { ConditionallyMergeRecordTypes } from './utilityTypes';\n\ntype CollectorMethods<S extends SubscriberAndCallbacksFor<any, any>> = {\n  actions: S['actions'];\n  query: S['query'];\n};\n\nexport type useCollectorReturnType<\n  S extends SubscriberAndCallbacksFor<any, any>,\n  C = null\n> = ConditionallyMergeRecordTypes<C, CollectorMethods<S>>;\nexport function useCollector<S extends SubscriberAndCallbacksFor<any, any>, C>(\n  store: S,\n  collector?: (\n    state: ReturnType<S['getState']>['current'],\n    query: S['query']\n  ) => C\n): useCollectorReturnType<S, C> {\n  const { subscribe, getState, actions, query } = store;\n\n  const initial = useRef(true);\n  const collected = useRef<any>(null);\n  const collectorRef = useRef(collector);\n  collectorRef.current = collector;\n\n  const onCollect = useCallback(\n    (collected) => {\n      return { ...collected, actions, query };\n    },\n    [actions, query]\n  );\n\n  // Collect states for initial render\n  if (initial.current && collector) {\n    collected.current = collector(getState(), query);\n    initial.current = false;\n  }\n\n  const [renderCollected, setRenderCollected] = useState(\n    onCollect(collected.current)\n  );\n\n  // Collect states on state change\n  useEffect(() => {\n    let unsubscribe;\n    if (collectorRef.current) {\n      unsubscribe = subscribe(\n        (current) => collectorRef.current(current, query),\n        (collected) => {\n          setRenderCollected(onCollect(collected));\n        }\n      );\n    }\n    return () => {\n      if (unsubscribe) unsubscribe();\n    };\n  }, [onCollect, query, subscribe]);\n\n  return renderCollected;\n}\n", "import { nanoid } from 'nanoid';\n\n// By default nanoid generate an ID with 21 characters. To reduce the footprint, we default to 10 characters.\n// We have a higher probability for collisions, though\n\n/**\n * Generate a random ID. That ID can for example be used as a node ID.\n *\n * @param size The number of characters that are generated for the ID. Defaults to `10`\n * @returns A random id\n */\nexport const getRandomId = (size: number = 10) => nanoid(size);\n", "import { EventHandlers } from './EventHandlers';\n\nexport type Connector = (el: HTMLElement, ...args: any) => any;\n\nexport type ConnectorsRecord = Record<string, Connector>;\n\nexport type ChainableConnector<T extends Connector, O extends any> = T extends (\n  element: infer E,\n  ...args: infer P\n) => any\n  ? <B extends E | O>(element: B, ...args: P) => B\n  : never;\n\nexport type ChainableConnectors<\n  H extends ConnectorsRecord,\n  E extends any = HTMLElement\n> = {\n  [T in keyof H]: H[T] extends Connector ? ChainableConnector<H[T], E> : never;\n};\n\nexport type CraftDOMEvent<T extends Event> = T & {\n  craft: {\n    stopPropagation: () => void;\n    blockedEvents: Record<string, HTMLElement[]>;\n  };\n};\n\nexport type CraftEventListener<K extends keyof HTMLElementEventMap> = (\n  ev: CraftDOMEvent<HTMLElementEventMap[K]>\n) => any;\n\nexport type EventHandlerConnectors<\n  H extends EventHandlers,\n  E extends any = HTMLElement\n> = ChainableConnectors<ReturnType<H['handlers']>, E>;\n\nexport type ConnectorsUsage<H extends EventHandlers> = {\n  register: () => void;\n  cleanup: () => void;\n  connectors: EventHandlerConnectors<H>;\n};\n\nexport enum EventHandlerUpdates {\n  HandlerDisabled,\n  HandlerEnabled,\n}\n\nexport type ConnectorToRegister = {\n  name: string;\n  required: any;\n  connector: Connector;\n  options?: Record<string, any>;\n};\n\nexport type RegisteredConnector = {\n  id: string;\n  required: any;\n  enable: () => void;\n  disable: () => void;\n  remove: () => void;\n};\n", "import isEqual from 'shallowequal';\n\nimport { ConnectorToRegister, RegisteredConnector } from './interfaces';\n\nimport { getRandomId } from '../getRandomId';\n\n/**\n * Stores all connected DOM elements and their connectors here\n * This allows us to easily enable/disable and perform cleanups\n */\nexport class ConnectorRegistry {\n  private isEnabled: boolean = true;\n\n  private elementIdMap: WeakMap<HTMLElement, string> = new WeakMap();\n  private registry: Map<String, RegisteredConnector> = new Map();\n\n  private getElementId(element: HTMLElement) {\n    const existingId = this.elementIdMap.get(element);\n    if (existingId) {\n      return existingId;\n    }\n\n    const newId = getRandomId();\n\n    this.elementIdMap.set(element, newId);\n    return newId;\n  }\n\n  getConnectorId(element: HTMLElement, connectorName: string) {\n    const elementId = this.getElementId(element);\n    return `${connectorName}--${elementId}`;\n  }\n\n  register(element: HTMLElement, connectorPayload: ConnectorToRegister) {\n    const existingConnector = this.getByElement(element, connectorPayload.name);\n\n    if (existingConnector) {\n      if (isEqual(connectorPayload.required, existingConnector.required)) {\n        return existingConnector;\n      }\n\n      this.getByElement(element, connectorPayload.name).disable();\n    }\n\n    let cleanup: () => void | null = null;\n\n    const id = this.getConnectorId(element, connectorPayload.name);\n    this.registry.set(id, {\n      id,\n      required: connectorPayload.required,\n      enable: () => {\n        if (cleanup) {\n          cleanup();\n        }\n\n        cleanup = connectorPayload.connector(\n          element,\n          connectorPayload.required,\n          connectorPayload.options\n        );\n      },\n      disable: () => {\n        if (!cleanup) {\n          return;\n        }\n\n        cleanup();\n      },\n      remove: () => {\n        return this.remove(id);\n      },\n    });\n\n    if (this.isEnabled) {\n      this.registry.get(id).enable();\n    }\n\n    return this.registry.get(id);\n  }\n\n  get(id: string) {\n    return this.registry.get(id);\n  }\n\n  remove(id: string) {\n    const connector = this.get(id);\n    if (!connector) {\n      return;\n    }\n\n    connector.disable();\n    this.registry.delete(connector.id);\n  }\n\n  enable() {\n    this.isEnabled = true;\n    this.registry.forEach((connectors) => {\n      connectors.enable();\n    });\n  }\n\n  disable() {\n    this.isEnabled = false;\n    this.registry.forEach((connectors) => {\n      connectors.disable();\n    });\n  }\n\n  getByElement(element: HTMLElement, connectorName: string) {\n    return this.get(this.getConnectorId(element, connectorName));\n  }\n\n  removeByElement(element: HTMLElement, connectorName: string) {\n    return this.remove(this.getConnectorId(element, connectorName));\n  }\n\n  clear() {\n    this.disable();\n    this.elementIdMap = new WeakMap();\n    this.registry = new Map();\n  }\n}\n", "import { ConnectorRegistry } from './ConnectorRegistry';\nimport {\n  EventHandlerUpdates,\n  CraftEventListener,\n  EventHandlerConnectors,\n  CraftDOMEvent,\n  Connector,\n  ConnectorsUsage,\n  RegisteredConnector,\n} from './interfaces';\nimport { isEventBlockedByDescendant } from './isEventBlockedByDescendant';\n\nexport abstract class EventHandlers<O extends Record<string, any> = {}> {\n  options: O;\n\n  private registry: ConnectorRegistry = new ConnectorRegistry();\n  private subscribers: Set<(msg: EventHandlerUpdates) => void> = new Set();\n\n  onEnable?(): void;\n  onDisable?(): void;\n\n  constructor(options?: O) {\n    this.options = options;\n  }\n\n  listen(cb: (msg: EventHandlerUpdates) => void) {\n    this.subscribers.add(cb);\n    return () => this.subscribers.delete(cb);\n  }\n\n  disable() {\n    if (this.onDisable) {\n      this.onDisable();\n    }\n\n    this.registry.disable();\n\n    this.subscribers.forEach((listener) => {\n      listener(EventHandlerUpdates.HandlerDisabled);\n    });\n  }\n\n  enable() {\n    if (this.onEnable) {\n      this.onEnable();\n    }\n\n    this.registry.enable();\n\n    this.subscribers.forEach((listener) => {\n      listener(EventHandlerUpdates.HandlerEnabled);\n    });\n  }\n\n  cleanup() {\n    this.disable();\n    this.subscribers.clear();\n    this.registry.clear();\n  }\n\n  addCraftEventListener<K extends keyof HTMLElementEventMap>(\n    el: HTMLElement,\n    eventName: K,\n    listener: CraftEventListener<K>,\n    options?: boolean | AddEventListenerOptions\n  ) {\n    const bindedListener = (e: CraftDOMEvent<HTMLElementEventMap[K]>) => {\n      if (!isEventBlockedByDescendant(e, eventName, el)) {\n        e.craft.stopPropagation = () => {\n          if (!e.craft.blockedEvents[eventName]) {\n            e.craft.blockedEvents[eventName] = [];\n          }\n\n          e.craft.blockedEvents[eventName].push(el);\n        };\n\n        listener(e);\n      }\n    };\n\n    el.addEventListener(eventName, bindedListener, options);\n\n    return () => el.removeEventListener(eventName, bindedListener, options);\n  }\n\n  // Defines the connectors and their logic\n  abstract handlers(): Record<string, (el: HTMLElement, ...args: any[]) => any>;\n\n  /**\n   * Creates a record of chainable connectors and tracks their usages\n   */\n  createConnectorsUsage(): ConnectorsUsage<this> {\n    const handlers = this.handlers();\n\n    // Track all active connector ids here\n    // This is so we can return a cleanup method below so the callee can programmatically cleanup all connectors\n\n    const activeConnectorIds: Set<string> = new Set();\n\n    let canRegisterConnectors = false;\n    const connectorsToRegister: Map<\n      string,\n      () => RegisteredConnector\n    > = new Map();\n\n    const connectors = Object.entries(handlers).reduce<\n      Record<string, Connector>\n    >(\n      (accum, [name, handler]) => ({\n        ...accum,\n        [name]: (el, required, options) => {\n          const registerConnector = () => {\n            const connector = this.registry.register(el, {\n              required,\n              name,\n              options,\n              connector: handler,\n            });\n\n            activeConnectorIds.add(connector.id);\n            return connector;\n          };\n\n          connectorsToRegister.set(\n            this.registry.getConnectorId(el, name),\n            registerConnector\n          );\n\n          /**\n           * If register() has been called,\n           * register the connector immediately.\n           *\n           * Otherwise, registration is deferred until after register() is called\n           */\n          if (canRegisterConnectors) {\n            registerConnector();\n          }\n\n          return el;\n        },\n      }),\n      {}\n    ) as any;\n\n    return {\n      connectors,\n      register: () => {\n        canRegisterConnectors = true;\n\n        connectorsToRegister.forEach((registerConnector) => {\n          registerConnector();\n        });\n      },\n      cleanup: () => {\n        canRegisterConnectors = false;\n\n        activeConnectorIds.forEach((connectorId) =>\n          this.registry.remove(connectorId)\n        );\n      },\n    };\n  }\n\n  derive<C extends EventHandlers>(\n    type: {\n      new (...args: any[]): C;\n    },\n    opts: C['options']\n  ) {\n    return new type(this, opts);\n  }\n\n  // This method allows us to execute multiple connectors and returns a single cleanup method for all of them\n  protected createProxyHandlers<H extends EventHandlers>(\n    instance: H,\n    cb: (connectors: EventHandlerConnectors<H>) => void\n  ) {\n    const connectorsToCleanup = [];\n    const handlers = instance.handlers();\n\n    const proxiedHandlers = new Proxy(handlers, {\n      get: (target, key: any, receiver) => {\n        if (key in handlers === false) {\n          return Reflect.get(target, key, receiver);\n        }\n\n        return (el, ...args) => {\n          const cleanup = handlers[key](el, ...args);\n          if (!cleanup) {\n            return;\n          }\n\n          connectorsToCleanup.push(cleanup);\n        };\n      },\n    });\n\n    cb(proxiedHandlers as any);\n\n    return () => {\n      connectorsToCleanup.forEach((cleanup) => {\n        cleanup();\n      });\n    };\n  }\n\n  // This lets us to execute and cleanup sibling connectors\n  reflect(cb: (connectors: EventHandlerConnectors<this>) => void) {\n    return this.createProxyHandlers(this, cb);\n  }\n}\n", "import { CraftDOMEvent } from './interfaces';\n\n/**\n * Check if a specified event is blocked by a child\n * that's a descendant of the specified element\n */\nexport function isEventBlockedByDescendant<K extends keyof HTMLElementEventMap>(\n  e: CraftDOMEvent<HTMLElementEventMap[K]>,\n  eventName: K,\n  el: HTMLElement\n) {\n  // Store initial Craft event value\n  if (!e.craft) {\n    e.craft = {\n      stopPropagation: () => {},\n      blockedEvents: {},\n    };\n  }\n\n  const blockingElements = (e.craft && e.craft.blockedEvents[eventName]) || [];\n\n  for (let i = 0; i < blockingElements.length; i++) {\n    const blockingElement = blockingElements[i];\n\n    if (el !== blockingElement && el.contains(blockingElement)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n", "import { EventHandlers } from './EventHandlers';\nimport { EventHandlerConnectors, EventHandlerUpdates } from './interfaces';\n\n// Creates EventHandlers that depends on another EventHandlers instance\n// This lets us to easily create new connectors that composites of the parent EventHandlers instance\nexport abstract class DerivedEventHandlers<\n  P extends EventHandlers,\n  O extends Record<string, any> = {}\n> extends EventHandlers<O> {\n  derived: P;\n  unsubscribeParentHandlerListener: () => void;\n\n  constructor(derived: P, options?: O) {\n    super(options);\n    this.derived = derived;\n    this.options = options;\n\n    // Automatically disable/enable depending on the parent handlers\n    this.unsubscribeParentHandlerListener = this.derived.listen((msg) => {\n      switch (msg) {\n        case EventHandlerUpdates.HandlerEnabled: {\n          return this.enable();\n        }\n        case EventHandlerUpdates.HandlerDisabled: {\n          return this.disable();\n        }\n        default: {\n          return;\n        }\n      }\n    });\n  }\n\n  // A method to easily inherit parent connectors\n  inherit(cb: (connectors: EventHandlerConnectors<P>) => void) {\n    return this.createProxyHandlers(this.derived, cb);\n  }\n\n  cleanup() {\n    super.cleanup();\n    this.unsubscribeParentHandlerListener();\n  }\n}\n", "// https://github.com/react-dnd/react-dnd\nimport { isValidElement, ReactElement } from 'react';\nimport { cloneElement } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { ChainableConnectors, ConnectorsRecord } from './interfaces';\n\nfunction setRef(ref: any, node: any) {\n  if (node) {\n    if (typeof ref === 'function') {\n      ref(node);\n    } else {\n      ref.current = node;\n    }\n  }\n}\n\nexport function cloneWithRef(\n  element: any,\n  newRef: any\n): React.ReactElement<any> {\n  const previousRef = element.ref;\n  invariant(\n    typeof previousRef !== 'string',\n    'Cannot connect to an element with an existing string ref. ' +\n      'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' +\n      'Read more: https://facebook.github.io/react/docs/more-about-refs.html#the-ref-callback-attribute'\n  );\n\n  if (!previousRef) {\n    // When there is no ref on the element, use the new ref directly\n    return cloneElement(element, {\n      ref: newRef,\n    });\n  } else {\n    return cloneElement(element, {\n      ref: (node: any) => {\n        setRef(previousRef, node);\n        setRef(newRef, node);\n      },\n    });\n  }\n}\n\nfunction throwIfCompositeComponentElement(element: React.ReactElement<any>) {\n  if (typeof element.type === 'string') {\n    return;\n  }\n\n  throw new Error();\n}\n\nexport function wrapHookToRecognizeElement(\n  hook: (node: any, ...args: any[]) => void\n) {\n  return (elementOrNode = null, ...args: any) => {\n    // When passed a node, call the hook straight away.\n    if (!isValidElement(elementOrNode)) {\n      if (!elementOrNode) {\n        return;\n      }\n\n      const node = elementOrNode;\n      node && hook(node, ...args);\n      return node;\n    }\n\n    // If passed a ReactElement, clone it and attach this function as a ref.\n    // This helps us achieve a neat API where user doesn't even know that refs\n    // are being used under the hood.\n    const element: ReactElement | null = elementOrNode;\n    throwIfCompositeComponentElement(element as any);\n\n    return cloneWithRef(element, hook);\n  };\n}\n\n// A React wrapper for our connectors\n// Wrap all our connectors so that would additionally accept React.ReactElement\nexport function wrapConnectorHooks<H extends ConnectorsRecord>(\n  connectors: H\n): ChainableConnectors<H, React.ReactElement | HTMLElement> {\n  return Object.keys(connectors).reduce((accum, key) => {\n    accum[key] = wrapHookToRecognizeElement((...args) => {\n      // @ts-ignore\n      return connectors[key](...args);\n    });\n\n    return accum;\n  }, {}) as any;\n}\n", "import React from 'react';\nimport ReactDOM from 'react-dom';\n\ntype RenderIndicatorProps = {\n  style: React.CSSProperties;\n  className?: string;\n  parentDom?: HTMLElement;\n};\n\nexport const RenderIndicator = ({\n  style,\n  className,\n  parentDom,\n}: RenderIndicatorProps) => {\n  const indicator = (\n    <div\n      className={className}\n      style={{\n        position: 'fixed',\n        display: 'block',\n        opacity: 1,\n        borderStyle: 'solid',\n        borderWidth: '1px',\n        borderColor: 'transparent',\n        zIndex: 99999,\n        ...style,\n      }}\n    ></div>\n  );\n\n  if (parentDom && parentDom.ownerDocument !== document) {\n    return ReactDOM.createPortal(indicator, parentDom.ownerDocument.body);\n  }\n\n  return indicator;\n};\n", "import { useEffect } from 'react';\n\nexport const useEffectOnce = (effect: () => void) => {\n  /* eslint-disable-next-line react-hooks/exhaustive-deps */\n  useEffect(effect, []);\n};\n", "type DeprecationPayload = Partial<{\n  suggest: string;\n  doc: string;\n}>;\n\nexport const deprecationWarning = (name, payload?: DeprecationPayload) => {\n  let message = `Deprecation warning: ${name} will be deprecated in future relases.`;\n\n  const { suggest, doc } = payload;\n\n  if (suggest) {\n    message += ` Please use ${suggest} instead.`;\n  }\n\n  // URL link to Documentation\n  if (doc) {\n    message += `(${doc})`;\n  }\n\n  // eslint-disable-next-line no-console\n  console.warn(message);\n};\n", "export const isClientSide = () => typeof window !== 'undefined';\n\nexport const isLinux = () =>\n  isClientSide() && /Linux/i.test(window.navigator.userAgent);\n\nexport const isChromium = () =>\n  isClientSide() && /Chrome/i.test(window.navigator.userAgent);\n"], "names": ["ROOT_NODE", "DEPRECATED_ROOT_NODE", "ERROR_NOPARENT", "ERROR_DUPLICATE_NODEID", "ERROR_INVALID_NODEID", "ERROR_TOP_LEVEL_ELEMENT_NO_ID", "ERROR_MISSING_PLACEHOLDER_PLACEMENT", "ERROR_MOVE_CANNOT_DROP", "ERROR_MOVE_INCOMING_PARENT", "ERROR_MOVE_OUTGOING_PARENT", "ERROR_MOVE_NONCANVAS_CHILD", "ERROR_MOVE_TO_NONCANVAS_PARENT", "ERROR_MOVE_TOP_LEVEL_NODE", "ERROR_MOVE_ROOT_NODE", "ERROR_MOVE_TO_DESCENDANT", "ERROR_NOT_IN_RESOLVER", "ERROR_INFINITE_CANVAS", "ERROR_CANNOT_DRAG", "ERROR_INVALID_NODE_ID", "ERROR_DELETE_TOP_LEVEL_NODE", "ERROR_RESOLVER_NOT_AN_OBJECT", "ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER", "ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT", "ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT", "HISTORY_ACTIONS", "UNDO", "REDO", "THROTTLE", "IGNORE", "MERGE", "CLEAR", "History", "_classCallCheck", "this", "_defineProperty", "_createClass", "key", "value", "patches", "inversePatches", "length", "pointer", "timeline", "timestamp", "Date", "now", "throttleRate", "_this$timeline$this$p", "currPatches", "currInversePatches", "getTime", "concat", "_toConsumableArray", "add", "_this$timeline$this$p2", "state", "canUndo", "applyPatches", "canRedo", "useMethods", "methodsOrOptions", "initialState", "queryMethods", "patchListener", "methodsFactory", "history", "useMemo", "ignoreHistoryForActionsRef", "useRef", "normalizeHistoryRef", "methods", "current", "ignoreHistoryForActions", "normalizeHistory", "patchListenerRef", "stateRef", "reducer", "action", "finalState", "query", "createQuery", "_produceWithPatches2", "_slicedToArray", "produceWithPatches", "draft", "_methodsFactory2", "type", "undo", "redo", "clear", "_objectSpread", "_methodsFactory", "payload", "_action$payload", "params", "slice", "apply", "nextState", "cb", "normalizedDraft", "includes", "produce", "throttleAdd", "config", "rate", "merge", "getState", "useCallback", "watcher", "Watcher", "dispatch", "newState", "notify", "useEffect", "actions", "actionTypes", "Object", "keys", "reduce", "accum", "_len", "arguments", "Array", "_key", "throttle", "filter", "_len2", "_key2", "ignore", "_len3", "_key3", "_len4", "_key4", "subscribe", "collector", "collectOnCreate", "queries", "_queryMethods", "enableMapSet", "enablePatches", "onChange", "_this", "subscriber", "Subscriber", "subscribers", "push", "unsubscribe", "bind", "index", "indexOf", "splice", "for<PERSON>ach", "collect", "recollect", "isEqualWith", "collected", "err", "console", "warn", "getDOMInfo", "el", "getBoundingClientRect", "x", "y", "top", "left", "bottom", "right", "width", "height", "style", "window", "getComputedStyle", "margin", "parseInt", "marginLeft", "marginRight", "marginBottom", "marginTop", "padding", "paddingLeft", "paddingRight", "paddingBottom", "paddingTop", "outerWidth", "Math", "round", "outerHeight", "inFlow", "parentElement", "parent", "parentStyle", "overflow", "float", "display", "position", "tagName", "styleInFlow", "useCollector", "store", "initial", "collectorRef", "onCollect", "renderCollected", "setRenderCollected", "useState", "getRandomId", "EventHandlerUpdates", "nanoid", "ConnectorRegistry", "WeakMap", "Map", "element", "existingId", "elementIdMap", "get", "newId", "set", "connectorName", "elementId", "getElementId", "connectorPayload", "existingConnector", "getByElement", "name", "isEqual", "required", "disable", "cleanup", "id", "getConnectorId", "registry", "enable", "connector", "options", "remove", "isEnabled", "delete", "connectors", "EventHandlers", "Set", "onDisable", "listener", "HandlerDisabled", "onEnable", "Handler<PERSON><PERSON>bled", "eventName", "bindedListener", "e", "craft", "stopPropagation", "blockedEvents", "blockingElements", "i", "blockingElement", "contains", "isEventBlockedByDescendant", "addEventListener", "removeEventListener", "_this2", "handlers", "activeConnectorIds", "canRegisterConnectors", "connectorsToRegister", "entries", "_ref", "_ref2", "handler", "registerConnector", "register", "connectorId", "opts", "instance", "connectorsToCleanup", "proxiedHandlers", "Proxy", "target", "receiver", "Reflect", "args", "createProxyHandlers", "DerivedEventHandlers", "_EventHandlers", "_inherits", "_super", "derived", "_assertThisInitialized", "call", "unsubscribeParentHandlerListener", "listen", "msg", "_get", "_getPrototypeOf", "prototype", "setRef", "ref", "node", "cloneWithRef", "newRef", "previousRef", "invariant", "cloneElement", "wrapHookToRecognizeElement", "hook", "elementOrNode", "isValidElement", "Error", "throwIfCompositeComponentElement", "wrapConnectorHooks", "RenderIndicator", "className", "parentDom", "indicator", "React", "createElement", "opacity", "borderStyle", "borderWidth", "borderColor", "zIndex", "ownerDocument", "document", "ReactDOM", "createPortal", "body", "useEffectOnce", "effect", "deprecationWarning", "message", "suggest", "doc", "isClientSide", "isLinux", "test", "navigator", "userAgent", "isChromium"], "mappings": "ofAAO,IAAMA,EAAY,OACZC,EAAuB,cAGvBC,EAAiB,8BACjBC,EACX,8CACWC,EACX,gDAC<PERSON>,EACX,8HACWC,EACX,2EACWC,EACX,4CACWC,EAA6B,sCAC7BC,EACX,uCACWC,EACX,+<PERSON><PERSON><PERSON>,EACX,4CACWC,EAA4B,mCAC5BC,EAAuB,4BAEvBC,EAA2B,qCAC3BC,EACX,0FACWC,EACX,6GACWC,EACX,8EACWC,EAAwB,sCACxBC,EACX,wCAEWC,EAE2C,sLAE3CC,EAI2C,4QAE3CC,EAE0E,sJAE1EC,EAEwE,8kHC5C9E,IAAMC,GAAkB,CAC7BC,KAAM,eACNC,KAAM,eACNC,SAAU,mBACVC,OAAQ,iBACRC,MAAO,gBACPC,MAAO,iBAGIC,GAAO,WAAA,SAAAA,IAAAC,EAAAC,KAAAF,GAAAG,EAAAD,KAAA,WACG,IAAEC,EAAAD,KAAA,WACZ,EAAC,CAsGX,OAtGWE,EAAAJ,EAAA,CAAA,CAAAK,IAAA,MAAAC,MAEZ,SAAIC,EAAkBC,GACG,IAAnBD,EAAQE,QAA0C,IAA1BD,EAAeC,SAI3CP,KAAKQ,QAAUR,KAAKQ,QAAU,EAC9BR,KAAKS,SAASF,OAASP,KAAKQ,QAC5BR,KAAKS,SAAST,KAAKQ,SAAW,CAC5BH,QAAAA,EACAC,eAAAA,EACAI,UAAWC,KAAKC,OAEpB,GAAC,CAAAT,IAAA,cAAAC,MAED,SACEC,EACAC,GAC0B,IAA1BO,yDAAuB,IAEvB,GAAuB,IAAnBR,EAAQE,QAA0C,IAA1BD,EAAeC,OAA3C,CAIA,GAAIP,KAAKS,SAASF,QAAUP,KAAKQ,SAAW,EAAG,CAC7C,IAAAM,EAIId,KAAKS,SAAST,KAAKQ,SAHZO,IAATV,QACgBW,IAAhBV,eACAI,IAAAA,UAMF,IAHY,IAAIC,MACCM,UAAYP,EAElBG,EAMT,YALAb,KAAKS,SAAST,KAAKQ,SAAW,CAC5BE,UAAAA,EACAL,QAAaU,GAAAA,OAAAA,EAAAA,GAAgBV,EAAAA,IAC7BC,eAAc,GAAAY,OAAAC,EAAMb,GAAca,EAAKH,KAI7C,CAEAhB,KAAKoB,IAAIf,EAASC,EAtBlB,CAuBF,GAAC,CAAAH,IAAA,QAAAC,MAED,SAAMC,EAAkBC,GACtB,GAAuB,IAAnBD,EAAQE,QAA0C,IAA1BD,EAAeC,OAI3C,GAAIP,KAAKS,SAASF,QAAUP,KAAKQ,SAAW,EAA5C,CACE,IAAAa,EAIIrB,KAAKS,SAAST,KAAKQ,SAFLQ,IAAhBV,eAIFN,KAAKS,SAAST,KAAKQ,SAAW,CAC5BE,YAJAA,UAKAL,QAAaU,GAAAA,OAAAA,IAPbV,SAO6BA,EAAAA,IAC7BC,eAAc,GAAAY,OAAAC,EAAMb,GAAca,EAAKH,IAG3C,MAEAhB,KAAKoB,IAAIf,EAASC,EACpB,GAAC,CAAAH,IAAA,QAAAC,MAED,WACEJ,KAAKS,SAAW,GAChBT,KAAKQ,SAAW,CAClB,GAAC,CAAAL,IAAA,UAAAC,MAED,WACE,OAAOJ,KAAKQ,SAAW,CACzB,GAAC,CAAAL,IAAA,UAAAC,MAED,WACE,OAAOJ,KAAKQ,QAAUR,KAAKS,SAASF,OAAS,CAC/C,GAAC,CAAAJ,IAAA,OAAAC,MAED,SAAKkB,GACH,GAAKtB,KAAKuB,UAAV,CAIA,IAAQjB,EAAmBN,KAAKS,SAAST,KAAKQ,SAAtCF,eAER,OADAN,KAAKQ,QAAUR,KAAKQ,QAAU,EACvBgB,EAAaF,EAAOhB,EAJ3B,CAKF,GAAC,CAAAH,IAAA,OAAAC,MAED,SAAKkB,GACH,GAAKtB,KAAKyB,UAMV,OAFAzB,KAAKQ,QAAUR,KAAKQ,QAAU,EAEvBgB,EAAaF,EADAtB,KAAKS,SAAST,KAAKQ,SAA/BH,QAEV,KAACP,CAAA,CAxGiB,GCuKd,SAAU4B,GAKdC,EACAC,EACAC,EACAC,GAEA,IAEIC,EAFEC,EAAUC,GAAQ,WAAA,OAAM,IAAInC,EAAS,GAAE,IAGzCoC,EAA6BC,EAAO,IACpCC,EAAsBD,GAAY,WAAK,IAEX,mBAArBR,EACTI,EAAiBJ,GAEjBI,EAAiBJ,EAAiBU,QAClCH,EAA2BI,QAAUX,EAAiBY,wBACtDH,EAAoBE,QAAUX,EAAiBa,kBAGjD,IAAMC,EAAmBN,EAAOL,GAChCW,EAAiBH,QAAUR,EAE3B,IAAMY,EAAWP,EAAOP,GAElBe,EAAUV,GAAQ,WACtB,IAAiBO,EAAqBJ,EAA9BE,QACSC,EAA4BL,EAArCI,QACSR,EAAkBW,EAA3BH,QAER,OAAO,SAAChB,EAAUsB,GAChB,IAGIC,EAHEC,EACJjB,GAAgBkB,GAAYlB,GAAc,WAAA,OAAMP,CAAK,GAAEU,GAgCxDgB,EAAAC,EA7B2CC,EAC1C5B,GACA,SAAC6B,GAAY,IAAAC,IACX,OAAQR,EAAOS,MACb,KAAK9D,GAAgBC,KACnB,OAAOwC,EAAQsB,KAAKH,GAEtB,KAAK5D,GAAgBE,KACnB,OAAOuC,EAAQuB,KAAKJ,GAEtB,KAAK5D,GAAgBM,MAEnB,OADAmC,EAAQwB,QACRC,EAAA,GACKN,GAKP,KAAK5D,GAAgBI,OACrB,KAAKJ,GAAgBK,MACrB,KAAKL,GAAgBG,SAAU,IAAAgE,EACHd,OAAAA,EAAOe,6BAA1BN,EAAIO,EAAA,GAAKC,EAAMD,EAAAE,MAAA,IACtB/B,EAAAA,EAAeoB,EAAOL,IAAOO,GAASQ,MAAAA,EAAAA,EAAAA,IACtC,MAEF,SACET,EAAArB,EAAeoB,EAAOL,IAAOF,EAAOS,MAAKU,MAAAX,EAAAjC,EAAIyB,EAAOe,UAE1D,IACD,GA7BIK,EAAShB,EAAA,GAAE3C,EAAO2C,EAAA,GAAE1C,EAAc0C,EAAA,GAgFvC,OAjDAH,EAAamB,EAETlC,GACFA,EACEkC,EACA1C,EACA,CAAE+B,KAAMT,EAAOS,KAAMQ,OAAQjB,EAAOe,QAAStD,QAAAA,GAC7CyC,GACA,SAACmB,GACC,IAAIC,EAAkBhB,EAAmBc,EAAWC,GACpDpB,EAAaqB,EAAgB,GAE7B7D,cAAcA,GAAOc,EAAK+C,EAAgB,KAC1C5D,cAAqB4D,EAAgB,IAAE/C,EAAKb,GAC9C,IAKF,CAACf,GAAgBC,KAAMD,GAAgBE,MAAM0E,SAC3CvB,EAAOS,OAETb,IAEAK,EAAauB,EAAQvB,EAAYL,IAIhC,GACID,OAAAA,EAAAA,GACHhD,CAAAA,GAAgBC,KAChBD,GAAgBE,KAChBF,GAAgBI,OAChBJ,GAAgBM,QAChBsE,SAASvB,EAAOS,QAEdT,EAAOS,OAAS9D,GAAgBG,SAClCsC,EAAQqC,YACNhE,EACAC,EACAsC,EAAO0B,QAAU1B,EAAO0B,OAAOC,MAExB3B,EAAOS,OAAS9D,GAAgBK,MACzCoC,EAAQwC,MAAMnE,EAASC,GAEvB0B,EAAQZ,IAAIf,EAASC,IAIlBuC,EAEV,GAAE,CAACb,EAASD,EAAgBF,IAEvB4C,EAAWC,GAAY,WAAA,OAAMhC,EAASJ,OAAO,GAAE,IAC/CqC,EAAU1C,GAAQ,WAAA,OAAM,IAAI2C,GAAWH,KAAW,CAACA,IAEnDI,EAAWH,GACf,SAAC9B,GACC,IAAMkC,EAAWnC,EAAQD,EAASJ,QAASM,GAC3CF,EAASJ,QAAUwC,EACnBH,EAAQI,QACV,GACA,CAACpC,EAASgC,IAGZK,GAAU,WACRL,EAAQI,QACV,GAAG,CAACJ,IAEJ,IAAM7B,EAAQb,GACZ,WAAA,OACGJ,EAEGkB,GAAYlB,GAAc,WAAA,OAAMa,EAASJ,OAAO,GAAEN,GADlD,EAC0D,GAChE,CAACA,EAASH,IAGNoD,EAAUhD,GAAQ,WACtB,IAAMiD,EAAcC,OAAOC,KAAKrD,EAAe,KAAM,OAEpCQ,EAA4BL,EAArCI,QAER,OACK4C,EAAAA,EAAAA,CAAAA,EAAAA,EAAYG,QAAO,SAACC,EAAOjC,GAE5B,OADAiC,EAAMjC,GAAQ,WAAA,IAAA,IAAAkC,EAAAC,UAAAjF,OAAIoD,EAAO,IAAA8B,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAP/B,EAAO+B,GAAAF,UAAAE,GAAA,OAAKb,EAAS,CAAExB,KAAAA,EAAMM,QAAAA,GAAU,EAClD2B,IACN,CAAS,IAAC,GAAA,CACbtD,QAAS,CACPsB,KAAI,WACF,OAAOuB,EAAS,CACdxB,KAAM9D,GAAgBC,MAEzB,EACD+D,KAAI,WACF,OAAOsB,EAAS,CACdxB,KAAM9D,GAAgBE,MAEzB,EACD+D,MAAO,WACL,OAAOqB,EAAS,CACdxB,KAAM9D,GAAgBM,OAEzB,EACD8F,SAAU,SAACpB,GACT,OAAAd,EAAA,CAAA,EACKyB,EACAU,QAAO,SAACvC,GAAI,OAAMd,EAAwB4B,SAASd,EAAK,IACxDgC,QAAO,SAACC,EAAOjC,GASd,OARAiC,EAAMjC,GAAQ,WAAA,IAAA,IAAAwC,EAAAL,UAAAjF,OAAIoD,EAAO,IAAA8B,MAAAI,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAPnC,EAAOmC,GAAAN,UAAAM,GAAA,OACvBjB,EAAS,CACPxB,KAAM9D,GAAgBG,SACtBiE,QAAUN,CAAAA,GAASM,OAAAA,GACnBW,OAAQ,CACNC,KAAMA,IAER,EACGe,IACN,CAAA,GAER,EACDS,OAAQ,WACN,OAAAtC,EAAA,CAAA,EACKyB,EACAU,QAAO,SAACvC,GAAI,OAAMd,EAAwB4B,SAASd,EAAK,IACxDgC,QAAO,SAACC,EAAOjC,GAMd,OALAiC,EAAMjC,GAAQ,WAAA,IAAA,IAAA2C,EAAAR,UAAAjF,OAAIoD,EAAO,IAAA8B,MAAAO,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAPtC,EAAOsC,GAAAT,UAAAS,GAAA,OACvBpB,EAAS,CACPxB,KAAM9D,GAAgBI,OACtBgE,QAAO,CAAGN,GAAInC,OAAKyC,IACnB,EACG2B,IACN,CAAA,GAER,EACDd,MAAO,WACL,OAAAf,EAAA,CAAA,EACKyB,EACAU,QAAO,SAACvC,GAAI,OAAMd,EAAwB4B,SAASd,EAAK,IACxDgC,QAAO,SAACC,EAAOjC,GAMd,OALAiC,EAAMjC,GAAQ,WAAA,IAAA,IAAA6C,EAAAV,UAAAjF,OAAIoD,EAAO,IAAA8B,MAAAS,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAPxC,EAAOwC,GAAAX,UAAAW,GAAA,OACvBtB,EAAS,CACPxB,KAAM9D,GAAgBK,MACtB+D,QAAO,CAAGN,GAAInC,OAAKyC,IACnB,EACG2B,IACN,CAAA,GAET,IAGN,GAAG,CAACT,EAAU9C,IAEd,OAAOE,GACL,WAAA,MAAO,CACLwC,SAAAA,EACA2B,UAAW,SAACC,EAAWpC,EAAIqC,GAAe,OACxC3B,EAAQyB,UAAUC,EAAWpC,EAAIqC,EAAgB,EACnDrB,QAAAA,EACAnC,MAAAA,EACAd,QAAAA,EACD,GACD,CAACiD,EAASnC,EAAO6B,EAASF,EAAUzC,GAExC,UAEgBe,GACdlB,EACA4C,EACAzC,GAEA,IAAMuE,EAAUpB,OAAOC,KAAKvD,KAAgBwD,QAAO,SAACC,EAAOnF,GACzD,OAAAsD,EAAAA,EAAA,CAAA,EACK6B,GAAK,CAAA,EAAArF,EAAA,GACPE,GAAM,WAAiB,IAAAqG,EACtB,OAAOA,EAAA3E,EAAa4C,MAAYtE,GAAa4D,MAAAyC,EAAAhB,UAC9C,IAEJ,GAAE,CAA4C,GAE/C,OAAA/B,EAAAA,EAAA,CAAA,EACK8C,GAAO,CAAA,EAAA,CACVvE,QAAS,CACPT,QAAS,WAAA,OAAMS,EAAQT,SAAS,EAChCE,QAAS,WAAA,OAAMO,EAAQP,SAAS,IAGtC,CA3aAgF,IACAC,IA0aC,IAEK9B,GAAO,WAIX,SAAAA,EAAYH,GAAQ1E,EAAAC,KAAA4E,GAAA3E,EAAAD,KAAA,gBAAA,GAAAC,EAAAD,KAAA,cAFQ,IAG1BA,KAAKyE,SAAWA,CAClB,CA6BC,OA3BDvE,EAAA0E,EAAA,CAAA,CAAAzE,IAAA,YAAAC,MAIA,SACEiG,EACAM,EACAL,GAAyB,IAAAM,EAAA5G,KAEnB6G,EAAa,IAAIC,IACrB,WAAA,OAAMT,EAAUO,EAAKnC,cACrBkC,EACAL,GAGF,OADAtG,KAAK+G,YAAYC,KAAKH,GACf7G,KAAKiH,YAAYC,KAAKlH,KAAM6G,EACrC,GAAC,CAAA1G,IAAA,cAAAC,MAED,SAAYyG,GACV,GAAI7G,KAAK+G,YAAYxG,OAAQ,CAC3B,IAAM4G,EAAQnH,KAAK+G,YAAYK,QAAQP,GACvC,GAAIM,GAAS,EAAG,OAAOnH,KAAK+G,YAAYM,OAAOF,EAAO,EACxD,CACF,GAAC,CAAAhH,IAAA,SAAAC,MAED,WACEJ,KAAK+G,YAAYO,SAAQ,SAACT,GAAU,OAAKA,EAAWU,YACtD,KAAC3C,CAAA,CAnCU,GAsCPkC,GAAU,WAYd,SAAYT,EAAAA,EAAWM,GAAiC,IAAvBL,0DAAuBvG,EAAAC,KAAA8G,GAAA7G,EAAAD,KAAA,iBAAA,GAAAC,EAAAD,KAAA,iBAAA,GAAAC,EAAAD,KAAA,gBAAA,GAAAC,EAAAD,KAAA,UAAA,GACtDA,KAAKqG,UAAYA,EACjBrG,KAAK2G,SAAWA,EAGZL,GAAiBtG,KAAKuH,SAC5B,CAaC,OAbArH,EAAA4G,EAAA,CAAA,CAAA3G,IAAA,UAAAC,MAED,WACE,IACE,IAAMoH,EAAYxH,KAAKqG,YAClBoB,EAAYD,EAAWxH,KAAK0H,aAC/B1H,KAAK0H,UAAYF,EACbxH,KAAK2G,UAAU3G,KAAK2G,SAAS3G,KAAK0H,WAK1C,CAHE,MAAOC,GAEPC,QAAQC,KAAKF,EACf,CACF,KAACb,CAAA,CA/Ba,GCheHgB,GAAa,SAACC,GACzB,IASIA,EAAAA,EAAGC,wBARLC,IAAAA,EACAC,IAAAA,EACAC,IAAAA,IACAC,IAAAA,KACAC,IAAAA,OACAC,IAAAA,MACAC,IAAAA,MACAC,IAAAA,OAGIC,EAAQC,OAAOC,iBAAiBZ,GAEhCa,EAAS,CACbR,KAAMS,SAASJ,EAAMK,YACrBR,MAAOO,SAASJ,EAAMM,aACtBV,OAAQQ,SAASJ,EAAMO,cACvBb,IAAKU,SAASJ,EAAMQ,YAGhBC,EAAU,CACdd,KAAMS,SAASJ,EAAMU,aACrBb,MAAOO,SAASJ,EAAMW,cACtBf,OAAQQ,SAASJ,EAAMY,eACvBlB,IAAKU,SAASJ,EAAMa,aAqDtB,MAAO,CACLrB,EAAAA,EACAC,EAAAA,EACAC,IAAAA,EACAC,KAAAA,EACAC,OAAAA,EACAC,MAAAA,EACAC,MAAAA,EACAC,OAAAA,EACAe,WAAYC,KAAKC,MAAMlB,EAAQK,EAAOR,KAAOQ,EAAON,OACpDoB,YAAaF,KAAKC,MAAMjB,EAASI,EAAOT,IAAMS,EAAOP,QACrDO,OAAAA,EACAM,QAAAA,EACAS,OAAQ5B,EAAG6B,iBA/DO,SAACC,GACnB,IAAMC,EAAmBnB,iBAAiBkB,GAE1C,KAAIpB,EAAMsB,UAA+B,YAAnBtB,EAAMsB,UAIF,SAAtBD,EAAYE,OAIY,SAAxBF,EAAYG,SAKU,SAAxBH,EAAYG,SACsB,WAAlCH,EAAY,mBAFd,CAOA,OAAQrB,EAAMyB,UACZ,IAAK,SACL,IAAK,WACH,MACF,QACE,OAGJ,OAAQnC,EAAGoC,SACT,IAAK,KACL,IAAK,QACL,IAAK,QACL,IAAK,QACH,OAAO,EAGX,OAAQ1B,EAAMwB,SACZ,IAAK,QACL,IAAK,YACL,IAAK,QACL,IAAK,OACL,IAAK,OACH,OAAO,EAxBX,EA2C8BG,CAAYrC,EAAG6B,eAEjD,EC/EgB,SAAAS,GACdC,EACAjE,GAKA,MAAMD,UAAEA,EAAS3B,SAAEA,EAAQQ,QAAEA,EAAOnC,MAAEA,GAAUwH,EAE1CC,EAAUpI,GAAO,GACjBuF,EAAYvF,EAAY,MACxBqI,EAAerI,EAAOkE,GAC5BmE,EAAalI,QAAU+D,EAEvB,MAAMoE,EAAY/F,GACfgD,IACQ,IAAKA,EAAWzC,UAASnC,WAElC,CAACmC,EAASnC,IAIRyH,EAAQjI,SAAW+D,IACrBqB,EAAUpF,QAAU+D,EAAU5B,IAAY3B,GAC1CyH,EAAQjI,SAAU,GAGpB,MAAOoI,EAAiBC,GAAsBC,EAC5CH,EAAU/C,EAAUpF,UAmBtB,OAfA0C,GAAU,KACR,IAAIiC,EASJ,OARIuD,EAAalI,UACf2E,EAAcb,GACX9D,GAAYkI,EAAalI,QAAQA,EAASQ,KAC1C4E,IACCiD,EAAmBF,EAAU/C,GAAW,KAIvC,KACDT,GAAaA,GAAa,CAC/B,GACA,CAACwD,EAAW3H,EAAOsD,IAEfsE,CACT,CCnDaG,IC+BDC,GD/BCD,GAAc,WAAkB,OAAKE,yDAAP,GAAmB,EEDjDC,GAAiB,WAAA,SAAAA,IAAAjL,EAAAC,KAAAgL,GAAA/K,EAAAD,KAAA,aACC,GAAIC,EAEoBD,KAAA,eAAA,IAAIiL,SAAShL,EACbD,KAAA,WAAA,IAAIkL,IAAK,CA0G7D,OA1G6DhL,EAAA8K,EAAA,CAAA,CAAA7K,IAAA,eAAAC,MAEtD,SAAa+K,GACnB,IAAMC,EAAapL,KAAKqL,aAAaC,IAAIH,GACzC,GAAIC,EACF,OAAOA,EAGT,IAAMG,EAAQV,KAGd,OADA7K,KAAKqL,aAAaG,IAAIL,EAASI,GACxBA,CACT,GAAC,CAAApL,IAAA,iBAAAC,MAED,SAAe+K,EAAsBM,GACnC,IAAMC,EAAY1L,KAAK2L,aAAaR,GACpC,MAAUM,GAAAA,OAAAA,eAAkBC,EAC9B,GAAC,CAAAvL,IAAA,WAAAC,MAED,SAAS+K,EAAsBS,GAAqC,IAAAhF,EAAA5G,KAC5D6L,EAAoB7L,KAAK8L,aAAaX,EAASS,EAAiBG,MAEtE,GAAIF,EAAmB,CACrB,GAAIG,EAAQJ,EAAiBK,SAAUJ,EAAkBI,UACvD,OAAOJ,EAGT7L,KAAK8L,aAAaX,EAASS,EAAiBG,MAAMG,SACpD,CAEA,IAAIC,EAA6B,KAE3BC,EAAKpM,KAAKqM,eAAelB,EAASS,EAAiBG,MA+BzD,OA9BA/L,KAAKsM,SAASd,IAAIY,EAAI,CACpBA,GAAAA,EACAH,SAAUL,EAAiBK,SAC3BM,OAAQ,WACFJ,GACFA,IAGFA,EAAUP,EAAiBY,UACzBrB,EACAS,EAAiBK,SACjBL,EAAiBa,QAEpB,EACDP,QAAS,WACFC,GAILA,GACD,EACDO,OAAQ,WACN,OAAO9F,EAAK8F,OAAON,EACrB,IAGEpM,KAAK2M,WACP3M,KAAKsM,SAAShB,IAAIc,GAAIG,SAGjBvM,KAAKsM,SAAShB,IAAIc,EAC3B,GAAC,CAAAjM,IAAA,MAAAC,MAED,SAAIgM,GACF,OAAOpM,KAAKsM,SAAShB,IAAIc,EAC3B,GAAC,CAAAjM,IAAA,SAAAC,MAED,SAAOgM,GACL,IAAMI,EAAYxM,KAAKsL,IAAIc,GACtBI,IAILA,EAAUN,UACVlM,KAAKsM,SAASM,OAAOJ,EAAUJ,IACjC,GAAC,CAAAjM,IAAA,SAAAC,MAED,WACEJ,KAAK2M,WAAY,EACjB3M,KAAKsM,SAAShF,SAAQ,SAACuF,GACrBA,EAAWN,QACb,GACF,GAAC,CAAApM,IAAA,UAAAC,MAED,WACEJ,KAAK2M,WAAY,EACjB3M,KAAKsM,SAAShF,SAAQ,SAACuF,GACrBA,EAAWX,SACb,GACF,GAAC,CAAA/L,IAAA,eAAAC,MAED,SAAa+K,EAAsBM,GACjC,OAAOzL,KAAKsL,IAAItL,KAAKqM,eAAelB,EAASM,GAC/C,GAAC,CAAAtL,IAAA,kBAAAC,MAED,SAAgB+K,EAAsBM,GACpC,OAAOzL,KAAK0M,OAAO1M,KAAKqM,eAAelB,EAASM,GAClD,GAAC,CAAAtL,IAAA,QAAAC,MAED,WACEJ,KAAKkM,UACLlM,KAAKqL,aAAe,IAAIJ,QACxBjL,KAAKsM,SAAW,IAAIpB,GACtB,KAACF,CAAA,CA9G2B,IDgC9B,SAAYF,GACVA,EAAAA,EAAA,gBAAA,GAAA,kBACAA,EAAAA,EAAA,eAAA,GAAA,gBACD,CAHD,CAAYA,KAAAA,GAGX,CAAA,IEjCD,IAAsBgC,GAAa,WASjC,SAAAA,EAAYL,GAAW1M,EAAAC,KAAA8M,GAAA7M,EAAAD,KAAA,eAAA,GAAAC,EANeD,KAAA,WAAA,IAAIgL,IAAmB/K,EACED,KAAA,cAAA,IAAI+M,KAMjE/M,KAAKyM,QAAUA,CACjB,CA0LC,OA1LAvM,EAAA4M,EAAA,CAAA,CAAA3M,IAAA,SAAAC,MAED,SAAO6D,GAAsC,IAAA2C,EAAA5G,KAE3C,OADAA,KAAK+G,YAAY3F,IAAI6C,GACd,WAAA,OAAM2C,EAAKG,YAAY6F,OAAO3I,EAAG,CAC1C,GAAC,CAAA9D,IAAA,UAAAC,MAED,WACMJ,KAAKgN,WACPhN,KAAKgN,YAGPhN,KAAKsM,SAASJ,UAEdlM,KAAK+G,YAAYO,SAAQ,SAAC2F,GACxBA,EAASnC,GAAoBoC,gBAC/B,GACF,GAAC,CAAA/M,IAAA,SAAAC,MAED,WACMJ,KAAKmN,UACPnN,KAAKmN,WAGPnN,KAAKsM,SAASC,SAEdvM,KAAK+G,YAAYO,SAAQ,SAAC2F,GACxBA,EAASnC,GAAoBsC,eAC/B,GACF,GAAC,CAAAjN,IAAA,UAAAC,MAED,WACEJ,KAAKkM,UACLlM,KAAK+G,YAAYvD,QACjBxD,KAAKsM,SAAS9I,OAChB,GAAC,CAAArD,IAAA,wBAAAC,MAED,SACE2H,EACAsF,EACAJ,EACAR,GAEA,IAAMa,EAAiB,SAACC,aC3D1BA,EACAF,EACAtF,GAGKwF,EAAEC,QACLD,EAAEC,MAAQ,CACRC,gBAAiB,WAAQ,EACzBC,cAAe,CAAE,IAMrB,IAFA,IAAMC,EAAoBJ,EAAEC,OAASD,EAAEC,MAAME,cAAcL,IAAe,GAEjEO,EAAI,EAAGA,EAAID,EAAiBpN,OAAQqN,IAAK,CAChD,IAAMC,EAAkBF,EAAiBC,GAEzC,GAAI7F,IAAO8F,GAAmB9F,EAAG+F,SAASD,GACxC,OAAO,CAEX,CAEA,OAAO,CACT,EDqCWE,CAA2BR,EAAGF,EAAWtF,KAC5CwF,EAAEC,MAAMC,gBAAkB,WACnBF,EAAEC,MAAME,cAAcL,KACzBE,EAAEC,MAAME,cAAcL,GAAa,IAGrCE,EAAEC,MAAME,cAAcL,GAAWrG,KAAKe,IAGxCkF,EAASM,KAMb,OAFAxF,EAAGiG,iBAAiBX,EAAWC,EAAgBb,GAExC,WAAA,OAAM1E,EAAGkG,oBAAoBZ,EAAWC,EAAgBb,EAAQ,CACzE,GAKA,CAAAtM,IAAA,wBAAAC,MAGA,WAAqB,IAAA8N,EAAAlO,KACbmO,EAAWnO,KAAKmO,WAKhBC,EAAkC,IAAIrB,IAExCsB,GAAwB,EACtBC,EAGF,IAAIpD,IAyCR,MAAO,CACL2B,WAxCiB1H,OAAOoJ,QAAQJ,GAAU9I,QAG1C,SAACC,EAAKkJ,GAAA,IAAAC,EAAAxL,EAAAuL,EAAA,GAAGzC,EAAI0C,EAAA,GAAEC,EAAOD,EAAA,GAAA,OACjBnJ,EAAAA,EAAAA,GAAAA,GACFyG,CAAAA,EAAAA,EAAAA,CAAAA,EAAAA,GAAO,SAAChE,EAAIkE,EAAUQ,GACrB,IAAMkC,EAAoB,WACxB,IAAMnC,EAAY0B,EAAK5B,SAASsC,SAAS7G,EAAI,CAC3CkE,SAAAA,EACAF,KAAAA,EACAU,QAAAA,EACAD,UAAWkC,IAIb,OADAN,EAAmBhN,IAAIoL,EAAUJ,IAC1BI,GAkBT,OAfA8B,EAAqB9C,IACnB0C,EAAK5B,SAASD,eAAetE,EAAIgE,GACjC4C,GASEN,GACFM,IAGK5G,CACR,IACD,GACF,CAAE,GAKF6G,SAAU,WACRP,GAAwB,EAExBC,EAAqBhH,SAAQ,SAACqH,GAC5BA,GACF,GACD,EACDxC,QAAS,WACPkC,GAAwB,EAExBD,EAAmB9G,SAAQ,SAACuH,GAAW,OACrCX,EAAK5B,SAASI,OAAOmC,KAEzB,EAEJ,GAAC,CAAA1O,IAAA,SAAAC,MAED,SACEiD,EAGAyL,GAEA,OAAO,IAAIzL,EAAKrD,KAAM8O,EACxB,GAEA,CAAA3O,IAAA,sBAAAC,MACU,SACR2O,EACA9K,GAEA,IAAM+K,EAAsB,GACtBb,EAAWY,EAASZ,WAEpBc,EAAkB,IAAIC,MAAMf,EAAU,CAC1C7C,IAAK,SAAC6D,EAAQhP,EAAUiP,GACtB,OAAIjP,KAAOgO,GAAa,EACfkB,QAAQ/D,IAAI6D,EAAQhP,EAAKiP,GAG3B,SAACrH,GAAe,IAAA,IAAAxC,EAAAC,UAAAjF,OAAR+O,EAAI,IAAA7J,MAAAF,EAAA,EAAAA,EAAA,EAAA,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAJ4J,EAAI5J,EAAA,GAAAF,UAAAE,GACjB,IAAMyG,EAAUgC,EAAShO,GAATgO,MAAAA,EAAcpG,CAAAA,GAAOuH,OAAAA,IAChCnD,GAIL6C,EAAoBhI,KAAKmF,GAE7B,IAKF,OAFAlI,EAAGgL,GAEI,WACLD,EAAoB1H,SAAQ,SAAC6E,GAC3BA,GACF,IAEJ,GAEA,CAAAhM,IAAA,UAAAC,MACA,SAAQ6D,GACN,OAAOjE,KAAKuP,oBAAoBvP,KAAMiE,EACxC,KAAC6I,CAAA,CArMgC,GEPb0C,GAGpB,SAAAC,yRAAAC,CAAAF,EAAQ1C,IAAR,QAAA6C,KAAAH,kkBAIA,SAAYI,EAAAA,EAAYnD,GAAW,IAAA7F,EAkB9B,OAlB8B7G,EAAAC,KAAAwP,GAClBvP,EAAA4P,EAAfjJ,EAAA+I,EAAAG,KAAA9P,KAAMyM,IAAS,eAAA,GAAAxM,EAAA4P,EAAAjJ,GAAA,wCAAA,GACfA,EAAKgJ,QAAUA,EACfhJ,EAAK6F,QAAUA,EAGf7F,EAAKmJ,iCAAmCnJ,EAAKgJ,QAAQI,QAAO,SAACC,GAC3D,OAAQA,GACN,KAAKnF,GAAoBsC,eACvB,OAAOxG,EAAK2F,SAEd,KAAKzB,GAAoBoC,gBACvB,OAAOtG,EAAKsF,UAEd,QACE,OAGN,IAAGtF,CACL,CAUC,OARD1G,EAAAsP,EAAA,CAAA,CAAArP,IAAA,UAAAC,MACA,SAAQ6D,GACN,OAAOjE,KAAKuP,oBAAoBvP,KAAK4P,QAAS3L,EAChD,GAAC,CAAA9D,IAAA,UAAAC,MAED,WACE8P,EAAAC,EAAAX,EAAAY,WAAA,UAAApQ,MAAA8P,KAAA9P,MACAA,KAAK+P,kCACP,KAACP,CAAA,CAjCD,GCDF,SAASa,GAAOC,EAAUC,GACpBA,IACiB,mBAARD,EACTA,EAAIC,GAEJD,EAAIhO,QAAUiO,EAGpB,CAEgB,SAAAC,GACdrF,EACAsF,GAEA,MAAMC,EAAcvF,EAAQmF,IAQ5B,OAPAK,EACyB,iBAAhBD,EACP,kPAWOE,EAAazF,EANjBuF,EAM0B,CAC3BJ,IAAMC,IACJF,GAAOK,EAAaH,GACpBF,GAAOI,EAAQF,EAAK,GAPK,CAC3BD,IAAKG,GAUX,CAUM,SAAUI,GACdC,GAEA,MAAO,CAACC,EAAgB,QAASzB,KAE/B,IAAK0B,EAAeD,GAAgB,CAClC,IAAKA,EACH,OAGF,MAAMR,EAAOQ,EAEb,OADAR,GAAQO,EAAKP,KAASjB,GACfiB,CACR,CAKD,MAAMpF,EAA+B4F,EAGrC,OA7BJ,SAA0C5F,GACxC,GAA4B,iBAAjBA,EAAQ9H,KAInB,MAAM,IAAI4N,KACZ,CAqBIC,CAAiC/F,GAE1BqF,GAAarF,EAAS2F,EAAK,CAEtC,CAIM,SAAUK,GACdtE,GAEA,OAAO1H,OAAOC,KAAKyH,GAAYxH,QAAO,CAACC,EAAOnF,KAC5CmF,EAAMnF,GAAO0Q,IAA2B,IAAIvB,IAEnCzC,EAAW1M,MAAQmP,KAGrBhK,IACN,CAAE,EACP,CCjFO,MAAM8L,GAAkB,EAC7B3I,QACA4I,YACAC,gBAEA,MAAMC,EACJC,EAAAC,cAAA,MAAA,CACEJ,UAAWA,EACX5I,MAAO,CACLyB,SAAU,QACVD,QAAS,QACTyH,QAAS,EACTC,YAAa,QACbC,YAAa,MACbC,YAAa,cACbC,OAAQ,SACLrJ,KAKT,OAAI6I,GAAaA,EAAUS,gBAAkBC,SACpCC,EAASC,aAAaX,EAAWD,EAAUS,cAAcI,MAG3DZ,CAAS,EChCLa,GAAiBC,IAE5BrN,EAAUqN,EAAQ,GAAG,ECChB,IAAMC,GAAqB,SAACvG,EAAMpI,GACvC,IAAI4O,EAAkCxG,wBAAAA,OAAAA,EAA4C,0CAE1EyG,EAAiB7O,EAAjB6O,QAASC,EAAQ9O,EAAR8O,IAEbD,IACFD,GAAO,eAAArR,OAAmBsR,EAAkB,cAI1CC,IACFF,GAAO,IAAArR,OAAQuR,EAAM,MAIvB7K,QAAQC,KAAK0K,EACf,ECrBaG,GAAe,WAAH,MAA2B,oBAAXhK,MAAsB,EAElDiK,GAAU,WAAH,OAClBD,MAAkB,SAASE,KAAKlK,OAAOmK,UAAUC,UAAU,EAEhDC,GAAa,WAAH,OACrBL,MAAkB,UAAUE,KAAKlK,OAAOmK,UAAUC,UAAU"}