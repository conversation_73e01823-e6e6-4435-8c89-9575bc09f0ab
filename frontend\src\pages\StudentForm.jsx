import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { ArrowLeft, Upload, X } from 'lucide-react'
import { studentAPI } from '../services/api'
import toast from 'react-hot-toast'

export default function StudentForm() {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEdit = Boolean(id)

  const [loading, setLoading] = useState(false)
  const [photoPreview, setPhotoPreview] = useState(null)
  const [photoFile, setPhotoFile] = useState(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm({
    defaultValues: {
      student_id: '',
      name: '',
      course: '',
      email: '',
      address: ''
    }
  })

  useEffect(() => {
    if (isEdit) {
      fetchStudent()
    }
  }, [id, isEdit])

  const fetchStudent = async () => {
    try {
      setLoading(true)
      const response = await studentAPI.getById(id)
      const student = response.data
      
      // Set form values
      Object.keys(student).forEach(key => {
        if (key !== 'photo_url') {
          setValue(key, student[key])
        }
      })

      // Set photo preview if exists
      if (student.photo_url) {
        setPhotoPreview(`http://localhost:5001${student.photo_url}`)
      }
      
    } catch (error) {
      console.error('Error fetching student:', error)
      toast.error('Failed to load student data')
      navigate('/students')
    } finally {
      setLoading(false)
    }
  }



  const handlePhotoChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('File size must be less than 5MB')
        return
      }

      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file')
        return
      }

      setPhotoFile(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setPhotoPreview(e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  const removePhoto = () => {
    setPhotoFile(null)
    setPhotoPreview(null)
    // Reset file input
    const fileInput = document.getElementById('photo')
    if (fileInput) {
      fileInput.value = ''
    }
  }

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      const formData = { ...data }
      if (photoFile) {
        formData.photo = photoFile
      }

      if (isEdit) {
        await studentAPI.update(id, formData)
        toast.success('Student updated successfully')
      } else {
        await studentAPI.create(formData)
        toast.success('Student created successfully')
      }

      navigate('/students')

    } catch (error) {
      console.error('Error saving student:', error)
      const errorMessage = error.response?.data?.error || 'Failed to save student'
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (loading && isEdit) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="card p-6">
            <div className="space-y-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/students')}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {isEdit ? 'Edit Student' : 'Add New Student'}
          </h1>
          <p className="text-gray-600">
            {isEdit ? 'Update student information' : 'Enter student details to create a new record'}
          </p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="card p-6">
          <div className="space-y-6">
            {/* Photo Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Student Photo
              </label>
              <div className="flex items-center gap-4">
                {photoPreview ? (
                  <div className="relative">
                    <img
                      src={photoPreview}
                      alt="Student photo preview"
                      className="h-24 w-24 rounded-lg object-cover border-2 border-gray-200"
                    />
                    <button
                      type="button"
                      onClick={removePhoto}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ) : (
                  <div className="h-24 w-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                    <Upload className="h-8 w-8 text-gray-400" />
                  </div>
                )}
                <div>
                  <input
                    id="photo"
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoChange}
                    className="hidden"
                  />
                  <label
                    htmlFor="photo"
                    className="btn-secondary cursor-pointer"
                  >
                    {photoPreview ? 'Change Photo' : 'Upload Photo'}
                  </label>
                  <p className="text-xs text-gray-500 mt-1">
                    PNG, JPG, GIF up to 5MB
                  </p>
                </div>
              </div>
            </div>

            {/* Student Information */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Student ID *
                </label>
                <input
                  type="text"
                  {...register('student_id', {
                    required: 'Student ID is required',
                    pattern: {
                      value: /^[a-zA-Z0-9]+$/,
                      message: 'Student ID must be alphanumeric'
                    }
                  })}
                  className={`input ${errors.student_id ? 'border-red-500' : ''}`}
                  placeholder="e.g., STU001"
                />
                {errors.student_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.student_id.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name *
                </label>
                <input
                  type="text"
                  {...register('name', { required: 'Name is required' })}
                  className={`input ${errors.name ? 'border-red-500' : ''}`}
                  placeholder="John Doe"
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Course *
                </label>
                <input
                  type="text"
                  {...register('course', { required: 'Course is required' })}
                  className={`input ${errors.course ? 'border-red-500' : ''}`}
                  placeholder="Computer Science"
                />
                {errors.course && (
                  <p className="text-red-500 text-sm mt-1">{errors.course.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address'
                    }
                  })}
                  className={`input ${errors.email ? 'border-red-500' : ''}`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                )}
              </div>
            </div>

            {/* Address */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Address
              </label>
              <textarea
                {...register('address')}
                rows={3}
                className="input"
                placeholder="123 Main St, City, State 12345"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end gap-4">
          <button
            type="button"
            onClick={() => navigate('/students')}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Saving...' : isEdit ? 'Update Student' : 'Create Student'}
          </button>
        </div>
      </form>
    </div>
  )
}
