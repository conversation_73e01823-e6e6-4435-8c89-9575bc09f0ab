// MySQL database
const { getDatabase } = require('../config/mysqlDatabase');

// Helper function to execute MySQL queries
async function executeQuery(query, params = []) {
  const db = getDatabase();
  const [rows] = await db.execute(query, params);
  return rows;
}

// Helper function to get a single row
async function getOne(query, params = []) {
  const db = getDatabase();
  const [rows] = await db.execute(query, params);
  return rows[0] || null;
}
const { createCanvas, loadImage, registerFont } = require('canvas');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Ensure cards directory exists
const cardsDir = path.join(__dirname, '../uploads/cards');
if (!fs.existsSync(cardsDir)) {
  fs.mkdirSync(cardsDir, { recursive: true });
}

// Get all templates
const getAllTemplates = async (req, res) => {
  try {
    const rows = await executeQuery('SELECT * FROM card_templates ORDER BY created_at DESC');

    // Parse template_data JSON for each template
    const templates = rows.map(template => ({
      ...template,
      template_data: JSON.parse(template.template_data)
    }));

    res.json(templates);
  } catch (error) {
    console.error('Error fetching templates:', error);
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

// Get template by ID
const getTemplateById = async (req, res) => {
  try {
    const { id } = req.params;

    const template = await getOne('SELECT * FROM card_templates WHERE id = ?', [id]);

    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    res.json({
      ...template,
      template_data: JSON.parse(template.template_data)
    });
  } catch (error) {
    console.error('Error fetching template:', error);
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

// Create new template
const createTemplate = async (req, res) => {
  try {
    const { name, template_data, is_default } = req.body;

    const query = `
      INSERT INTO card_templates (name, template_data, is_default, created_at, updated_at)
      VALUES (?, ?, ?, NOW(), NOW())
    `;

    const result = await executeQuery(query, [name, JSON.stringify(template_data), is_default || 0]);

    // Get the created template
    const getQuery = 'SELECT * FROM card_templates WHERE id = ?';
    const template = await getOne(getQuery, [result.insertId]);

    res.status(201).json({
      message: 'Template created successfully',
      template: {
        ...template,
        template_data: JSON.parse(template.template_data)
      }
    });
  } catch (error) {
    console.error('Error creating template:', error);
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

// Update template
const updateTemplate = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  const { name, template_data, is_default } = req.body;
  
  const query = `
    UPDATE card_templates SET 
      name = ?, template_data = ?, is_default = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `;
  
  db.run(query, [name, JSON.stringify(template_data), is_default || 0, id], function(err) {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Get the updated template
    db.get('SELECT * FROM card_templates WHERE id = ?', [id], (err, row) => {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }
      
      res.json({
        message: 'Template updated successfully',
        template: {
          ...row,
          template_data: JSON.parse(row.template_data)
        }
      });
    });
  });
};

// Delete template
const deleteTemplate = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  
  // Check if template is default
  db.get('SELECT is_default FROM card_templates WHERE id = ?', [id], (err, row) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    if (!row) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    if (row.is_default) {
      return res.status(400).json({ error: 'Cannot delete default template' });
    }
    
    db.run('DELETE FROM card_templates WHERE id = ?', [id], function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }
      
      res.json({ message: 'Template deleted successfully' });
    });
  });
};

// Generate card for student using default template
const generateCard = async (req, res) => {
  try {
    const db = getDatabase();
    const { studentId } = req.params;

    // Get student data
    const student = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM students WHERE id = ?', [studentId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Get default template
    const template = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM card_templates WHERE is_default = 1', (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!template) {
      return res.status(404).json({ error: 'Default template not found' });
    }

    const cardUrl = await generateCardImage(student, JSON.parse(template.template_data));

    // Save generated card record
    db.run(
      'INSERT INTO generated_cards (student_id, template_id, card_url) VALUES (?, ?, ?)',
      [studentId, template.id, cardUrl],
      function(err) {
        if (err) {
          return res.status(500).json({ error: 'Database error', details: err.message });
        }

        res.json({
          message: 'Card generated successfully',
          cardUrl: cardUrl,
          generatedCardId: this.lastID
        });
      }
    );

  } catch (error) {
    console.error('Error generating card:', error);
    res.status(500).json({ error: 'Failed to generate card', details: error.message });
  }
};

// Generate card with specific template
const generateCardWithTemplate = async (req, res) => {
  try {
    const { studentId, templateId } = req.params;

    // Get student data
    const student = await getOne('SELECT * FROM students WHERE id = ?', [studentId]);
    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Get template
    const template = await getOne('SELECT * FROM card_templates WHERE id = ?', [templateId]);
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    let cardUrl;
    const templateData = JSON.parse(template.template_data);

    // Check if it's a Craft.js template
    if (templateData.craftjs) {
      cardUrl = await generateCraftJSCard(student, templateData, template.name);
    } else {
      cardUrl = await generateCardImage(student, templateData);
    }

    // Save generated card record
    const insertQuery = 'INSERT INTO generated_cards (student_id, template_id, card_url, generated_at) VALUES (?, ?, ?, NOW())';
    const result = await executeQuery(insertQuery, [studentId, templateId, cardUrl]);

    res.json({
      message: 'Card generated successfully',
      cardUrl: cardUrl,
      generatedCardId: result.insertId
    });

  } catch (error) {
    console.error('Error generating card:', error);
    res.status(500).json({ error: 'Failed to generate card', details: error.message });
  }
};

// Get generated cards for a student
const getGeneratedCards = async (req, res) => {
  try {
    const { studentId } = req.params;

    const query = `
      SELECT gc.*, ct.name as template_name
      FROM generated_cards gc
      JOIN card_templates ct ON gc.template_id = ct.id
      WHERE gc.student_id = ?
      ORDER BY gc.generated_at DESC
    `;

    const cards = await executeQuery(query, [studentId]);
    res.json(cards);
  } catch (error) {
    console.error('Error fetching generated cards:', error);
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

// Delete generated card
const deleteGeneratedCard = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;

  // Get card info first to delete file
  db.get('SELECT * FROM generated_cards WHERE id = ?', [id], (err, card) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    if (!card) {
      return res.status(404).json({ error: 'Generated card not found' });
    }

    // Delete from database
    db.run('DELETE FROM generated_cards WHERE id = ?', [id], function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }

      // Delete file
      if (card.card_url) {
        const filePath = path.join(__dirname, '..', card.card_url);
        fs.unlink(filePath, () => {});
      }

      res.json({ message: 'Generated card deleted successfully' });
    });
  });
};

// Preview card without saving
const previewCard = async (req, res) => {
  try {
    const { studentData, templateData } = req.body;

    if (!studentData || !templateData) {
      return res.status(400).json({ error: 'Student data and template data are required' });
    }

    const cardUrl = await generateCardImage(studentData, templateData, true); // true for preview

    res.json({
      message: 'Card preview generated successfully',
      previewUrl: cardUrl
    });

  } catch (error) {
    console.error('Error generating card preview:', error);
    res.status(500).json({ error: 'Failed to generate card preview', details: error.message });
  }
};

// Helper function to generate card image using Canvas
async function generateCardImage(student, templateData, isPreview = false) {
  const { width, height, backgroundColor, elements } = templateData;

  // Create canvas
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Set background
  ctx.fillStyle = backgroundColor;
  ctx.fillRect(0, 0, width, height);

  // Process each element
  for (const element of elements) {
    try {
      if (element.type === 'text') {
        await renderTextElement(ctx, element, student);
      } else if (element.type === 'image') {
        await renderImageElement(ctx, element, student);
      } else if (element.type === 'shape') {
        await renderShapeElement(ctx, element);
      }
    } catch (error) {
      console.error(`Error rendering element:`, error);
      // Continue with other elements even if one fails
    }
  }

  // Save the image
  const filename = `${isPreview ? 'preview_' : ''}${uuidv4()}.png`;
  const filepath = path.join(cardsDir, filename);
  const buffer = canvas.toBuffer('image/png');

  fs.writeFileSync(filepath, buffer);

  return `/uploads/cards/${filename}`;
}

// Render text element
async function renderTextElement(ctx, element, student) {
  const { content, x, y, fontSize = 14, fontWeight = 'normal', color = '#000000', textAlign = 'left' } = element;

  // Replace placeholders with student data
  let text = content;
  const placeholders = {
    '{name}': student.name || '',
    '{student_id}': student.student_id || '',
    '{email}': student.email || '',
    '{course}': student.course || '',
    '{address}': student.address || ''
  };

  Object.keys(placeholders).forEach(placeholder => {
    text = text.replace(new RegExp(placeholder, 'g'), placeholders[placeholder]);
  });

  // Set font properties
  ctx.font = `${fontWeight} ${fontSize}px Arial`;
  ctx.fillStyle = color;
  ctx.textAlign = textAlign;

  // Draw text
  ctx.fillText(text, x, y);
}

// Render image element
async function renderImageElement(ctx, element, student) {
  const { x, y, width, height, placeholder } = element;

  let imagePath = null;

  if (placeholder === 'student_photo' && student.photo_url) {
    imagePath = path.join(__dirname, '..', student.photo_url);
  }

  if (imagePath && fs.existsSync(imagePath)) {
    try {
      const image = await loadImage(imagePath);
      ctx.drawImage(image, x, y, width, height);
    } catch (error) {
      console.error('Error loading image:', error);
      // Draw placeholder rectangle
      drawImagePlaceholder(ctx, x, y, width, height);
    }
  } else {
    // Draw placeholder rectangle
    drawImagePlaceholder(ctx, x, y, width, height);
  }
}

// Draw image placeholder
function drawImagePlaceholder(ctx, x, y, width, height) {
  ctx.strokeStyle = '#cccccc';
  ctx.lineWidth = 2;
  ctx.strokeRect(x, y, width, height);

  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(x, y, width, height);

  // Draw X
  ctx.strokeStyle = '#999999';
  ctx.beginPath();
  ctx.moveTo(x, y);
  ctx.lineTo(x + width, y + height);
  ctx.moveTo(x + width, y);
  ctx.lineTo(x, y + height);
  ctx.stroke();
}

// Render shape element
async function renderShapeElement(ctx, element) {
  const { x, y, width, height, shapeType = 'rectangle', fillColor, strokeColor, strokeWidth = 1 } = element;

  ctx.lineWidth = strokeWidth;

  if (shapeType === 'rectangle') {
    if (fillColor) {
      ctx.fillStyle = fillColor;
      ctx.fillRect(x, y, width, height);
    }
    if (strokeColor) {
      ctx.strokeStyle = strokeColor;
      ctx.strokeRect(x, y, width, height);
    }
  } else if (shapeType === 'circle') {
    const radius = Math.min(width, height) / 2;
    const centerX = x + width / 2;
    const centerY = y + height / 2;

    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);

    if (fillColor) {
      ctx.fillStyle = fillColor;
      ctx.fill();
    }
    if (strokeColor) {
      ctx.strokeStyle = strokeColor;
      ctx.stroke();
    }
  } else if (shapeType === 'line') {
    if (strokeColor) {
      ctx.strokeStyle = strokeColor;
      ctx.beginPath();
      ctx.moveTo(x, y);
      ctx.lineTo(x + width, y + height);
      ctx.stroke();
    }
  }
}

// Bulk generate cards for all students using a template
const bulkGenerateCards = async (req, res) => {
  try {
    const db = getDatabase();
    const { templateId } = req.params;

    // Get template
    const template = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM card_templates WHERE id = ?', [templateId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    // Get all active students
    const students = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM students WHERE status = ?', ['active'], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (students.length === 0) {
      return res.status(400).json({ error: 'No active students found' });
    }

    // Create bulk job record
    const jobId = await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO bulk_card_jobs (template_id, total_students, status) VALUES (?, ?, ?)',
        [templateId, students.length, 'processing'],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });

    // Start background processing
    processBulkGeneration(jobId, students, JSON.parse(template.template_data), templateId);

    res.json({
      message: 'Bulk card generation started',
      jobId: jobId,
      totalStudents: students.length
    });

  } catch (error) {
    console.error('Error starting bulk generation:', error);
    res.status(500).json({ error: 'Failed to start bulk generation', details: error.message });
  }
};

// Process bulk generation in background
async function processBulkGeneration(jobId, students, templateData, templateId) {
  const db = getDatabase();
  let completed = 0;

  for (const student of students) {
    try {
      const cardUrl = await generateCardImage(student, templateData);

      // Save generated card record
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO generated_cards (student_id, template_id, card_url) VALUES (?, ?, ?)',
          [student.id, templateId, cardUrl],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      completed++;

      // Update job progress
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE bulk_card_jobs SET completed_students = ? WHERE id = ?',
          [completed, jobId],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

    } catch (error) {
      console.error(`Error generating card for student ${student.id}:`, error);
    }
  }

  // Mark job as completed
  await new Promise((resolve, reject) => {
    db.run(
      'UPDATE bulk_card_jobs SET status = ?, completed_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['completed', jobId],
      (err) => {
        if (err) reject(err);
        else resolve();
      }
    );
  });


}

// Get bulk generation jobs
const getBulkJobs = (req, res) => {
  const db = getDatabase();

  const query = `
    SELECT bj.*, ct.name as template_name
    FROM bulk_card_jobs bj
    JOIN card_templates ct ON bj.template_id = ct.id
    ORDER BY bj.created_at DESC
    LIMIT 20
  `;

  db.all(query, (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get bulk job status
const getBulkJobStatus = (req, res) => {
  const db = getDatabase();
  const { jobId } = req.params;

  const query = `
    SELECT bj.*, ct.name as template_name
    FROM bulk_card_jobs bj
    JOIN card_templates ct ON bj.template_id = ct.id
    WHERE bj.id = ?
  `;

  db.get(query, [jobId], (err, row) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    if (!row) {
      return res.status(404).json({ error: 'Job not found' });
    }

    res.json(row);
  });
};

// Generate card using Craft.js template data
async function generateCraftJSCard(student, templateData, templateName) {
  try {
    // Extract canvas dimensions from Craft.js data
    const craftData = templateData.craftjs;
    const canvasWidth = templateData.width || 400;
    const canvasHeight = templateData.height || 250;

    // Create canvas
    const canvas = createCanvas(canvasWidth, canvasHeight);
    const ctx = canvas.getContext('2d');

    // Set background color
    ctx.fillStyle = templateData.backgroundColor || '#ffffff';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // Process Craft.js nodes and render elements
    if (craftData && craftData.ROOT && craftData.ROOT.nodes) {
      await renderCraftJSNodes(ctx, craftData, student);
    }

    // Generate unique filename
    const timestamp = Date.now();
    const filename = `card_${student.id}_${timestamp}.png`;
    const filepath = `uploads/cards/${filename}`;

    // Ensure directory exists
    const fs = require('fs');
    const path = require('path');
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Save the image
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(filepath, buffer);

    return `/uploads/cards/${filename}`;
  } catch (error) {
    console.error('Error generating Craft.js card:', error);
    throw error;
  }
}

// Render Craft.js nodes recursively
async function renderCraftJSNodes(ctx, craftData, student, parentX = 0, parentY = 0) {
  try {
    // Get all nodes
    const nodes = craftData;

    // Process each node
    for (const nodeId in nodes) {
      const node = nodes[nodeId];
      if (!node || !node.type) continue;

      const props = node.props || {};
      const x = (props.x || 0) + parentX;
      const y = (props.y || 0) + parentY;

      switch (node.type.resolvedName) {
        case 'Text':
          await renderTextElement(ctx, props, student, x, y);
          break;
        case 'ImagePlaceholder':
          await renderImagePlaceholderElement(ctx, props, student, x, y);
          break;
        case 'Container':
          await renderContainerElement(ctx, props, x, y);
          // Render children if any
          if (node.nodes && node.nodes.length > 0) {
            for (const childId of node.nodes) {
              if (nodes[childId]) {
                await renderCraftJSNodes(ctx, { [childId]: nodes[childId] }, student, x, y);
              }
            }
          }
          break;
      }
    }
  } catch (error) {
    console.error('Error rendering Craft.js nodes:', error);
  }
}

// Render text element from Craft.js
async function renderTextElement(ctx, props, student, x, y) {
  try {
    let text = props.text || '';

    // Replace placeholders with student data
    text = text.replace(/{name}/g, student.name || '')
               .replace(/{student_id}/g, student.student_id || '')
               .replace(/{course}/g, student.course || '')
               .replace(/{email}/g, student.email || '')
               .replace(/{address}/g, student.address || '');

    // Set font properties
    const fontSize = props.fontSize || 16;
    const fontWeight = props.fontWeight || 'normal';
    const color = props.color || '#333333';
    const textAlign = props.textAlign || 'left';

    ctx.font = `${fontWeight} ${fontSize}px Arial`;
    ctx.fillStyle = color;
    ctx.textAlign = textAlign;
    ctx.textBaseline = 'top';

    // Draw text
    ctx.fillText(text, x, y);
  } catch (error) {
    console.error('Error rendering text element:', error);
  }
}

// Render image placeholder element from Craft.js
async function renderImagePlaceholderElement(ctx, props, student, x, y) {
  try {
    const width = props.width || 80;
    const height = props.height || 100;

    if (student.photo_url) {
      try {
        // Load and draw student photo
        const imagePath = `uploads${student.photo_url}`;
        if (require('fs').existsSync(imagePath)) {
          const image = await loadImage(imagePath);
          ctx.drawImage(image, x, y, width, height);
        } else {
          // Draw placeholder if image not found
          drawImagePlaceholder(ctx, x, y, width, height);
        }
      } catch (error) {
        console.error('Error loading student photo:', error);
        drawImagePlaceholder(ctx, x, y, width, height);
      }
    } else {
      // Draw placeholder
      drawImagePlaceholder(ctx, x, y, width, height);
    }
  } catch (error) {
    console.error('Error rendering image placeholder:', error);
  }
}

// Render container element from Craft.js
async function renderContainerElement(ctx, props, x, y) {
  try {
    const width = props.width || 400;
    const height = props.height || 250;
    const backgroundColor = props.backgroundColor || 'transparent';

    if (backgroundColor !== 'transparent') {
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(x, y, width, height);
    }
  } catch (error) {
    console.error('Error rendering container element:', error);
  }
}

// Draw image placeholder
function drawImagePlaceholder(ctx, x, y, width, height) {
  // Draw border
  ctx.strokeStyle = '#cccccc';
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]);
  ctx.strokeRect(x, y, width, height);
  ctx.setLineDash([]);

  // Draw placeholder text
  ctx.fillStyle = '#999999';
  ctx.font = '12px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('Photo', x + width/2, y + height/2);
}

// Download card file
const downloadCard = (req, res) => {
  try {
    const filePath = req.params[0]; // Get the full path after /download/
    const fullPath = path.join(__dirname, '../uploads/cards', filePath);

    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Set proper headers for download
    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Content-Disposition', `attachment; filename="${path.basename(filePath)}"`);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Send file
    res.sendFile(fullPath);
  } catch (error) {
    console.error('Error downloading file:', error);
    res.status(500).json({ error: 'Failed to download file' });
  }
};

module.exports = {
  getAllTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  generateCard,
  generateCardWithTemplate,
  getGeneratedCards,
  deleteGeneratedCard,
  previewCard,
  bulkGenerateCards,
  getBulkJobs,
  getBulkJobStatus,
  generateCraftJSCard,
  downloadCard
};
