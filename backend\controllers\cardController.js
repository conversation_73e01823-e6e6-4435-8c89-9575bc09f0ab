const { getDatabase } = require('../config/database');
const { createCanvas, loadImage, registerFont } = require('canvas');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Ensure cards directory exists
const cardsDir = path.join(__dirname, '../uploads/cards');
if (!fs.existsSync(cardsDir)) {
  fs.mkdirSync(cardsDir, { recursive: true });
}

// Get all templates
const getAllTemplates = (req, res) => {
  const db = getDatabase();
  
  db.all('SELECT * FROM card_templates ORDER BY created_at DESC', (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    // Parse template_data JSON for each template
    const templates = rows.map(template => ({
      ...template,
      template_data: JSON.parse(template.template_data)
    }));
    
    res.json(templates);
  });
};

// Get template by ID
const getTemplateById = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  
  db.get('SELECT * FROM card_templates WHERE id = ?', [id], (err, row) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    if (!row) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    res.json({
      ...row,
      template_data: JSON.parse(row.template_data)
    });
  });
};

// Create new template
const createTemplate = (req, res) => {
  const db = getDatabase();
  const { name, template_data, is_default } = req.body;
  
  const query = `
    INSERT INTO card_templates (name, template_data, is_default)
    VALUES (?, ?, ?)
  `;
  
  db.run(query, [name, JSON.stringify(template_data), is_default || 0], function(err) {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    // Get the created template
    db.get('SELECT * FROM card_templates WHERE id = ?', [this.lastID], (err, row) => {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }
      
      res.status(201).json({
        message: 'Template created successfully',
        template: {
          ...row,
          template_data: JSON.parse(row.template_data)
        }
      });
    });
  });
};

// Update template
const updateTemplate = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  const { name, template_data, is_default } = req.body;
  
  const query = `
    UPDATE card_templates SET 
      name = ?, template_data = ?, is_default = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `;
  
  db.run(query, [name, JSON.stringify(template_data), is_default || 0, id], function(err) {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    if (this.changes === 0) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Get the updated template
    db.get('SELECT * FROM card_templates WHERE id = ?', [id], (err, row) => {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }
      
      res.json({
        message: 'Template updated successfully',
        template: {
          ...row,
          template_data: JSON.parse(row.template_data)
        }
      });
    });
  });
};

// Delete template
const deleteTemplate = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  
  // Check if template is default
  db.get('SELECT is_default FROM card_templates WHERE id = ?', [id], (err, row) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    if (!row) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    if (row.is_default) {
      return res.status(400).json({ error: 'Cannot delete default template' });
    }
    
    db.run('DELETE FROM card_templates WHERE id = ?', [id], function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }
      
      res.json({ message: 'Template deleted successfully' });
    });
  });
};

// Generate card for student using default template
const generateCard = async (req, res) => {
  try {
    const db = getDatabase();
    const { studentId } = req.params;

    // Get student data
    const student = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM students WHERE id = ?', [studentId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Get default template
    const template = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM card_templates WHERE is_default = 1', (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!template) {
      return res.status(404).json({ error: 'Default template not found' });
    }

    const cardUrl = await generateCardImage(student, JSON.parse(template.template_data));

    // Save generated card record
    db.run(
      'INSERT INTO generated_cards (student_id, template_id, card_url) VALUES (?, ?, ?)',
      [studentId, template.id, cardUrl],
      function(err) {
        if (err) {
          return res.status(500).json({ error: 'Database error', details: err.message });
        }

        res.json({
          message: 'Card generated successfully',
          cardUrl: cardUrl,
          generatedCardId: this.lastID
        });
      }
    );

  } catch (error) {
    console.error('Error generating card:', error);
    res.status(500).json({ error: 'Failed to generate card', details: error.message });
  }
};

// Generate card with specific template
const generateCardWithTemplate = async (req, res) => {
  try {
    const db = getDatabase();
    const { studentId, templateId } = req.params;

    // Get student data
    const student = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM students WHERE id = ?', [studentId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Get template
    const template = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM card_templates WHERE id = ?', [templateId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    const cardUrl = await generateCardImage(student, JSON.parse(template.template_data));

    // Save generated card record
    db.run(
      'INSERT INTO generated_cards (student_id, template_id, card_url) VALUES (?, ?, ?)',
      [studentId, templateId, cardUrl],
      function(err) {
        if (err) {
          return res.status(500).json({ error: 'Database error', details: err.message });
        }

        res.json({
          message: 'Card generated successfully',
          cardUrl: cardUrl,
          generatedCardId: this.lastID
        });
      }
    );

  } catch (error) {
    console.error('Error generating card:', error);
    res.status(500).json({ error: 'Failed to generate card', details: error.message });
  }
};

// Get generated cards for a student
const getGeneratedCards = (req, res) => {
  const db = getDatabase();
  const { studentId } = req.params;

  const query = `
    SELECT gc.*, ct.name as template_name
    FROM generated_cards gc
    JOIN card_templates ct ON gc.template_id = ct.id
    WHERE gc.student_id = ?
    ORDER BY gc.generated_at DESC
  `;

  db.all(query, [studentId], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Delete generated card
const deleteGeneratedCard = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;

  // Get card info first to delete file
  db.get('SELECT * FROM generated_cards WHERE id = ?', [id], (err, card) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    if (!card) {
      return res.status(404).json({ error: 'Generated card not found' });
    }

    // Delete from database
    db.run('DELETE FROM generated_cards WHERE id = ?', [id], function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }

      // Delete file
      if (card.card_url) {
        const filePath = path.join(__dirname, '..', card.card_url);
        fs.unlink(filePath, () => {});
      }

      res.json({ message: 'Generated card deleted successfully' });
    });
  });
};

// Preview card without saving
const previewCard = async (req, res) => {
  try {
    const { studentData, templateData } = req.body;

    if (!studentData || !templateData) {
      return res.status(400).json({ error: 'Student data and template data are required' });
    }

    const cardUrl = await generateCardImage(studentData, templateData, true); // true for preview

    res.json({
      message: 'Card preview generated successfully',
      previewUrl: cardUrl
    });

  } catch (error) {
    console.error('Error generating card preview:', error);
    res.status(500).json({ error: 'Failed to generate card preview', details: error.message });
  }
};

// Helper function to generate card image using Canvas
async function generateCardImage(student, templateData, isPreview = false) {
  const { width, height, backgroundColor, elements } = templateData;

  // Create canvas
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Set background
  ctx.fillStyle = backgroundColor;
  ctx.fillRect(0, 0, width, height);

  // Process each element
  for (const element of elements) {
    try {
      if (element.type === 'text') {
        await renderTextElement(ctx, element, student);
      } else if (element.type === 'image') {
        await renderImageElement(ctx, element, student);
      } else if (element.type === 'shape') {
        await renderShapeElement(ctx, element);
      }
    } catch (error) {
      console.error(`Error rendering element:`, error);
      // Continue with other elements even if one fails
    }
  }

  // Save the image
  const filename = `${isPreview ? 'preview_' : ''}${uuidv4()}.png`;
  const filepath = path.join(cardsDir, filename);
  const buffer = canvas.toBuffer('image/png');

  fs.writeFileSync(filepath, buffer);

  return `/uploads/cards/${filename}`;
}

// Render text element
async function renderTextElement(ctx, element, student) {
  const { content, x, y, fontSize = 14, fontWeight = 'normal', color = '#000000', textAlign = 'left' } = element;

  // Replace placeholders with student data
  let text = content;
  const placeholders = {
    '{first_name}': student.first_name || '',
    '{last_name}': student.last_name || '',
    '{student_id}': student.student_id || '',
    '{email}': student.email || '',
    '{phone}': student.phone || '',
    '{course}': student.course || '',
    '{year_of_study}': student.year_of_study || '',
    '{address}': student.address || '',
    '{enrollment_date}': student.enrollment_date || '',
    '{status}': student.status || ''
  };

  Object.keys(placeholders).forEach(placeholder => {
    text = text.replace(new RegExp(placeholder, 'g'), placeholders[placeholder]);
  });

  // Set font properties
  ctx.font = `${fontWeight} ${fontSize}px Arial`;
  ctx.fillStyle = color;
  ctx.textAlign = textAlign;

  // Draw text
  ctx.fillText(text, x, y);
}

// Render image element
async function renderImageElement(ctx, element, student) {
  const { x, y, width, height, placeholder } = element;

  let imagePath = null;

  if (placeholder === 'student_photo' && student.photo_url) {
    imagePath = path.join(__dirname, '..', student.photo_url);
  }

  if (imagePath && fs.existsSync(imagePath)) {
    try {
      const image = await loadImage(imagePath);
      ctx.drawImage(image, x, y, width, height);
    } catch (error) {
      console.error('Error loading image:', error);
      // Draw placeholder rectangle
      drawImagePlaceholder(ctx, x, y, width, height);
    }
  } else {
    // Draw placeholder rectangle
    drawImagePlaceholder(ctx, x, y, width, height);
  }
}

// Draw image placeholder
function drawImagePlaceholder(ctx, x, y, width, height) {
  ctx.strokeStyle = '#cccccc';
  ctx.lineWidth = 2;
  ctx.strokeRect(x, y, width, height);

  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(x, y, width, height);

  // Draw X
  ctx.strokeStyle = '#999999';
  ctx.beginPath();
  ctx.moveTo(x, y);
  ctx.lineTo(x + width, y + height);
  ctx.moveTo(x + width, y);
  ctx.lineTo(x, y + height);
  ctx.stroke();
}

// Render shape element
async function renderShapeElement(ctx, element) {
  const { x, y, width, height, shapeType = 'rectangle', fillColor, strokeColor, strokeWidth = 1 } = element;

  ctx.lineWidth = strokeWidth;

  if (shapeType === 'rectangle') {
    if (fillColor) {
      ctx.fillStyle = fillColor;
      ctx.fillRect(x, y, width, height);
    }
    if (strokeColor) {
      ctx.strokeStyle = strokeColor;
      ctx.strokeRect(x, y, width, height);
    }
  } else if (shapeType === 'circle') {
    const radius = Math.min(width, height) / 2;
    const centerX = x + width / 2;
    const centerY = y + height / 2;

    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);

    if (fillColor) {
      ctx.fillStyle = fillColor;
      ctx.fill();
    }
    if (strokeColor) {
      ctx.strokeStyle = strokeColor;
      ctx.stroke();
    }
  } else if (shapeType === 'line') {
    if (strokeColor) {
      ctx.strokeStyle = strokeColor;
      ctx.beginPath();
      ctx.moveTo(x, y);
      ctx.lineTo(x + width, y + height);
      ctx.stroke();
    }
  }
}

module.exports = {
  getAllTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  generateCard,
  generateCardWithTemplate,
  getGeneratedCards,
  deleteGeneratedCard,
  previewCard
};
