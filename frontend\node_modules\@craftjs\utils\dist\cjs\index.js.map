{"version": 3, "file": "index.js", "sources": ["../../src/History.ts", "../../src/useMethods.ts", "../../src/EventHandlers/interfaces.ts", "../../src/getRandomId.ts", "../../src/EventHandlers/ConnectorRegistry.ts", "../../src/EventHandlers/EventHandlers.ts", "../../src/EventHandlers/isEventBlockedByDescendant.ts", "../../src/EventHandlers/DerivedEventHandlers.ts", "../../src/EventHandlers/wrapConnectorHooks.tsx", "../../src/deprecate.ts", "../../src/platform.ts", "../../src/constants.ts", "../../src/RenderIndicator.tsx", "../../src/getDOMInfo.ts", "../../src/useCollector.tsx", "../../src/useEffectOnce.tsx"], "sourcesContent": ["import { Patch, applyPatches } from 'immer';\n\ntype Timeline = Array<{\n  patches: Patch[];\n  inversePatches: Patch[];\n  timestamp: number;\n}>;\n\nexport const HISTORY_ACTIONS = {\n  UNDO: 'HISTORY_UNDO',\n  REDO: 'HISTORY_REDO',\n  THROTTLE: 'HISTORY_THROTTLE',\n  IGNORE: 'HISTORY_IGNORE',\n  MERGE: 'HISTORY_MERGE',\n  CLEAR: 'HISTORY_CLEAR',\n};\n\nexport class History {\n  timeline: Timeline = [];\n  pointer = -1;\n\n  add(patches: Patch[], inversePatches: Patch[]) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    this.pointer = this.pointer + 1;\n    this.timeline.length = this.pointer;\n    this.timeline[this.pointer] = {\n      patches,\n      inversePatches,\n      timestamp: Date.now(),\n    };\n  }\n\n  throttleAdd(\n    patches: Patch[],\n    inversePatches: Patch[],\n    throttleRate: number = 500\n  ) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    if (this.timeline.length && this.pointer >= 0) {\n      const {\n        patches: currPatches,\n        inversePatches: currInversePatches,\n        timestamp,\n      } = this.timeline[this.pointer];\n\n      const now = new Date();\n      const diff = now.getTime() - timestamp;\n\n      if (diff < throttleRate) {\n        this.timeline[this.pointer] = {\n          timestamp,\n          patches: [...currPatches, ...patches],\n          inversePatches: [...inversePatches, ...currInversePatches],\n        };\n        return;\n      }\n    }\n\n    this.add(patches, inversePatches);\n  }\n\n  merge(patches: Patch[], inversePatches: Patch[]) {\n    if (patches.length === 0 && inversePatches.length === 0) {\n      return;\n    }\n\n    if (this.timeline.length && this.pointer >= 0) {\n      const {\n        patches: currPatches,\n        inversePatches: currInversePatches,\n        timestamp,\n      } = this.timeline[this.pointer];\n\n      this.timeline[this.pointer] = {\n        timestamp,\n        patches: [...currPatches, ...patches],\n        inversePatches: [...inversePatches, ...currInversePatches],\n      };\n      return;\n    }\n\n    this.add(patches, inversePatches);\n  }\n\n  clear() {\n    this.timeline = [];\n    this.pointer = -1;\n  }\n\n  canUndo() {\n    return this.pointer >= 0;\n  }\n\n  canRedo() {\n    return this.pointer < this.timeline.length - 1;\n  }\n\n  undo(state) {\n    if (!this.canUndo()) {\n      return;\n    }\n\n    const { inversePatches } = this.timeline[this.pointer];\n    this.pointer = this.pointer - 1;\n    return applyPatches(state, inversePatches);\n  }\n\n  redo(state) {\n    if (!this.canRedo()) {\n      return;\n    }\n\n    this.pointer = this.pointer + 1;\n    const { patches } = this.timeline[this.pointer];\n    return applyPatches(state, patches);\n  }\n}\n", "// https://github.com/pelotom/use-methods\nimport produce, {\n  Patch,\n  produceWithPatches,\n  enableMapSet,\n  enablePatches,\n} from 'immer';\nimport isEqualWith from 'lodash/isEqualWith';\nimport { useMemo, useEffect, useRef, useCallback } from 'react';\n\nimport { History, HISTORY_ACTIONS } from './History';\nimport { Delete } from './utilityTypes';\n\nenableMapSet();\nenablePatches();\n\nexport type SubscriberAndCallbacksFor<\n  M extends MethodsOrOptions,\n  Q extends QueryMethods = any\n> = {\n  subscribe: Watcher<StateFor<M>>['subscribe'];\n  getState: () => { prev: StateFor<M>; current: StateFor<M> };\n  actions: CallbacksFor<M>;\n  query: QueryCallbacksFor<Q>;\n  history: History;\n};\n\nexport type StateFor<M extends MethodsOrOptions> = M extends MethodsOrOptions<\n  infer S,\n  any\n>\n  ? S\n  : never;\n\nexport type CallbacksFor<\n  M extends MethodsOrOptions\n> = M extends MethodsOrOptions<any, infer R>\n  ? {\n      [T in ActionUnion<R>['type']]: (\n        ...payload: ActionByType<ActionUnion<R>, T>['payload']\n      ) => void;\n    } & {\n      history: {\n        undo: () => void;\n        redo: () => void;\n        clear: () => void;\n        throttle: (\n          rate?: number\n        ) => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n        merge: () => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n        ignore: () => Delete<\n          {\n            [T in ActionUnion<R>['type']]: (\n              ...payload: ActionByType<ActionUnion<R>, T>['payload']\n            ) => void;\n          },\n          M extends Options ? M['ignoreHistoryForActions'][number] : never\n        >;\n      };\n    }\n  : {};\n\nexport type Methods<S = any, R extends MethodRecordBase<S> = any, Q = any> = (\n  state: S,\n  query: Q\n) => R;\n\nexport type Options<S = any, R extends MethodRecordBase<S> = any, Q = any> = {\n  methods: Methods<S, R, Q>;\n  ignoreHistoryForActions: ReadonlyArray<keyof MethodRecordBase>;\n  normalizeHistory?: (state: S) => void;\n};\n\nexport type MethodsOrOptions<\n  S = any,\n  R extends MethodRecordBase<S> = any,\n  Q = any\n> = Methods<S, R, Q> | Options<S, R, Q>;\n\nexport type MethodRecordBase<S = any> = Record<\n  string,\n  (...args: any[]) => S extends object ? S | void : S\n>;\n\nexport type Action<T = any, P = any> = {\n  type: T;\n  payload?: P;\n  config?: Record<string, any>;\n};\n\nexport type ActionUnion<R extends MethodRecordBase> = {\n  [T in keyof R]: { type: T; payload: Parameters<R[T]> };\n}[keyof R];\n\nexport type ActionByType<A, T> = A extends { type: infer T2 }\n  ? T extends T2\n    ? A\n    : never\n  : never;\n\nexport type QueryMethods<\n  S = any,\n  O = any,\n  R extends MethodRecordBase<S> = any\n> = (state?: S, options?: O) => R;\nexport type QueryCallbacksFor<M extends QueryMethods> = M extends QueryMethods<\n  any,\n  any,\n  infer R\n>\n  ? {\n      [T in ActionUnion<R>['type']]: (\n        ...payload: ActionByType<ActionUnion<R>, T>['payload']\n      ) => ReturnType<R[T]>;\n    } & {\n      history: {\n        canUndo: () => boolean;\n        canRedo: () => boolean;\n      };\n    }\n  : {};\n\nexport type PatchListenerAction<M extends MethodsOrOptions> = {\n  type: keyof CallbacksFor<M>;\n  params: any;\n  patches: Patch[];\n};\n\nexport type PatchListener<\n  S,\n  M extends MethodsOrOptions,\n  Q extends QueryMethods\n> = (\n  newState: S,\n  previousState: S,\n  actionPerformedWithPatches: PatchListenerAction<M>,\n  query: QueryCallbacksFor<Q>,\n  normalizer: (cb: (draft: S) => void) => void\n) => void;\n\nexport function useMethods<S, R extends MethodRecordBase<S>>(\n  methodsOrOptions: MethodsOrOptions<S, R>, // methods to manipulate the state\n  initialState: any\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods\n>(\n  methodsOrOptions: MethodsOrOptions<S, R, QueryCallbacksFor<Q>>, // methods to manipulate the state\n  initialState: any,\n  queryMethods: Q\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods\n>(\n  methodsOrOptions: MethodsOrOptions<S, R, QueryCallbacksFor<Q>>, // methods to manipulate the state\n  initialState: any,\n  queryMethods: Q,\n  patchListener: PatchListener<\n    S,\n    MethodsOrOptions<S, R, QueryCallbacksFor<Q>>,\n    Q\n  >\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q>;\n\nexport function useMethods<\n  S,\n  R extends MethodRecordBase<S>,\n  Q extends QueryMethods = null\n>(\n  methodsOrOptions: MethodsOrOptions<S, R>,\n  initialState: any,\n  queryMethods?: Q,\n  patchListener?: any\n): SubscriberAndCallbacksFor<MethodsOrOptions<S, R>, Q> {\n  const history = useMemo(() => new History(), []);\n\n  let methodsFactory: Methods<S, R>;\n  let ignoreHistoryForActionsRef = useRef([]);\n  let normalizeHistoryRef = useRef<any>(() => {});\n\n  if (typeof methodsOrOptions === 'function') {\n    methodsFactory = methodsOrOptions;\n  } else {\n    methodsFactory = methodsOrOptions.methods;\n    ignoreHistoryForActionsRef.current = methodsOrOptions.ignoreHistoryForActions as any;\n    normalizeHistoryRef.current = methodsOrOptions.normalizeHistory;\n  }\n\n  const patchListenerRef = useRef(patchListener);\n  patchListenerRef.current = patchListener;\n\n  const stateRef = useRef(initialState);\n\n  const reducer = useMemo(() => {\n    const { current: normalizeHistory } = normalizeHistoryRef;\n    const { current: ignoreHistoryForActions } = ignoreHistoryForActionsRef;\n    const { current: patchListener } = patchListenerRef;\n\n    return (state: S, action: Action) => {\n      const query =\n        queryMethods && createQuery(queryMethods, () => state, history);\n\n      let finalState;\n      let [nextState, patches, inversePatches] = (produceWithPatches as any)(\n        state,\n        (draft: S) => {\n          switch (action.type) {\n            case HISTORY_ACTIONS.UNDO: {\n              return history.undo(draft);\n            }\n            case HISTORY_ACTIONS.REDO: {\n              return history.redo(draft);\n            }\n            case HISTORY_ACTIONS.CLEAR: {\n              history.clear();\n              return {\n                ...draft,\n              };\n            }\n\n            // TODO: Simplify History API\n            case HISTORY_ACTIONS.IGNORE:\n            case HISTORY_ACTIONS.MERGE:\n            case HISTORY_ACTIONS.THROTTLE: {\n              const [type, ...params] = action.payload;\n              methodsFactory(draft, query)[type](...params);\n              break;\n            }\n            default:\n              methodsFactory(draft, query)[action.type](...action.payload);\n          }\n        }\n      );\n\n      finalState = nextState;\n\n      if (patchListener) {\n        patchListener(\n          nextState,\n          state,\n          { type: action.type, params: action.payload, patches },\n          query,\n          (cb) => {\n            let normalizedDraft = produceWithPatches(nextState, cb);\n            finalState = normalizedDraft[0];\n\n            patches = [...patches, ...normalizedDraft[1]];\n            inversePatches = [...normalizedDraft[2], ...inversePatches];\n          }\n        );\n      }\n\n      if (\n        [HISTORY_ACTIONS.UNDO, HISTORY_ACTIONS.REDO].includes(\n          action.type as any\n        ) &&\n        normalizeHistory\n      ) {\n        finalState = produce(finalState, normalizeHistory);\n      }\n\n      if (\n        ![\n          ...ignoreHistoryForActions,\n          HISTORY_ACTIONS.UNDO,\n          HISTORY_ACTIONS.REDO,\n          HISTORY_ACTIONS.IGNORE,\n          HISTORY_ACTIONS.CLEAR,\n        ].includes(action.type as any)\n      ) {\n        if (action.type === HISTORY_ACTIONS.THROTTLE) {\n          history.throttleAdd(\n            patches,\n            inversePatches,\n            action.config && action.config.rate\n          );\n        } else if (action.type === HISTORY_ACTIONS.MERGE) {\n          history.merge(patches, inversePatches);\n        } else {\n          history.add(patches, inversePatches);\n        }\n      }\n\n      return finalState;\n    };\n  }, [history, methodsFactory, queryMethods]);\n\n  const getState = useCallback(() => stateRef.current, []);\n  const watcher = useMemo(() => new Watcher<S>(getState), [getState]);\n\n  const dispatch = useCallback(\n    (action: any) => {\n      const newState = reducer(stateRef.current, action);\n      stateRef.current = newState;\n      watcher.notify();\n    },\n    [reducer, watcher]\n  );\n\n  useEffect(() => {\n    watcher.notify();\n  }, [watcher]);\n\n  const query = useMemo(\n    () =>\n      !queryMethods\n        ? []\n        : createQuery(queryMethods, () => stateRef.current, history),\n    [history, queryMethods]\n  );\n\n  const actions = useMemo(() => {\n    const actionTypes = Object.keys(methodsFactory(null, null));\n\n    const { current: ignoreHistoryForActions } = ignoreHistoryForActionsRef;\n\n    return {\n      ...actionTypes.reduce((accum, type) => {\n        accum[type] = (...payload) => dispatch({ type, payload });\n        return accum;\n      }, {} as any),\n      history: {\n        undo() {\n          return dispatch({\n            type: HISTORY_ACTIONS.UNDO,\n          });\n        },\n        redo() {\n          return dispatch({\n            type: HISTORY_ACTIONS.REDO,\n          });\n        },\n        clear: () => {\n          return dispatch({\n            type: HISTORY_ACTIONS.CLEAR,\n          });\n        },\n        throttle: (rate) => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.THROTTLE,\n                    payload: [type, ...payload],\n                    config: {\n                      rate: rate,\n                    },\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n        ignore: () => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.IGNORE,\n                    payload: [type, ...payload],\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n        merge: () => {\n          return {\n            ...actionTypes\n              .filter((type) => !ignoreHistoryForActions.includes(type))\n              .reduce((accum, type) => {\n                accum[type] = (...payload) =>\n                  dispatch({\n                    type: HISTORY_ACTIONS.MERGE,\n                    payload: [type, ...payload],\n                  });\n                return accum;\n              }, {} as any),\n          };\n        },\n      },\n    };\n  }, [dispatch, methodsFactory]);\n\n  return useMemo(\n    () => ({\n      getState,\n      subscribe: (collector, cb, collectOnCreate) =>\n        watcher.subscribe(collector, cb, collectOnCreate),\n      actions,\n      query,\n      history,\n    }),\n    [actions, query, watcher, getState, history]\n  ) as any;\n}\n\nexport function createQuery<Q extends QueryMethods>(\n  queryMethods: Q,\n  getState,\n  history: History\n) {\n  const queries = Object.keys(queryMethods()).reduce((accum, key) => {\n    return {\n      ...accum,\n      [key]: (...args: any) => {\n        return queryMethods(getState())[key](...args);\n      },\n    };\n  }, {} as QueryCallbacksFor<typeof queryMethods>);\n\n  return {\n    ...queries,\n    history: {\n      canUndo: () => history.canUndo(),\n      canRedo: () => history.canRedo(),\n    },\n  };\n}\n\nclass Watcher<S> {\n  getState;\n  subscribers: Subscriber[] = [];\n\n  constructor(getState) {\n    this.getState = getState;\n  }\n\n  /**\n   * Creates a Subscriber\n   * @returns {() => void} a Function that removes the Subscriber\n   */\n  subscribe<C>(\n    collector: (state: S) => C,\n    onChange: (collected: C) => void,\n    collectOnCreate?: boolean\n  ): () => void {\n    const subscriber = new Subscriber(\n      () => collector(this.getState()),\n      onChange,\n      collectOnCreate\n    );\n    this.subscribers.push(subscriber);\n    return this.unsubscribe.bind(this, subscriber);\n  }\n\n  unsubscribe(subscriber) {\n    if (this.subscribers.length) {\n      const index = this.subscribers.indexOf(subscriber);\n      if (index > -1) return this.subscribers.splice(index, 1);\n    }\n  }\n\n  notify() {\n    this.subscribers.forEach((subscriber) => subscriber.collect());\n  }\n}\n\nclass Subscriber {\n  collected: any;\n  collector: () => any;\n  onChange: (collected: any) => void;\n  id;\n\n  /**\n   * Creates a Subscriber\n   * @param collector The method that returns an object of values to be collected\n   * @param onChange A callback method that is triggered when the collected values has changed\n   * @param collectOnCreate If set to true, the collector/onChange will be called on instantiation\n   */\n  constructor(collector, onChange, collectOnCreate = false) {\n    this.collector = collector;\n    this.onChange = onChange;\n\n    // Collect and run onChange callback when Subscriber is created\n    if (collectOnCreate) this.collect();\n  }\n\n  collect() {\n    try {\n      const recollect = this.collector();\n      if (!isEqualWith(recollect, this.collected)) {\n        this.collected = recollect;\n        if (this.onChange) this.onChange(this.collected);\n      }\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.warn(err);\n    }\n  }\n}\n", "import { EventHandlers } from './EventHandlers';\n\nexport type Connector = (el: HTMLElement, ...args: any) => any;\n\nexport type ConnectorsRecord = Record<string, Connector>;\n\nexport type ChainableConnector<T extends Connector, O extends any> = T extends (\n  element: infer E,\n  ...args: infer P\n) => any\n  ? <B extends E | O>(element: B, ...args: P) => B\n  : never;\n\nexport type ChainableConnectors<\n  H extends ConnectorsRecord,\n  E extends any = HTMLElement\n> = {\n  [T in keyof H]: H[T] extends Connector ? ChainableConnector<H[T], E> : never;\n};\n\nexport type CraftDOMEvent<T extends Event> = T & {\n  craft: {\n    stopPropagation: () => void;\n    blockedEvents: Record<string, HTMLElement[]>;\n  };\n};\n\nexport type CraftEventListener<K extends keyof HTMLElementEventMap> = (\n  ev: CraftDOMEvent<HTMLElementEventMap[K]>\n) => any;\n\nexport type EventHandlerConnectors<\n  H extends EventHandlers,\n  E extends any = HTMLElement\n> = ChainableConnectors<ReturnType<H['handlers']>, E>;\n\nexport type ConnectorsUsage<H extends EventHandlers> = {\n  register: () => void;\n  cleanup: () => void;\n  connectors: EventHandlerConnectors<H>;\n};\n\nexport enum EventHandlerUpdates {\n  HandlerDisabled,\n  HandlerEnabled,\n}\n\nexport type ConnectorToRegister = {\n  name: string;\n  required: any;\n  connector: Connector;\n  options?: Record<string, any>;\n};\n\nexport type RegisteredConnector = {\n  id: string;\n  required: any;\n  enable: () => void;\n  disable: () => void;\n  remove: () => void;\n};\n", "import { nanoid } from 'nanoid';\n\n// By default nanoid generate an ID with 21 characters. To reduce the footprint, we default to 10 characters.\n// We have a higher probability for collisions, though\n\n/**\n * Generate a random ID. That ID can for example be used as a node ID.\n *\n * @param size The number of characters that are generated for the ID. Defaults to `10`\n * @returns A random id\n */\nexport const getRandomId = (size: number = 10) => nanoid(size);\n", "import isEqual from 'shallowequal';\n\nimport { ConnectorToRegister, RegisteredConnector } from './interfaces';\n\nimport { getRandomId } from '../getRandomId';\n\n/**\n * Stores all connected DOM elements and their connectors here\n * This allows us to easily enable/disable and perform cleanups\n */\nexport class ConnectorRegistry {\n  private isEnabled: boolean = true;\n\n  private elementIdMap: WeakMap<HTMLElement, string> = new WeakMap();\n  private registry: Map<String, RegisteredConnector> = new Map();\n\n  private getElementId(element: HTMLElement) {\n    const existingId = this.elementIdMap.get(element);\n    if (existingId) {\n      return existingId;\n    }\n\n    const newId = getRandomId();\n\n    this.elementIdMap.set(element, newId);\n    return newId;\n  }\n\n  getConnectorId(element: HTMLElement, connectorName: string) {\n    const elementId = this.getElementId(element);\n    return `${connectorName}--${elementId}`;\n  }\n\n  register(element: HTMLElement, connectorPayload: ConnectorToRegister) {\n    const existingConnector = this.getByElement(element, connectorPayload.name);\n\n    if (existingConnector) {\n      if (isEqual(connectorPayload.required, existingConnector.required)) {\n        return existingConnector;\n      }\n\n      this.getByElement(element, connectorPayload.name).disable();\n    }\n\n    let cleanup: () => void | null = null;\n\n    const id = this.getConnectorId(element, connectorPayload.name);\n    this.registry.set(id, {\n      id,\n      required: connectorPayload.required,\n      enable: () => {\n        if (cleanup) {\n          cleanup();\n        }\n\n        cleanup = connectorPayload.connector(\n          element,\n          connectorPayload.required,\n          connectorPayload.options\n        );\n      },\n      disable: () => {\n        if (!cleanup) {\n          return;\n        }\n\n        cleanup();\n      },\n      remove: () => {\n        return this.remove(id);\n      },\n    });\n\n    if (this.isEnabled) {\n      this.registry.get(id).enable();\n    }\n\n    return this.registry.get(id);\n  }\n\n  get(id: string) {\n    return this.registry.get(id);\n  }\n\n  remove(id: string) {\n    const connector = this.get(id);\n    if (!connector) {\n      return;\n    }\n\n    connector.disable();\n    this.registry.delete(connector.id);\n  }\n\n  enable() {\n    this.isEnabled = true;\n    this.registry.forEach((connectors) => {\n      connectors.enable();\n    });\n  }\n\n  disable() {\n    this.isEnabled = false;\n    this.registry.forEach((connectors) => {\n      connectors.disable();\n    });\n  }\n\n  getByElement(element: HTMLElement, connectorName: string) {\n    return this.get(this.getConnectorId(element, connectorName));\n  }\n\n  removeByElement(element: HTMLElement, connectorName: string) {\n    return this.remove(this.getConnectorId(element, connectorName));\n  }\n\n  clear() {\n    this.disable();\n    this.elementIdMap = new WeakMap();\n    this.registry = new Map();\n  }\n}\n", "import { ConnectorRegistry } from './ConnectorRegistry';\nimport {\n  EventHandlerUpdates,\n  CraftEventListener,\n  EventHandlerConnectors,\n  CraftDOMEvent,\n  Connector,\n  ConnectorsUsage,\n  RegisteredConnector,\n} from './interfaces';\nimport { isEventBlockedByDescendant } from './isEventBlockedByDescendant';\n\nexport abstract class EventHandlers<O extends Record<string, any> = {}> {\n  options: O;\n\n  private registry: ConnectorRegistry = new ConnectorRegistry();\n  private subscribers: Set<(msg: EventHandlerUpdates) => void> = new Set();\n\n  onEnable?(): void;\n  onDisable?(): void;\n\n  constructor(options?: O) {\n    this.options = options;\n  }\n\n  listen(cb: (msg: EventHandlerUpdates) => void) {\n    this.subscribers.add(cb);\n    return () => this.subscribers.delete(cb);\n  }\n\n  disable() {\n    if (this.onDisable) {\n      this.onDisable();\n    }\n\n    this.registry.disable();\n\n    this.subscribers.forEach((listener) => {\n      listener(EventHandlerUpdates.HandlerDisabled);\n    });\n  }\n\n  enable() {\n    if (this.onEnable) {\n      this.onEnable();\n    }\n\n    this.registry.enable();\n\n    this.subscribers.forEach((listener) => {\n      listener(EventHandlerUpdates.HandlerEnabled);\n    });\n  }\n\n  cleanup() {\n    this.disable();\n    this.subscribers.clear();\n    this.registry.clear();\n  }\n\n  addCraftEventListener<K extends keyof HTMLElementEventMap>(\n    el: HTMLElement,\n    eventName: K,\n    listener: CraftEventListener<K>,\n    options?: boolean | AddEventListenerOptions\n  ) {\n    const bindedListener = (e: CraftDOMEvent<HTMLElementEventMap[K]>) => {\n      if (!isEventBlockedByDescendant(e, eventName, el)) {\n        e.craft.stopPropagation = () => {\n          if (!e.craft.blockedEvents[eventName]) {\n            e.craft.blockedEvents[eventName] = [];\n          }\n\n          e.craft.blockedEvents[eventName].push(el);\n        };\n\n        listener(e);\n      }\n    };\n\n    el.addEventListener(eventName, bindedListener, options);\n\n    return () => el.removeEventListener(eventName, bindedListener, options);\n  }\n\n  // Defines the connectors and their logic\n  abstract handlers(): Record<string, (el: HTMLElement, ...args: any[]) => any>;\n\n  /**\n   * Creates a record of chainable connectors and tracks their usages\n   */\n  createConnectorsUsage(): ConnectorsUsage<this> {\n    const handlers = this.handlers();\n\n    // Track all active connector ids here\n    // This is so we can return a cleanup method below so the callee can programmatically cleanup all connectors\n\n    const activeConnectorIds: Set<string> = new Set();\n\n    let canRegisterConnectors = false;\n    const connectorsToRegister: Map<\n      string,\n      () => RegisteredConnector\n    > = new Map();\n\n    const connectors = Object.entries(handlers).reduce<\n      Record<string, Connector>\n    >(\n      (accum, [name, handler]) => ({\n        ...accum,\n        [name]: (el, required, options) => {\n          const registerConnector = () => {\n            const connector = this.registry.register(el, {\n              required,\n              name,\n              options,\n              connector: handler,\n            });\n\n            activeConnectorIds.add(connector.id);\n            return connector;\n          };\n\n          connectorsToRegister.set(\n            this.registry.getConnectorId(el, name),\n            registerConnector\n          );\n\n          /**\n           * If register() has been called,\n           * register the connector immediately.\n           *\n           * Otherwise, registration is deferred until after register() is called\n           */\n          if (canRegisterConnectors) {\n            registerConnector();\n          }\n\n          return el;\n        },\n      }),\n      {}\n    ) as any;\n\n    return {\n      connectors,\n      register: () => {\n        canRegisterConnectors = true;\n\n        connectorsToRegister.forEach((registerConnector) => {\n          registerConnector();\n        });\n      },\n      cleanup: () => {\n        canRegisterConnectors = false;\n\n        activeConnectorIds.forEach((connectorId) =>\n          this.registry.remove(connectorId)\n        );\n      },\n    };\n  }\n\n  derive<C extends EventHandlers>(\n    type: {\n      new (...args: any[]): C;\n    },\n    opts: C['options']\n  ) {\n    return new type(this, opts);\n  }\n\n  // This method allows us to execute multiple connectors and returns a single cleanup method for all of them\n  protected createProxyHandlers<H extends EventHandlers>(\n    instance: H,\n    cb: (connectors: EventHandlerConnectors<H>) => void\n  ) {\n    const connectorsToCleanup = [];\n    const handlers = instance.handlers();\n\n    const proxiedHandlers = new Proxy(handlers, {\n      get: (target, key: any, receiver) => {\n        if (key in handlers === false) {\n          return Reflect.get(target, key, receiver);\n        }\n\n        return (el, ...args) => {\n          const cleanup = handlers[key](el, ...args);\n          if (!cleanup) {\n            return;\n          }\n\n          connectorsToCleanup.push(cleanup);\n        };\n      },\n    });\n\n    cb(proxiedHandlers as any);\n\n    return () => {\n      connectorsToCleanup.forEach((cleanup) => {\n        cleanup();\n      });\n    };\n  }\n\n  // This lets us to execute and cleanup sibling connectors\n  reflect(cb: (connectors: EventHandlerConnectors<this>) => void) {\n    return this.createProxyHandlers(this, cb);\n  }\n}\n", "import { CraftDOMEvent } from './interfaces';\n\n/**\n * Check if a specified event is blocked by a child\n * that's a descendant of the specified element\n */\nexport function isEventBlockedByDescendant<K extends keyof HTMLElementEventMap>(\n  e: CraftDOMEvent<HTMLElementEventMap[K]>,\n  eventName: K,\n  el: HTMLElement\n) {\n  // Store initial Craft event value\n  if (!e.craft) {\n    e.craft = {\n      stopPropagation: () => {},\n      blockedEvents: {},\n    };\n  }\n\n  const blockingElements = (e.craft && e.craft.blockedEvents[eventName]) || [];\n\n  for (let i = 0; i < blockingElements.length; i++) {\n    const blockingElement = blockingElements[i];\n\n    if (el !== blockingElement && el.contains(blockingElement)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n", "import { EventHandlers } from './EventHandlers';\nimport { EventHandlerConnectors, EventHandlerUpdates } from './interfaces';\n\n// Creates EventHandlers that depends on another EventHandlers instance\n// This lets us to easily create new connectors that composites of the parent EventHandlers instance\nexport abstract class DerivedEventHandlers<\n  P extends EventHandlers,\n  O extends Record<string, any> = {}\n> extends EventHandlers<O> {\n  derived: P;\n  unsubscribeParentHandlerListener: () => void;\n\n  constructor(derived: P, options?: O) {\n    super(options);\n    this.derived = derived;\n    this.options = options;\n\n    // Automatically disable/enable depending on the parent handlers\n    this.unsubscribeParentHandlerListener = this.derived.listen((msg) => {\n      switch (msg) {\n        case EventHandlerUpdates.HandlerEnabled: {\n          return this.enable();\n        }\n        case EventHandlerUpdates.HandlerDisabled: {\n          return this.disable();\n        }\n        default: {\n          return;\n        }\n      }\n    });\n  }\n\n  // A method to easily inherit parent connectors\n  inherit(cb: (connectors: EventHandlerConnectors<P>) => void) {\n    return this.createProxyHandlers(this.derived, cb);\n  }\n\n  cleanup() {\n    super.cleanup();\n    this.unsubscribeParentHandlerListener();\n  }\n}\n", "// https://github.com/react-dnd/react-dnd\nimport { isValidElement, ReactElement } from 'react';\nimport { cloneElement } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { ChainableConnectors, ConnectorsRecord } from './interfaces';\n\nfunction setRef(ref: any, node: any) {\n  if (node) {\n    if (typeof ref === 'function') {\n      ref(node);\n    } else {\n      ref.current = node;\n    }\n  }\n}\n\nexport function cloneWithRef(\n  element: any,\n  newRef: any\n): React.ReactElement<any> {\n  const previousRef = element.ref;\n  invariant(\n    typeof previousRef !== 'string',\n    'Cannot connect to an element with an existing string ref. ' +\n      'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' +\n      'Read more: https://facebook.github.io/react/docs/more-about-refs.html#the-ref-callback-attribute'\n  );\n\n  if (!previousRef) {\n    // When there is no ref on the element, use the new ref directly\n    return cloneElement(element, {\n      ref: newRef,\n    });\n  } else {\n    return cloneElement(element, {\n      ref: (node: any) => {\n        setRef(previousRef, node);\n        setRef(newRef, node);\n      },\n    });\n  }\n}\n\nfunction throwIfCompositeComponentElement(element: React.ReactElement<any>) {\n  if (typeof element.type === 'string') {\n    return;\n  }\n\n  throw new Error();\n}\n\nexport function wrapHookToRecognizeElement(\n  hook: (node: any, ...args: any[]) => void\n) {\n  return (elementOrNode = null, ...args: any) => {\n    // When passed a node, call the hook straight away.\n    if (!isValidElement(elementOrNode)) {\n      if (!elementOrNode) {\n        return;\n      }\n\n      const node = elementOrNode;\n      node && hook(node, ...args);\n      return node;\n    }\n\n    // If passed a ReactElement, clone it and attach this function as a ref.\n    // This helps us achieve a neat API where user doesn't even know that refs\n    // are being used under the hood.\n    const element: ReactElement | null = elementOrNode;\n    throwIfCompositeComponentElement(element as any);\n\n    return cloneWithRef(element, hook);\n  };\n}\n\n// A React wrapper for our connectors\n// Wrap all our connectors so that would additionally accept React.ReactElement\nexport function wrapConnectorHooks<H extends ConnectorsRecord>(\n  connectors: H\n): ChainableConnectors<H, React.ReactElement | HTMLElement> {\n  return Object.keys(connectors).reduce((accum, key) => {\n    accum[key] = wrapHookToRecognizeElement((...args) => {\n      // @ts-ignore\n      return connectors[key](...args);\n    });\n\n    return accum;\n  }, {}) as any;\n}\n", "type DeprecationPayload = Partial<{\n  suggest: string;\n  doc: string;\n}>;\n\nexport const deprecationWarning = (name, payload?: DeprecationPayload) => {\n  let message = `Deprecation warning: ${name} will be deprecated in future relases.`;\n\n  const { suggest, doc } = payload;\n\n  if (suggest) {\n    message += ` Please use ${suggest} instead.`;\n  }\n\n  // URL link to Documentation\n  if (doc) {\n    message += `(${doc})`;\n  }\n\n  // eslint-disable-next-line no-console\n  console.warn(message);\n};\n", "export const isClientSide = () => typeof window !== 'undefined';\n\nexport const isLinux = () =>\n  isClientSide() && /Linux/i.test(window.navigator.userAgent);\n\nexport const isChromium = () =>\n  isClientSide() && /Chrome/i.test(window.navigator.userAgent);\n", "export const ROOT_NODE = 'ROOT';\nexport const DEPRECATED_ROOT_NODE = 'canvas-ROOT';\n\n// TODO: Use a better way to store/display error messages\nexport const ERROR_NOPARENT = 'Parent id cannot be ommited';\nexport const ERROR_DUPLICATE_NODEID =\n  'Attempting to add a node with duplicated id';\nexport const ERROR_INVALID_NODEID =\n  'Node does not exist, it may have been removed';\nexport const ERROR_TOP_LEVEL_ELEMENT_NO_ID =\n  'A <Element /> that is used inside a User Component must specify an `id` prop, eg: <Element id=\"text_element\">...</Element> ';\nexport const ERROR_MISSING_PLACEHOLDER_PLACEMENT =\n  'Placeholder required placement info (parent, index, or where) is missing';\nexport const ERROR_MOVE_CANNOT_DROP =\n  'Node cannot be dropped into target parent';\nexport const ERROR_MOVE_INCOMING_PARENT = 'Target parent rejects incoming node';\nexport const ERROR_MOVE_OUTGOING_PARENT =\n  'Current parent rejects outgoing node';\nexport const ERROR_MOVE_NONCANVAS_CHILD =\n  'Cannot move node that is not a direct child of a Canvas node';\nexport const ERROR_MOVE_TO_NONCANVAS_PARENT =\n  'Cannot move node into a non-Canvas parent';\nexport const ERROR_MOVE_TOP_LEVEL_NODE = 'A top-level Node cannot be moved';\nexport const ERROR_MOVE_ROOT_NODE = 'Root Node cannot be moved';\n\nexport const ERROR_MOVE_TO_DESCENDANT = 'Cannot move node into a descendant';\nexport const ERROR_NOT_IN_RESOLVER =\n  'The component type specified for this node (%node_type%) does not exist in the resolver';\nexport const ERROR_INFINITE_CANVAS =\n  \"The component specified in the <Canvas> `is` prop has additional Canvas specified in it's render template.\";\nexport const ERROR_CANNOT_DRAG =\n  'The node has specified a canDrag() rule that prevents it from being dragged';\nexport const ERROR_INVALID_NODE_ID = 'Invalid parameter Node Id specified';\nexport const ERROR_DELETE_TOP_LEVEL_NODE =\n  'Attempting to delete a top-level Node';\n\nexport const ERROR_RESOLVER_NOT_AN_OBJECT = `Resolver in <Editor /> has to be an object. For (de)serialization Craft.js needs a list of all the User Components. \n    \nMore info: https://craft.js.org/r/docs/api/editor#props`;\n\nexport const ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER = `An Error occurred while deserializing components: Cannot find component <%displayName% /> in resolver map. Please check your resolver in <Editor />\n\nAvailable components in resolver: %availableComponents%\n\nMore info: https://craft.js.org/r/docs/api/editor#props`;\n\nexport const ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT = `You can only use useEditor in the context of <Editor />. \n\nPlease only use useEditor in components that are children of the <Editor /> component.`;\n\nexport const ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT = `You can only use useNode in the context of <Editor />. \n\nPlease only use useNode in components that are children of the <Editor /> component.`;\n", "import React from 'react';\nimport ReactDOM from 'react-dom';\n\ntype RenderIndicatorProps = {\n  style: React.CSSProperties;\n  className?: string;\n  parentDom?: HTMLElement;\n};\n\nexport const RenderIndicator = ({\n  style,\n  className,\n  parentDom,\n}: RenderIndicatorProps) => {\n  const indicator = (\n    <div\n      className={className}\n      style={{\n        position: 'fixed',\n        display: 'block',\n        opacity: 1,\n        borderStyle: 'solid',\n        borderWidth: '1px',\n        borderColor: 'transparent',\n        zIndex: 99999,\n        ...style,\n      }}\n    ></div>\n  );\n\n  if (parentDom && parentDom.ownerDocument !== document) {\n    return ReactDOM.createPortal(indicator, parentDom.ownerDocument.body);\n  }\n\n  return indicator;\n};\n", "export const getDOMInfo = (el: HTMLElement) => {\n  const {\n    x,\n    y,\n    top,\n    left,\n    bottom,\n    right,\n    width,\n    height,\n  } = el.getBoundingClientRect() as DOMRect;\n\n  const style = window.getComputedStyle(el);\n\n  const margin = {\n    left: parseInt(style.marginLeft),\n    right: parseInt(style.marginRight),\n    bottom: parseInt(style.marginBottom),\n    top: parseInt(style.marginTop),\n  };\n\n  const padding = {\n    left: parseInt(style.paddingLeft),\n    right: parseInt(style.paddingRight),\n    bottom: parseInt(style.paddingBottom),\n    top: parseInt(style.paddingTop),\n  };\n\n  const styleInFlow = (parent: HTMLElement) => {\n    const parentStyle: any = getComputedStyle(parent);\n\n    if (style.overflow && style.overflow !== 'visible') {\n      return;\n    }\n\n    if (parentStyle.float !== 'none') {\n      return;\n    }\n\n    if (parentStyle.display === 'grid') {\n      return;\n    }\n\n    if (\n      parentStyle.display === 'flex' &&\n      parentStyle['flex-direction'] !== 'column'\n    ) {\n      return;\n    }\n\n    switch (style.position) {\n      case 'static':\n      case 'relative':\n        break;\n      default:\n        return;\n    }\n\n    switch (el.tagName) {\n      case 'TR':\n      case 'TBODY':\n      case 'THEAD':\n      case 'TFOOT':\n        return true;\n    }\n\n    switch (style.display) {\n      case 'block':\n      case 'list-item':\n      case 'table':\n      case 'flex':\n      case 'grid':\n        return true;\n    }\n\n    return;\n  };\n\n  return {\n    x,\n    y,\n    top,\n    left,\n    bottom,\n    right,\n    width,\n    height,\n    outerWidth: Math.round(width + margin.left + margin.right),\n    outerHeight: Math.round(height + margin.top + margin.bottom),\n    margin,\n    padding,\n    inFlow: el.parentElement && !!styleInFlow(el.parentElement),\n  };\n};\n", "import { useState, useCallback, useRef, useEffect } from 'react';\n\nimport { SubscriberAndCallbacksFor } from './useMethods';\nimport { ConditionallyMergeRecordTypes } from './utilityTypes';\n\ntype CollectorMethods<S extends SubscriberAndCallbacksFor<any, any>> = {\n  actions: S['actions'];\n  query: S['query'];\n};\n\nexport type useCollectorReturnType<\n  S extends SubscriberAndCallbacksFor<any, any>,\n  C = null\n> = ConditionallyMergeRecordTypes<C, CollectorMethods<S>>;\nexport function useCollector<S extends SubscriberAndCallbacksFor<any, any>, C>(\n  store: S,\n  collector?: (\n    state: ReturnType<S['getState']>['current'],\n    query: S['query']\n  ) => C\n): useCollectorReturnType<S, C> {\n  const { subscribe, getState, actions, query } = store;\n\n  const initial = useRef(true);\n  const collected = useRef<any>(null);\n  const collectorRef = useRef(collector);\n  collectorRef.current = collector;\n\n  const onCollect = useCallback(\n    (collected) => {\n      return { ...collected, actions, query };\n    },\n    [actions, query]\n  );\n\n  // Collect states for initial render\n  if (initial.current && collector) {\n    collected.current = collector(getState(), query);\n    initial.current = false;\n  }\n\n  const [renderCollected, setRenderCollected] = useState(\n    onCollect(collected.current)\n  );\n\n  // Collect states on state change\n  useEffect(() => {\n    let unsubscribe;\n    if (collectorRef.current) {\n      unsubscribe = subscribe(\n        (current) => collectorRef.current(current, query),\n        (collected) => {\n          setRenderCollected(onCollect(collected));\n        }\n      );\n    }\n    return () => {\n      if (unsubscribe) unsubscribe();\n    };\n  }, [onCollect, query, subscribe]);\n\n  return renderCollected;\n}\n", "import { useEffect } from 'react';\n\nexport const useEffectOnce = (effect: () => void) => {\n  /* eslint-disable-next-line react-hooks/exhaustive-deps */\n  useEffect(effect, []);\n};\n"], "names": ["HISTORY_ACTIONS", "UNDO", "REDO", "THROTTLE", "IGNORE", "MERGE", "CLEAR", "History", "_classCallCheck", "this", "_defineProperty", "_createClass", "key", "value", "patches", "inversePatches", "length", "pointer", "timeline", "timestamp", "Date", "now", "throttleRate", "_this$timeline$this$p", "currPatches", "currInversePatches", "getTime", "concat", "_toConsumableArray", "add", "_this$timeline$this$p2", "state", "canUndo", "applyPatches", "canRedo", "createQuery", "queryMethods", "getState", "history", "queries", "Object", "keys", "reduce", "accum", "_objectSpread", "_queryMethods", "apply", "arguments", "enableMapSet", "enablePatches", "EventHandlerUpdates", "Watcher", "collector", "onChange", "collectOnCreate", "_this", "subscriber", "Subscriber", "subscribers", "push", "unsubscribe", "bind", "index", "indexOf", "splice", "for<PERSON>ach", "collect", "recollect", "isEqualWith", "collected", "err", "console", "warn", "getRandomId", "nanoid", "ConnectorRegistry", "WeakMap", "Map", "element", "existingId", "elementIdMap", "get", "newId", "set", "connectorName", "elementId", "getElementId", "connectorPayload", "existingConnector", "getByElement", "name", "isEqual", "required", "disable", "cleanup", "id", "getConnectorId", "registry", "enable", "connector", "options", "remove", "isEnabled", "delete", "connectors", "EventHandlers", "Set", "cb", "onDisable", "listener", "HandlerDisabled", "onEnable", "Handler<PERSON><PERSON>bled", "clear", "el", "eventName", "bindedListener", "e", "craft", "stopPropagation", "blockedEvents", "blockingElements", "i", "blockingElement", "contains", "isEventBlockedByDescendant", "addEventListener", "removeEventListener", "_this2", "handlers", "activeConnectorIds", "canRegisterConnectors", "connectorsToRegister", "entries", "_ref", "_ref2", "_slicedToArray", "handler", "registerConnector", "register", "connectorId", "type", "opts", "instance", "connectorsToCleanup", "proxiedHandlers", "Proxy", "target", "receiver", "Reflect", "_len", "args", "Array", "_key", "createProxyHandlers", "DerivedEventHandlers", "_EventHandlers", "_inherits", "_super", "derived", "_assertThisInitialized", "call", "unsubscribeParentHandlerListener", "listen", "msg", "_get", "_getPrototypeOf", "prototype", "setRef", "ref", "node", "current", "cloneWithRef", "newRef", "previousRef", "invariant", "cloneElement", "wrapHookToRecognizeElement", "hook", "elementOrNode", "isValidElement", "Error", "throwIfCompositeComponentElement", "isClientSide", "window", "style", "className", "parentDom", "indicator", "React", "createElement", "position", "display", "opacity", "borderStyle", "borderWidth", "borderColor", "zIndex", "ownerDocument", "document", "ReactDOM", "createPortal", "body", "payload", "message", "suggest", "doc", "getBoundingClientRect", "x", "y", "top", "left", "bottom", "right", "width", "height", "getComputedStyle", "margin", "parseInt", "marginLeft", "marginRight", "marginBottom", "marginTop", "padding", "paddingLeft", "paddingRight", "paddingBottom", "paddingTop", "outerWidth", "Math", "round", "outerHeight", "inFlow", "parentElement", "parent", "parentStyle", "overflow", "float", "tagName", "styleInFlow", "test", "navigator", "userAgent", "store", "subscribe", "actions", "query", "initial", "useRef", "collectorRef", "onCollect", "useCallback", "renderCollected", "setRenderCollected", "useState", "useEffect", "effect", "methodsOrOptions", "initialState", "patchListener", "methodsFactory", "useMemo", "ignoreHistoryForActionsRef", "normalizeHistoryRef", "methods", "ignoreHistoryForActions", "normalizeHistory", "patchListenerRef", "stateRef", "reducer", "action", "finalState", "_produceWithPatches2", "produceWithPatches", "draft", "_methodsFactory2", "undo", "redo", "_methodsFactory", "_action$payload", "params", "slice", "nextState", "normalizedDraft", "includes", "produce", "throttleAdd", "config", "rate", "merge", "watcher", "dispatch", "newState", "notify", "actionTypes", "throttle", "filter", "_len2", "_key2", "ignore", "_len3", "_key3", "_len4", "_key4"], "mappings": "w4HAQO,IAAMA,EAAkB,CAC7BC,KAAM,eACNC,KAAM,eACNC,SAAU,mBACVC,OAAQ,iBACRC,MAAO,gBACPC,MAAO,iBAGIC,EAAO,WAAA,SAAAA,IAAAC,EAAAC,KAAAF,GAAAG,EAAAD,KAAA,WACG,IAAEC,EAAAD,KAAA,WACZ,EAAC,CAsGX,OAtGWE,EAAAJ,EAAA,CAAA,CAAAK,IAAA,MAAAC,MAEZ,SAAIC,EAAkBC,GACG,IAAnBD,EAAQE,QAA0C,IAA1BD,EAAeC,SAI3CP,KAAKQ,QAAUR,KAAKQ,QAAU,EAC9BR,KAAKS,SAASF,OAASP,KAAKQ,QAC5BR,KAAKS,SAAST,KAAKQ,SAAW,CAC5BH,QAAAA,EACAC,eAAAA,EACAI,UAAWC,KAAKC,OAEpB,GAAC,CAAAT,IAAA,cAAAC,MAED,SACEC,EACAC,GAC0B,IAA1BO,yDAAuB,IAEvB,GAAuB,IAAnBR,EAAQE,QAA0C,IAA1BD,EAAeC,OAA3C,CAIA,GAAIP,KAAKS,SAASF,QAAUP,KAAKQ,SAAW,EAAG,CAC7C,IAAAM,EAIId,KAAKS,SAAST,KAAKQ,SAHZO,IAATV,QACgBW,IAAhBV,eACAI,IAAAA,UAMF,IAHY,IAAIC,MACCM,UAAYP,EAElBG,EAMT,YALAb,KAAKS,SAAST,KAAKQ,SAAW,CAC5BE,UAAAA,EACAL,QAAaU,GAAAA,OAAAA,EAAAA,GAAgBV,EAAAA,IAC7BC,eAAc,GAAAY,OAAAC,EAAMb,GAAca,EAAKH,KAI7C,CAEAhB,KAAKoB,IAAIf,EAASC,EAtBlB,CAuBF,GAAC,CAAAH,IAAA,QAAAC,MAED,SAAMC,EAAkBC,GACtB,GAAuB,IAAnBD,EAAQE,QAA0C,IAA1BD,EAAeC,OAI3C,GAAIP,KAAKS,SAASF,QAAUP,KAAKQ,SAAW,EAA5C,CACE,IAAAa,EAIIrB,KAAKS,SAAST,KAAKQ,SAFLQ,IAAhBV,eAIFN,KAAKS,SAAST,KAAKQ,SAAW,CAC5BE,YAJAA,UAKAL,QAAaU,GAAAA,OAAAA,IAPbV,SAO6BA,EAAAA,IAC7BC,eAAc,GAAAY,OAAAC,EAAMb,GAAca,EAAKH,IAG3C,MAEAhB,KAAKoB,IAAIf,EAASC,EACpB,GAAC,CAAAH,IAAA,QAAAC,MAED,WACEJ,KAAKS,SAAW,GAChBT,KAAKQ,SAAW,CAClB,GAAC,CAAAL,IAAA,UAAAC,MAED,WACE,OAAOJ,KAAKQ,SAAW,CACzB,GAAC,CAAAL,IAAA,UAAAC,MAED,WACE,OAAOJ,KAAKQ,QAAUR,KAAKS,SAASF,OAAS,CAC/C,GAAC,CAAAJ,IAAA,OAAAC,MAED,SAAKkB,GACH,GAAKtB,KAAKuB,UAAV,CAIA,IAAQjB,EAAmBN,KAAKS,SAAST,KAAKQ,SAAtCF,eAER,OADAN,KAAKQ,QAAUR,KAAKQ,QAAU,EACvBgB,EAAYA,aAACF,EAAOhB,EAJ3B,CAKF,GAAC,CAAAH,IAAA,OAAAC,MAED,SAAKkB,GACH,GAAKtB,KAAKyB,UAMV,OAFAzB,KAAKQ,QAAUR,KAAKQ,QAAU,EAEvBgB,EAAYA,aAACF,EADAtB,KAAKS,SAAST,KAAKQ,SAA/BH,QAEV,KAACP,CAAA,CAxGiB,YCkZJ4B,EACdC,EACAC,EACAC,GAEA,IAAMC,EAAUC,OAAOC,KAAKL,KAAgBM,QAAO,SAACC,EAAO/B,GACzD,OAAAgC,EAAAA,EAAA,CAAA,EACKD,GAAK,CAAA,EAAAjC,EAAA,GACPE,GAAM,WAAiB,IAAAiC,EACtB,OAAOA,EAAAT,EAAaC,MAAYzB,GAAakC,MAAAD,EAAAE,UAC9C,IAEJ,GAAE,CAA4C,GAE/C,OAAAH,EAAAA,EAAA,CAAA,EACKL,GAAO,CAAA,EAAA,CACVD,QAAS,CACPN,QAAS,WAAA,OAAMM,EAAQN,SAAS,EAChCE,QAAS,WAAA,OAAMI,EAAQJ,SAAS,IAGtC,CA3aAc,EAAAA,eACAC,EAAAA,gBA0aC,IC9YWC,EDgZNC,EAAO,WAIX,SAAAA,EAAYd,GAAQ7B,EAAAC,KAAA0C,GAAAzC,EAAAD,KAAA,gBAAA,GAAAC,EAAAD,KAAA,cAFQ,IAG1BA,KAAK4B,SAAWA,CAClB,CA6BC,OA3BD1B,EAAAwC,EAAA,CAAA,CAAAvC,IAAA,YAAAC,MAIA,SACEuC,EACAC,EACAC,GAAyB,IAAAC,EAAA9C,KAEnB+C,EAAa,IAAIC,GACrB,WAAA,OAAML,EAAUG,EAAKlB,cACrBgB,EACAC,GAGF,OADA7C,KAAKiD,YAAYC,KAAKH,GACf/C,KAAKmD,YAAYC,KAAKpD,KAAM+C,EACrC,GAAC,CAAA5C,IAAA,cAAAC,MAED,SAAY2C,GACV,GAAI/C,KAAKiD,YAAY1C,OAAQ,CAC3B,IAAM8C,EAAQrD,KAAKiD,YAAYK,QAAQP,GACvC,GAAIM,GAAS,EAAG,OAAOrD,KAAKiD,YAAYM,OAAOF,EAAO,EACxD,CACF,GAAC,CAAAlD,IAAA,SAAAC,MAED,WACEJ,KAAKiD,YAAYO,SAAQ,SAACT,GAAU,OAAKA,EAAWU,YACtD,KAACf,CAAA,CAnCU,GAsCPM,EAAU,WAYd,SAAYL,EAAAA,EAAWC,GAAiC,IAAvBC,0DAAuB9C,EAAAC,KAAAgD,GAAA/C,EAAAD,KAAA,iBAAA,GAAAC,EAAAD,KAAA,iBAAA,GAAAC,EAAAD,KAAA,gBAAA,GAAAC,EAAAD,KAAA,UAAA,GACtDA,KAAK2C,UAAYA,EACjB3C,KAAK4C,SAAWA,EAGZC,GAAiB7C,KAAKyD,SAC5B,CAaC,OAbAvD,EAAA8C,EAAA,CAAA,CAAA7C,IAAA,UAAAC,MAED,WACE,IACE,IAAMsD,EAAY1D,KAAK2C,YAClBgB,EAAAA,QAAYD,EAAW1D,KAAK4D,aAC/B5D,KAAK4D,UAAYF,EACb1D,KAAK4C,UAAU5C,KAAK4C,SAAS5C,KAAK4D,WAK1C,CAHE,MAAOC,GAEPC,QAAQC,KAAKF,EACf,CACF,KAACb,CAAA,CA/Ba,GErdHgB,EAAc,WAAkB,OAAKC,EAAAA,8DAAP,GAAmB,ECDjDC,EAAiB,WAAA,SAAAA,IAAAnE,EAAAC,KAAAkE,GAAAjE,EAAAD,KAAA,aACC,GAAIC,EAEoBD,KAAA,eAAA,IAAImE,SAASlE,EACbD,KAAA,WAAA,IAAIoE,IAAK,CA0G7D,OA1G6DlE,EAAAgE,EAAA,CAAA,CAAA/D,IAAA,eAAAC,MAEtD,SAAaiE,GACnB,IAAMC,EAAatE,KAAKuE,aAAaC,IAAIH,GACzC,GAAIC,EACF,OAAOA,EAGT,IAAMG,EAAQT,IAGd,OADAhE,KAAKuE,aAAaG,IAAIL,EAASI,GACxBA,CACT,GAAC,CAAAtE,IAAA,iBAAAC,MAED,SAAeiE,EAAsBM,GACnC,IAAMC,EAAY5E,KAAK6E,aAAaR,GACpC,MAAUM,GAAAA,OAAAA,eAAkBC,EAC9B,GAAC,CAAAzE,IAAA,WAAAC,MAED,SAASiE,EAAsBS,GAAqC,IAAAhC,EAAA9C,KAC5D+E,EAAoB/E,KAAKgF,aAAaX,EAASS,EAAiBG,MAEtE,GAAIF,EAAmB,CACrB,GAAIG,EAAO,QAACJ,EAAiBK,SAAUJ,EAAkBI,UACvD,OAAOJ,EAGT/E,KAAKgF,aAAaX,EAASS,EAAiBG,MAAMG,SACpD,CAEA,IAAIC,EAA6B,KAE3BC,EAAKtF,KAAKuF,eAAelB,EAASS,EAAiBG,MA+BzD,OA9BAjF,KAAKwF,SAASd,IAAIY,EAAI,CACpBA,GAAAA,EACAH,SAAUL,EAAiBK,SAC3BM,OAAQ,WACFJ,GACFA,IAGFA,EAAUP,EAAiBY,UACzBrB,EACAS,EAAiBK,SACjBL,EAAiBa,QAEpB,EACDP,QAAS,WACFC,GAILA,GACD,EACDO,OAAQ,WACN,OAAO9C,EAAK8C,OAAON,EACrB,IAGEtF,KAAK6F,WACP7F,KAAKwF,SAAShB,IAAIc,GAAIG,SAGjBzF,KAAKwF,SAAShB,IAAIc,EAC3B,GAAC,CAAAnF,IAAA,MAAAC,MAED,SAAIkF,GACF,OAAOtF,KAAKwF,SAAShB,IAAIc,EAC3B,GAAC,CAAAnF,IAAA,SAAAC,MAED,SAAOkF,GACL,IAAMI,EAAY1F,KAAKwE,IAAIc,GACtBI,IAILA,EAAUN,UACVpF,KAAKwF,SAASM,OAAOJ,EAAUJ,IACjC,GAAC,CAAAnF,IAAA,SAAAC,MAED,WACEJ,KAAK6F,WAAY,EACjB7F,KAAKwF,SAAShC,SAAQ,SAACuC,GACrBA,EAAWN,QACb,GACF,GAAC,CAAAtF,IAAA,UAAAC,MAED,WACEJ,KAAK6F,WAAY,EACjB7F,KAAKwF,SAAShC,SAAQ,SAACuC,GACrBA,EAAWX,SACb,GACF,GAAC,CAAAjF,IAAA,eAAAC,MAED,SAAaiE,EAAsBM,GACjC,OAAO3E,KAAKwE,IAAIxE,KAAKuF,eAAelB,EAASM,GAC/C,GAAC,CAAAxE,IAAA,kBAAAC,MAED,SAAgBiE,EAAsBM,GACpC,OAAO3E,KAAK4F,OAAO5F,KAAKuF,eAAelB,EAASM,GAClD,GAAC,CAAAxE,IAAA,QAAAC,MAED,WACEJ,KAAKoF,UACLpF,KAAKuE,aAAe,IAAIJ,QACxBnE,KAAKwF,SAAW,IAAIpB,GACtB,KAACF,CAAA,CA9G2B,GFgClBzB,QAGXA,yBAAA,GAHWA,EAAAA,QAAmBA,sBAAnBA,4BAGX,CAAA,IAFCA,EAAA,gBAAA,GAAA,kBACAA,EAAAA,EAAA,eAAA,GAAA,iBGhCF,IAAsBuD,EAAa,WASjC,SAAAA,EAAYL,GAAW5F,EAAAC,KAAAgG,GAAA/F,EAAAD,KAAA,eAAA,GAAAC,EANeD,KAAA,WAAA,IAAIkE,GAAmBjE,EACED,KAAA,cAAA,IAAIiG,KAMjEjG,KAAK2F,QAAUA,CACjB,CA0LC,OA1LAzF,EAAA8F,EAAA,CAAA,CAAA7F,IAAA,SAAAC,MAED,SAAO8F,GAAsC,IAAApD,EAAA9C,KAE3C,OADAA,KAAKiD,YAAY7B,IAAI8E,GACd,WAAA,OAAMpD,EAAKG,YAAY6C,OAAOI,EAAG,CAC1C,GAAC,CAAA/F,IAAA,UAAAC,MAED,WACMJ,KAAKmG,WACPnG,KAAKmG,YAGPnG,KAAKwF,SAASJ,UAEdpF,KAAKiD,YAAYO,SAAQ,SAAC4C,GACxBA,EAAS3D,QAAAA,oBAAoB4D,gBAC/B,GACF,GAAC,CAAAlG,IAAA,SAAAC,MAED,WACMJ,KAAKsG,UACPtG,KAAKsG,WAGPtG,KAAKwF,SAASC,SAEdzF,KAAKiD,YAAYO,SAAQ,SAAC4C,GACxBA,EAAS3D,QAAAA,oBAAoB8D,eAC/B,GACF,GAAC,CAAApG,IAAA,UAAAC,MAED,WACEJ,KAAKoF,UACLpF,KAAKiD,YAAYuD,QACjBxG,KAAKwF,SAASgB,OAChB,GAAC,CAAArG,IAAA,wBAAAC,MAED,SACEqG,EACAC,EACAN,EACAT,GAEA,IAAMgB,EAAiB,SAACC,aC3D1BA,EACAF,EACAD,GAGKG,EAAEC,QACLD,EAAEC,MAAQ,CACRC,gBAAiB,WAAQ,EACzBC,cAAe,CAAE,IAMrB,IAFA,IAAMC,EAAoBJ,EAAEC,OAASD,EAAEC,MAAME,cAAcL,IAAe,GAEjEO,EAAI,EAAGA,EAAID,EAAiBzG,OAAQ0G,IAAK,CAChD,IAAMC,EAAkBF,EAAiBC,GAEzC,GAAIR,IAAOS,GAAmBT,EAAGU,SAASD,GACxC,OAAO,CAEX,CAEA,OAAO,CACT,EDqCWE,CAA2BR,EAAGF,EAAWD,KAC5CG,EAAEC,MAAMC,gBAAkB,WACnBF,EAAEC,MAAME,cAAcL,KACzBE,EAAEC,MAAME,cAAcL,GAAa,IAGrCE,EAAEC,MAAME,cAAcL,GAAWxD,KAAKuD,IAGxCL,EAASQ,KAMb,OAFAH,EAAGY,iBAAiBX,EAAWC,EAAgBhB,GAExC,WAAA,OAAMc,EAAGa,oBAAoBZ,EAAWC,EAAgBhB,EAAQ,CACzE,GAKA,CAAAxF,IAAA,wBAAAC,MAGA,WAAqB,IAAAmH,EAAAvH,KACbwH,EAAWxH,KAAKwH,WAKhBC,EAAkC,IAAIxB,IAExCyB,GAAwB,EACtBC,EAGF,IAAIvD,IAyCR,MAAO,CACL2B,WAxCiBhE,OAAO6F,QAAQJ,GAAUvF,QAG1C,SAACC,EAAK2F,GAAA,IAAAC,EAAAC,EAAAF,EAAA,GAAG5C,EAAI6C,EAAA,GAAEE,EAAOF,EAAA,GAAA,OACjB5F,EAAAA,EAAAA,GAAAA,GACF+C,CAAAA,EAAAA,EAAAA,CAAAA,EAAAA,GAAO,SAACwB,EAAItB,EAAUQ,GACrB,IAAMsC,EAAoB,WACxB,IAAMvC,EAAY6B,EAAK/B,SAAS0C,SAASzB,EAAI,CAC3CtB,SAAAA,EACAF,KAAAA,EACAU,QAAAA,EACAD,UAAWsC,IAIb,OADAP,EAAmBrG,IAAIsE,EAAUJ,IAC1BI,GAkBT,OAfAiC,EAAqBjD,IACnB6C,EAAK/B,SAASD,eAAekB,EAAIxB,GACjCgD,GASEP,GACFO,IAGKxB,CACR,IACD,GACF,CAAE,GAKFyB,SAAU,WACRR,GAAwB,EAExBC,EAAqBnE,SAAQ,SAACyE,GAC5BA,GACF,GACD,EACD5C,QAAS,WACPqC,GAAwB,EAExBD,EAAmBjE,SAAQ,SAAC2E,GAAW,OACrCZ,EAAK/B,SAASI,OAAOuC,KAEzB,EAEJ,GAAC,CAAAhI,IAAA,SAAAC,MAED,SACEgI,EAGAC,GAEA,OAAO,IAAID,EAAKpI,KAAMqI,EACxB,GAEA,CAAAlI,IAAA,sBAAAC,MACU,SACRkI,EACApC,GAEA,IAAMqC,EAAsB,GACtBf,EAAWc,EAASd,WAEpBgB,EAAkB,IAAIC,MAAMjB,EAAU,CAC1ChD,IAAK,SAACkE,EAAQvI,EAAUwI,GACtB,OAAIxI,KAAOqH,GAAa,EACfoB,QAAQpE,IAAIkE,EAAQvI,EAAKwI,GAG3B,SAAClC,GAAe,IAAA,IAAAoC,EAAAvG,UAAA/B,OAARuI,EAAI,IAAAC,MAAAF,EAAA,EAAAA,EAAA,EAAA,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAJF,EAAIE,EAAA,GAAA1G,UAAA0G,GACjB,IAAM3D,EAAUmC,EAASrH,GAATqH,MAAAA,EAAcf,CAAAA,GAAOqC,OAAAA,IAChCzD,GAILkD,EAAoBrF,KAAKmC,GAE7B,IAKF,OAFAa,EAAGsC,GAEI,WACLD,EAAoB/E,SAAQ,SAAC6B,GAC3BA,GACF,IAEJ,GAEA,CAAAlF,IAAA,UAAAC,MACA,SAAQ8F,GACN,OAAOlG,KAAKiJ,oBAAoBjJ,KAAMkG,EACxC,KAACF,CAAA,CArMgC,GEPbkD,EAGpB,SAAAC,yRAAAC,CAAAF,EAAQlD,GAAR,QAAAqD,KAAAH,kkBAIA,SAAYI,EAAAA,EAAY3D,GAAW,IAAA7C,EAkB9B,OAlB8B/C,EAAAC,KAAAkJ,GAClBjJ,EAAAsJ,EAAfzG,EAAAuG,EAAAG,KAAAxJ,KAAM2F,IAAS,eAAA,GAAA1F,EAAAsJ,EAAAzG,GAAA,wCAAA,GACfA,EAAKwG,QAAUA,EACfxG,EAAK6C,QAAUA,EAGf7C,EAAK2G,iCAAmC3G,EAAKwG,QAAQI,QAAO,SAACC,GAC3D,OAAQA,GACN,KAAKlH,QAAmBA,oBAAC8D,eACvB,OAAOzD,EAAK2C,SAEd,KAAKhD,QAAmBA,oBAAC4D,gBACvB,OAAOvD,EAAKsC,UAEd,QACE,OAGN,IAAGtC,CACL,CAUC,OARD5C,EAAAgJ,EAAA,CAAA,CAAA/I,IAAA,UAAAC,MACA,SAAQ8F,GACN,OAAOlG,KAAKiJ,oBAAoBjJ,KAAKsJ,QAASpD,EAChD,GAAC,CAAA/F,IAAA,UAAAC,MAED,WACEwJ,EAAAC,EAAAX,EAAAY,WAAA,UAAA9J,MAAAwJ,KAAAxJ,MACAA,KAAKyJ,kCACP,KAACP,CAAA,CAjCD,GCDF,SAASa,EAAOC,EAAUC,GACpBA,IACiB,mBAARD,EACTA,EAAIC,GAEJD,EAAIE,QAAUD,EAGpB,CAEgB,SAAAE,EACd9F,EACA+F,GAEA,MAAMC,EAAchG,EAAQ2F,IAQ5B,OAPAM,UACyB,iBAAhBD,EACP,kPAWOE,EAAAA,aAAalG,EANjBgG,EAM0B,CAC3BL,IAAMC,IACJF,EAAOM,EAAaJ,GACpBF,EAAOK,EAAQH,EAAK,GAPK,CAC3BD,IAAKI,GAUX,CAUM,SAAUI,EACdC,GAEA,MAAO,CAACC,EAAgB,QAAS5B,KAE/B,IAAK6B,EAAAA,eAAeD,GAAgB,CAClC,IAAKA,EACH,OAGF,MAAMT,EAAOS,EAEb,OADAT,GAAQQ,EAAKR,KAASnB,GACfmB,CACR,CAKD,MAAM5F,EAA+BqG,EAGrC,OA7BJ,SAA0CrG,GACxC,GAA4B,iBAAjBA,EAAQ+D,KAInB,MAAM,IAAIwC,KACZ,CAqBIC,CAAiCxG,GAE1B8F,EAAa9F,EAASoG,EAAK,CAEtC,CCtEO,ICLMK,EAAe,WAAH,MAA2B,oBAAXC,MAAsB,+BCC3B,uEA8BlC,kHAGA,4FAUsD,2SAtCtD,4EAuBA,0IArBA,8EAwBmC,kFApBnC,0GAEA,+EACwC,yEAIxC,kGAFA,oEAMkC,8DADK,oEAGD,4EAJtC,mEAjB4B,4DAuB5B,+HAWsD,4NA5BtD,iLAsCqF,uMAIF,wOApD5D,+BCSM,EAC7BC,QACAC,YACAC,gBAEA,MAAMC,EACJC,EAAAA,QAAAC,cAAA,MAAA,CACEJ,UAAWA,EACXD,MAAO,CACLM,SAAU,QACVC,QAAS,QACTC,QAAS,EACTC,YAAa,QACbC,YAAa,MACbC,YAAa,cACbC,OAAQ,SACLZ,KAKT,OAAIE,GAAaA,EAAUW,gBAAkBC,SACpCC,EAAAA,QAASC,aAAab,EAAWD,EAAUW,cAAcI,MAG3Dd,CAAS,0EH7BgB,SAAClG,EAAMiH,GACvC,IAAIC,EAAkClH,wBAAAA,OAAAA,EAA4C,0CAE1EmH,EAAiBF,EAAjBE,QAASC,EAAQH,EAARG,IAEbD,IACFD,GAAO,eAAAjL,OAAmBkL,EAAkB,cAI1CC,IACFF,GAAO,IAAAjL,OAAQmL,EAAM,MAIvBvI,QAAQC,KAAKoI,EACf,qBIrB0B,SAAC1F,GACzB,IASIA,EAAAA,EAAG6F,wBARLC,IAAAA,EACAC,IAAAA,EACAC,IAAAA,IACAC,IAAAA,KACAC,IAAAA,OACAC,IAAAA,MACAC,IAAAA,MACAC,IAAAA,OAGI9B,EAAQD,OAAOgC,iBAAiBtG,GAEhCuG,EAAS,CACbN,KAAMO,SAASjC,EAAMkC,YACrBN,MAAOK,SAASjC,EAAMmC,aACtBR,OAAQM,SAASjC,EAAMoC,cACvBX,IAAKQ,SAASjC,EAAMqC,YAGhBC,EAAU,CACdZ,KAAMO,SAASjC,EAAMuC,aACrBX,MAAOK,SAASjC,EAAMwC,cACtBb,OAAQM,SAASjC,EAAMyC,eACvBhB,IAAKQ,SAASjC,EAAM0C,aAqDtB,MAAO,CACLnB,EAAAA,EACAC,EAAAA,EACAC,IAAAA,EACAC,KAAAA,EACAC,OAAAA,EACAC,MAAAA,EACAC,MAAAA,EACAC,OAAAA,EACAa,WAAYC,KAAKC,MAAMhB,EAAQG,EAAON,KAAOM,EAAOJ,OACpDkB,YAAaF,KAAKC,MAAMf,EAASE,EAAOP,IAAMO,EAAOL,QACrDK,OAAAA,EACAM,QAAAA,EACAS,OAAQtH,EAAGuH,iBA/DO,SAACC,GACnB,IAAMC,EAAmBnB,iBAAiBkB,GAE1C,KAAIjD,EAAMmD,UAA+B,YAAnBnD,EAAMmD,UAIF,SAAtBD,EAAYE,OAIY,SAAxBF,EAAY3C,SAKU,SAAxB2C,EAAY3C,SACsB,WAAlC2C,EAAY,mBAFd,CAOA,OAAQlD,EAAMM,UACZ,IAAK,SACL,IAAK,WACH,MACF,QACE,OAGJ,OAAQ7E,EAAG4H,SACT,IAAK,KACL,IAAK,QACL,IAAK,QACL,IAAK,QACH,OAAO,EAGX,OAAQrD,EAAMO,SACZ,IAAK,QACL,IAAK,YACL,IAAK,QACL,IAAK,OACL,IAAK,OACH,OAAO,EAxBX,EA2C8B+C,CAAY7H,EAAGuH,eAEjD,2CHxF0B,WAAH,OACrBlD,KAAkB,UAAUyD,KAAKxD,OAAOyD,UAAUC,UAAU,yCAJvC,WAAH,OAClB3D,KAAkB,SAASyD,KAAKxD,OAAOyD,UAAUC,UAAU,uBIW7C,SACdC,EACA/L,GAKA,MAAMgM,UAAEA,EAAS/M,SAAEA,EAAQgN,QAAEA,EAAOC,MAAEA,GAAUH,EAE1CI,EAAUC,UAAO,GACjBnL,EAAYmL,SAAY,MACxBC,EAAeD,SAAOpM,GAC5BqM,EAAa9E,QAAUvH,EAEvB,MAAMsM,EAAYC,eACftL,IACQ,IAAKA,EAAWgL,UAASC,WAElC,CAACD,EAASC,IAIRC,EAAQ5E,SAAWvH,IACrBiB,EAAUsG,QAAUvH,EAAUf,IAAYiN,GAC1CC,EAAQ5E,SAAU,GAGpB,MAAOiF,EAAiBC,GAAsBC,EAAAA,SAC5CJ,EAAUrL,EAAUsG,UAmBtB,OAfAoF,EAAAA,WAAU,KACR,IAAInM,EASJ,OARI6L,EAAa9E,UACf/G,EAAcwL,GACXzE,GAAY8E,EAAa9E,QAAQA,EAAS2E,KAC1CjL,IACCwL,EAAmBH,EAAUrL,GAAW,KAIvC,KACDT,GAAaA,GAAa,CAC/B,GACA,CAAC8L,EAAWJ,EAAOF,IAEfQ,CACT,wBC5D8BI,IAE5BD,YAAUC,EAAQ,GAAG,qBdoLjB,SAKJC,EACAC,EACA9N,EACA+N,GAEA,IAEIC,EAFE9N,EAAU+N,EAAAA,SAAQ,WAAA,OAAM,IAAI9P,CAAS,GAAE,IAGzC+P,EAA6Bd,SAAO,IACpCe,EAAsBf,EAAAA,QAAY,WAAK,IAEX,mBAArBS,EACTG,EAAiBH,GAEjBG,EAAiBH,EAAiBO,QAClCF,EAA2B3F,QAAUsF,EAAiBQ,wBACtDF,EAAoB5F,QAAUsF,EAAiBS,kBAGjD,IAAMC,EAAmBnB,SAAOW,GAChCQ,EAAiBhG,QAAUwF,EAE3B,IAAMS,EAAWpB,SAAOU,GAElBW,EAAUR,EAAAA,SAAQ,WACtB,IAAiBK,EAAqBH,EAA9B5F,QACS8F,EAA4BH,EAArC3F,QACSwF,EAAkBQ,EAA3BhG,QAER,OAAO,SAAC5I,EAAU+O,GAChB,IAGIC,EAHEzB,EACJlN,GAAgBD,EAAYC,GAAc,WAAA,OAAML,CAAK,GAAEO,GAgCxD0O,EAAAxI,EA7B2CyI,EAAAA,mBAC1ClP,GACA,SAACmP,GAAY,IAAAC,IACX,OAAQL,EAAOjI,MACb,KAAK7I,EAAgBC,KACnB,OAAOqC,EAAQ8O,KAAKF,GAEtB,KAAKlR,EAAgBE,KACnB,OAAOoC,EAAQ+O,KAAKH,GAEtB,KAAKlR,EAAgBM,MAEnB,OADAgC,EAAQ2E,QACRrE,EAAA,GACKsO,GAKP,KAAKlR,EAAgBI,OACrB,KAAKJ,EAAgBK,MACrB,KAAKL,EAAgBG,SAAU,IAAAmR,EACHR,MAAAA,EAAOnE,0BAA1B9D,EAAI0I,EAAA,GAAKC,EAAMD,EAAAE,MAAA,IACtBrB,EAAAA,EAAec,EAAO5B,IAAOzG,GAAS2I,MAAAA,EAAAA,EAAAA,IACtC,MAEF,SACEL,EAAAf,EAAec,EAAO5B,IAAOwB,EAAOjI,MAAK/F,MAAAqO,EAAAvP,EAAIkP,EAAOnE,UAE1D,IACD,GA7BI+E,EAASV,EAAA,GAAElQ,EAAOkQ,EAAA,GAAEjQ,EAAciQ,EAAA,GAgFvC,OAjDAD,EAAaW,EAETvB,GACFA,EACEuB,EACA3P,EACA,CAAE8G,KAAMiI,EAAOjI,KAAM2I,OAAQV,EAAOnE,QAAS7L,QAAAA,GAC7CwO,GACA,SAAC3I,GACC,IAAIgL,EAAkBV,EAAAA,mBAAmBS,EAAW/K,GACpDoK,EAAaY,EAAgB,GAE7B7Q,cAAcA,GAAOc,EAAK+P,EAAgB,KAC1C5Q,cAAqB4Q,EAAgB,IAAE/P,EAAKb,GAC9C,IAKF,CAACf,EAAgBC,KAAMD,EAAgBE,MAAM0R,SAC3Cd,EAAOjI,OAET6H,IAEAK,EAAac,EAAO,QAACd,EAAYL,IAIhC,GACID,OAAAA,EAAAA,GACHzQ,CAAAA,EAAgBC,KAChBD,EAAgBE,KAChBF,EAAgBI,OAChBJ,EAAgBM,QAChBsR,SAASd,EAAOjI,QAEdiI,EAAOjI,OAAS7I,EAAgBG,SAClCmC,EAAQwP,YACNhR,EACAC,EACA+P,EAAOiB,QAAUjB,EAAOiB,OAAOC,MAExBlB,EAAOjI,OAAS7I,EAAgBK,MACzCiC,EAAQ2P,MAAMnR,EAASC,GAEvBuB,EAAQT,IAAIf,EAASC,IAIlBgQ,EAEV,GAAE,CAACzO,EAAS8N,EAAgBhO,IAEvBC,EAAWsN,EAAAA,aAAY,WAAA,OAAMiB,EAASjG,OAAO,GAAE,IAC/CuH,EAAU7B,EAAAA,SAAQ,WAAA,OAAM,IAAIlN,EAAWd,KAAW,CAACA,IAEnD8P,EAAWxC,eACf,SAACmB,GACC,IAAMsB,EAAWvB,EAAQD,EAASjG,QAASmG,GAC3CF,EAASjG,QAAUyH,EACnBF,EAAQG,QACV,GACA,CAACxB,EAASqB,IAGZnC,EAAAA,WAAU,WACRmC,EAAQG,QACV,GAAG,CAACH,IAEJ,IAAM5C,EAAQe,EAAAA,SACZ,WAAA,OACGjO,EAEGD,EAAYC,GAAc,WAAA,OAAMwO,EAASjG,OAAO,GAAErI,GADlD,EAC0D,GAChE,CAACA,EAASF,IAGNiN,EAAUgB,EAAAA,SAAQ,WACtB,IAAMiC,EAAc9P,OAAOC,KAAK2N,EAAe,KAAM,OAEpCK,EAA4BH,EAArC3F,QAER,OACK2H,EAAAA,EAAAA,CAAAA,EAAAA,EAAY5P,QAAO,SAACC,EAAOkG,GAE5B,OADAlG,EAAMkG,GAAQ,WAAA,IAAA,IAAAS,EAAAvG,UAAA/B,OAAI2L,EAAO,IAAAnD,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAPkD,EAAOlD,GAAA1G,UAAA0G,GAAA,OAAK0I,EAAS,CAAEtJ,KAAAA,EAAM8D,QAAAA,GAAU,EAClDhK,IACN,CAAS,IAAC,GAAA,CACbL,QAAS,CACP8O,KAAI,WACF,OAAOe,EAAS,CACdtJ,KAAM7I,EAAgBC,MAEzB,EACDoR,KAAI,WACF,OAAOc,EAAS,CACdtJ,KAAM7I,EAAgBE,MAEzB,EACD+G,MAAO,WACL,OAAOkL,EAAS,CACdtJ,KAAM7I,EAAgBM,OAEzB,EACDiS,SAAU,SAACP,GACT,OAAApP,EAAA,CAAA,EACK0P,EACAE,QAAO,SAAC3J,GAAI,OAAM4H,EAAwBmB,SAAS/I,EAAK,IACxDnG,QAAO,SAACC,EAAOkG,GASd,OARAlG,EAAMkG,GAAQ,WAAA,IAAA,IAAA4J,EAAA1P,UAAA/B,OAAI2L,EAAO,IAAAnD,MAAAiJ,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAP/F,EAAO+F,GAAA3P,UAAA2P,GAAA,OACvBP,EAAS,CACPtJ,KAAM7I,EAAgBG,SACtBwM,QAAU9D,CAAAA,GAAS8D,OAAAA,GACnBoF,OAAQ,CACNC,KAAMA,IAER,EACGrP,IACN,CAAA,GAER,EACDgQ,OAAQ,WACN,OAAA/P,EAAA,CAAA,EACK0P,EACAE,QAAO,SAAC3J,GAAI,OAAM4H,EAAwBmB,SAAS/I,EAAK,IACxDnG,QAAO,SAACC,EAAOkG,GAMd,OALAlG,EAAMkG,GAAQ,WAAA,IAAA,IAAA+J,EAAA7P,UAAA/B,OAAI2L,EAAO,IAAAnD,MAAAoJ,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAPlG,EAAOkG,GAAA9P,UAAA8P,GAAA,OACvBV,EAAS,CACPtJ,KAAM7I,EAAgBI,OACtBuM,QAAO,CAAG9D,GAAIlH,OAAKgL,IACnB,EACGhK,IACN,CAAA,GAER,EACDsP,MAAO,WACL,OAAArP,EAAA,CAAA,EACK0P,EACAE,QAAO,SAAC3J,GAAI,OAAM4H,EAAwBmB,SAAS/I,EAAK,IACxDnG,QAAO,SAACC,EAAOkG,GAMd,OALAlG,EAAMkG,GAAQ,WAAA,IAAA,IAAAiK,EAAA/P,UAAA/B,OAAI2L,EAAO,IAAAnD,MAAAsJ,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAPpG,EAAOoG,GAAAhQ,UAAAgQ,GAAA,OACvBZ,EAAS,CACPtJ,KAAM7I,EAAgBK,MACtBsM,QAAO,CAAG9D,GAAIlH,OAAKgL,IACnB,EACGhK,IACN,CAAA,GAET,IAGN,GAAG,CAACwP,EAAU/B,IAEd,OAAOC,EAAOA,SACZ,WAAA,MAAO,CACLhO,SAAAA,EACA+M,UAAW,SAAChM,EAAWuD,EAAIrD,GAAe,OACxC4O,EAAQ9C,UAAUhM,EAAWuD,EAAIrD,EAAgB,EACnD+L,QAAAA,EACAC,MAAAA,EACAhN,QAAAA,EACD,GACD,CAAC+M,EAASC,EAAO4C,EAAS7P,EAAUC,GAExC,6BOlVM,SACJkE,GAEA,OAAOhE,OAAOC,KAAK+D,GAAY9D,QAAO,CAACC,EAAO/B,KAC5C+B,EAAM/B,GAAOqK,GAA2B,IAAI1B,IAEnC/C,EAAW5F,MAAQ2I,KAGrB5G,IACN,CAAE,EACP"}