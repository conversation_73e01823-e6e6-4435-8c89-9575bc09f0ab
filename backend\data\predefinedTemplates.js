// Pre-defined templates like Builder.io template library
const predefinedTemplates = [
  {
    name: "Classic Professional",
    category: "Professional",
    preview: "/templates/classic-professional.png",
    template_data: {
      width: 400,
      height: 250,
      backgroundColor: "#ffffff",
      elements: [
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 0,
          width: 400,
          height: 60,
          fillColor: "#1e40af",
          strokeColor: "transparent",
          strokeWidth: 0
        },
        {
          type: "text",
          content: "STUDENT ID CARD",
          x: 200,
          y: 35,
          fontSize: 20,
          fontWeight: "bold",
          color: "#ffffff",
          textAlign: "center"
        },
        {
          type: "image",
          x: 30,
          y: 80,
          width: 80,
          height: 100,
          placeholder: "student_photo"
        },
        {
          type: "text",
          content: "{first_name} {last_name}",
          x: 130,
          y: 100,
          fontSize: 18,
          fontWeight: "bold",
          color: "#1e40af"
        },
        {
          type: "text",
          content: "ID: {student_id}",
          x: 130,
          y: 125,
          fontSize: 14,
          color: "#374151"
        },
        {
          type: "text",
          content: "{course}",
          x: 130,
          y: 145,
          fontSize: 12,
          color: "#6b7280"
        },
        {
          type: "text",
          content: "Year {year_of_study}",
          x: 130,
          y: 165,
          fontSize: 12,
          color: "#6b7280"
        },
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 210,
          width: 400,
          height: 40,
          fillColor: "#f3f4f6",
          strokeColor: "transparent",
          strokeWidth: 0
        },
        {
          type: "text",
          content: "University Name",
          x: 200,
          y: 235,
          fontSize: 14,
          fontWeight: "bold",
          color: "#374151",
          textAlign: "center"
        }
      ]
    }
  },
  {
    name: "Modern Gradient",
    category: "Modern",
    preview: "/templates/modern-gradient.png",
    template_data: {
      width: 400,
      height: 250,
      backgroundColor: "#ffffff",
      elements: [
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 0,
          width: 400,
          height: 250,
          fillColor: "#f8fafc",
          strokeColor: "#e2e8f0",
          strokeWidth: 2
        },
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 0,
          width: 400,
          height: 80,
          fillColor: "#7c3aed",
          strokeColor: "transparent",
          strokeWidth: 0
        },
        {
          type: "text",
          content: "STUDENT CARD",
          x: 200,
          y: 45,
          fontSize: 22,
          fontWeight: "bold",
          color: "#ffffff",
          textAlign: "center"
        },
        {
          type: "shape",
          shapeType: "circle",
          x: 40,
          y: 100,
          width: 80,
          height: 80,
          fillColor: "#ffffff",
          strokeColor: "#e2e8f0",
          strokeWidth: 3
        },
        {
          type: "image",
          x: 45,
          y: 105,
          width: 70,
          height: 70,
          placeholder: "student_photo"
        },
        {
          type: "text",
          content: "{first_name} {last_name}",
          x: 150,
          y: 115,
          fontSize: 20,
          fontWeight: "bold",
          color: "#1f2937"
        },
        {
          type: "text",
          content: "Student ID: {student_id}",
          x: 150,
          y: 140,
          fontSize: 12,
          color: "#7c3aed"
        },
        {
          type: "text",
          content: "{course}",
          x: 150,
          y: 160,
          fontSize: 14,
          fontWeight: "500",
          color: "#374151"
        },
        {
          type: "text",
          content: "Academic Year {year_of_study}",
          x: 150,
          y: 180,
          fontSize: 12,
          color: "#6b7280"
        },
        {
          type: "text",
          content: "Valid for Academic Session 2024-25",
          x: 200,
          y: 220,
          fontSize: 10,
          color: "#9ca3af",
          textAlign: "center"
        }
      ]
    }
  },
  {
    name: "Minimalist Clean",
    category: "Minimalist",
    preview: "/templates/minimalist-clean.png",
    template_data: {
      width: 400,
      height: 250,
      backgroundColor: "#ffffff",
      elements: [
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 0,
          width: 400,
          height: 250,
          fillColor: "#ffffff",
          strokeColor: "#d1d5db",
          strokeWidth: 1
        },
        {
          type: "shape",
          shapeType: "line",
          x: 0,
          y: 50,
          width: 400,
          height: 0,
          fillColor: "transparent",
          strokeColor: "#10b981",
          strokeWidth: 3
        },
        {
          type: "text",
          content: "STUDENT IDENTIFICATION",
          x: 200,
          y: 30,
          fontSize: 16,
          fontWeight: "600",
          color: "#374151",
          textAlign: "center"
        },
        {
          type: "image",
          x: 50,
          y: 80,
          width: 70,
          height: 90,
          placeholder: "student_photo"
        },
        {
          type: "text",
          content: "{first_name} {last_name}",
          x: 150,
          y: 100,
          fontSize: 18,
          fontWeight: "600",
          color: "#111827"
        },
        {
          type: "text",
          content: "{student_id}",
          x: 150,
          y: 125,
          fontSize: 16,
          fontWeight: "500",
          color: "#10b981"
        },
        {
          type: "text",
          content: "{course}",
          x: 150,
          y: 150,
          fontSize: 13,
          color: "#6b7280"
        },
        {
          type: "text",
          content: "Year {year_of_study} Student",
          x: 150,
          y: 170,
          fontSize: 12,
          color: "#9ca3af"
        },
        {
          type: "text",
          content: "{email}",
          x: 50,
          y: 200,
          fontSize: 10,
          color: "#6b7280"
        },
        {
          type: "text",
          content: "Issued: 2024",
          x: 350,
          y: 230,
          fontSize: 9,
          color: "#d1d5db",
          textAlign: "right"
        }
      ]
    }
  },
  {
    name: "Corporate Blue",
    category: "Corporate",
    preview: "/templates/corporate-blue.png",
    template_data: {
      width: 400,
      height: 250,
      backgroundColor: "#f8fafc",
      elements: [
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 0,
          width: 400,
          height: 70,
          fillColor: "#1e3a8a",
          strokeColor: "transparent",
          strokeWidth: 0
        },
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 70,
          width: 400,
          height: 10,
          fillColor: "#3b82f6",
          strokeColor: "transparent",
          strokeWidth: 0
        },
        {
          type: "text",
          content: "UNIVERSITY STUDENT CARD",
          x: 200,
          y: 45,
          fontSize: 18,
          fontWeight: "bold",
          color: "#ffffff",
          textAlign: "center"
        },
        {
          type: "image",
          x: 30,
          y: 100,
          width: 75,
          height: 95,
          placeholder: "student_photo"
        },
        {
          type: "text",
          content: "{first_name} {last_name}",
          x: 125,
          y: 110,
          fontSize: 16,
          fontWeight: "bold",
          color: "#1e3a8a"
        },
        {
          type: "text",
          content: "Student ID: {student_id}",
          x: 125,
          y: 135,
          fontSize: 12,
          fontWeight: "600",
          color: "#3b82f6"
        },
        {
          type: "text",
          content: "Program: {course}",
          x: 125,
          y: 155,
          fontSize: 11,
          color: "#374151"
        },
        {
          type: "text",
          content: "Year: {year_of_study}",
          x: 125,
          y: 175,
          fontSize: 11,
          color: "#374151"
        },
        {
          type: "text",
          content: "Contact: {email}",
          x: 125,
          y: 195,
          fontSize: 9,
          color: "#6b7280"
        },
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 220,
          width: 400,
          height: 30,
          fillColor: "#1e3a8a",
          strokeColor: "transparent",
          strokeWidth: 0
        },
        {
          type: "text",
          content: "www.university.edu",
          x: 200,
          y: 240,
          fontSize: 12,
          color: "#ffffff",
          textAlign: "center"
        }
      ]
    }
  },
  {
    name: "Creative Colorful",
    category: "Creative",
    preview: "/templates/creative-colorful.png",
    template_data: {
      width: 400,
      height: 250,
      backgroundColor: "#ffffff",
      elements: [
        {
          type: "shape",
          shapeType: "rectangle",
          x: 0,
          y: 0,
          width: 400,
          height: 250,
          fillColor: "#fef3c7",
          strokeColor: "#f59e0b",
          strokeWidth: 3
        },
        {
          type: "shape",
          shapeType: "circle",
          x: 320,
          y: -20,
          width: 100,
          height: 100,
          fillColor: "#f59e0b",
          strokeColor: "transparent",
          strokeWidth: 0
        },
        {
          type: "shape",
          shapeType: "circle",
          x: -30,
          y: 170,
          width: 80,
          height: 80,
          fillColor: "#ef4444",
          strokeColor: "transparent",
          strokeWidth: 0
        },
        {
          type: "text",
          content: "STUDENT ID",
          x: 200,
          y: 40,
          fontSize: 24,
          fontWeight: "bold",
          color: "#dc2626",
          textAlign: "center"
        },
        {
          type: "shape",
          shapeType: "rectangle",
          x: 40,
          y: 70,
          width: 80,
          height: 100,
          fillColor: "#ffffff",
          strokeColor: "#f59e0b",
          strokeWidth: 3
        },
        {
          type: "image",
          x: 45,
          y: 75,
          width: 70,
          height: 90,
          placeholder: "student_photo"
        },
        {
          type: "text",
          content: "{first_name} {last_name}",
          x: 150,
          y: 90,
          fontSize: 18,
          fontWeight: "bold",
          color: "#dc2626"
        },
        {
          type: "text",
          content: "ID: {student_id}",
          x: 150,
          y: 115,
          fontSize: 14,
          fontWeight: "600",
          color: "#f59e0b"
        },
        {
          type: "text",
          content: "{course}",
          x: 150,
          y: 140,
          fontSize: 12,
          fontWeight: "500",
          color: "#374151"
        },
        {
          type: "text",
          content: "Year {year_of_study}",
          x: 150,
          y: 160,
          fontSize: 12,
          color: "#6b7280"
        },
        {
          type: "text",
          content: "🎓 Academic Excellence",
          x: 200,
          y: 200,
          fontSize: 12,
          fontWeight: "500",
          color: "#dc2626",
          textAlign: "center"
        },
        {
          type: "text",
          content: "Valid 2024-2025",
          x: 200,
          y: 220,
          fontSize: 10,
          color: "#9ca3af",
          textAlign: "center"
        }
      ]
    }
  }
];

module.exports = predefinedTemplates;
