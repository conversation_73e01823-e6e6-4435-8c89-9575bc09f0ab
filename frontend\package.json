{"name": "student-management-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@craftjs/core": "^0.2.12", "@craftjs/utils": "^0.2.5", "axios": "^1.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "fabric": "^5.3.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.14.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}