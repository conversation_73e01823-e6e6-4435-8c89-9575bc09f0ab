import React from 'react'
import { useNode } from '@craftjs/core'

export const Container = ({ children, backgroundColor, padding, width, height }) => {
  const { connectors: { connect, drag }, selected, actions: { setProp } } = useNode((state) => ({
    selected: state.events.selected
  }))

  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        backgroundColor,
        padding: `${padding}px`,
        width: `${width}px`,
        height: `${height}px`,
        minHeight: '50px',
        border: selected ? '2px solid #3b82f6' : '1px solid #e5e7eb',
        position: 'relative',
        cursor: 'move',
        display: 'inline-block'
      }}
      className="hover:border-blue-400"
    >
      {children}

      {/* Resize handles when selected */}
      {selected && (
        <>
          {/* Corner resize handles */}
          <div
            style={{
              position: 'absolute',
              bottom: '-4px',
              right: '-4px',
              width: '8px',
              height: '8px',
              backgroundColor: '#3b82f6',
              cursor: 'se-resize',
              border: '1px solid white'
            }}
            onMouseDown={(e) => {
              e.stopPropagation()
              const startX = e.clientX
              const startY = e.clientY
              const startWidth = width
              const startHeight = height

              const handleMouseMove = (e) => {
                const newWidth = Math.max(50, startWidth + (e.clientX - startX))
                const newHeight = Math.max(30, startHeight + (e.clientY - startY))
                setProp((props) => {
                  props.width = newWidth
                  props.height = newHeight
                })
              }

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove)
                document.removeEventListener('mouseup', handleMouseUp)
              }

              document.addEventListener('mousemove', handleMouseMove)
              document.addEventListener('mouseup', handleMouseUp)
            }}
          />

          {/* Right edge resize handle */}
          <div
            style={{
              position: 'absolute',
              top: '50%',
              right: '-4px',
              width: '8px',
              height: '20px',
              backgroundColor: '#3b82f6',
              cursor: 'e-resize',
              transform: 'translateY(-50%)',
              border: '1px solid white'
            }}
            onMouseDown={(e) => {
              e.stopPropagation()
              const startX = e.clientX
              const startWidth = width

              const handleMouseMove = (e) => {
                const newWidth = Math.max(50, startWidth + (e.clientX - startX))
                setProp((props) => props.width = newWidth)
              }

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove)
                document.removeEventListener('mouseup', handleMouseUp)
              }

              document.addEventListener('mousemove', handleMouseMove)
              document.addEventListener('mouseup', handleMouseUp)
            }}
          />

          {/* Bottom edge resize handle */}
          <div
            style={{
              position: 'absolute',
              bottom: '-4px',
              left: '50%',
              width: '20px',
              height: '8px',
              backgroundColor: '#3b82f6',
              cursor: 's-resize',
              transform: 'translateX(-50%)',
              border: '1px solid white'
            }}
            onMouseDown={(e) => {
              e.stopPropagation()
              const startY = e.clientY
              const startHeight = height

              const handleMouseMove = (e) => {
                const newHeight = Math.max(30, startHeight + (e.clientY - startY))
                setProp((props) => props.height = newHeight)
              }

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove)
                document.removeEventListener('mouseup', handleMouseUp)
              }

              document.addEventListener('mousemove', handleMouseMove)
              document.addEventListener('mouseup', handleMouseUp)
            }}
          />
        </>
      )}
    </div>
  )
}

export const ContainerSettings = () => {
  const { actions: { setProp }, props } = useNode((node) => ({
    props: node.data.props
  }))

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-2">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Width (px)
          </label>
          <input
            type="number"
            value={props.width}
            onChange={(e) => setProp((props) => props.width = parseInt(e.target.value))}
            className="input"
            min="50"
            max="800"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Height (px)
          </label>
          <input
            type="number"
            value={props.height}
            onChange={(e) => setProp((props) => props.height = parseInt(e.target.value))}
            className="input"
            min="30"
            max="600"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Background Color
        </label>
        <input
          type="color"
          value={props.backgroundColor}
          onChange={(e) => setProp((props) => props.backgroundColor = e.target.value)}
          className="input"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Padding (px)
        </label>
        <input
          type="number"
          value={props.padding}
          onChange={(e) => setProp((props) => props.padding = parseInt(e.target.value))}
          className="input"
          min="0"
          max="50"
        />
      </div>
    </div>
  )
}

Container.craft = {
  props: {
    backgroundColor: '#ffffff',
    padding: 10,
    width: 400,
    height: 250
  },
  related: {
    settings: ContainerSettings
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true
  }
}
