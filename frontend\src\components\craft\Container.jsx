import React from 'react'
import { useNode } from '@craftjs/core'

export const Container = ({ children, backgroundColor, padding, width, height }) => {
  const { connectors: { connect, drag } } = useNode()
  
  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        backgroundColor,
        padding: `${padding}px`,
        width: `${width}px`,
        height: `${height}px`,
        minHeight: '50px',
        border: '1px solid #e5e7eb',
        position: 'relative'
      }}
      className="hover:border-blue-400"
    >
      {children}
    </div>
  )
}

export const ContainerSettings = () => {
  const { actions: { setProp }, props } = useNode((node) => ({
    props: node.data.props
  }))

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Background Color
        </label>
        <input
          type="color"
          value={props.backgroundColor}
          onChange={(e) => setProp((props) => props.backgroundColor = e.target.value)}
          className="input"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Padding (px)
        </label>
        <input
          type="number"
          value={props.padding}
          onChange={(e) => setProp((props) => props.padding = parseInt(e.target.value))}
          className="input"
          min="0"
          max="50"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Width (px)
        </label>
        <input
          type="number"
          value={props.width}
          onChange={(e) => setProp((props) => props.width = parseInt(e.target.value))}
          className="input"
          min="200"
          max="800"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Height (px)
        </label>
        <input
          type="number"
          value={props.height}
          onChange={(e) => setProp((props) => props.height = parseInt(e.target.value))}
          className="input"
          min="150"
          max="600"
        />
      </div>
    </div>
  )
}

Container.craft = {
  props: {
    backgroundColor: '#ffffff',
    padding: 10,
    width: 400,
    height: 250
  },
  related: {
    settings: ContainerSettings
  },
  rules: {
    canDrag: () => true,
    canDrop: () => true,
    canMoveIn: () => true,
    canMoveOut: () => true
  }
}
