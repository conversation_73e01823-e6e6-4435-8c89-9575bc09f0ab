# Student Management System with ID Card Generator

A comprehensive student management system built with <PERSON>act (frontend) and Node.js (backend) featuring CRUD operations and ID card generation using template builders.

## Project Structure

```
Student Management/
├── frontend/          # React application with Vite
├── backend/           # Node.js/Express server
├── database/          # SQL database scripts
└── README.md
```

## Features

- **Student Management**: Complete CRUD operations for student records
- **ID Card Generation**: Dynamic ID card creation using template builders
- **Template System**: Customizable ID card templates
- **Database Integration**: SQL database for data persistence
- **RESTful API**: Clean API design for frontend-backend communication

## Technology Stack

### Frontend
- React 18
- Vite (build tool)
- Fabric.js (for ID card template building)
- Axios (HTTP client)
- React Router (navigation)
- Tailwind CSS (styling)

### Backend
- Node.js
- Express.js
- SQLite/MySQL (database)
- Multer (file uploads)
- CORS (cross-origin requests)

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```bash
   cd backend
   npm install
   ```

3. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

4. Set up the database:
   ```bash
   cd backend
   npm run setup-db
   ```

5. Start the development servers:
   
   Backend:
   ```bash
   cd backend
   npm run dev
   ```
   
   Frontend:
   ```bash
   cd frontend
   npm run dev
   ```

## API Endpoints

- `GET /api/students` - Get all students
- `POST /api/students` - Create new student
- `GET /api/students/:id` - Get student by ID
- `PUT /api/students/:id` - Update student
- `DELETE /api/students/:id` - Delete student
- `POST /api/students/:id/generate-card` - Generate ID card

## ID Card Templates

The system uses Fabric.js as an alternative to Builder.io for creating customizable ID card templates. Templates support:

- Text elements (name, ID, course, etc.)
- Images (student photo, logo)
- Shapes and backgrounds
- Custom styling and positioning

## License

MIT License
