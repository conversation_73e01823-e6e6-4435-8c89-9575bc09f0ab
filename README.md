# Student Management System with ID Card Generator

A comprehensive student management system built with <PERSON>act (frontend) and Node.js (backend) featuring CRUD operations and ID card generation using template builders.

## Project Structure

```
Student Management/
├── frontend/          # React application with Vite
├── backend/           # Node.js/Express server
├── database/          # SQL database scripts
└── README.md
```

## Features

- **Student Management**: Complete CRUD operations for student records
- **ID Card Generation**: Dynamic ID card creation using template builders
- **Template System**: Customizable ID card templates
- **Database Integration**: SQL database for data persistence
- **RESTful API**: Clean API design for frontend-backend communication

## Technology Stack

### Frontend
- React 18
- Vite (build tool)
- Fabric.js (for ID card template building)
- Axios (HTTP client)
- React Router (navigation)
- Tailwind CSS (styling)

### Backend
- Node.js
- Express.js
- SQLite/MySQL (database)
- Multer (file uploads)
- CORS (cross-origin requests)

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Navigate to the project directory
2. Install backend dependencies:
   ```bash
   cd backend
   npm install
   ```

3. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

4. Set up environment variables:
   ```bash
   # Copy environment files
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```

5. Set up the database:
   ```bash
   cd backend
   npm run setup-db
   ```

6. Start the development servers:

   Backend (Terminal 1):
   ```bash
   cd backend
   npm run dev
   # or
   node server.js
   ```

   Frontend (Terminal 2):
   ```bash
   cd frontend
   npm run dev
   ```

7. Open your browser and navigate to:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:5001/api/health

## API Endpoints

### Students
- `GET /api/students` - Get all students (with pagination)
- `POST /api/students` - Create new student
- `GET /api/students/:id` - Get student by ID
- `PUT /api/students/:id` - Update student
- `DELETE /api/students/:id` - Delete student
- `GET /api/students/search/:query` - Search students
- `GET /api/students/filter/course/:course` - Filter by course
- `GET /api/students/filter/year/:year` - Filter by year
- `GET /api/students/filter/status/:status` - Filter by status

### Templates
- `GET /api/cards/templates` - Get all templates
- `POST /api/cards/templates` - Create new template
- `GET /api/cards/templates/:id` - Get template by ID
- `PUT /api/cards/templates/:id` - Update template
- `DELETE /api/cards/templates/:id` - Delete template

### Card Generation
- `POST /api/cards/generate/:studentId` - Generate card with default template
- `POST /api/cards/generate/:studentId/template/:templateId` - Generate card with specific template
- `GET /api/cards/generated/:studentId` - Get generated cards for student
- `POST /api/cards/preview` - Preview card without saving

## ID Card Templates

The system uses Fabric.js as an alternative to Builder.io for creating customizable ID card templates. Templates support:

- Text elements (name, ID, course, etc.)
- Images (student photo, logo)
- Shapes and backgrounds
- Custom styling and positioning

## License

MIT License
