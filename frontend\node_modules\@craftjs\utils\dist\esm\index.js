"undefined"!=typeof window&&(window.__CRAFTJS__||(window.__CRAFTJS__={}),window.__CRAFTJS__["@craftjs/utils"]="0.2.5");import e,{applyPatches as t,enableMapSet as n,enablePatches as r,produceWithPatches as i}from"immer";import o from"lodash/isEqualWith";import a,{useMemo as c,useRef as s,useCallback as u,useEffect as l,useState as f,cloneElement as d,isValidElement as h}from"react";import p from"shallowequal";import{nanoid as y}from"nanoid";import v from"tiny-invariant";import b from"react-dom";var m="ROOT",g="canvas-ROOT",E="Parent id cannot be ommited",w="Attempting to add a node with duplicated id",O="Node does not exist, it may have been removed",R='A <Element /> that is used inside a User Component must specify an `id` prop, eg: <Element id="text_element">...</Element> ',k="Placeholder required placement info (parent, index, or where) is missing",P="Node cannot be dropped into target parent",j="Target parent rejects incoming node",T="Current parent rejects outgoing node",C="Cannot move node that is not a direct child of a Canvas node",I="Cannot move node into a non-Canvas parent",A="A top-level Node cannot be moved",D="Root Node cannot be moved",S="Cannot move node into a descendant",H="The component type specified for this node (%node_type%) does not exist in the resolver",_="The component specified in the <Canvas> `is` prop has additional Canvas specified in it's render template.",x="The node has specified a canDrag() rule that prevents it from being dragged",N="Invalid parameter Node Id specified",L="Attempting to delete a top-level Node",M="Resolver in <Editor /> has to be an object. For (de)serialization Craft.js needs a list of all the User Components. \n    \nMore info: https://craft.js.org/r/docs/api/editor#props",U="An Error occurred while deserializing components: Cannot find component <%displayName% /> in resolver map. Please check your resolver in <Editor />\n\nAvailable components in resolver: %availableComponents%\n\nMore info: https://craft.js.org/r/docs/api/editor#props",q="You can only use useEditor in the context of <Editor />. \n\nPlease only use useEditor in components that are children of the <Editor /> component.",B="You can only use useNode in the context of <Editor />. \n\nPlease only use useNode in components that are children of the <Editor /> component.";function G(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?G(Object(n),!0).forEach((function(t){J(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):G(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function F(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function W(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,oe(r.key),r)}}function z(e,t,n){return t&&W(e.prototype,t),n&&W(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function J(e,t,n){return(t=oe(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $(e){return $=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$(e)}function K(e,t){return K=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},K(e,t)}function Q(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function V(){return V="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=$(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},V.apply(this,arguments)}function X(e,t){return ee(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,c=[],s=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(c.push(r.value),c.length!==t);s=!0);}catch(e){u=!0,i=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(e,t)||ne(e,t)||ie()}function Z(e){return function(e){if(Array.isArray(e))return re(e)}(e)||te(e)||ne(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ee(e){if(Array.isArray(e))return e}function te(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function ne(e,t){if(e){if("string"==typeof e)return re(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?re(e,t):void 0}}function re(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ie(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function oe(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var ae={UNDO:"HISTORY_UNDO",REDO:"HISTORY_REDO",THROTTLE:"HISTORY_THROTTLE",IGNORE:"HISTORY_IGNORE",MERGE:"HISTORY_MERGE",CLEAR:"HISTORY_CLEAR"},ce=function(){function e(){F(this,e),J(this,"timeline",[]),J(this,"pointer",-1)}return z(e,[{key:"add",value:function(e,t){0===e.length&&0===t.length||(this.pointer=this.pointer+1,this.timeline.length=this.pointer,this.timeline[this.pointer]={patches:e,inversePatches:t,timestamp:Date.now()})}},{key:"throttleAdd",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:500;if(0!==e.length||0!==t.length){if(this.timeline.length&&this.pointer>=0){var r=this.timeline[this.pointer],i=r.patches,o=r.inversePatches,a=r.timestamp;if((new Date).getTime()-a<n)return void(this.timeline[this.pointer]={timestamp:a,patches:[].concat(Z(i),Z(e)),inversePatches:[].concat(Z(t),Z(o))})}this.add(e,t)}}},{key:"merge",value:function(e,t){if(0!==e.length||0!==t.length)if(this.timeline.length&&this.pointer>=0){var n=this.timeline[this.pointer],r=n.inversePatches;this.timeline[this.pointer]={timestamp:n.timestamp,patches:[].concat(Z(n.patches),Z(e)),inversePatches:[].concat(Z(t),Z(r))}}else this.add(e,t)}},{key:"clear",value:function(){this.timeline=[],this.pointer=-1}},{key:"canUndo",value:function(){return this.pointer>=0}},{key:"canRedo",value:function(){return this.pointer<this.timeline.length-1}},{key:"undo",value:function(e){if(this.canUndo()){var n=this.timeline[this.pointer].inversePatches;return this.pointer=this.pointer-1,t(e,n)}}},{key:"redo",value:function(e){if(this.canRedo())return this.pointer=this.pointer+1,t(e,this.timeline[this.pointer].patches)}}]),e}();function se(t,n,r,o){var a,f=c((function(){return new ce}),[]),d=s([]),h=s((function(){}));"function"==typeof t?a=t:(a=t.methods,d.current=t.ignoreHistoryForActions,h.current=t.normalizeHistory);var p=s(o);p.current=o;var y=s(n),v=c((function(){var t=h.current,n=d.current,o=p.current;return function(c,s){var u,l=r&&ue(r,(function(){return c}),f),d=X(i(c,(function(e){var t,n;switch(s.type){case ae.UNDO:return f.undo(e);case ae.REDO:return f.redo(e);case ae.CLEAR:return f.clear(),Y({},e);case ae.IGNORE:case ae.MERGE:case ae.THROTTLE:var r,i=ee(n=s.payload)||te(n)||ne(n)||ie(),o=i[0],c=i.slice(1);(r=a(e,l))[o].apply(r,Z(c));break;default:(t=a(e,l))[s.type].apply(t,Z(s.payload))}})),3),h=d[0],p=d[1],y=d[2];return u=h,o&&o(h,c,{type:s.type,params:s.payload,patches:p},l,(function(e){var t=i(h,e);u=t[0],p=[].concat(Z(p),Z(t[1])),y=[].concat(Z(t[2]),Z(y))})),[ae.UNDO,ae.REDO].includes(s.type)&&t&&(u=e(u,t)),[].concat(Z(n),[ae.UNDO,ae.REDO,ae.IGNORE,ae.CLEAR]).includes(s.type)||(s.type===ae.THROTTLE?f.throttleAdd(p,y,s.config&&s.config.rate):s.type===ae.MERGE?f.merge(p,y):f.add(p,y)),u}}),[f,a,r]),b=u((function(){return y.current}),[]),m=c((function(){return new le(b)}),[b]),g=u((function(e){var t=v(y.current,e);y.current=t,m.notify()}),[v,m]);l((function(){m.notify()}),[m]);var E=c((function(){return r?ue(r,(function(){return y.current}),f):[]}),[f,r]),w=c((function(){var e=Object.keys(a(null,null)),t=d.current;return Y(Y({},e.reduce((function(e,t){return e[t]=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return g({type:t,payload:n})},e}),{})),{},{history:{undo:function(){return g({type:ae.UNDO})},redo:function(){return g({type:ae.REDO})},clear:function(){return g({type:ae.CLEAR})},throttle:function(n){return Y({},e.filter((function(e){return!t.includes(e)})).reduce((function(e,t){return e[t]=function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return g({type:ae.THROTTLE,payload:[t].concat(r),config:{rate:n}})},e}),{}))},ignore:function(){return Y({},e.filter((function(e){return!t.includes(e)})).reduce((function(e,t){return e[t]=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return g({type:ae.IGNORE,payload:[t].concat(n)})},e}),{}))},merge:function(){return Y({},e.filter((function(e){return!t.includes(e)})).reduce((function(e,t){return e[t]=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return g({type:ae.MERGE,payload:[t].concat(n)})},e}),{}))}}})}),[g,a]);return c((function(){return{getState:b,subscribe:function(e,t,n){return m.subscribe(e,t,n)},actions:w,query:E,history:f}}),[w,E,m,b,f])}function ue(e,t,n){var r=Object.keys(e()).reduce((function(n,r){return Y(Y({},n),{},J({},r,(function(){var n;return(n=e(t()))[r].apply(n,arguments)})))}),{});return Y(Y({},r),{},{history:{canUndo:function(){return n.canUndo()},canRedo:function(){return n.canRedo()}}})}n(),r();var le=function(){function e(t){F(this,e),J(this,"getState",void 0),J(this,"subscribers",[]),this.getState=t}return z(e,[{key:"subscribe",value:function(e,t,n){var r=this,i=new fe((function(){return e(r.getState())}),t,n);return this.subscribers.push(i),this.unsubscribe.bind(this,i)}},{key:"unsubscribe",value:function(e){if(this.subscribers.length){var t=this.subscribers.indexOf(e);if(t>-1)return this.subscribers.splice(t,1)}}},{key:"notify",value:function(){this.subscribers.forEach((function(e){return e.collect()}))}}]),e}(),fe=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];F(this,e),J(this,"collected",void 0),J(this,"collector",void 0),J(this,"onChange",void 0),J(this,"id",void 0),this.collector=t,this.onChange=n,r&&this.collect()}return z(e,[{key:"collect",value:function(){try{var e=this.collector();o(e,this.collected)||(this.collected=e,this.onChange&&this.onChange(this.collected))}catch(e){console.warn(e)}}}]),e}(),de=function(e){var t=e.getBoundingClientRect(),n=t.x,r=t.y,i=t.top,o=t.left,a=t.bottom,c=t.right,s=t.width,u=t.height,l=window.getComputedStyle(e),f={left:parseInt(l.marginLeft),right:parseInt(l.marginRight),bottom:parseInt(l.marginBottom),top:parseInt(l.marginTop)},d={left:parseInt(l.paddingLeft),right:parseInt(l.paddingRight),bottom:parseInt(l.paddingBottom),top:parseInt(l.paddingTop)};return{x:n,y:r,top:i,left:o,bottom:a,right:c,width:s,height:u,outerWidth:Math.round(s+f.left+f.right),outerHeight:Math.round(u+f.top+f.bottom),margin:f,padding:d,inFlow:e.parentElement&&!!function(t){var n=getComputedStyle(t);if(!(l.overflow&&"visible"!==l.overflow||"none"!==n.float||"grid"===n.display||"flex"===n.display&&"column"!==n["flex-direction"])){switch(l.position){case"static":case"relative":break;default:return}switch(e.tagName){case"TR":case"TBODY":case"THEAD":case"TFOOT":return!0}switch(l.display){case"block":case"list-item":case"table":case"flex":case"grid":return!0}}}(e.parentElement)}};function he(e,t){const{subscribe:n,getState:r,actions:i,query:o}=e,a=s(!0),c=s(null),d=s(t);d.current=t;const h=u((e=>({...e,actions:i,query:o})),[i,o]);a.current&&t&&(c.current=t(r(),o),a.current=!1);const[p,y]=f(h(c.current));return l((()=>{let e;return d.current&&(e=n((e=>d.current(e,o)),(e=>{y(h(e))}))),()=>{e&&e()}}),[h,o,n]),p}var pe,ye=function(){return y(arguments.length>0&&void 0!==arguments[0]?arguments[0]:10)},ve=function(){function e(){F(this,e),J(this,"isEnabled",!0),J(this,"elementIdMap",new WeakMap),J(this,"registry",new Map)}return z(e,[{key:"getElementId",value:function(e){var t=this.elementIdMap.get(e);if(t)return t;var n=ye();return this.elementIdMap.set(e,n),n}},{key:"getConnectorId",value:function(e,t){var n=this.getElementId(e);return"".concat(t,"--").concat(n)}},{key:"register",value:function(e,t){var n=this,r=this.getByElement(e,t.name);if(r){if(p(t.required,r.required))return r;this.getByElement(e,t.name).disable()}var i=null,o=this.getConnectorId(e,t.name);return this.registry.set(o,{id:o,required:t.required,enable:function(){i&&i(),i=t.connector(e,t.required,t.options)},disable:function(){i&&i()},remove:function(){return n.remove(o)}}),this.isEnabled&&this.registry.get(o).enable(),this.registry.get(o)}},{key:"get",value:function(e){return this.registry.get(e)}},{key:"remove",value:function(e){var t=this.get(e);t&&(t.disable(),this.registry.delete(t.id))}},{key:"enable",value:function(){this.isEnabled=!0,this.registry.forEach((function(e){e.enable()}))}},{key:"disable",value:function(){this.isEnabled=!1,this.registry.forEach((function(e){e.disable()}))}},{key:"getByElement",value:function(e,t){return this.get(this.getConnectorId(e,t))}},{key:"removeByElement",value:function(e,t){return this.remove(this.getConnectorId(e,t))}},{key:"clear",value:function(){this.disable(),this.elementIdMap=new WeakMap,this.registry=new Map}}]),e}();!function(e){e[e.HandlerDisabled=0]="HandlerDisabled",e[e.HandlerEnabled=1]="HandlerEnabled"}(pe||(pe={}));var be=function(){function e(t){F(this,e),J(this,"options",void 0),J(this,"registry",new ve),J(this,"subscribers",new Set),this.options=t}return z(e,[{key:"listen",value:function(e){var t=this;return this.subscribers.add(e),function(){return t.subscribers.delete(e)}}},{key:"disable",value:function(){this.onDisable&&this.onDisable(),this.registry.disable(),this.subscribers.forEach((function(e){e(pe.HandlerDisabled)}))}},{key:"enable",value:function(){this.onEnable&&this.onEnable(),this.registry.enable(),this.subscribers.forEach((function(e){e(pe.HandlerEnabled)}))}},{key:"cleanup",value:function(){this.disable(),this.subscribers.clear(),this.registry.clear()}},{key:"addCraftEventListener",value:function(e,t,n,r){var i=function(r){(function(e,t,n){e.craft||(e.craft={stopPropagation:function(){},blockedEvents:{}});for(var r=e.craft&&e.craft.blockedEvents[t]||[],i=0;i<r.length;i++){var o=r[i];if(n!==o&&n.contains(o))return!0}return!1})(r,t,e)||(r.craft.stopPropagation=function(){r.craft.blockedEvents[t]||(r.craft.blockedEvents[t]=[]),r.craft.blockedEvents[t].push(e)},n(r))};return e.addEventListener(t,i,r),function(){return e.removeEventListener(t,i,r)}}},{key:"createConnectorsUsage",value:function(){var e=this,t=this.handlers(),n=new Set,r=!1,i=new Map;return{connectors:Object.entries(t).reduce((function(t,o){var a=X(o,2),c=a[0],s=a[1];return Y(Y({},t),{},J({},c,(function(t,o,a){var u=function(){var r=e.registry.register(t,{required:o,name:c,options:a,connector:s});return n.add(r.id),r};return i.set(e.registry.getConnectorId(t,c),u),r&&u(),t})))}),{}),register:function(){r=!0,i.forEach((function(e){e()}))},cleanup:function(){r=!1,n.forEach((function(t){return e.registry.remove(t)}))}}}},{key:"derive",value:function(e,t){return new e(this,t)}},{key:"createProxyHandlers",value:function(e,t){var n=[],r=e.handlers(),i=new Proxy(r,{get:function(e,t,i){return t in r==0?Reflect.get(e,t,i):function(e){for(var i=arguments.length,o=new Array(i>1?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];var c=r[t].apply(r,[e].concat(o));c&&n.push(c)}}});return t(i),function(){n.forEach((function(e){e()}))}}},{key:"reflect",value:function(e){return this.createProxyHandlers(this,e)}}]),e}(),me=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&K(e,t)}(i,be);var t,n,r=(t=i,n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,r=$(t);if(n){var i=$(this).constructor;e=Reflect.construct(r,arguments,i)}else e=r.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Q(e)}(this,e)});function i(e,t){var n;return F(this,i),J(Q(n=r.call(this,t)),"derived",void 0),J(Q(n),"unsubscribeParentHandlerListener",void 0),n.derived=e,n.options=t,n.unsubscribeParentHandlerListener=n.derived.listen((function(e){switch(e){case pe.HandlerEnabled:return n.enable();case pe.HandlerDisabled:return n.disable();default:return}})),n}return z(i,[{key:"inherit",value:function(e){return this.createProxyHandlers(this.derived,e)}},{key:"cleanup",value:function(){V($(i.prototype),"cleanup",this).call(this),this.unsubscribeParentHandlerListener()}}]),i}();function ge(e,t){t&&("function"==typeof e?e(t):e.current=t)}function Ee(e,t){const n=e.ref;return v("string"!=typeof n,"Cannot connect to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://facebook.github.io/react/docs/more-about-refs.html#the-ref-callback-attribute"),d(e,n?{ref:e=>{ge(n,e),ge(t,e)}}:{ref:t})}function we(e){return(t=null,...n)=>{if(!h(t)){if(!t)return;const r=t;return r&&e(r,...n),r}const r=t;return function(e){if("string"!=typeof e.type)throw new Error}(r),Ee(r,e)}}function Oe(e){return Object.keys(e).reduce(((t,n)=>(t[n]=we(((...t)=>e[n](...t))),t)),{})}const Re=({style:e,className:t,parentDom:n})=>{const r=a.createElement("div",{className:t,style:{position:"fixed",display:"block",opacity:1,borderStyle:"solid",borderWidth:"1px",borderColor:"transparent",zIndex:99999,...e}});return n&&n.ownerDocument!==document?b.createPortal(r,n.ownerDocument.body):r},ke=e=>{l(e,[])};var Pe=function(e,t){var n="Deprecation warning: ".concat(e," will be deprecated in future relases."),r=t.suggest,i=t.doc;r&&(n+=" Please use ".concat(r," instead.")),i&&(n+="(".concat(i,")")),console.warn(n)},je=function(){return"undefined"!=typeof window},Te=function(){return je()&&/Linux/i.test(window.navigator.userAgent)},Ce=function(){return je()&&/Chrome/i.test(window.navigator.userAgent)};export{g as DEPRECATED_ROOT_NODE,me as DerivedEventHandlers,x as ERROR_CANNOT_DRAG,L as ERROR_DELETE_TOP_LEVEL_NODE,U as ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER,w as ERROR_DUPLICATE_NODEID,_ as ERROR_INFINITE_CANVAS,O as ERROR_INVALID_NODEID,N as ERROR_INVALID_NODE_ID,k as ERROR_MISSING_PLACEHOLDER_PLACEMENT,P as ERROR_MOVE_CANNOT_DROP,j as ERROR_MOVE_INCOMING_PARENT,C as ERROR_MOVE_NONCANVAS_CHILD,T as ERROR_MOVE_OUTGOING_PARENT,D as ERROR_MOVE_ROOT_NODE,A as ERROR_MOVE_TOP_LEVEL_NODE,S as ERROR_MOVE_TO_DESCENDANT,I as ERROR_MOVE_TO_NONCANVAS_PARENT,E as ERROR_NOPARENT,H as ERROR_NOT_IN_RESOLVER,M as ERROR_RESOLVER_NOT_AN_OBJECT,R as ERROR_TOP_LEVEL_ELEMENT_NO_ID,q as ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT,B as ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT,pe as EventHandlerUpdates,be as EventHandlers,ae as HISTORY_ACTIONS,ce as History,m as ROOT_NODE,Re as RenderIndicator,Ee as cloneWithRef,ue as createQuery,Pe as deprecationWarning,de as getDOMInfo,ye as getRandomId,Ce as isChromium,je as isClientSide,Te as isLinux,he as useCollector,ke as useEffectOnce,se as useMethods,Oe as wrapConnectorHooks,we as wrapHookToRecognizeElement};
//# sourceMappingURL=index.js.map
