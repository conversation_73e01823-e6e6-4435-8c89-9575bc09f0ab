// Choose database based on environment
const dbType = process.env.DB_TYPE || 'sqlite';
const { getDatabase } = dbType === 'mysql'
  ? require('../config/mysqlDatabase')
  : require('../config/database');
const fs = require('fs');
const path = require('path');

// Helper function to execute queries for both MySQL and SQLite
async function executeQuery(query, params = []) {
  const db = getDatabase();

  if (dbType === 'mysql') {
    const [rows] = await db.execute(query, params);
    return rows;
  } else {
    // SQLite
    return new Promise((resolve, reject) => {
      if (query.trim().toUpperCase().startsWith('SELECT')) {
        db.all(query, params, (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      } else {
        db.run(query, params, function(err) {
          if (err) reject(err);
          else resolve({ insertId: this.lastID, changes: this.changes });
        });
      }
    });
  }
}

// Helper function to get single row
async function getOne(query, params = []) {
  const db = getDatabase();

  if (dbType === 'mysql') {
    const [rows] = await db.execute(query, params);
    return rows[0] || null;
  } else {
    // SQLite
    return new Promise((resolve, reject) => {
      db.get(query, params, (err, row) => {
        if (err) reject(err);
        else resolve(row || null);
      });
    });
  }
}

// Get all students with pagination
const getAllStudents = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Get total count
    const countResult = await getOne('SELECT COUNT(*) as total FROM students');
    const total = countResult.total;

    // Get paginated results
    const query = `
      SELECT * FROM students
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const students = await executeQuery(query, [limit, offset]);

    res.json({
      students,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalStudents: total,
        hasNext: offset + limit < total,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching students:', error);
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

// Get student by ID
const getStudentById = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  
  db.get('SELECT * FROM students WHERE id = ?', [id], (err, row) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    if (!row) {
      return res.status(404).json({ error: 'Student not found' });
    }
    
    res.json(row);
  });
};

// Create new student
const createStudent = (req, res) => {
  const db = getDatabase();
  const {
    student_id,
    name,
    course,
    email,
    address
  } = req.body;

  const photo_url = req.file ? `/uploads/photos/${req.file.filename}` : null;

  const query = `
    INSERT INTO students (
      student_id, name, course, email, address, photo_url
    ) VALUES (?, ?, ?, ?, ?, ?)
  `;

  db.run(query, [
    student_id, name, course, email, address, photo_url
  ], function(err) {
    if (err) {
      // Clean up uploaded file if database insert fails
      if (req.file) {
        fs.unlink(req.file.path, () => {});
      }

      if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return res.status(400).json({
          error: 'Student ID or email already exists'
        });
      }

      return res.status(500).json({
        error: 'Database error',
        details: err.message
      });
    }
    
    // Get the created student
    db.get('SELECT * FROM students WHERE id = ?', [this.lastID], (err, row) => {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }
      
      res.status(201).json({
        message: 'Student created successfully',
        student: row
      });
    });
  });
};

// Update student
const updateStudent = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  const {
    student_id,
    name,
    course,
    email,
    address
  } = req.body;

  // First, get the current student to handle photo replacement
  db.get('SELECT * FROM students WHERE id = ?', [id], (err, currentStudent) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    if (!currentStudent) {
      // Clean up uploaded file if student doesn't exist
      if (req.file) {
        fs.unlink(req.file.path, () => {});
      }
      return res.status(404).json({ error: 'Student not found' });
    }

    const photo_url = req.file ? `/uploads/photos/${req.file.filename}` : currentStudent.photo_url;

    const query = `
      UPDATE students SET
        student_id = ?, name = ?, course = ?, email = ?, address = ?, photo_url = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    db.run(query, [
      student_id, name, course, email, address, photo_url, id
    ], function(err) {
      if (err) {
        // Clean up uploaded file if database update fails
        if (req.file) {
          fs.unlink(req.file.path, () => {});
        }

        if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
          return res.status(400).json({
            error: 'Student ID or email already exists'
          });
        }

        return res.status(500).json({
          error: 'Database error',
          details: err.message
        });
      }

      // Delete old photo if a new one was uploaded
      if (req.file && currentStudent.photo_url) {
        const oldPhotoPath = path.join(__dirname, '..', currentStudent.photo_url);
        fs.unlink(oldPhotoPath, () => {});
      }
      
      // Get the updated student
      db.get('SELECT * FROM students WHERE id = ?', [id], (err, row) => {
        if (err) {
          return res.status(500).json({ error: 'Database error', details: err.message });
        }
        
        res.json({
          message: 'Student updated successfully',
          student: row
        });
      });
    });
  });
};

// Delete student
const deleteStudent = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;

  // First, get the student to delete associated photo
  db.get('SELECT * FROM students WHERE id = ?', [id], (err, student) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Delete student from database
    db.run('DELETE FROM students WHERE id = ?', [id], function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }

      // Delete associated photo file
      if (student.photo_url) {
        const photoPath = path.join(__dirname, '..', student.photo_url);
        fs.unlink(photoPath, () => {});
      }

      res.json({ message: 'Student deleted successfully' });
    });
  });
};

// Search students
const searchStudents = (req, res) => {
  const db = getDatabase();
  const { query } = req.params;
  const searchTerm = `%${query}%`;

  const sql = `
    SELECT * FROM students
    WHERE first_name LIKE ? OR last_name LIKE ? OR student_id LIKE ? OR email LIKE ?
    ORDER BY created_at DESC
  `;

  db.all(sql, [searchTerm, searchTerm, searchTerm, searchTerm], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get students by course
const getStudentsByCourse = (req, res) => {
  const db = getDatabase();
  const { course } = req.params;

  db.all('SELECT * FROM students WHERE course = ? ORDER BY created_at DESC', [course], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get students by year
const getStudentsByYear = (req, res) => {
  const db = getDatabase();
  const { year } = req.params;

  db.all('SELECT * FROM students WHERE year_of_study = ? ORDER BY created_at DESC', [year], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get students by status
const getStudentsByStatus = (req, res) => {
  const db = getDatabase();
  const { status } = req.params;

  db.all('SELECT * FROM students WHERE status = ? ORDER BY created_at DESC', [status], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get card generation history for a student
const getCardHistory = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;

  const query = `
    SELECT gc.*, ct.name as template_name
    FROM generated_cards gc
    JOIN card_templates ct ON gc.template_id = ct.id
    WHERE gc.student_id = ?
    ORDER BY gc.generated_at DESC
  `;

  db.all(query, [id], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

module.exports = {
  getAllStudents,
  getStudentById,
  createStudent,
  updateStudent,
  deleteStudent,
  searchStudents,
  getStudentsByCourse,
  getStudentsByYear,
  getStudentsByStatus,
  getCardHistory
};
