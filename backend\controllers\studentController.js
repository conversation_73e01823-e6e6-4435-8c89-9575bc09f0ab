const { getDatabase } = require('../config/database');
const fs = require('fs');
const path = require('path');

// Get all students with pagination
const getAllStudents = (req, res) => {
  const db = getDatabase();
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;
  
  // Get total count
  db.get('SELECT COUNT(*) as total FROM students', (err, countResult) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    // Get paginated results
    const query = `
      SELECT * FROM students 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    db.all(query, [limit, offset], (err, rows) => {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }
      
      res.json({
        students: rows,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(countResult.total / limit),
          totalStudents: countResult.total,
          hasNext: offset + limit < countResult.total,
          hasPrev: page > 1
        }
      });
    });
  });
};

// Get student by ID
const getStudentById = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  
  db.get('SELECT * FROM students WHERE id = ?', [id], (err, row) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }
    
    if (!row) {
      return res.status(404).json({ error: 'Student not found' });
    }
    
    res.json(row);
  });
};

// Create new student
const createStudent = (req, res) => {
  const db = getDatabase();
  const {
    student_id,
    name,
    course,
    email,
    address
  } = req.body;

  const query = `
    INSERT INTO students (
      student_id, name, course, email, address
    ) VALUES (?, ?, ?, ?, ?)
  `;

  db.run(query, [
    student_id, name, course, email, address
  ], function(err) {
    if (err) {
      if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        return res.status(400).json({
          error: 'Student ID or email already exists'
        });
      }

      return res.status(500).json({
        error: 'Database error',
        details: err.message
      });
    }
    
    // Get the created student
    db.get('SELECT * FROM students WHERE id = ?', [this.lastID], (err, row) => {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }
      
      res.status(201).json({
        message: 'Student created successfully',
        student: row
      });
    });
  });
};

// Update student
const updateStudent = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;
  const {
    student_id,
    name,
    course,
    email,
    address
  } = req.body;

  // Check if student exists
  db.get('SELECT * FROM students WHERE id = ?', [id], (err, currentStudent) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    if (!currentStudent) {
      return res.status(404).json({ error: 'Student not found' });
    }

    const query = `
      UPDATE students SET
        student_id = ?, name = ?, course = ?, email = ?, address = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    db.run(query, [
      student_id, name, course, email, address, id
    ], function(err) {
      if (err) {
        if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
          return res.status(400).json({
            error: 'Student ID or email already exists'
          });
        }

        return res.status(500).json({
          error: 'Database error',
          details: err.message
        });
      }
      
      // Get the updated student
      db.get('SELECT * FROM students WHERE id = ?', [id], (err, row) => {
        if (err) {
          return res.status(500).json({ error: 'Database error', details: err.message });
        }
        
        res.json({
          message: 'Student updated successfully',
          student: row
        });
      });
    });
  });
};

// Delete student
const deleteStudent = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;

  // First, get the student to delete associated photo
  db.get('SELECT * FROM students WHERE id = ?', [id], (err, student) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Delete student from database
    db.run('DELETE FROM students WHERE id = ?', [id], function(err) {
      if (err) {
        return res.status(500).json({ error: 'Database error', details: err.message });
      }

      // Delete associated photo file
      if (student.photo_url) {
        const photoPath = path.join(__dirname, '..', student.photo_url);
        fs.unlink(photoPath, () => {});
      }

      res.json({ message: 'Student deleted successfully' });
    });
  });
};

// Search students
const searchStudents = (req, res) => {
  const db = getDatabase();
  const { query } = req.params;
  const searchTerm = `%${query}%`;

  const sql = `
    SELECT * FROM students
    WHERE first_name LIKE ? OR last_name LIKE ? OR student_id LIKE ? OR email LIKE ?
    ORDER BY created_at DESC
  `;

  db.all(sql, [searchTerm, searchTerm, searchTerm, searchTerm], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get students by course
const getStudentsByCourse = (req, res) => {
  const db = getDatabase();
  const { course } = req.params;

  db.all('SELECT * FROM students WHERE course = ? ORDER BY created_at DESC', [course], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get students by year
const getStudentsByYear = (req, res) => {
  const db = getDatabase();
  const { year } = req.params;

  db.all('SELECT * FROM students WHERE year_of_study = ? ORDER BY created_at DESC', [year], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get students by status
const getStudentsByStatus = (req, res) => {
  const db = getDatabase();
  const { status } = req.params;

  db.all('SELECT * FROM students WHERE status = ? ORDER BY created_at DESC', [status], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

// Get card generation history for a student
const getCardHistory = (req, res) => {
  const db = getDatabase();
  const { id } = req.params;

  const query = `
    SELECT gc.*, ct.name as template_name
    FROM generated_cards gc
    JOIN card_templates ct ON gc.template_id = ct.id
    WHERE gc.student_id = ?
    ORDER BY gc.generated_at DESC
  `;

  db.all(query, [id], (err, rows) => {
    if (err) {
      return res.status(500).json({ error: 'Database error', details: err.message });
    }

    res.json(rows);
  });
};

module.exports = {
  getAllStudents,
  getStudentById,
  createStudent,
  updateStudent,
  deleteStudent,
  searchStudents,
  getStudentsByCourse,
  getStudentsByYear,
  getStudentsByStatus,
  getCardHistory
};
