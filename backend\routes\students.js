const express = require('express');
const studentController = require('../controllers/studentController');
const { validateStudent, validateStudentUpdate } = require('../middleware/validation');

const router = express.Router();

// Routes
router.get('/', studentController.getAllStudents);
router.get('/:id', studentController.getStudentById);
router.post('/', validateStudent, studentController.createStudent);
router.put('/:id', validateStudentUpdate, studentController.updateStudent);
router.delete('/:id', studentController.deleteStudent);
router.get('/:id/card-history', studentController.getCardHistory);

// Search and filter routes
router.get('/search/:query', studentController.searchStudents);
router.get('/filter/course/:course', studentController.getStudentsByCourse);
router.get('/filter/year/:year', studentController.getStudentsByYear);
router.get('/filter/status/:status', studentController.getStudentsByStatus);

module.exports = router;
