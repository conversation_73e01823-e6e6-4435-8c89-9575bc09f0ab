const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const studentController = require('../controllers/studentController');
const { validateStudent, validateStudentUpdate } = require('../middleware/validation');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/photos');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = /jpeg|jpg|png|gif/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);
  
  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'));
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB
  },
  fileFilter: fileFilter
});

// Routes
router.get('/', studentController.getAllStudents);
router.get('/:id', studentController.getStudentById);
router.post('/', upload.single('photo'), validateStudent, studentController.createStudent);
router.put('/:id', upload.single('photo'), validateStudentUpdate, studentController.updateStudent);
router.delete('/:id', studentController.deleteStudent);
router.get('/:id/card-history', studentController.getCardHistory);

// Search and filter routes
router.get('/search/:query', studentController.searchStudents);
router.get('/filter/course/:course', studentController.getStudentsByCourse);
router.get('/filter/year/:year', studentController.getStudentsByYear);
router.get('/filter/status/:status', studentController.getStudentsByStatus);

module.exports = router;
