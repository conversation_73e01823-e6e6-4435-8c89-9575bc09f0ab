import { useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { ArrowLeft, Eye, Palette, Briefcase, Sparkles, Building, Heart } from 'lucide-react'
import { templateAPI, studentAPI } from '../services/api'
import toast from 'react-hot-toast'

const categoryIcons = {
  Professional: Briefcase,
  Modern: Sparkles,
  Minimalist: Eye,
  Corporate: Building,
  Creative: Heart,
  All: Palette
}

const categoryColors = {
  Professional: 'bg-blue-100 text-blue-800',
  Modern: 'bg-purple-100 text-purple-800',
  Minimalist: 'bg-green-100 text-green-800',
  Corporate: 'bg-gray-100 text-gray-800',
  Creative: 'bg-pink-100 text-pink-800'
}

export default function TemplateGallery() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const studentId = searchParams.get('studentId')
  
  const [templates, setTemplates] = useState([])
  const [filteredTemplates, setFilteredTemplates] = useState([])
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [loading, setLoading] = useState(true)
  const [student, setStudent] = useState(null)

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    filterTemplates()
  }, [templates, selectedCategory])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch templates
      const templatesResponse = await templateAPI.getAll()
      setTemplates(templatesResponse.data)
      
      // Fetch student if ID provided
      if (studentId) {
        const studentResponse = await studentAPI.getById(studentId)
        setStudent(studentResponse.data)
      }
      
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load templates')
    } finally {
      setLoading(false)
    }
  }

  const filterTemplates = () => {
    if (selectedCategory === 'All') {
      setFilteredTemplates(templates)
    } else {
      // For now, we'll categorize based on template name keywords
      const filtered = templates.filter(template => {
        const name = template.name.toLowerCase()
        const category = selectedCategory.toLowerCase()
        
        if (category === 'professional') return name.includes('professional') || name.includes('classic')
        if (category === 'modern') return name.includes('modern') || name.includes('gradient')
        if (category === 'minimalist') return name.includes('minimalist') || name.includes('clean')
        if (category === 'corporate') return name.includes('corporate') || name.includes('blue')
        if (category === 'creative') return name.includes('creative') || name.includes('colorful')
        
        return true
      })
      setFilteredTemplates(filtered)
    }
  }

  const getTemplateCategory = (templateName) => {
    const name = templateName.toLowerCase()
    if (name.includes('professional') || name.includes('classic')) return 'Professional'
    if (name.includes('modern') || name.includes('gradient')) return 'Modern'
    if (name.includes('minimalist') || name.includes('clean')) return 'Minimalist'
    if (name.includes('corporate') || name.includes('blue')) return 'Corporate'
    if (name.includes('creative') || name.includes('colorful')) return 'Creative'
    return 'Professional'
  }

  const handleTemplateSelect = (template) => {
    if (studentId) {
      // Go to card generator with selected template and student
      navigate(`/card-generator/${studentId}?template=${template.id}`)
    } else {
      // Go to card generator with selected template
      navigate(`/card-generator?template=${template.id}`)
    }
  }

  const categories = ['All', 'Professional', 'Modern', 'Minimalist', 'Corporate', 'Creative']

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="card p-6">
                <div className="h-32 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate(-1)}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Choose a Template
          </h1>
          <p className="text-gray-600">
            {student 
              ? `Select a template for ${student.first_name} ${student.last_name}'s ID card`
              : 'Browse our collection of professional ID card templates'
            }
          </p>
        </div>
      </div>

      {/* Student Info (if applicable) */}
      {student && (
        <div className="card p-4">
          <div className="flex items-center gap-4">
            {student.photo_url ? (
              <img
                src={`http://localhost:5001${student.photo_url}`}
                alt={`${student.first_name} ${student.last_name}`}
                className="h-12 w-12 rounded-lg object-cover"
              />
            ) : (
              <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                <span className="text-sm font-medium text-gray-600">
                  {student.first_name?.[0]}{student.last_name?.[0]}
                </span>
              </div>
            )}
            <div>
              <h3 className="font-medium text-gray-900">
                {student.first_name} {student.last_name}
              </h3>
              <p className="text-sm text-gray-600">
                {student.student_id} • {student.course} • Year {student.year_of_study}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Category Filter */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
        <div className="flex flex-wrap gap-3">
          {categories.map((category) => {
            const Icon = categoryIcons[category] || Palette
            const isSelected = selectedCategory === category
            
            return (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isSelected
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <Icon className="h-4 w-4" />
                {category}
              </button>
            )
          })}
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => {
          const category = getTemplateCategory(template.name)
          
          return (
            <div key={template.id} className="card overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group">
              {/* Template Preview */}
              <div className="h-48 bg-gray-50 flex items-center justify-center border-b border-gray-200 relative overflow-hidden">
                <div 
                  className="relative border border-gray-300 shadow-sm transition-transform group-hover:scale-105"
                  style={{
                    width: `${Math.min(template.template_data.width * 0.4, 160)}px`,
                    height: `${Math.min(template.template_data.height * 0.4, 100)}px`,
                    backgroundColor: template.template_data.backgroundColor || '#ffffff'
                  }}
                >
                  {/* Simplified preview of template elements */}
                  {template.template_data.elements?.slice(0, 5).map((element, index) => {
                    if (element.type === 'text') {
                      return (
                        <div
                          key={index}
                          className="absolute text-xs overflow-hidden"
                          style={{
                            left: `${(element.x / template.template_data.width) * 100}%`,
                            top: `${(element.y / template.template_data.height) * 100}%`,
                            fontSize: `${Math.max((element.fontSize || 14) * 0.3, 6)}px`,
                            color: element.color || '#000000',
                            fontWeight: element.fontWeight || 'normal'
                          }}
                        >
                          {element.content?.replace(/\{[^}]+\}/g, 'Sample') || 'Text'}
                        </div>
                      )
                    } else if (element.type === 'image') {
                      return (
                        <div
                          key={index}
                          className="absolute bg-gray-200 border border-gray-300"
                          style={{
                            left: `${(element.x / template.template_data.width) * 100}%`,
                            top: `${(element.y / template.template_data.height) * 100}%`,
                            width: `${(element.width / template.template_data.width) * 100}%`,
                            height: `${(element.height / template.template_data.height) * 100}%`
                          }}
                        />
                      )
                    } else if (element.type === 'shape') {
                      return (
                        <div
                          key={index}
                          className="absolute"
                          style={{
                            left: `${(element.x / template.template_data.width) * 100}%`,
                            top: `${(element.y / template.template_data.height) * 100}%`,
                            width: `${(element.width / template.template_data.width) * 100}%`,
                            height: `${(element.height / template.template_data.height) * 100}%`,
                            backgroundColor: element.fillColor || 'transparent',
                            border: element.strokeColor ? `${element.strokeWidth || 1}px solid ${element.strokeColor}` : 'none',
                            borderRadius: element.shapeType === 'circle' ? '50%' : '0'
                          }}
                        />
                      )
                    }
                    return null
                  })}
                </div>
                
                {/* Hover overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all flex items-center justify-center">
                  <button
                    onClick={() => handleTemplateSelect(template)}
                    className="opacity-0 group-hover:opacity-100 transition-opacity btn-primary"
                  >
                    Use This Template
                  </button>
                </div>
              </div>

              {/* Template Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">{template.name}</h3>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${categoryColors[category] || 'bg-gray-100 text-gray-800'}`}>
                    {category}
                  </span>
                </div>
                
                <div className="text-sm text-gray-600 mb-3">
                  <p>Size: {template.template_data.width} × {template.template_data.height}px</p>
                  <p>Elements: {template.template_data.elements?.length || 0}</p>
                </div>

                <button
                  onClick={() => handleTemplateSelect(template)}
                  className="w-full btn-primary"
                >
                  Select Template
                </button>
              </div>
            </div>
          )
        })}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Palette className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try selecting a different category or check back later for new templates.
          </p>
        </div>
      )}

      {/* Template Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{templates.length}</div>
          <div className="text-sm text-gray-600">Total Templates</div>
        </div>
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{categories.length - 1}</div>
          <div className="text-sm text-gray-600">Categories</div>
        </div>
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">100%</div>
          <div className="text-sm text-gray-600">Free to Use</div>
        </div>
        <div className="card p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">HD</div>
          <div className="text-sm text-gray-600">Quality</div>
        </div>
      </div>
    </div>
  )
}
