import {
  require_react_dom
} from "./chunk-WALXKXZM.js";
import {
  require_react
} from "./chunk-WQMOH32Y.js";
import {
  __commonJS,
  __toESM
} from "./chunk-5WWUZCGV.js";

// node_modules/lodash/_listCacheClear.js
var require_listCacheClear = __commonJS({
  "node_modules/lodash/_listCacheClear.js"(exports, module) {
    function listCacheClear() {
      this.__data__ = [];
      this.size = 0;
    }
    module.exports = listCacheClear;
  }
});

// node_modules/lodash/eq.js
var require_eq = __commonJS({
  "node_modules/lodash/eq.js"(exports, module) {
    function eq(value, other) {
      return value === other || value !== value && other !== other;
    }
    module.exports = eq;
  }
});

// node_modules/lodash/_assocIndexOf.js
var require_assocIndexOf = __commonJS({
  "node_modules/lodash/_assocIndexOf.js"(exports, module) {
    var eq = require_eq();
    function assocIndexOf(array, key) {
      var length = array.length;
      while (length--) {
        if (eq(array[length][0], key)) {
          return length;
        }
      }
      return -1;
    }
    module.exports = assocIndexOf;
  }
});

// node_modules/lodash/_listCacheDelete.js
var require_listCacheDelete = __commonJS({
  "node_modules/lodash/_listCacheDelete.js"(exports, module) {
    var assocIndexOf = require_assocIndexOf();
    var arrayProto = Array.prototype;
    var splice = arrayProto.splice;
    function listCacheDelete(key) {
      var data = this.__data__, index = assocIndexOf(data, key);
      if (index < 0) {
        return false;
      }
      var lastIndex = data.length - 1;
      if (index == lastIndex) {
        data.pop();
      } else {
        splice.call(data, index, 1);
      }
      --this.size;
      return true;
    }
    module.exports = listCacheDelete;
  }
});

// node_modules/lodash/_listCacheGet.js
var require_listCacheGet = __commonJS({
  "node_modules/lodash/_listCacheGet.js"(exports, module) {
    var assocIndexOf = require_assocIndexOf();
    function listCacheGet(key) {
      var data = this.__data__, index = assocIndexOf(data, key);
      return index < 0 ? void 0 : data[index][1];
    }
    module.exports = listCacheGet;
  }
});

// node_modules/lodash/_listCacheHas.js
var require_listCacheHas = __commonJS({
  "node_modules/lodash/_listCacheHas.js"(exports, module) {
    var assocIndexOf = require_assocIndexOf();
    function listCacheHas(key) {
      return assocIndexOf(this.__data__, key) > -1;
    }
    module.exports = listCacheHas;
  }
});

// node_modules/lodash/_listCacheSet.js
var require_listCacheSet = __commonJS({
  "node_modules/lodash/_listCacheSet.js"(exports, module) {
    var assocIndexOf = require_assocIndexOf();
    function listCacheSet(key, value) {
      var data = this.__data__, index = assocIndexOf(data, key);
      if (index < 0) {
        ++this.size;
        data.push([key, value]);
      } else {
        data[index][1] = value;
      }
      return this;
    }
    module.exports = listCacheSet;
  }
});

// node_modules/lodash/_ListCache.js
var require_ListCache = __commonJS({
  "node_modules/lodash/_ListCache.js"(exports, module) {
    var listCacheClear = require_listCacheClear();
    var listCacheDelete = require_listCacheDelete();
    var listCacheGet = require_listCacheGet();
    var listCacheHas = require_listCacheHas();
    var listCacheSet = require_listCacheSet();
    function ListCache(entries) {
      var index = -1, length = entries == null ? 0 : entries.length;
      this.clear();
      while (++index < length) {
        var entry = entries[index];
        this.set(entry[0], entry[1]);
      }
    }
    ListCache.prototype.clear = listCacheClear;
    ListCache.prototype["delete"] = listCacheDelete;
    ListCache.prototype.get = listCacheGet;
    ListCache.prototype.has = listCacheHas;
    ListCache.prototype.set = listCacheSet;
    module.exports = ListCache;
  }
});

// node_modules/lodash/_stackClear.js
var require_stackClear = __commonJS({
  "node_modules/lodash/_stackClear.js"(exports, module) {
    var ListCache = require_ListCache();
    function stackClear() {
      this.__data__ = new ListCache();
      this.size = 0;
    }
    module.exports = stackClear;
  }
});

// node_modules/lodash/_stackDelete.js
var require_stackDelete = __commonJS({
  "node_modules/lodash/_stackDelete.js"(exports, module) {
    function stackDelete(key) {
      var data = this.__data__, result = data["delete"](key);
      this.size = data.size;
      return result;
    }
    module.exports = stackDelete;
  }
});

// node_modules/lodash/_stackGet.js
var require_stackGet = __commonJS({
  "node_modules/lodash/_stackGet.js"(exports, module) {
    function stackGet(key) {
      return this.__data__.get(key);
    }
    module.exports = stackGet;
  }
});

// node_modules/lodash/_stackHas.js
var require_stackHas = __commonJS({
  "node_modules/lodash/_stackHas.js"(exports, module) {
    function stackHas(key) {
      return this.__data__.has(key);
    }
    module.exports = stackHas;
  }
});

// node_modules/lodash/_freeGlobal.js
var require_freeGlobal = __commonJS({
  "node_modules/lodash/_freeGlobal.js"(exports, module) {
    var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
    module.exports = freeGlobal;
  }
});

// node_modules/lodash/_root.js
var require_root = __commonJS({
  "node_modules/lodash/_root.js"(exports, module) {
    var freeGlobal = require_freeGlobal();
    var freeSelf = typeof self == "object" && self && self.Object === Object && self;
    var root = freeGlobal || freeSelf || Function("return this")();
    module.exports = root;
  }
});

// node_modules/lodash/_Symbol.js
var require_Symbol = __commonJS({
  "node_modules/lodash/_Symbol.js"(exports, module) {
    var root = require_root();
    var Symbol2 = root.Symbol;
    module.exports = Symbol2;
  }
});

// node_modules/lodash/_getRawTag.js
var require_getRawTag = __commonJS({
  "node_modules/lodash/_getRawTag.js"(exports, module) {
    var Symbol2 = require_Symbol();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var nativeObjectToString = objectProto.toString;
    var symToStringTag = Symbol2 ? Symbol2.toStringTag : void 0;
    function getRawTag(value) {
      var isOwn = hasOwnProperty.call(value, symToStringTag), tag = value[symToStringTag];
      try {
        value[symToStringTag] = void 0;
        var unmasked = true;
      } catch (e) {
      }
      var result = nativeObjectToString.call(value);
      if (unmasked) {
        if (isOwn) {
          value[symToStringTag] = tag;
        } else {
          delete value[symToStringTag];
        }
      }
      return result;
    }
    module.exports = getRawTag;
  }
});

// node_modules/lodash/_objectToString.js
var require_objectToString = __commonJS({
  "node_modules/lodash/_objectToString.js"(exports, module) {
    var objectProto = Object.prototype;
    var nativeObjectToString = objectProto.toString;
    function objectToString(value) {
      return nativeObjectToString.call(value);
    }
    module.exports = objectToString;
  }
});

// node_modules/lodash/_baseGetTag.js
var require_baseGetTag = __commonJS({
  "node_modules/lodash/_baseGetTag.js"(exports, module) {
    var Symbol2 = require_Symbol();
    var getRawTag = require_getRawTag();
    var objectToString = require_objectToString();
    var nullTag = "[object Null]";
    var undefinedTag = "[object Undefined]";
    var symToStringTag = Symbol2 ? Symbol2.toStringTag : void 0;
    function baseGetTag(value) {
      if (value == null) {
        return value === void 0 ? undefinedTag : nullTag;
      }
      return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);
    }
    module.exports = baseGetTag;
  }
});

// node_modules/lodash/isObject.js
var require_isObject = __commonJS({
  "node_modules/lodash/isObject.js"(exports, module) {
    function isObject(value) {
      var type = typeof value;
      return value != null && (type == "object" || type == "function");
    }
    module.exports = isObject;
  }
});

// node_modules/lodash/isFunction.js
var require_isFunction = __commonJS({
  "node_modules/lodash/isFunction.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObject = require_isObject();
    var asyncTag = "[object AsyncFunction]";
    var funcTag = "[object Function]";
    var genTag = "[object GeneratorFunction]";
    var proxyTag = "[object Proxy]";
    function isFunction(value) {
      if (!isObject(value)) {
        return false;
      }
      var tag = baseGetTag(value);
      return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;
    }
    module.exports = isFunction;
  }
});

// node_modules/lodash/_coreJsData.js
var require_coreJsData = __commonJS({
  "node_modules/lodash/_coreJsData.js"(exports, module) {
    var root = require_root();
    var coreJsData = root["__core-js_shared__"];
    module.exports = coreJsData;
  }
});

// node_modules/lodash/_isMasked.js
var require_isMasked = __commonJS({
  "node_modules/lodash/_isMasked.js"(exports, module) {
    var coreJsData = require_coreJsData();
    var maskSrcKey = function() {
      var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || "");
      return uid ? "Symbol(src)_1." + uid : "";
    }();
    function isMasked(func) {
      return !!maskSrcKey && maskSrcKey in func;
    }
    module.exports = isMasked;
  }
});

// node_modules/lodash/_toSource.js
var require_toSource = __commonJS({
  "node_modules/lodash/_toSource.js"(exports, module) {
    var funcProto = Function.prototype;
    var funcToString = funcProto.toString;
    function toSource(func) {
      if (func != null) {
        try {
          return funcToString.call(func);
        } catch (e) {
        }
        try {
          return func + "";
        } catch (e) {
        }
      }
      return "";
    }
    module.exports = toSource;
  }
});

// node_modules/lodash/_baseIsNative.js
var require_baseIsNative = __commonJS({
  "node_modules/lodash/_baseIsNative.js"(exports, module) {
    var isFunction = require_isFunction();
    var isMasked = require_isMasked();
    var isObject = require_isObject();
    var toSource = require_toSource();
    var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;
    var reIsHostCtor = /^\[object .+?Constructor\]$/;
    var funcProto = Function.prototype;
    var objectProto = Object.prototype;
    var funcToString = funcProto.toString;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var reIsNative = RegExp(
      "^" + funcToString.call(hasOwnProperty).replace(reRegExpChar, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"
    );
    function baseIsNative(value) {
      if (!isObject(value) || isMasked(value)) {
        return false;
      }
      var pattern = isFunction(value) ? reIsNative : reIsHostCtor;
      return pattern.test(toSource(value));
    }
    module.exports = baseIsNative;
  }
});

// node_modules/lodash/_getValue.js
var require_getValue = __commonJS({
  "node_modules/lodash/_getValue.js"(exports, module) {
    function getValue(object, key) {
      return object == null ? void 0 : object[key];
    }
    module.exports = getValue;
  }
});

// node_modules/lodash/_getNative.js
var require_getNative = __commonJS({
  "node_modules/lodash/_getNative.js"(exports, module) {
    var baseIsNative = require_baseIsNative();
    var getValue = require_getValue();
    function getNative(object, key) {
      var value = getValue(object, key);
      return baseIsNative(value) ? value : void 0;
    }
    module.exports = getNative;
  }
});

// node_modules/lodash/_Map.js
var require_Map = __commonJS({
  "node_modules/lodash/_Map.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var Map2 = getNative(root, "Map");
    module.exports = Map2;
  }
});

// node_modules/lodash/_nativeCreate.js
var require_nativeCreate = __commonJS({
  "node_modules/lodash/_nativeCreate.js"(exports, module) {
    var getNative = require_getNative();
    var nativeCreate = getNative(Object, "create");
    module.exports = nativeCreate;
  }
});

// node_modules/lodash/_hashClear.js
var require_hashClear = __commonJS({
  "node_modules/lodash/_hashClear.js"(exports, module) {
    var nativeCreate = require_nativeCreate();
    function hashClear() {
      this.__data__ = nativeCreate ? nativeCreate(null) : {};
      this.size = 0;
    }
    module.exports = hashClear;
  }
});

// node_modules/lodash/_hashDelete.js
var require_hashDelete = __commonJS({
  "node_modules/lodash/_hashDelete.js"(exports, module) {
    function hashDelete(key) {
      var result = this.has(key) && delete this.__data__[key];
      this.size -= result ? 1 : 0;
      return result;
    }
    module.exports = hashDelete;
  }
});

// node_modules/lodash/_hashGet.js
var require_hashGet = __commonJS({
  "node_modules/lodash/_hashGet.js"(exports, module) {
    var nativeCreate = require_nativeCreate();
    var HASH_UNDEFINED = "__lodash_hash_undefined__";
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function hashGet(key) {
      var data = this.__data__;
      if (nativeCreate) {
        var result = data[key];
        return result === HASH_UNDEFINED ? void 0 : result;
      }
      return hasOwnProperty.call(data, key) ? data[key] : void 0;
    }
    module.exports = hashGet;
  }
});

// node_modules/lodash/_hashHas.js
var require_hashHas = __commonJS({
  "node_modules/lodash/_hashHas.js"(exports, module) {
    var nativeCreate = require_nativeCreate();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function hashHas(key) {
      var data = this.__data__;
      return nativeCreate ? data[key] !== void 0 : hasOwnProperty.call(data, key);
    }
    module.exports = hashHas;
  }
});

// node_modules/lodash/_hashSet.js
var require_hashSet = __commonJS({
  "node_modules/lodash/_hashSet.js"(exports, module) {
    var nativeCreate = require_nativeCreate();
    var HASH_UNDEFINED = "__lodash_hash_undefined__";
    function hashSet(key, value) {
      var data = this.__data__;
      this.size += this.has(key) ? 0 : 1;
      data[key] = nativeCreate && value === void 0 ? HASH_UNDEFINED : value;
      return this;
    }
    module.exports = hashSet;
  }
});

// node_modules/lodash/_Hash.js
var require_Hash = __commonJS({
  "node_modules/lodash/_Hash.js"(exports, module) {
    var hashClear = require_hashClear();
    var hashDelete = require_hashDelete();
    var hashGet = require_hashGet();
    var hashHas = require_hashHas();
    var hashSet = require_hashSet();
    function Hash(entries) {
      var index = -1, length = entries == null ? 0 : entries.length;
      this.clear();
      while (++index < length) {
        var entry = entries[index];
        this.set(entry[0], entry[1]);
      }
    }
    Hash.prototype.clear = hashClear;
    Hash.prototype["delete"] = hashDelete;
    Hash.prototype.get = hashGet;
    Hash.prototype.has = hashHas;
    Hash.prototype.set = hashSet;
    module.exports = Hash;
  }
});

// node_modules/lodash/_mapCacheClear.js
var require_mapCacheClear = __commonJS({
  "node_modules/lodash/_mapCacheClear.js"(exports, module) {
    var Hash = require_Hash();
    var ListCache = require_ListCache();
    var Map2 = require_Map();
    function mapCacheClear() {
      this.size = 0;
      this.__data__ = {
        "hash": new Hash(),
        "map": new (Map2 || ListCache)(),
        "string": new Hash()
      };
    }
    module.exports = mapCacheClear;
  }
});

// node_modules/lodash/_isKeyable.js
var require_isKeyable = __commonJS({
  "node_modules/lodash/_isKeyable.js"(exports, module) {
    function isKeyable(value) {
      var type = typeof value;
      return type == "string" || type == "number" || type == "symbol" || type == "boolean" ? value !== "__proto__" : value === null;
    }
    module.exports = isKeyable;
  }
});

// node_modules/lodash/_getMapData.js
var require_getMapData = __commonJS({
  "node_modules/lodash/_getMapData.js"(exports, module) {
    var isKeyable = require_isKeyable();
    function getMapData(map, key) {
      var data = map.__data__;
      return isKeyable(key) ? data[typeof key == "string" ? "string" : "hash"] : data.map;
    }
    module.exports = getMapData;
  }
});

// node_modules/lodash/_mapCacheDelete.js
var require_mapCacheDelete = __commonJS({
  "node_modules/lodash/_mapCacheDelete.js"(exports, module) {
    var getMapData = require_getMapData();
    function mapCacheDelete(key) {
      var result = getMapData(this, key)["delete"](key);
      this.size -= result ? 1 : 0;
      return result;
    }
    module.exports = mapCacheDelete;
  }
});

// node_modules/lodash/_mapCacheGet.js
var require_mapCacheGet = __commonJS({
  "node_modules/lodash/_mapCacheGet.js"(exports, module) {
    var getMapData = require_getMapData();
    function mapCacheGet(key) {
      return getMapData(this, key).get(key);
    }
    module.exports = mapCacheGet;
  }
});

// node_modules/lodash/_mapCacheHas.js
var require_mapCacheHas = __commonJS({
  "node_modules/lodash/_mapCacheHas.js"(exports, module) {
    var getMapData = require_getMapData();
    function mapCacheHas(key) {
      return getMapData(this, key).has(key);
    }
    module.exports = mapCacheHas;
  }
});

// node_modules/lodash/_mapCacheSet.js
var require_mapCacheSet = __commonJS({
  "node_modules/lodash/_mapCacheSet.js"(exports, module) {
    var getMapData = require_getMapData();
    function mapCacheSet(key, value) {
      var data = getMapData(this, key), size = data.size;
      data.set(key, value);
      this.size += data.size == size ? 0 : 1;
      return this;
    }
    module.exports = mapCacheSet;
  }
});

// node_modules/lodash/_MapCache.js
var require_MapCache = __commonJS({
  "node_modules/lodash/_MapCache.js"(exports, module) {
    var mapCacheClear = require_mapCacheClear();
    var mapCacheDelete = require_mapCacheDelete();
    var mapCacheGet = require_mapCacheGet();
    var mapCacheHas = require_mapCacheHas();
    var mapCacheSet = require_mapCacheSet();
    function MapCache(entries) {
      var index = -1, length = entries == null ? 0 : entries.length;
      this.clear();
      while (++index < length) {
        var entry = entries[index];
        this.set(entry[0], entry[1]);
      }
    }
    MapCache.prototype.clear = mapCacheClear;
    MapCache.prototype["delete"] = mapCacheDelete;
    MapCache.prototype.get = mapCacheGet;
    MapCache.prototype.has = mapCacheHas;
    MapCache.prototype.set = mapCacheSet;
    module.exports = MapCache;
  }
});

// node_modules/lodash/_stackSet.js
var require_stackSet = __commonJS({
  "node_modules/lodash/_stackSet.js"(exports, module) {
    var ListCache = require_ListCache();
    var Map2 = require_Map();
    var MapCache = require_MapCache();
    var LARGE_ARRAY_SIZE = 200;
    function stackSet(key, value) {
      var data = this.__data__;
      if (data instanceof ListCache) {
        var pairs = data.__data__;
        if (!Map2 || pairs.length < LARGE_ARRAY_SIZE - 1) {
          pairs.push([key, value]);
          this.size = ++data.size;
          return this;
        }
        data = this.__data__ = new MapCache(pairs);
      }
      data.set(key, value);
      this.size = data.size;
      return this;
    }
    module.exports = stackSet;
  }
});

// node_modules/lodash/_Stack.js
var require_Stack = __commonJS({
  "node_modules/lodash/_Stack.js"(exports, module) {
    var ListCache = require_ListCache();
    var stackClear = require_stackClear();
    var stackDelete = require_stackDelete();
    var stackGet = require_stackGet();
    var stackHas = require_stackHas();
    var stackSet = require_stackSet();
    function Stack(entries) {
      var data = this.__data__ = new ListCache(entries);
      this.size = data.size;
    }
    Stack.prototype.clear = stackClear;
    Stack.prototype["delete"] = stackDelete;
    Stack.prototype.get = stackGet;
    Stack.prototype.has = stackHas;
    Stack.prototype.set = stackSet;
    module.exports = Stack;
  }
});

// node_modules/lodash/_setCacheAdd.js
var require_setCacheAdd = __commonJS({
  "node_modules/lodash/_setCacheAdd.js"(exports, module) {
    var HASH_UNDEFINED = "__lodash_hash_undefined__";
    function setCacheAdd(value) {
      this.__data__.set(value, HASH_UNDEFINED);
      return this;
    }
    module.exports = setCacheAdd;
  }
});

// node_modules/lodash/_setCacheHas.js
var require_setCacheHas = __commonJS({
  "node_modules/lodash/_setCacheHas.js"(exports, module) {
    function setCacheHas(value) {
      return this.__data__.has(value);
    }
    module.exports = setCacheHas;
  }
});

// node_modules/lodash/_SetCache.js
var require_SetCache = __commonJS({
  "node_modules/lodash/_SetCache.js"(exports, module) {
    var MapCache = require_MapCache();
    var setCacheAdd = require_setCacheAdd();
    var setCacheHas = require_setCacheHas();
    function SetCache(values) {
      var index = -1, length = values == null ? 0 : values.length;
      this.__data__ = new MapCache();
      while (++index < length) {
        this.add(values[index]);
      }
    }
    SetCache.prototype.add = SetCache.prototype.push = setCacheAdd;
    SetCache.prototype.has = setCacheHas;
    module.exports = SetCache;
  }
});

// node_modules/lodash/_arraySome.js
var require_arraySome = __commonJS({
  "node_modules/lodash/_arraySome.js"(exports, module) {
    function arraySome(array, predicate) {
      var index = -1, length = array == null ? 0 : array.length;
      while (++index < length) {
        if (predicate(array[index], index, array)) {
          return true;
        }
      }
      return false;
    }
    module.exports = arraySome;
  }
});

// node_modules/lodash/_cacheHas.js
var require_cacheHas = __commonJS({
  "node_modules/lodash/_cacheHas.js"(exports, module) {
    function cacheHas(cache, key) {
      return cache.has(key);
    }
    module.exports = cacheHas;
  }
});

// node_modules/lodash/_equalArrays.js
var require_equalArrays = __commonJS({
  "node_modules/lodash/_equalArrays.js"(exports, module) {
    var SetCache = require_SetCache();
    var arraySome = require_arraySome();
    var cacheHas = require_cacheHas();
    var COMPARE_PARTIAL_FLAG = 1;
    var COMPARE_UNORDERED_FLAG = 2;
    function equalArrays(array, other, bitmask, customizer, equalFunc, stack) {
      var isPartial = bitmask & COMPARE_PARTIAL_FLAG, arrLength = array.length, othLength = other.length;
      if (arrLength != othLength && !(isPartial && othLength > arrLength)) {
        return false;
      }
      var arrStacked = stack.get(array);
      var othStacked = stack.get(other);
      if (arrStacked && othStacked) {
        return arrStacked == other && othStacked == array;
      }
      var index = -1, result = true, seen = bitmask & COMPARE_UNORDERED_FLAG ? new SetCache() : void 0;
      stack.set(array, other);
      stack.set(other, array);
      while (++index < arrLength) {
        var arrValue = array[index], othValue = other[index];
        if (customizer) {
          var compared = isPartial ? customizer(othValue, arrValue, index, other, array, stack) : customizer(arrValue, othValue, index, array, other, stack);
        }
        if (compared !== void 0) {
          if (compared) {
            continue;
          }
          result = false;
          break;
        }
        if (seen) {
          if (!arraySome(other, function(othValue2, othIndex) {
            if (!cacheHas(seen, othIndex) && (arrValue === othValue2 || equalFunc(arrValue, othValue2, bitmask, customizer, stack))) {
              return seen.push(othIndex);
            }
          })) {
            result = false;
            break;
          }
        } else if (!(arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {
          result = false;
          break;
        }
      }
      stack["delete"](array);
      stack["delete"](other);
      return result;
    }
    module.exports = equalArrays;
  }
});

// node_modules/lodash/_Uint8Array.js
var require_Uint8Array = __commonJS({
  "node_modules/lodash/_Uint8Array.js"(exports, module) {
    var root = require_root();
    var Uint8Array2 = root.Uint8Array;
    module.exports = Uint8Array2;
  }
});

// node_modules/lodash/_mapToArray.js
var require_mapToArray = __commonJS({
  "node_modules/lodash/_mapToArray.js"(exports, module) {
    function mapToArray(map) {
      var index = -1, result = Array(map.size);
      map.forEach(function(value, key) {
        result[++index] = [key, value];
      });
      return result;
    }
    module.exports = mapToArray;
  }
});

// node_modules/lodash/_setToArray.js
var require_setToArray = __commonJS({
  "node_modules/lodash/_setToArray.js"(exports, module) {
    function setToArray(set) {
      var index = -1, result = Array(set.size);
      set.forEach(function(value) {
        result[++index] = value;
      });
      return result;
    }
    module.exports = setToArray;
  }
});

// node_modules/lodash/_equalByTag.js
var require_equalByTag = __commonJS({
  "node_modules/lodash/_equalByTag.js"(exports, module) {
    var Symbol2 = require_Symbol();
    var Uint8Array2 = require_Uint8Array();
    var eq = require_eq();
    var equalArrays = require_equalArrays();
    var mapToArray = require_mapToArray();
    var setToArray = require_setToArray();
    var COMPARE_PARTIAL_FLAG = 1;
    var COMPARE_UNORDERED_FLAG = 2;
    var boolTag = "[object Boolean]";
    var dateTag = "[object Date]";
    var errorTag = "[object Error]";
    var mapTag = "[object Map]";
    var numberTag = "[object Number]";
    var regexpTag = "[object RegExp]";
    var setTag = "[object Set]";
    var stringTag = "[object String]";
    var symbolTag = "[object Symbol]";
    var arrayBufferTag = "[object ArrayBuffer]";
    var dataViewTag = "[object DataView]";
    var symbolProto = Symbol2 ? Symbol2.prototype : void 0;
    var symbolValueOf = symbolProto ? symbolProto.valueOf : void 0;
    function equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {
      switch (tag) {
        case dataViewTag:
          if (object.byteLength != other.byteLength || object.byteOffset != other.byteOffset) {
            return false;
          }
          object = object.buffer;
          other = other.buffer;
        case arrayBufferTag:
          if (object.byteLength != other.byteLength || !equalFunc(new Uint8Array2(object), new Uint8Array2(other))) {
            return false;
          }
          return true;
        case boolTag:
        case dateTag:
        case numberTag:
          return eq(+object, +other);
        case errorTag:
          return object.name == other.name && object.message == other.message;
        case regexpTag:
        case stringTag:
          return object == other + "";
        case mapTag:
          var convert = mapToArray;
        case setTag:
          var isPartial = bitmask & COMPARE_PARTIAL_FLAG;
          convert || (convert = setToArray);
          if (object.size != other.size && !isPartial) {
            return false;
          }
          var stacked = stack.get(object);
          if (stacked) {
            return stacked == other;
          }
          bitmask |= COMPARE_UNORDERED_FLAG;
          stack.set(object, other);
          var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);
          stack["delete"](object);
          return result;
        case symbolTag:
          if (symbolValueOf) {
            return symbolValueOf.call(object) == symbolValueOf.call(other);
          }
      }
      return false;
    }
    module.exports = equalByTag;
  }
});

// node_modules/lodash/_arrayPush.js
var require_arrayPush = __commonJS({
  "node_modules/lodash/_arrayPush.js"(exports, module) {
    function arrayPush(array, values) {
      var index = -1, length = values.length, offset = array.length;
      while (++index < length) {
        array[offset + index] = values[index];
      }
      return array;
    }
    module.exports = arrayPush;
  }
});

// node_modules/lodash/isArray.js
var require_isArray = __commonJS({
  "node_modules/lodash/isArray.js"(exports, module) {
    var isArray = Array.isArray;
    module.exports = isArray;
  }
});

// node_modules/lodash/_baseGetAllKeys.js
var require_baseGetAllKeys = __commonJS({
  "node_modules/lodash/_baseGetAllKeys.js"(exports, module) {
    var arrayPush = require_arrayPush();
    var isArray = require_isArray();
    function baseGetAllKeys(object, keysFunc, symbolsFunc) {
      var result = keysFunc(object);
      return isArray(object) ? result : arrayPush(result, symbolsFunc(object));
    }
    module.exports = baseGetAllKeys;
  }
});

// node_modules/lodash/_arrayFilter.js
var require_arrayFilter = __commonJS({
  "node_modules/lodash/_arrayFilter.js"(exports, module) {
    function arrayFilter(array, predicate) {
      var index = -1, length = array == null ? 0 : array.length, resIndex = 0, result = [];
      while (++index < length) {
        var value = array[index];
        if (predicate(value, index, array)) {
          result[resIndex++] = value;
        }
      }
      return result;
    }
    module.exports = arrayFilter;
  }
});

// node_modules/lodash/stubArray.js
var require_stubArray = __commonJS({
  "node_modules/lodash/stubArray.js"(exports, module) {
    function stubArray() {
      return [];
    }
    module.exports = stubArray;
  }
});

// node_modules/lodash/_getSymbols.js
var require_getSymbols = __commonJS({
  "node_modules/lodash/_getSymbols.js"(exports, module) {
    var arrayFilter = require_arrayFilter();
    var stubArray = require_stubArray();
    var objectProto = Object.prototype;
    var propertyIsEnumerable = objectProto.propertyIsEnumerable;
    var nativeGetSymbols = Object.getOwnPropertySymbols;
    var getSymbols = !nativeGetSymbols ? stubArray : function(object) {
      if (object == null) {
        return [];
      }
      object = Object(object);
      return arrayFilter(nativeGetSymbols(object), function(symbol) {
        return propertyIsEnumerable.call(object, symbol);
      });
    };
    module.exports = getSymbols;
  }
});

// node_modules/lodash/_baseTimes.js
var require_baseTimes = __commonJS({
  "node_modules/lodash/_baseTimes.js"(exports, module) {
    function baseTimes(n2, iteratee) {
      var index = -1, result = Array(n2);
      while (++index < n2) {
        result[index] = iteratee(index);
      }
      return result;
    }
    module.exports = baseTimes;
  }
});

// node_modules/lodash/isObjectLike.js
var require_isObjectLike = __commonJS({
  "node_modules/lodash/isObjectLike.js"(exports, module) {
    function isObjectLike(value) {
      return value != null && typeof value == "object";
    }
    module.exports = isObjectLike;
  }
});

// node_modules/lodash/_baseIsArguments.js
var require_baseIsArguments = __commonJS({
  "node_modules/lodash/_baseIsArguments.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var argsTag = "[object Arguments]";
    function baseIsArguments(value) {
      return isObjectLike(value) && baseGetTag(value) == argsTag;
    }
    module.exports = baseIsArguments;
  }
});

// node_modules/lodash/isArguments.js
var require_isArguments = __commonJS({
  "node_modules/lodash/isArguments.js"(exports, module) {
    var baseIsArguments = require_baseIsArguments();
    var isObjectLike = require_isObjectLike();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var propertyIsEnumerable = objectProto.propertyIsEnumerable;
    var isArguments = baseIsArguments(function() {
      return arguments;
    }()) ? baseIsArguments : function(value) {
      return isObjectLike(value) && hasOwnProperty.call(value, "callee") && !propertyIsEnumerable.call(value, "callee");
    };
    module.exports = isArguments;
  }
});

// node_modules/lodash/stubFalse.js
var require_stubFalse = __commonJS({
  "node_modules/lodash/stubFalse.js"(exports, module) {
    function stubFalse() {
      return false;
    }
    module.exports = stubFalse;
  }
});

// node_modules/lodash/isBuffer.js
var require_isBuffer = __commonJS({
  "node_modules/lodash/isBuffer.js"(exports, module) {
    var root = require_root();
    var stubFalse = require_stubFalse();
    var freeExports = typeof exports == "object" && exports && !exports.nodeType && exports;
    var freeModule = freeExports && typeof module == "object" && module && !module.nodeType && module;
    var moduleExports = freeModule && freeModule.exports === freeExports;
    var Buffer = moduleExports ? root.Buffer : void 0;
    var nativeIsBuffer = Buffer ? Buffer.isBuffer : void 0;
    var isBuffer = nativeIsBuffer || stubFalse;
    module.exports = isBuffer;
  }
});

// node_modules/lodash/_isIndex.js
var require_isIndex = __commonJS({
  "node_modules/lodash/_isIndex.js"(exports, module) {
    var MAX_SAFE_INTEGER = 9007199254740991;
    var reIsUint = /^(?:0|[1-9]\d*)$/;
    function isIndex(value, length) {
      var type = typeof value;
      length = length == null ? MAX_SAFE_INTEGER : length;
      return !!length && (type == "number" || type != "symbol" && reIsUint.test(value)) && (value > -1 && value % 1 == 0 && value < length);
    }
    module.exports = isIndex;
  }
});

// node_modules/lodash/isLength.js
var require_isLength = __commonJS({
  "node_modules/lodash/isLength.js"(exports, module) {
    var MAX_SAFE_INTEGER = 9007199254740991;
    function isLength(value) {
      return typeof value == "number" && value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;
    }
    module.exports = isLength;
  }
});

// node_modules/lodash/_baseIsTypedArray.js
var require_baseIsTypedArray = __commonJS({
  "node_modules/lodash/_baseIsTypedArray.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isLength = require_isLength();
    var isObjectLike = require_isObjectLike();
    var argsTag = "[object Arguments]";
    var arrayTag = "[object Array]";
    var boolTag = "[object Boolean]";
    var dateTag = "[object Date]";
    var errorTag = "[object Error]";
    var funcTag = "[object Function]";
    var mapTag = "[object Map]";
    var numberTag = "[object Number]";
    var objectTag = "[object Object]";
    var regexpTag = "[object RegExp]";
    var setTag = "[object Set]";
    var stringTag = "[object String]";
    var weakMapTag = "[object WeakMap]";
    var arrayBufferTag = "[object ArrayBuffer]";
    var dataViewTag = "[object DataView]";
    var float32Tag = "[object Float32Array]";
    var float64Tag = "[object Float64Array]";
    var int8Tag = "[object Int8Array]";
    var int16Tag = "[object Int16Array]";
    var int32Tag = "[object Int32Array]";
    var uint8Tag = "[object Uint8Array]";
    var uint8ClampedTag = "[object Uint8ClampedArray]";
    var uint16Tag = "[object Uint16Array]";
    var uint32Tag = "[object Uint32Array]";
    var typedArrayTags = {};
    typedArrayTags[float32Tag] = typedArrayTags[float64Tag] = typedArrayTags[int8Tag] = typedArrayTags[int16Tag] = typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] = typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] = typedArrayTags[uint32Tag] = true;
    typedArrayTags[argsTag] = typedArrayTags[arrayTag] = typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] = typedArrayTags[dataViewTag] = typedArrayTags[dateTag] = typedArrayTags[errorTag] = typedArrayTags[funcTag] = typedArrayTags[mapTag] = typedArrayTags[numberTag] = typedArrayTags[objectTag] = typedArrayTags[regexpTag] = typedArrayTags[setTag] = typedArrayTags[stringTag] = typedArrayTags[weakMapTag] = false;
    function baseIsTypedArray(value) {
      return isObjectLike(value) && isLength(value.length) && !!typedArrayTags[baseGetTag(value)];
    }
    module.exports = baseIsTypedArray;
  }
});

// node_modules/lodash/_baseUnary.js
var require_baseUnary = __commonJS({
  "node_modules/lodash/_baseUnary.js"(exports, module) {
    function baseUnary(func) {
      return function(value) {
        return func(value);
      };
    }
    module.exports = baseUnary;
  }
});

// node_modules/lodash/_nodeUtil.js
var require_nodeUtil = __commonJS({
  "node_modules/lodash/_nodeUtil.js"(exports, module) {
    var freeGlobal = require_freeGlobal();
    var freeExports = typeof exports == "object" && exports && !exports.nodeType && exports;
    var freeModule = freeExports && typeof module == "object" && module && !module.nodeType && module;
    var moduleExports = freeModule && freeModule.exports === freeExports;
    var freeProcess = moduleExports && freeGlobal.process;
    var nodeUtil = function() {
      try {
        var types = freeModule && freeModule.require && freeModule.require("util").types;
        if (types) {
          return types;
        }
        return freeProcess && freeProcess.binding && freeProcess.binding("util");
      } catch (e) {
      }
    }();
    module.exports = nodeUtil;
  }
});

// node_modules/lodash/isTypedArray.js
var require_isTypedArray = __commonJS({
  "node_modules/lodash/isTypedArray.js"(exports, module) {
    var baseIsTypedArray = require_baseIsTypedArray();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsTypedArray = nodeUtil && nodeUtil.isTypedArray;
    var isTypedArray = nodeIsTypedArray ? baseUnary(nodeIsTypedArray) : baseIsTypedArray;
    module.exports = isTypedArray;
  }
});

// node_modules/lodash/_arrayLikeKeys.js
var require_arrayLikeKeys = __commonJS({
  "node_modules/lodash/_arrayLikeKeys.js"(exports, module) {
    var baseTimes = require_baseTimes();
    var isArguments = require_isArguments();
    var isArray = require_isArray();
    var isBuffer = require_isBuffer();
    var isIndex = require_isIndex();
    var isTypedArray = require_isTypedArray();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function arrayLikeKeys(value, inherited) {
      var isArr = isArray(value), isArg = !isArr && isArguments(value), isBuff = !isArr && !isArg && isBuffer(value), isType = !isArr && !isArg && !isBuff && isTypedArray(value), skipIndexes = isArr || isArg || isBuff || isType, result = skipIndexes ? baseTimes(value.length, String) : [], length = result.length;
      for (var key in value) {
        if ((inherited || hasOwnProperty.call(value, key)) && !(skipIndexes && // Safari 9 has enumerable `arguments.length` in strict mode.
        (key == "length" || // Node.js 0.10 has enumerable non-index properties on buffers.
        isBuff && (key == "offset" || key == "parent") || // PhantomJS 2 has enumerable non-index properties on typed arrays.
        isType && (key == "buffer" || key == "byteLength" || key == "byteOffset") || // Skip index properties.
        isIndex(key, length)))) {
          result.push(key);
        }
      }
      return result;
    }
    module.exports = arrayLikeKeys;
  }
});

// node_modules/lodash/_isPrototype.js
var require_isPrototype = __commonJS({
  "node_modules/lodash/_isPrototype.js"(exports, module) {
    var objectProto = Object.prototype;
    function isPrototype(value) {
      var Ctor = value && value.constructor, proto = typeof Ctor == "function" && Ctor.prototype || objectProto;
      return value === proto;
    }
    module.exports = isPrototype;
  }
});

// node_modules/lodash/_overArg.js
var require_overArg = __commonJS({
  "node_modules/lodash/_overArg.js"(exports, module) {
    function overArg(func, transform) {
      return function(arg) {
        return func(transform(arg));
      };
    }
    module.exports = overArg;
  }
});

// node_modules/lodash/_nativeKeys.js
var require_nativeKeys = __commonJS({
  "node_modules/lodash/_nativeKeys.js"(exports, module) {
    var overArg = require_overArg();
    var nativeKeys = overArg(Object.keys, Object);
    module.exports = nativeKeys;
  }
});

// node_modules/lodash/_baseKeys.js
var require_baseKeys = __commonJS({
  "node_modules/lodash/_baseKeys.js"(exports, module) {
    var isPrototype = require_isPrototype();
    var nativeKeys = require_nativeKeys();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function baseKeys(object) {
      if (!isPrototype(object)) {
        return nativeKeys(object);
      }
      var result = [];
      for (var key in Object(object)) {
        if (hasOwnProperty.call(object, key) && key != "constructor") {
          result.push(key);
        }
      }
      return result;
    }
    module.exports = baseKeys;
  }
});

// node_modules/lodash/isArrayLike.js
var require_isArrayLike = __commonJS({
  "node_modules/lodash/isArrayLike.js"(exports, module) {
    var isFunction = require_isFunction();
    var isLength = require_isLength();
    function isArrayLike(value) {
      return value != null && isLength(value.length) && !isFunction(value);
    }
    module.exports = isArrayLike;
  }
});

// node_modules/lodash/keys.js
var require_keys = __commonJS({
  "node_modules/lodash/keys.js"(exports, module) {
    var arrayLikeKeys = require_arrayLikeKeys();
    var baseKeys = require_baseKeys();
    var isArrayLike = require_isArrayLike();
    function keys(object) {
      return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);
    }
    module.exports = keys;
  }
});

// node_modules/lodash/_getAllKeys.js
var require_getAllKeys = __commonJS({
  "node_modules/lodash/_getAllKeys.js"(exports, module) {
    var baseGetAllKeys = require_baseGetAllKeys();
    var getSymbols = require_getSymbols();
    var keys = require_keys();
    function getAllKeys(object) {
      return baseGetAllKeys(object, keys, getSymbols);
    }
    module.exports = getAllKeys;
  }
});

// node_modules/lodash/_equalObjects.js
var require_equalObjects = __commonJS({
  "node_modules/lodash/_equalObjects.js"(exports, module) {
    var getAllKeys = require_getAllKeys();
    var COMPARE_PARTIAL_FLAG = 1;
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function equalObjects(object, other, bitmask, customizer, equalFunc, stack) {
      var isPartial = bitmask & COMPARE_PARTIAL_FLAG, objProps = getAllKeys(object), objLength = objProps.length, othProps = getAllKeys(other), othLength = othProps.length;
      if (objLength != othLength && !isPartial) {
        return false;
      }
      var index = objLength;
      while (index--) {
        var key = objProps[index];
        if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {
          return false;
        }
      }
      var objStacked = stack.get(object);
      var othStacked = stack.get(other);
      if (objStacked && othStacked) {
        return objStacked == other && othStacked == object;
      }
      var result = true;
      stack.set(object, other);
      stack.set(other, object);
      var skipCtor = isPartial;
      while (++index < objLength) {
        key = objProps[index];
        var objValue = object[key], othValue = other[key];
        if (customizer) {
          var compared = isPartial ? customizer(othValue, objValue, key, other, object, stack) : customizer(objValue, othValue, key, object, other, stack);
        }
        if (!(compared === void 0 ? objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack) : compared)) {
          result = false;
          break;
        }
        skipCtor || (skipCtor = key == "constructor");
      }
      if (result && !skipCtor) {
        var objCtor = object.constructor, othCtor = other.constructor;
        if (objCtor != othCtor && ("constructor" in object && "constructor" in other) && !(typeof objCtor == "function" && objCtor instanceof objCtor && typeof othCtor == "function" && othCtor instanceof othCtor)) {
          result = false;
        }
      }
      stack["delete"](object);
      stack["delete"](other);
      return result;
    }
    module.exports = equalObjects;
  }
});

// node_modules/lodash/_DataView.js
var require_DataView = __commonJS({
  "node_modules/lodash/_DataView.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var DataView = getNative(root, "DataView");
    module.exports = DataView;
  }
});

// node_modules/lodash/_Promise.js
var require_Promise = __commonJS({
  "node_modules/lodash/_Promise.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var Promise2 = getNative(root, "Promise");
    module.exports = Promise2;
  }
});

// node_modules/lodash/_Set.js
var require_Set = __commonJS({
  "node_modules/lodash/_Set.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var Set2 = getNative(root, "Set");
    module.exports = Set2;
  }
});

// node_modules/lodash/_WeakMap.js
var require_WeakMap = __commonJS({
  "node_modules/lodash/_WeakMap.js"(exports, module) {
    var getNative = require_getNative();
    var root = require_root();
    var WeakMap2 = getNative(root, "WeakMap");
    module.exports = WeakMap2;
  }
});

// node_modules/lodash/_getTag.js
var require_getTag = __commonJS({
  "node_modules/lodash/_getTag.js"(exports, module) {
    var DataView = require_DataView();
    var Map2 = require_Map();
    var Promise2 = require_Promise();
    var Set2 = require_Set();
    var WeakMap2 = require_WeakMap();
    var baseGetTag = require_baseGetTag();
    var toSource = require_toSource();
    var mapTag = "[object Map]";
    var objectTag = "[object Object]";
    var promiseTag = "[object Promise]";
    var setTag = "[object Set]";
    var weakMapTag = "[object WeakMap]";
    var dataViewTag = "[object DataView]";
    var dataViewCtorString = toSource(DataView);
    var mapCtorString = toSource(Map2);
    var promiseCtorString = toSource(Promise2);
    var setCtorString = toSource(Set2);
    var weakMapCtorString = toSource(WeakMap2);
    var getTag = baseGetTag;
    if (DataView && getTag(new DataView(new ArrayBuffer(1))) != dataViewTag || Map2 && getTag(new Map2()) != mapTag || Promise2 && getTag(Promise2.resolve()) != promiseTag || Set2 && getTag(new Set2()) != setTag || WeakMap2 && getTag(new WeakMap2()) != weakMapTag) {
      getTag = function(value) {
        var result = baseGetTag(value), Ctor = result == objectTag ? value.constructor : void 0, ctorString = Ctor ? toSource(Ctor) : "";
        if (ctorString) {
          switch (ctorString) {
            case dataViewCtorString:
              return dataViewTag;
            case mapCtorString:
              return mapTag;
            case promiseCtorString:
              return promiseTag;
            case setCtorString:
              return setTag;
            case weakMapCtorString:
              return weakMapTag;
          }
        }
        return result;
      };
    }
    module.exports = getTag;
  }
});

// node_modules/lodash/_baseIsEqualDeep.js
var require_baseIsEqualDeep = __commonJS({
  "node_modules/lodash/_baseIsEqualDeep.js"(exports, module) {
    var Stack = require_Stack();
    var equalArrays = require_equalArrays();
    var equalByTag = require_equalByTag();
    var equalObjects = require_equalObjects();
    var getTag = require_getTag();
    var isArray = require_isArray();
    var isBuffer = require_isBuffer();
    var isTypedArray = require_isTypedArray();
    var COMPARE_PARTIAL_FLAG = 1;
    var argsTag = "[object Arguments]";
    var arrayTag = "[object Array]";
    var objectTag = "[object Object]";
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {
      var objIsArr = isArray(object), othIsArr = isArray(other), objTag = objIsArr ? arrayTag : getTag(object), othTag = othIsArr ? arrayTag : getTag(other);
      objTag = objTag == argsTag ? objectTag : objTag;
      othTag = othTag == argsTag ? objectTag : othTag;
      var objIsObj = objTag == objectTag, othIsObj = othTag == objectTag, isSameTag = objTag == othTag;
      if (isSameTag && isBuffer(object)) {
        if (!isBuffer(other)) {
          return false;
        }
        objIsArr = true;
        objIsObj = false;
      }
      if (isSameTag && !objIsObj) {
        stack || (stack = new Stack());
        return objIsArr || isTypedArray(object) ? equalArrays(object, other, bitmask, customizer, equalFunc, stack) : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);
      }
      if (!(bitmask & COMPARE_PARTIAL_FLAG)) {
        var objIsWrapped = objIsObj && hasOwnProperty.call(object, "__wrapped__"), othIsWrapped = othIsObj && hasOwnProperty.call(other, "__wrapped__");
        if (objIsWrapped || othIsWrapped) {
          var objUnwrapped = objIsWrapped ? object.value() : object, othUnwrapped = othIsWrapped ? other.value() : other;
          stack || (stack = new Stack());
          return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);
        }
      }
      if (!isSameTag) {
        return false;
      }
      stack || (stack = new Stack());
      return equalObjects(object, other, bitmask, customizer, equalFunc, stack);
    }
    module.exports = baseIsEqualDeep;
  }
});

// node_modules/lodash/_baseIsEqual.js
var require_baseIsEqual = __commonJS({
  "node_modules/lodash/_baseIsEqual.js"(exports, module) {
    var baseIsEqualDeep = require_baseIsEqualDeep();
    var isObjectLike = require_isObjectLike();
    function baseIsEqual(value, other, bitmask, customizer, stack) {
      if (value === other) {
        return true;
      }
      if (value == null || other == null || !isObjectLike(value) && !isObjectLike(other)) {
        return value !== value && other !== other;
      }
      return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);
    }
    module.exports = baseIsEqual;
  }
});

// node_modules/lodash/isEqualWith.js
var require_isEqualWith = __commonJS({
  "node_modules/lodash/isEqualWith.js"(exports, module) {
    var baseIsEqual = require_baseIsEqual();
    function isEqualWith(value, other, customizer) {
      customizer = typeof customizer == "function" ? customizer : void 0;
      var result = customizer ? customizer(value, other) : void 0;
      return result === void 0 ? baseIsEqual(value, other, void 0, customizer) : !!result;
    }
    module.exports = isEqualWith;
  }
});

// node_modules/shallowequal/index.js
var require_shallowequal = __commonJS({
  "node_modules/shallowequal/index.js"(exports, module) {
    module.exports = function shallowEqual(objA, objB, compare, compareContext) {
      var ret = compare ? compare.call(compareContext, objA, objB) : void 0;
      if (ret !== void 0) {
        return !!ret;
      }
      if (objA === objB) {
        return true;
      }
      if (typeof objA !== "object" || !objA || typeof objB !== "object" || !objB) {
        return false;
      }
      var keysA = Object.keys(objA);
      var keysB = Object.keys(objB);
      if (keysA.length !== keysB.length) {
        return false;
      }
      var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);
      for (var idx = 0; idx < keysA.length; idx++) {
        var key = keysA[idx];
        if (!bHasOwnProperty(key)) {
          return false;
        }
        var valueA = objA[key];
        var valueB = objB[key];
        ret = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;
        if (ret === false || ret === void 0 && valueA !== valueB) {
          return false;
        }
      }
      return true;
    };
  }
});

// node_modules/lodash/_arrayEach.js
var require_arrayEach = __commonJS({
  "node_modules/lodash/_arrayEach.js"(exports, module) {
    function arrayEach(array, iteratee) {
      var index = -1, length = array == null ? 0 : array.length;
      while (++index < length) {
        if (iteratee(array[index], index, array) === false) {
          break;
        }
      }
      return array;
    }
    module.exports = arrayEach;
  }
});

// node_modules/lodash/_defineProperty.js
var require_defineProperty = __commonJS({
  "node_modules/lodash/_defineProperty.js"(exports, module) {
    var getNative = require_getNative();
    var defineProperty = function() {
      try {
        var func = getNative(Object, "defineProperty");
        func({}, "", {});
        return func;
      } catch (e) {
      }
    }();
    module.exports = defineProperty;
  }
});

// node_modules/lodash/_baseAssignValue.js
var require_baseAssignValue = __commonJS({
  "node_modules/lodash/_baseAssignValue.js"(exports, module) {
    var defineProperty = require_defineProperty();
    function baseAssignValue(object, key, value) {
      if (key == "__proto__" && defineProperty) {
        defineProperty(object, key, {
          "configurable": true,
          "enumerable": true,
          "value": value,
          "writable": true
        });
      } else {
        object[key] = value;
      }
    }
    module.exports = baseAssignValue;
  }
});

// node_modules/lodash/_assignValue.js
var require_assignValue = __commonJS({
  "node_modules/lodash/_assignValue.js"(exports, module) {
    var baseAssignValue = require_baseAssignValue();
    var eq = require_eq();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function assignValue(object, key, value) {
      var objValue = object[key];
      if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) || value === void 0 && !(key in object)) {
        baseAssignValue(object, key, value);
      }
    }
    module.exports = assignValue;
  }
});

// node_modules/lodash/_copyObject.js
var require_copyObject = __commonJS({
  "node_modules/lodash/_copyObject.js"(exports, module) {
    var assignValue = require_assignValue();
    var baseAssignValue = require_baseAssignValue();
    function copyObject(source, props, object, customizer) {
      var isNew = !object;
      object || (object = {});
      var index = -1, length = props.length;
      while (++index < length) {
        var key = props[index];
        var newValue = customizer ? customizer(object[key], source[key], key, object, source) : void 0;
        if (newValue === void 0) {
          newValue = source[key];
        }
        if (isNew) {
          baseAssignValue(object, key, newValue);
        } else {
          assignValue(object, key, newValue);
        }
      }
      return object;
    }
    module.exports = copyObject;
  }
});

// node_modules/lodash/_baseAssign.js
var require_baseAssign = __commonJS({
  "node_modules/lodash/_baseAssign.js"(exports, module) {
    var copyObject = require_copyObject();
    var keys = require_keys();
    function baseAssign(object, source) {
      return object && copyObject(source, keys(source), object);
    }
    module.exports = baseAssign;
  }
});

// node_modules/lodash/_nativeKeysIn.js
var require_nativeKeysIn = __commonJS({
  "node_modules/lodash/_nativeKeysIn.js"(exports, module) {
    function nativeKeysIn(object) {
      var result = [];
      if (object != null) {
        for (var key in Object(object)) {
          result.push(key);
        }
      }
      return result;
    }
    module.exports = nativeKeysIn;
  }
});

// node_modules/lodash/_baseKeysIn.js
var require_baseKeysIn = __commonJS({
  "node_modules/lodash/_baseKeysIn.js"(exports, module) {
    var isObject = require_isObject();
    var isPrototype = require_isPrototype();
    var nativeKeysIn = require_nativeKeysIn();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function baseKeysIn(object) {
      if (!isObject(object)) {
        return nativeKeysIn(object);
      }
      var isProto = isPrototype(object), result = [];
      for (var key in object) {
        if (!(key == "constructor" && (isProto || !hasOwnProperty.call(object, key)))) {
          result.push(key);
        }
      }
      return result;
    }
    module.exports = baseKeysIn;
  }
});

// node_modules/lodash/keysIn.js
var require_keysIn = __commonJS({
  "node_modules/lodash/keysIn.js"(exports, module) {
    var arrayLikeKeys = require_arrayLikeKeys();
    var baseKeysIn = require_baseKeysIn();
    var isArrayLike = require_isArrayLike();
    function keysIn(object) {
      return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);
    }
    module.exports = keysIn;
  }
});

// node_modules/lodash/_baseAssignIn.js
var require_baseAssignIn = __commonJS({
  "node_modules/lodash/_baseAssignIn.js"(exports, module) {
    var copyObject = require_copyObject();
    var keysIn = require_keysIn();
    function baseAssignIn(object, source) {
      return object && copyObject(source, keysIn(source), object);
    }
    module.exports = baseAssignIn;
  }
});

// node_modules/lodash/_cloneBuffer.js
var require_cloneBuffer = __commonJS({
  "node_modules/lodash/_cloneBuffer.js"(exports, module) {
    var root = require_root();
    var freeExports = typeof exports == "object" && exports && !exports.nodeType && exports;
    var freeModule = freeExports && typeof module == "object" && module && !module.nodeType && module;
    var moduleExports = freeModule && freeModule.exports === freeExports;
    var Buffer = moduleExports ? root.Buffer : void 0;
    var allocUnsafe = Buffer ? Buffer.allocUnsafe : void 0;
    function cloneBuffer(buffer, isDeep) {
      if (isDeep) {
        return buffer.slice();
      }
      var length = buffer.length, result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);
      buffer.copy(result);
      return result;
    }
    module.exports = cloneBuffer;
  }
});

// node_modules/lodash/_copyArray.js
var require_copyArray = __commonJS({
  "node_modules/lodash/_copyArray.js"(exports, module) {
    function copyArray(source, array) {
      var index = -1, length = source.length;
      array || (array = Array(length));
      while (++index < length) {
        array[index] = source[index];
      }
      return array;
    }
    module.exports = copyArray;
  }
});

// node_modules/lodash/_copySymbols.js
var require_copySymbols = __commonJS({
  "node_modules/lodash/_copySymbols.js"(exports, module) {
    var copyObject = require_copyObject();
    var getSymbols = require_getSymbols();
    function copySymbols(source, object) {
      return copyObject(source, getSymbols(source), object);
    }
    module.exports = copySymbols;
  }
});

// node_modules/lodash/_getPrototype.js
var require_getPrototype = __commonJS({
  "node_modules/lodash/_getPrototype.js"(exports, module) {
    var overArg = require_overArg();
    var getPrototype = overArg(Object.getPrototypeOf, Object);
    module.exports = getPrototype;
  }
});

// node_modules/lodash/_getSymbolsIn.js
var require_getSymbolsIn = __commonJS({
  "node_modules/lodash/_getSymbolsIn.js"(exports, module) {
    var arrayPush = require_arrayPush();
    var getPrototype = require_getPrototype();
    var getSymbols = require_getSymbols();
    var stubArray = require_stubArray();
    var nativeGetSymbols = Object.getOwnPropertySymbols;
    var getSymbolsIn = !nativeGetSymbols ? stubArray : function(object) {
      var result = [];
      while (object) {
        arrayPush(result, getSymbols(object));
        object = getPrototype(object);
      }
      return result;
    };
    module.exports = getSymbolsIn;
  }
});

// node_modules/lodash/_copySymbolsIn.js
var require_copySymbolsIn = __commonJS({
  "node_modules/lodash/_copySymbolsIn.js"(exports, module) {
    var copyObject = require_copyObject();
    var getSymbolsIn = require_getSymbolsIn();
    function copySymbolsIn(source, object) {
      return copyObject(source, getSymbolsIn(source), object);
    }
    module.exports = copySymbolsIn;
  }
});

// node_modules/lodash/_getAllKeysIn.js
var require_getAllKeysIn = __commonJS({
  "node_modules/lodash/_getAllKeysIn.js"(exports, module) {
    var baseGetAllKeys = require_baseGetAllKeys();
    var getSymbolsIn = require_getSymbolsIn();
    var keysIn = require_keysIn();
    function getAllKeysIn(object) {
      return baseGetAllKeys(object, keysIn, getSymbolsIn);
    }
    module.exports = getAllKeysIn;
  }
});

// node_modules/lodash/_initCloneArray.js
var require_initCloneArray = __commonJS({
  "node_modules/lodash/_initCloneArray.js"(exports, module) {
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    function initCloneArray(array) {
      var length = array.length, result = new array.constructor(length);
      if (length && typeof array[0] == "string" && hasOwnProperty.call(array, "index")) {
        result.index = array.index;
        result.input = array.input;
      }
      return result;
    }
    module.exports = initCloneArray;
  }
});

// node_modules/lodash/_cloneArrayBuffer.js
var require_cloneArrayBuffer = __commonJS({
  "node_modules/lodash/_cloneArrayBuffer.js"(exports, module) {
    var Uint8Array2 = require_Uint8Array();
    function cloneArrayBuffer(arrayBuffer) {
      var result = new arrayBuffer.constructor(arrayBuffer.byteLength);
      new Uint8Array2(result).set(new Uint8Array2(arrayBuffer));
      return result;
    }
    module.exports = cloneArrayBuffer;
  }
});

// node_modules/lodash/_cloneDataView.js
var require_cloneDataView = __commonJS({
  "node_modules/lodash/_cloneDataView.js"(exports, module) {
    var cloneArrayBuffer = require_cloneArrayBuffer();
    function cloneDataView(dataView, isDeep) {
      var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;
      return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);
    }
    module.exports = cloneDataView;
  }
});

// node_modules/lodash/_cloneRegExp.js
var require_cloneRegExp = __commonJS({
  "node_modules/lodash/_cloneRegExp.js"(exports, module) {
    var reFlags = /\w*$/;
    function cloneRegExp(regexp) {
      var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));
      result.lastIndex = regexp.lastIndex;
      return result;
    }
    module.exports = cloneRegExp;
  }
});

// node_modules/lodash/_cloneSymbol.js
var require_cloneSymbol = __commonJS({
  "node_modules/lodash/_cloneSymbol.js"(exports, module) {
    var Symbol2 = require_Symbol();
    var symbolProto = Symbol2 ? Symbol2.prototype : void 0;
    var symbolValueOf = symbolProto ? symbolProto.valueOf : void 0;
    function cloneSymbol(symbol) {
      return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};
    }
    module.exports = cloneSymbol;
  }
});

// node_modules/lodash/_cloneTypedArray.js
var require_cloneTypedArray = __commonJS({
  "node_modules/lodash/_cloneTypedArray.js"(exports, module) {
    var cloneArrayBuffer = require_cloneArrayBuffer();
    function cloneTypedArray(typedArray, isDeep) {
      var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;
      return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);
    }
    module.exports = cloneTypedArray;
  }
});

// node_modules/lodash/_initCloneByTag.js
var require_initCloneByTag = __commonJS({
  "node_modules/lodash/_initCloneByTag.js"(exports, module) {
    var cloneArrayBuffer = require_cloneArrayBuffer();
    var cloneDataView = require_cloneDataView();
    var cloneRegExp = require_cloneRegExp();
    var cloneSymbol = require_cloneSymbol();
    var cloneTypedArray = require_cloneTypedArray();
    var boolTag = "[object Boolean]";
    var dateTag = "[object Date]";
    var mapTag = "[object Map]";
    var numberTag = "[object Number]";
    var regexpTag = "[object RegExp]";
    var setTag = "[object Set]";
    var stringTag = "[object String]";
    var symbolTag = "[object Symbol]";
    var arrayBufferTag = "[object ArrayBuffer]";
    var dataViewTag = "[object DataView]";
    var float32Tag = "[object Float32Array]";
    var float64Tag = "[object Float64Array]";
    var int8Tag = "[object Int8Array]";
    var int16Tag = "[object Int16Array]";
    var int32Tag = "[object Int32Array]";
    var uint8Tag = "[object Uint8Array]";
    var uint8ClampedTag = "[object Uint8ClampedArray]";
    var uint16Tag = "[object Uint16Array]";
    var uint32Tag = "[object Uint32Array]";
    function initCloneByTag(object, tag, isDeep) {
      var Ctor = object.constructor;
      switch (tag) {
        case arrayBufferTag:
          return cloneArrayBuffer(object);
        case boolTag:
        case dateTag:
          return new Ctor(+object);
        case dataViewTag:
          return cloneDataView(object, isDeep);
        case float32Tag:
        case float64Tag:
        case int8Tag:
        case int16Tag:
        case int32Tag:
        case uint8Tag:
        case uint8ClampedTag:
        case uint16Tag:
        case uint32Tag:
          return cloneTypedArray(object, isDeep);
        case mapTag:
          return new Ctor();
        case numberTag:
        case stringTag:
          return new Ctor(object);
        case regexpTag:
          return cloneRegExp(object);
        case setTag:
          return new Ctor();
        case symbolTag:
          return cloneSymbol(object);
      }
    }
    module.exports = initCloneByTag;
  }
});

// node_modules/lodash/_baseCreate.js
var require_baseCreate = __commonJS({
  "node_modules/lodash/_baseCreate.js"(exports, module) {
    var isObject = require_isObject();
    var objectCreate = Object.create;
    var baseCreate = function() {
      function object() {
      }
      return function(proto) {
        if (!isObject(proto)) {
          return {};
        }
        if (objectCreate) {
          return objectCreate(proto);
        }
        object.prototype = proto;
        var result = new object();
        object.prototype = void 0;
        return result;
      };
    }();
    module.exports = baseCreate;
  }
});

// node_modules/lodash/_initCloneObject.js
var require_initCloneObject = __commonJS({
  "node_modules/lodash/_initCloneObject.js"(exports, module) {
    var baseCreate = require_baseCreate();
    var getPrototype = require_getPrototype();
    var isPrototype = require_isPrototype();
    function initCloneObject(object) {
      return typeof object.constructor == "function" && !isPrototype(object) ? baseCreate(getPrototype(object)) : {};
    }
    module.exports = initCloneObject;
  }
});

// node_modules/lodash/_baseIsMap.js
var require_baseIsMap = __commonJS({
  "node_modules/lodash/_baseIsMap.js"(exports, module) {
    var getTag = require_getTag();
    var isObjectLike = require_isObjectLike();
    var mapTag = "[object Map]";
    function baseIsMap(value) {
      return isObjectLike(value) && getTag(value) == mapTag;
    }
    module.exports = baseIsMap;
  }
});

// node_modules/lodash/isMap.js
var require_isMap = __commonJS({
  "node_modules/lodash/isMap.js"(exports, module) {
    var baseIsMap = require_baseIsMap();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsMap = nodeUtil && nodeUtil.isMap;
    var isMap = nodeIsMap ? baseUnary(nodeIsMap) : baseIsMap;
    module.exports = isMap;
  }
});

// node_modules/lodash/_baseIsSet.js
var require_baseIsSet = __commonJS({
  "node_modules/lodash/_baseIsSet.js"(exports, module) {
    var getTag = require_getTag();
    var isObjectLike = require_isObjectLike();
    var setTag = "[object Set]";
    function baseIsSet(value) {
      return isObjectLike(value) && getTag(value) == setTag;
    }
    module.exports = baseIsSet;
  }
});

// node_modules/lodash/isSet.js
var require_isSet = __commonJS({
  "node_modules/lodash/isSet.js"(exports, module) {
    var baseIsSet = require_baseIsSet();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsSet = nodeUtil && nodeUtil.isSet;
    var isSet = nodeIsSet ? baseUnary(nodeIsSet) : baseIsSet;
    module.exports = isSet;
  }
});

// node_modules/lodash/_baseClone.js
var require_baseClone = __commonJS({
  "node_modules/lodash/_baseClone.js"(exports, module) {
    var Stack = require_Stack();
    var arrayEach = require_arrayEach();
    var assignValue = require_assignValue();
    var baseAssign = require_baseAssign();
    var baseAssignIn = require_baseAssignIn();
    var cloneBuffer = require_cloneBuffer();
    var copyArray = require_copyArray();
    var copySymbols = require_copySymbols();
    var copySymbolsIn = require_copySymbolsIn();
    var getAllKeys = require_getAllKeys();
    var getAllKeysIn = require_getAllKeysIn();
    var getTag = require_getTag();
    var initCloneArray = require_initCloneArray();
    var initCloneByTag = require_initCloneByTag();
    var initCloneObject = require_initCloneObject();
    var isArray = require_isArray();
    var isBuffer = require_isBuffer();
    var isMap = require_isMap();
    var isObject = require_isObject();
    var isSet = require_isSet();
    var keys = require_keys();
    var keysIn = require_keysIn();
    var CLONE_DEEP_FLAG = 1;
    var CLONE_FLAT_FLAG = 2;
    var CLONE_SYMBOLS_FLAG = 4;
    var argsTag = "[object Arguments]";
    var arrayTag = "[object Array]";
    var boolTag = "[object Boolean]";
    var dateTag = "[object Date]";
    var errorTag = "[object Error]";
    var funcTag = "[object Function]";
    var genTag = "[object GeneratorFunction]";
    var mapTag = "[object Map]";
    var numberTag = "[object Number]";
    var objectTag = "[object Object]";
    var regexpTag = "[object RegExp]";
    var setTag = "[object Set]";
    var stringTag = "[object String]";
    var symbolTag = "[object Symbol]";
    var weakMapTag = "[object WeakMap]";
    var arrayBufferTag = "[object ArrayBuffer]";
    var dataViewTag = "[object DataView]";
    var float32Tag = "[object Float32Array]";
    var float64Tag = "[object Float64Array]";
    var int8Tag = "[object Int8Array]";
    var int16Tag = "[object Int16Array]";
    var int32Tag = "[object Int32Array]";
    var uint8Tag = "[object Uint8Array]";
    var uint8ClampedTag = "[object Uint8ClampedArray]";
    var uint16Tag = "[object Uint16Array]";
    var uint32Tag = "[object Uint32Array]";
    var cloneableTags = {};
    cloneableTags[argsTag] = cloneableTags[arrayTag] = cloneableTags[arrayBufferTag] = cloneableTags[dataViewTag] = cloneableTags[boolTag] = cloneableTags[dateTag] = cloneableTags[float32Tag] = cloneableTags[float64Tag] = cloneableTags[int8Tag] = cloneableTags[int16Tag] = cloneableTags[int32Tag] = cloneableTags[mapTag] = cloneableTags[numberTag] = cloneableTags[objectTag] = cloneableTags[regexpTag] = cloneableTags[setTag] = cloneableTags[stringTag] = cloneableTags[symbolTag] = cloneableTags[uint8Tag] = cloneableTags[uint8ClampedTag] = cloneableTags[uint16Tag] = cloneableTags[uint32Tag] = true;
    cloneableTags[errorTag] = cloneableTags[funcTag] = cloneableTags[weakMapTag] = false;
    function baseClone(value, bitmask, customizer, key, object, stack) {
      var result, isDeep = bitmask & CLONE_DEEP_FLAG, isFlat = bitmask & CLONE_FLAT_FLAG, isFull = bitmask & CLONE_SYMBOLS_FLAG;
      if (customizer) {
        result = object ? customizer(value, key, object, stack) : customizer(value);
      }
      if (result !== void 0) {
        return result;
      }
      if (!isObject(value)) {
        return value;
      }
      var isArr = isArray(value);
      if (isArr) {
        result = initCloneArray(value);
        if (!isDeep) {
          return copyArray(value, result);
        }
      } else {
        var tag = getTag(value), isFunc = tag == funcTag || tag == genTag;
        if (isBuffer(value)) {
          return cloneBuffer(value, isDeep);
        }
        if (tag == objectTag || tag == argsTag || isFunc && !object) {
          result = isFlat || isFunc ? {} : initCloneObject(value);
          if (!isDeep) {
            return isFlat ? copySymbolsIn(value, baseAssignIn(result, value)) : copySymbols(value, baseAssign(result, value));
          }
        } else {
          if (!cloneableTags[tag]) {
            return object ? value : {};
          }
          result = initCloneByTag(value, tag, isDeep);
        }
      }
      stack || (stack = new Stack());
      var stacked = stack.get(value);
      if (stacked) {
        return stacked;
      }
      stack.set(value, result);
      if (isSet(value)) {
        value.forEach(function(subValue) {
          result.add(baseClone(subValue, bitmask, customizer, subValue, value, stack));
        });
      } else if (isMap(value)) {
        value.forEach(function(subValue, key2) {
          result.set(key2, baseClone(subValue, bitmask, customizer, key2, value, stack));
        });
      }
      var keysFunc = isFull ? isFlat ? getAllKeysIn : getAllKeys : isFlat ? keysIn : keys;
      var props = isArr ? void 0 : keysFunc(value);
      arrayEach(props || value, function(subValue, key2) {
        if (props) {
          key2 = subValue;
          subValue = value[key2];
        }
        assignValue(result, key2, baseClone(subValue, bitmask, customizer, key2, value, stack));
      });
      return result;
    }
    module.exports = baseClone;
  }
});

// node_modules/lodash/cloneDeep.js
var require_cloneDeep = __commonJS({
  "node_modules/lodash/cloneDeep.js"(exports, module) {
    var baseClone = require_baseClone();
    var CLONE_DEEP_FLAG = 1;
    var CLONE_SYMBOLS_FLAG = 4;
    function cloneDeep(value) {
      return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);
    }
    module.exports = cloneDeep;
  }
});

// node_modules/immer/dist/immer.esm.mjs
function n(n2) {
  for (var r2 = arguments.length, t2 = Array(r2 > 1 ? r2 - 1 : 0), e = 1; e < r2; e++)
    t2[e - 1] = arguments[e];
  if (true) {
    var i2 = Y[n2], o3 = i2 ? "function" == typeof i2 ? i2.apply(null, t2) : i2 : "unknown error nr: " + n2;
    throw Error("[Immer] " + o3);
  }
  throw Error("[Immer] minified error nr: " + n2 + (t2.length ? " " + t2.map(function(n3) {
    return "'" + n3 + "'";
  }).join(",") : "") + ". Find the full error at: https://bit.ly/3cXEKWf");
}
function r(n2) {
  return !!n2 && !!n2[Q];
}
function t(n2) {
  var r2;
  return !!n2 && (function(n3) {
    if (!n3 || "object" != typeof n3)
      return false;
    var r3 = Object.getPrototypeOf(n3);
    if (null === r3)
      return true;
    var t2 = Object.hasOwnProperty.call(r3, "constructor") && r3.constructor;
    return t2 === Object || "function" == typeof t2 && Function.toString.call(t2) === Z;
  }(n2) || Array.isArray(n2) || !!n2[L] || !!(null === (r2 = n2.constructor) || void 0 === r2 ? void 0 : r2[L]) || s(n2) || v(n2));
}
function i(n2, r2, t2) {
  void 0 === t2 && (t2 = false), 0 === o(n2) ? (t2 ? Object.keys : nn)(n2).forEach(function(e) {
    t2 && "symbol" == typeof e || r2(e, n2[e], n2);
  }) : n2.forEach(function(t3, e) {
    return r2(e, t3, n2);
  });
}
function o(n2) {
  var r2 = n2[Q];
  return r2 ? r2.i > 3 ? r2.i - 4 : r2.i : Array.isArray(n2) ? 1 : s(n2) ? 2 : v(n2) ? 3 : 0;
}
function u(n2, r2) {
  return 2 === o(n2) ? n2.has(r2) : Object.prototype.hasOwnProperty.call(n2, r2);
}
function a(n2, r2) {
  return 2 === o(n2) ? n2.get(r2) : n2[r2];
}
function f(n2, r2, t2) {
  var e = o(n2);
  2 === e ? n2.set(r2, t2) : 3 === e ? n2.add(t2) : n2[r2] = t2;
}
function c(n2, r2) {
  return n2 === r2 ? 0 !== n2 || 1 / n2 == 1 / r2 : n2 != n2 && r2 != r2;
}
function s(n2) {
  return X && n2 instanceof Map;
}
function v(n2) {
  return q && n2 instanceof Set;
}
function p(n2) {
  return n2.o || n2.t;
}
function l(n2) {
  if (Array.isArray(n2))
    return Array.prototype.slice.call(n2);
  var r2 = rn(n2);
  delete r2[Q];
  for (var t2 = nn(r2), e = 0; e < t2.length; e++) {
    var i2 = t2[e], o3 = r2[i2];
    false === o3.writable && (o3.writable = true, o3.configurable = true), (o3.get || o3.set) && (r2[i2] = { configurable: true, writable: true, enumerable: o3.enumerable, value: n2[i2] });
  }
  return Object.create(Object.getPrototypeOf(n2), r2);
}
function d(n2, e) {
  return void 0 === e && (e = false), y(n2) || r(n2) || !t(n2) || (o(n2) > 1 && (n2.set = n2.add = n2.clear = n2.delete = h), Object.freeze(n2), e && i(n2, function(n3, r2) {
    return d(r2, true);
  }, true)), n2;
}
function h() {
  n(2);
}
function y(n2) {
  return null == n2 || "object" != typeof n2 || Object.isFrozen(n2);
}
function b(r2) {
  var t2 = tn[r2];
  return t2 || n(18, r2), t2;
}
function m(n2, r2) {
  tn[n2] || (tn[n2] = r2);
}
function _() {
  return U || n(0), U;
}
function j(n2, r2) {
  r2 && (b("Patches"), n2.u = [], n2.s = [], n2.v = r2);
}
function g(n2) {
  O(n2), n2.p.forEach(S), n2.p = null;
}
function O(n2) {
  n2 === U && (U = n2.l);
}
function w(n2) {
  return U = { p: [], l: U, h: n2, m: true, _: 0 };
}
function S(n2) {
  var r2 = n2[Q];
  0 === r2.i || 1 === r2.i ? r2.j() : r2.g = true;
}
function P(r2, e) {
  e._ = e.p.length;
  var i2 = e.p[0], o3 = void 0 !== r2 && r2 !== i2;
  return e.h.O || b("ES5").S(e, r2, o3), o3 ? (i2[Q].P && (g(e), n(4)), t(r2) && (r2 = M(e, r2), e.l || x(e, r2)), e.u && b("Patches").M(i2[Q].t, r2, e.u, e.s)) : r2 = M(e, i2, []), g(e), e.u && e.v(e.u, e.s), r2 !== H ? r2 : void 0;
}
function M(n2, r2, t2) {
  if (y(r2))
    return r2;
  var e = r2[Q];
  if (!e)
    return i(r2, function(i2, o4) {
      return A(n2, e, r2, i2, o4, t2);
    }, true), r2;
  if (e.A !== n2)
    return r2;
  if (!e.P)
    return x(n2, e.t, true), e.t;
  if (!e.I) {
    e.I = true, e.A._--;
    var o3 = 4 === e.i || 5 === e.i ? e.o = l(e.k) : e.o, u3 = o3, a3 = false;
    3 === e.i && (u3 = new Set(o3), o3.clear(), a3 = true), i(u3, function(r3, i2) {
      return A(n2, e, o3, r3, i2, t2, a3);
    }), x(n2, o3, false), t2 && n2.u && b("Patches").N(e, t2, n2.u, n2.s);
  }
  return e.o;
}
function A(e, i2, o3, a3, c3, s3, v2) {
  if (c3 === o3 && n(5), r(c3)) {
    var p3 = M(e, c3, s3 && i2 && 3 !== i2.i && !u(i2.R, a3) ? s3.concat(a3) : void 0);
    if (f(o3, a3, p3), !r(p3))
      return;
    e.m = false;
  } else
    v2 && o3.add(c3);
  if (t(c3) && !y(c3)) {
    if (!e.h.D && e._ < 1)
      return;
    M(e, c3), i2 && i2.A.l || x(e, c3);
  }
}
function x(n2, r2, t2) {
  void 0 === t2 && (t2 = false), !n2.l && n2.h.D && n2.m && d(r2, t2);
}
function z(n2, r2) {
  var t2 = n2[Q];
  return (t2 ? p(t2) : n2)[r2];
}
function I(n2, r2) {
  if (r2 in n2)
    for (var t2 = Object.getPrototypeOf(n2); t2; ) {
      var e = Object.getOwnPropertyDescriptor(t2, r2);
      if (e)
        return e;
      t2 = Object.getPrototypeOf(t2);
    }
}
function k(n2) {
  n2.P || (n2.P = true, n2.l && k(n2.l));
}
function E(n2) {
  n2.o || (n2.o = l(n2.t));
}
function N(n2, r2, t2) {
  var e = s(r2) ? b("MapSet").F(r2, t2) : v(r2) ? b("MapSet").T(r2, t2) : n2.O ? function(n3, r3) {
    var t3 = Array.isArray(n3), e2 = { i: t3 ? 1 : 0, A: r3 ? r3.A : _(), P: false, I: false, R: {}, l: r3, t: n3, k: null, o: null, j: null, C: false }, i2 = e2, o3 = en;
    t3 && (i2 = [e2], o3 = on);
    var u3 = Proxy.revocable(i2, o3), a3 = u3.revoke, f3 = u3.proxy;
    return e2.k = f3, e2.j = a3, f3;
  }(r2, t2) : b("ES5").J(r2, t2);
  return (t2 ? t2.A : _()).p.push(e), e;
}
function R(e) {
  return r(e) || n(22, e), function n2(r2) {
    if (!t(r2))
      return r2;
    var e2, u3 = r2[Q], c3 = o(r2);
    if (u3) {
      if (!u3.P && (u3.i < 4 || !b("ES5").K(u3)))
        return u3.t;
      u3.I = true, e2 = D(r2, c3), u3.I = false;
    } else
      e2 = D(r2, c3);
    return i(e2, function(r3, t2) {
      u3 && a(u3.t, r3) === t2 || f(e2, r3, n2(t2));
    }), 3 === c3 ? new Set(e2) : e2;
  }(e);
}
function D(n2, r2) {
  switch (r2) {
    case 2:
      return new Map(n2);
    case 3:
      return Array.from(n2);
  }
  return l(n2);
}
function T() {
  function e(n2) {
    if (!t(n2))
      return n2;
    if (Array.isArray(n2))
      return n2.map(e);
    if (s(n2))
      return new Map(Array.from(n2.entries()).map(function(n3) {
        return [n3[0], e(n3[1])];
      }));
    if (v(n2))
      return new Set(Array.from(n2).map(e));
    var r2 = Object.create(Object.getPrototypeOf(n2));
    for (var i2 in n2)
      r2[i2] = e(n2[i2]);
    return u(n2, L) && (r2[L] = n2[L]), r2;
  }
  function f3(n2) {
    return r(n2) ? e(n2) : n2;
  }
  var c3 = "add";
  m("Patches", { $: function(r2, t2) {
    return t2.forEach(function(t3) {
      for (var i2 = t3.path, u3 = t3.op, f4 = r2, s3 = 0; s3 < i2.length - 1; s3++) {
        var v2 = o(f4), p3 = i2[s3];
        "string" != typeof p3 && "number" != typeof p3 && (p3 = "" + p3), 0 !== v2 && 1 !== v2 || "__proto__" !== p3 && "constructor" !== p3 || n(24), "function" == typeof f4 && "prototype" === p3 && n(24), "object" != typeof (f4 = a(f4, p3)) && n(15, i2.join("/"));
      }
      var l3 = o(f4), d3 = e(t3.value), h3 = i2[i2.length - 1];
      switch (u3) {
        case "replace":
          switch (l3) {
            case 2:
              return f4.set(h3, d3);
            case 3:
              n(16);
            default:
              return f4[h3] = d3;
          }
        case c3:
          switch (l3) {
            case 1:
              return "-" === h3 ? f4.push(d3) : f4.splice(h3, 0, d3);
            case 2:
              return f4.set(h3, d3);
            case 3:
              return f4.add(d3);
            default:
              return f4[h3] = d3;
          }
        case "remove":
          switch (l3) {
            case 1:
              return f4.splice(h3, 1);
            case 2:
              return f4.delete(h3);
            case 3:
              return f4.delete(t3.value);
            default:
              return delete f4[h3];
          }
        default:
          n(17, u3);
      }
    }), r2;
  }, N: function(n2, r2, t2, e2) {
    switch (n2.i) {
      case 0:
      case 4:
      case 2:
        return function(n3, r3, t3, e3) {
          var o3 = n3.t, s3 = n3.o;
          i(n3.R, function(n4, i2) {
            var v2 = a(o3, n4), p3 = a(s3, n4), l3 = i2 ? u(o3, n4) ? "replace" : c3 : "remove";
            if (v2 !== p3 || "replace" !== l3) {
              var d3 = r3.concat(n4);
              t3.push("remove" === l3 ? { op: l3, path: d3 } : { op: l3, path: d3, value: p3 }), e3.push(l3 === c3 ? { op: "remove", path: d3 } : "remove" === l3 ? { op: c3, path: d3, value: f3(v2) } : { op: "replace", path: d3, value: f3(v2) });
            }
          });
        }(n2, r2, t2, e2);
      case 5:
      case 1:
        return function(n3, r3, t3, e3) {
          var i2 = n3.t, o3 = n3.R, u3 = n3.o;
          if (u3.length < i2.length) {
            var a3 = [u3, i2];
            i2 = a3[0], u3 = a3[1];
            var s3 = [e3, t3];
            t3 = s3[0], e3 = s3[1];
          }
          for (var v2 = 0; v2 < i2.length; v2++)
            if (o3[v2] && u3[v2] !== i2[v2]) {
              var p3 = r3.concat([v2]);
              t3.push({ op: "replace", path: p3, value: f3(u3[v2]) }), e3.push({ op: "replace", path: p3, value: f3(i2[v2]) });
            }
          for (var l3 = i2.length; l3 < u3.length; l3++) {
            var d3 = r3.concat([l3]);
            t3.push({ op: c3, path: d3, value: f3(u3[l3]) });
          }
          i2.length < u3.length && e3.push({ op: "replace", path: r3.concat(["length"]), value: i2.length });
        }(n2, r2, t2, e2);
      case 3:
        return function(n3, r3, t3, e3) {
          var i2 = n3.t, o3 = n3.o, u3 = 0;
          i2.forEach(function(n4) {
            if (!o3.has(n4)) {
              var i3 = r3.concat([u3]);
              t3.push({ op: "remove", path: i3, value: n4 }), e3.unshift({ op: c3, path: i3, value: n4 });
            }
            u3++;
          }), u3 = 0, o3.forEach(function(n4) {
            if (!i2.has(n4)) {
              var o4 = r3.concat([u3]);
              t3.push({ op: c3, path: o4, value: n4 }), e3.unshift({ op: "remove", path: o4, value: n4 });
            }
            u3++;
          });
        }(n2, r2, t2, e2);
    }
  }, M: function(n2, r2, t2, e2) {
    t2.push({ op: "replace", path: [], value: r2 === H ? void 0 : r2 }), e2.push({ op: "replace", path: [], value: n2 });
  } });
}
function C() {
  function r2(n2, r3) {
    function t2() {
      this.constructor = n2;
    }
    a3(n2, r3), n2.prototype = (t2.prototype = r3.prototype, new t2());
  }
  function e(n2) {
    n2.o || (n2.R = /* @__PURE__ */ new Map(), n2.o = new Map(n2.t));
  }
  function o3(n2) {
    n2.o || (n2.o = /* @__PURE__ */ new Set(), n2.t.forEach(function(r3) {
      if (t(r3)) {
        var e2 = N(n2.A.h, r3, n2);
        n2.p.set(r3, e2), n2.o.add(e2);
      } else
        n2.o.add(r3);
    }));
  }
  function u3(r3) {
    r3.g && n(3, JSON.stringify(p(r3)));
  }
  var a3 = function(n2, r3) {
    return (a3 = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(n3, r4) {
      n3.__proto__ = r4;
    } || function(n3, r4) {
      for (var t2 in r4)
        r4.hasOwnProperty(t2) && (n3[t2] = r4[t2]);
    })(n2, r3);
  }, f3 = function() {
    function n2(n3, r3) {
      return this[Q] = { i: 2, l: r3, A: r3 ? r3.A : _(), P: false, I: false, o: void 0, R: void 0, t: n3, k: this, C: false, g: false }, this;
    }
    r2(n2, Map);
    var o4 = n2.prototype;
    return Object.defineProperty(o4, "size", { get: function() {
      return p(this[Q]).size;
    } }), o4.has = function(n3) {
      return p(this[Q]).has(n3);
    }, o4.set = function(n3, r3) {
      var t2 = this[Q];
      return u3(t2), p(t2).has(n3) && p(t2).get(n3) === r3 || (e(t2), k(t2), t2.R.set(n3, true), t2.o.set(n3, r3), t2.R.set(n3, true)), this;
    }, o4.delete = function(n3) {
      if (!this.has(n3))
        return false;
      var r3 = this[Q];
      return u3(r3), e(r3), k(r3), r3.t.has(n3) ? r3.R.set(n3, false) : r3.R.delete(n3), r3.o.delete(n3), true;
    }, o4.clear = function() {
      var n3 = this[Q];
      u3(n3), p(n3).size && (e(n3), k(n3), n3.R = /* @__PURE__ */ new Map(), i(n3.t, function(r3) {
        n3.R.set(r3, false);
      }), n3.o.clear());
    }, o4.forEach = function(n3, r3) {
      var t2 = this;
      p(this[Q]).forEach(function(e2, i2) {
        n3.call(r3, t2.get(i2), i2, t2);
      });
    }, o4.get = function(n3) {
      var r3 = this[Q];
      u3(r3);
      var i2 = p(r3).get(n3);
      if (r3.I || !t(i2))
        return i2;
      if (i2 !== r3.t.get(n3))
        return i2;
      var o5 = N(r3.A.h, i2, r3);
      return e(r3), r3.o.set(n3, o5), o5;
    }, o4.keys = function() {
      return p(this[Q]).keys();
    }, o4.values = function() {
      var n3, r3 = this, t2 = this.keys();
      return (n3 = {})[V] = function() {
        return r3.values();
      }, n3.next = function() {
        var n4 = t2.next();
        return n4.done ? n4 : { done: false, value: r3.get(n4.value) };
      }, n3;
    }, o4.entries = function() {
      var n3, r3 = this, t2 = this.keys();
      return (n3 = {})[V] = function() {
        return r3.entries();
      }, n3.next = function() {
        var n4 = t2.next();
        if (n4.done)
          return n4;
        var e2 = r3.get(n4.value);
        return { done: false, value: [n4.value, e2] };
      }, n3;
    }, o4[V] = function() {
      return this.entries();
    }, n2;
  }(), c3 = function() {
    function n2(n3, r3) {
      return this[Q] = { i: 3, l: r3, A: r3 ? r3.A : _(), P: false, I: false, o: void 0, t: n3, k: this, p: /* @__PURE__ */ new Map(), g: false, C: false }, this;
    }
    r2(n2, Set);
    var t2 = n2.prototype;
    return Object.defineProperty(t2, "size", { get: function() {
      return p(this[Q]).size;
    } }), t2.has = function(n3) {
      var r3 = this[Q];
      return u3(r3), r3.o ? !!r3.o.has(n3) || !(!r3.p.has(n3) || !r3.o.has(r3.p.get(n3))) : r3.t.has(n3);
    }, t2.add = function(n3) {
      var r3 = this[Q];
      return u3(r3), this.has(n3) || (o3(r3), k(r3), r3.o.add(n3)), this;
    }, t2.delete = function(n3) {
      if (!this.has(n3))
        return false;
      var r3 = this[Q];
      return u3(r3), o3(r3), k(r3), r3.o.delete(n3) || !!r3.p.has(n3) && r3.o.delete(r3.p.get(n3));
    }, t2.clear = function() {
      var n3 = this[Q];
      u3(n3), p(n3).size && (o3(n3), k(n3), n3.o.clear());
    }, t2.values = function() {
      var n3 = this[Q];
      return u3(n3), o3(n3), n3.o.values();
    }, t2.entries = function() {
      var n3 = this[Q];
      return u3(n3), o3(n3), n3.o.entries();
    }, t2.keys = function() {
      return this.values();
    }, t2[V] = function() {
      return this.values();
    }, t2.forEach = function(n3, r3) {
      for (var t3 = this.values(), e2 = t3.next(); !e2.done; )
        n3.call(r3, e2.value, e2.value, this), e2 = t3.next();
    }, n2;
  }();
  m("MapSet", { F: function(n2, r3) {
    return new f3(n2, r3);
  }, T: function(n2, r3) {
    return new c3(n2, r3);
  } });
}
var G;
var U;
var W = "undefined" != typeof Symbol && "symbol" == typeof Symbol("x");
var X = "undefined" != typeof Map;
var q = "undefined" != typeof Set;
var B = "undefined" != typeof Proxy && void 0 !== Proxy.revocable && "undefined" != typeof Reflect;
var H = W ? Symbol.for("immer-nothing") : ((G = {})["immer-nothing"] = true, G);
var L = W ? Symbol.for("immer-draftable") : "__$immer_draftable";
var Q = W ? Symbol.for("immer-state") : "__$immer_state";
var V = "undefined" != typeof Symbol && Symbol.iterator || "@@iterator";
var Y = { 0: "Illegal state", 1: "Immer drafts cannot have computed properties", 2: "This object has been frozen and should not be mutated", 3: function(n2) {
  return "Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? " + n2;
}, 4: "An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.", 5: "Immer forbids circular references", 6: "The first or second argument to `produce` must be a function", 7: "The third argument to `produce` must be a function or undefined", 8: "First argument to `createDraft` must be a plain object, an array, or an immerable object", 9: "First argument to `finishDraft` must be a draft returned by `createDraft`", 10: "The given draft is already finalized", 11: "Object.defineProperty() cannot be used on an Immer draft", 12: "Object.setPrototypeOf() cannot be used on an Immer draft", 13: "Immer only supports deleting array indices", 14: "Immer only supports setting array indices and the 'length' property", 15: function(n2) {
  return "Cannot apply patch, path doesn't resolve: " + n2;
}, 16: 'Sets cannot have "replace" patches.', 17: function(n2) {
  return "Unsupported patch operation: " + n2;
}, 18: function(n2) {
  return "The plugin for '" + n2 + "' has not been loaded into Immer. To enable the plugin, import and call `enable" + n2 + "()` when initializing your application.";
}, 20: "Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available", 21: function(n2) {
  return "produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '" + n2 + "'";
}, 22: function(n2) {
  return "'current' expects a draft, got: " + n2;
}, 23: function(n2) {
  return "'original' expects a draft, got: " + n2;
}, 24: "Patching reserved attributes like __proto__, prototype and constructor is not allowed" };
var Z = "" + Object.prototype.constructor;
var nn = "undefined" != typeof Reflect && Reflect.ownKeys ? Reflect.ownKeys : void 0 !== Object.getOwnPropertySymbols ? function(n2) {
  return Object.getOwnPropertyNames(n2).concat(Object.getOwnPropertySymbols(n2));
} : Object.getOwnPropertyNames;
var rn = Object.getOwnPropertyDescriptors || function(n2) {
  var r2 = {};
  return nn(n2).forEach(function(t2) {
    r2[t2] = Object.getOwnPropertyDescriptor(n2, t2);
  }), r2;
};
var tn = {};
var en = { get: function(n2, r2) {
  if (r2 === Q)
    return n2;
  var e = p(n2);
  if (!u(e, r2))
    return function(n3, r3, t2) {
      var e2, i3 = I(r3, t2);
      return i3 ? "value" in i3 ? i3.value : null === (e2 = i3.get) || void 0 === e2 ? void 0 : e2.call(n3.k) : void 0;
    }(n2, e, r2);
  var i2 = e[r2];
  return n2.I || !t(i2) ? i2 : i2 === z(n2.t, r2) ? (E(n2), n2.o[r2] = N(n2.A.h, i2, n2)) : i2;
}, has: function(n2, r2) {
  return r2 in p(n2);
}, ownKeys: function(n2) {
  return Reflect.ownKeys(p(n2));
}, set: function(n2, r2, t2) {
  var e = I(p(n2), r2);
  if (null == e ? void 0 : e.set)
    return e.set.call(n2.k, t2), true;
  if (!n2.P) {
    var i2 = z(p(n2), r2), o3 = null == i2 ? void 0 : i2[Q];
    if (o3 && o3.t === t2)
      return n2.o[r2] = t2, n2.R[r2] = false, true;
    if (c(t2, i2) && (void 0 !== t2 || u(n2.t, r2)))
      return true;
    E(n2), k(n2);
  }
  return n2.o[r2] === t2 && (void 0 !== t2 || r2 in n2.o) || Number.isNaN(t2) && Number.isNaN(n2.o[r2]) || (n2.o[r2] = t2, n2.R[r2] = true), true;
}, deleteProperty: function(n2, r2) {
  return void 0 !== z(n2.t, r2) || r2 in n2.t ? (n2.R[r2] = false, E(n2), k(n2)) : delete n2.R[r2], n2.o && delete n2.o[r2], true;
}, getOwnPropertyDescriptor: function(n2, r2) {
  var t2 = p(n2), e = Reflect.getOwnPropertyDescriptor(t2, r2);
  return e ? { writable: true, configurable: 1 !== n2.i || "length" !== r2, enumerable: e.enumerable, value: t2[r2] } : e;
}, defineProperty: function() {
  n(11);
}, getPrototypeOf: function(n2) {
  return Object.getPrototypeOf(n2.t);
}, setPrototypeOf: function() {
  n(12);
} };
var on = {};
i(en, function(n2, r2) {
  on[n2] = function() {
    return arguments[0] = arguments[0][0], r2.apply(this, arguments);
  };
}), on.deleteProperty = function(r2, t2) {
  return isNaN(parseInt(t2)) && n(13), on.set.call(this, r2, t2, void 0);
}, on.set = function(r2, t2, e) {
  return "length" !== t2 && isNaN(parseInt(t2)) && n(14), en.set.call(this, r2[0], t2, e, r2[0]);
};
var un = function() {
  function e(r2) {
    var e2 = this;
    this.O = B, this.D = true, this.produce = function(r3, i3, o3) {
      if ("function" == typeof r3 && "function" != typeof i3) {
        var u3 = i3;
        i3 = r3;
        var a3 = e2;
        return function(n2) {
          var r4 = this;
          void 0 === n2 && (n2 = u3);
          for (var t2 = arguments.length, e3 = Array(t2 > 1 ? t2 - 1 : 0), o4 = 1; o4 < t2; o4++)
            e3[o4 - 1] = arguments[o4];
          return a3.produce(n2, function(n3) {
            var t3;
            return (t3 = i3).call.apply(t3, [r4, n3].concat(e3));
          });
        };
      }
      var f3;
      if ("function" != typeof i3 && n(6), void 0 !== o3 && "function" != typeof o3 && n(7), t(r3)) {
        var c3 = w(e2), s3 = N(e2, r3, void 0), v2 = true;
        try {
          f3 = i3(s3), v2 = false;
        } finally {
          v2 ? g(c3) : O(c3);
        }
        return "undefined" != typeof Promise && f3 instanceof Promise ? f3.then(function(n2) {
          return j(c3, o3), P(n2, c3);
        }, function(n2) {
          throw g(c3), n2;
        }) : (j(c3, o3), P(f3, c3));
      }
      if (!r3 || "object" != typeof r3) {
        if (void 0 === (f3 = i3(r3)) && (f3 = r3), f3 === H && (f3 = void 0), e2.D && d(f3, true), o3) {
          var p3 = [], l3 = [];
          b("Patches").M(r3, f3, p3, l3), o3(p3, l3);
        }
        return f3;
      }
      n(21, r3);
    }, this.produceWithPatches = function(n2, r3) {
      if ("function" == typeof n2)
        return function(r4) {
          for (var t3 = arguments.length, i4 = Array(t3 > 1 ? t3 - 1 : 0), o4 = 1; o4 < t3; o4++)
            i4[o4 - 1] = arguments[o4];
          return e2.produceWithPatches(r4, function(r5) {
            return n2.apply(void 0, [r5].concat(i4));
          });
        };
      var t2, i3, o3 = e2.produce(n2, r3, function(n3, r4) {
        t2 = n3, i3 = r4;
      });
      return "undefined" != typeof Promise && o3 instanceof Promise ? o3.then(function(n3) {
        return [n3, t2, i3];
      }) : [o3, t2, i3];
    }, "boolean" == typeof (null == r2 ? void 0 : r2.useProxies) && this.setUseProxies(r2.useProxies), "boolean" == typeof (null == r2 ? void 0 : r2.autoFreeze) && this.setAutoFreeze(r2.autoFreeze);
  }
  var i2 = e.prototype;
  return i2.createDraft = function(e2) {
    t(e2) || n(8), r(e2) && (e2 = R(e2));
    var i3 = w(this), o3 = N(this, e2, void 0);
    return o3[Q].C = true, O(i3), o3;
  }, i2.finishDraft = function(r2, t2) {
    var e2 = r2 && r2[Q];
    e2 && e2.C || n(9), e2.I && n(10);
    var i3 = e2.A;
    return j(i3, t2), P(void 0, i3);
  }, i2.setAutoFreeze = function(n2) {
    this.D = n2;
  }, i2.setUseProxies = function(r2) {
    r2 && !B && n(20), this.O = r2;
  }, i2.applyPatches = function(n2, t2) {
    var e2;
    for (e2 = t2.length - 1; e2 >= 0; e2--) {
      var i3 = t2[e2];
      if (0 === i3.path.length && "replace" === i3.op) {
        n2 = i3.value;
        break;
      }
    }
    e2 > -1 && (t2 = t2.slice(e2 + 1));
    var o3 = b("Patches").$;
    return r(n2) ? o3(n2, t2) : this.produce(n2, function(n3) {
      return o3(n3, t2);
    });
  }, e;
}();
var an = new un();
var fn = an.produce;
var cn = an.produceWithPatches.bind(an);
var sn = an.setAutoFreeze.bind(an);
var vn = an.setUseProxies.bind(an);
var pn = an.applyPatches.bind(an);
var ln = an.createDraft.bind(an);
var dn = an.finishDraft.bind(an);
var immer_esm_default = fn;

// node_modules/@craftjs/utils/dist/esm/index.js
var import_isEqualWith = __toESM(require_isEqualWith());
var import_react = __toESM(require_react());
var import_shallowequal = __toESM(require_shallowequal());

// node_modules/nanoid/index.browser.js
var nanoid = (size = 21) => crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {
  byte &= 63;
  if (byte < 36) {
    id += byte.toString(36);
  } else if (byte < 62) {
    id += (byte - 26).toString(36).toUpperCase();
  } else if (byte > 62) {
    id += "-";
  } else {
    id += "_";
  }
  return id;
}, "");

// node_modules/tiny-invariant/dist/esm/tiny-invariant.js
var isProduction = false;
var prefix = "Invariant failed";
function invariant(condition, message) {
  if (condition) {
    return;
  }
  if (isProduction) {
    throw new Error(prefix);
  }
  var provided = typeof message === "function" ? message() : message;
  var value = provided ? "".concat(prefix, ": ").concat(provided) : prefix;
  throw new Error(value);
}

// node_modules/@craftjs/utils/dist/esm/index.js
var import_react_dom = __toESM(require_react_dom());
"undefined" != typeof window && (window.__CRAFTJS__ || (window.__CRAFTJS__ = {}), window.__CRAFTJS__["@craftjs/utils"] = "0.2.5");
var m2 = "ROOT";
var g2 = "canvas-ROOT";
var E2 = "Parent id cannot be ommited";
var w2 = "Attempting to add a node with duplicated id";
var O2 = "Node does not exist, it may have been removed";
var R2 = 'A <Element /> that is used inside a User Component must specify an `id` prop, eg: <Element id="text_element">...</Element> ';
var P2 = "Node cannot be dropped into target parent";
var j2 = "Target parent rejects incoming node";
var T2 = "Current parent rejects outgoing node";
var C2 = "Cannot move node that is not a direct child of a Canvas node";
var I2 = "Cannot move node into a non-Canvas parent";
var A2 = "A top-level Node cannot be moved";
var S2 = "Cannot move node into a descendant";
var H2 = "The component type specified for this node (%node_type%) does not exist in the resolver";
var x2 = "The node has specified a canDrag() rule that prevents it from being dragged";
var N2 = "Invalid parameter Node Id specified";
var L2 = "Attempting to delete a top-level Node";
var M2 = "Resolver in <Editor /> has to be an object. For (de)serialization Craft.js needs a list of all the User Components. \n    \nMore info: https://craft.js.org/r/docs/api/editor#props";
var U2 = "An Error occurred while deserializing components: Cannot find component <%displayName% /> in resolver map. Please check your resolver in <Editor />\n\nAvailable components in resolver: %availableComponents%\n\nMore info: https://craft.js.org/r/docs/api/editor#props";
var q2 = "You can only use useEditor in the context of <Editor />. \n\nPlease only use useEditor in components that are children of the <Editor /> component.";
var B2 = "You can only use useNode in the context of <Editor />. \n\nPlease only use useNode in components that are children of the <Editor /> component.";
function G2(e, t2) {
  var n2 = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function Y2(e) {
  for (var t2 = 1; t2 < arguments.length; t2++) {
    var n2 = null != arguments[t2] ? arguments[t2] : {};
    t2 % 2 ? G2(Object(n2), true).forEach(function(t3) {
      J(e, t3, n2[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n2)) : G2(Object(n2)).forEach(function(t3) {
      Object.defineProperty(e, t3, Object.getOwnPropertyDescriptor(n2, t3));
    });
  }
  return e;
}
function F(e, t2) {
  if (!(e instanceof t2))
    throw new TypeError("Cannot call a class as a function");
}
function W2(e, t2) {
  for (var n2 = 0; n2 < t2.length; n2++) {
    var r2 = t2[n2];
    r2.enumerable = r2.enumerable || false, r2.configurable = true, "value" in r2 && (r2.writable = true), Object.defineProperty(e, oe(r2.key), r2);
  }
}
function z2(e, t2, n2) {
  return t2 && W2(e.prototype, t2), n2 && W2(e, n2), Object.defineProperty(e, "prototype", { writable: false }), e;
}
function J(e, t2, n2) {
  return (t2 = oe(t2)) in e ? Object.defineProperty(e, t2, { value: n2, enumerable: true, configurable: true, writable: true }) : e[t2] = n2, e;
}
function $(e) {
  return $ = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e2) {
    return e2.__proto__ || Object.getPrototypeOf(e2);
  }, $(e);
}
function K(e, t2) {
  return K = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e2, t3) {
    return e2.__proto__ = t3, e2;
  }, K(e, t2);
}
function Q2(e) {
  if (void 0 === e)
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  return e;
}
function V2() {
  return V2 = "undefined" != typeof Reflect && Reflect.get ? Reflect.get.bind() : function(e, t2, n2) {
    var r2 = function(e2, t3) {
      for (; !Object.prototype.hasOwnProperty.call(e2, t3) && null !== (e2 = $(e2)); )
        ;
      return e2;
    }(e, t2);
    if (r2) {
      var i2 = Object.getOwnPropertyDescriptor(r2, t2);
      return i2.get ? i2.get.call(arguments.length < 3 ? e : n2) : i2.value;
    }
  }, V2.apply(this, arguments);
}
function X2(e, t2) {
  return ee(e) || function(e2, t3) {
    var n2 = null == e2 ? null : "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
    if (null != n2) {
      var r2, i2, o3, a3, c3 = [], s3 = true, u3 = false;
      try {
        if (o3 = (n2 = n2.call(e2)).next, 0 === t3) {
          if (Object(n2) !== n2)
            return;
          s3 = false;
        } else
          for (; !(s3 = (r2 = o3.call(n2)).done) && (c3.push(r2.value), c3.length !== t3); s3 = true)
            ;
      } catch (e3) {
        u3 = true, i2 = e3;
      } finally {
        try {
          if (!s3 && null != n2.return && (a3 = n2.return(), Object(a3) !== a3))
            return;
        } finally {
          if (u3)
            throw i2;
        }
      }
      return c3;
    }
  }(e, t2) || ne(e, t2) || ie();
}
function Z2(e) {
  return function(e2) {
    if (Array.isArray(e2))
      return re(e2);
  }(e) || te(e) || ne(e) || function() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function ee(e) {
  if (Array.isArray(e))
    return e;
}
function te(e) {
  if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"])
    return Array.from(e);
}
function ne(e, t2) {
  if (e) {
    if ("string" == typeof e)
      return re(e, t2);
    var n2 = Object.prototype.toString.call(e).slice(8, -1);
    return "Object" === n2 && e.constructor && (n2 = e.constructor.name), "Map" === n2 || "Set" === n2 ? Array.from(e) : "Arguments" === n2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n2) ? re(e, t2) : void 0;
  }
}
function re(e, t2) {
  (null == t2 || t2 > e.length) && (t2 = e.length);
  for (var n2 = 0, r2 = new Array(t2); n2 < t2; n2++)
    r2[n2] = e[n2];
  return r2;
}
function ie() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function oe(e) {
  var t2 = function(e2, t3) {
    if ("object" != typeof e2 || null === e2)
      return e2;
    var n2 = e2[Symbol.toPrimitive];
    if (void 0 !== n2) {
      var r2 = n2.call(e2, "string");
      if ("object" != typeof r2)
        return r2;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return String(e2);
  }(e);
  return "symbol" == typeof t2 ? t2 : String(t2);
}
var ae = { UNDO: "HISTORY_UNDO", REDO: "HISTORY_REDO", THROTTLE: "HISTORY_THROTTLE", IGNORE: "HISTORY_IGNORE", MERGE: "HISTORY_MERGE", CLEAR: "HISTORY_CLEAR" };
var ce = function() {
  function e() {
    F(this, e), J(this, "timeline", []), J(this, "pointer", -1);
  }
  return z2(e, [{ key: "add", value: function(e2, t2) {
    0 === e2.length && 0 === t2.length || (this.pointer = this.pointer + 1, this.timeline.length = this.pointer, this.timeline[this.pointer] = { patches: e2, inversePatches: t2, timestamp: Date.now() });
  } }, { key: "throttleAdd", value: function(e2, t2) {
    var n2 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 500;
    if (0 !== e2.length || 0 !== t2.length) {
      if (this.timeline.length && this.pointer >= 0) {
        var r2 = this.timeline[this.pointer], i2 = r2.patches, o3 = r2.inversePatches, a3 = r2.timestamp;
        if ((/* @__PURE__ */ new Date()).getTime() - a3 < n2)
          return void (this.timeline[this.pointer] = { timestamp: a3, patches: [].concat(Z2(i2), Z2(e2)), inversePatches: [].concat(Z2(t2), Z2(o3)) });
      }
      this.add(e2, t2);
    }
  } }, { key: "merge", value: function(e2, t2) {
    if (0 !== e2.length || 0 !== t2.length)
      if (this.timeline.length && this.pointer >= 0) {
        var n2 = this.timeline[this.pointer], r2 = n2.inversePatches;
        this.timeline[this.pointer] = { timestamp: n2.timestamp, patches: [].concat(Z2(n2.patches), Z2(e2)), inversePatches: [].concat(Z2(t2), Z2(r2)) };
      } else
        this.add(e2, t2);
  } }, { key: "clear", value: function() {
    this.timeline = [], this.pointer = -1;
  } }, { key: "canUndo", value: function() {
    return this.pointer >= 0;
  } }, { key: "canRedo", value: function() {
    return this.pointer < this.timeline.length - 1;
  } }, { key: "undo", value: function(e2) {
    if (this.canUndo()) {
      var n2 = this.timeline[this.pointer].inversePatches;
      return this.pointer = this.pointer - 1, pn(e2, n2);
    }
  } }, { key: "redo", value: function(e2) {
    if (this.canRedo())
      return this.pointer = this.pointer + 1, pn(e2, this.timeline[this.pointer].patches);
  } }]), e;
}();
function se(t2, n2, r2, o3) {
  var a3, f3 = (0, import_react.useMemo)(function() {
    return new ce();
  }, []), d3 = (0, import_react.useRef)([]), h3 = (0, import_react.useRef)(function() {
  });
  "function" == typeof t2 ? a3 = t2 : (a3 = t2.methods, d3.current = t2.ignoreHistoryForActions, h3.current = t2.normalizeHistory);
  var p3 = (0, import_react.useRef)(o3);
  p3.current = o3;
  var y2 = (0, import_react.useRef)(n2), v2 = (0, import_react.useMemo)(function() {
    var t3 = h3.current, n3 = d3.current, o4 = p3.current;
    return function(c3, s3) {
      var u3, l3 = r2 && ue(r2, function() {
        return c3;
      }, f3), d4 = X2(cn(c3, function(e) {
        var t4, n4;
        switch (s3.type) {
          case ae.UNDO:
            return f3.undo(e);
          case ae.REDO:
            return f3.redo(e);
          case ae.CLEAR:
            return f3.clear(), Y2({}, e);
          case ae.IGNORE:
          case ae.MERGE:
          case ae.THROTTLE:
            var r3, i2 = ee(n4 = s3.payload) || te(n4) || ne(n4) || ie(), o5 = i2[0], c4 = i2.slice(1);
            (r3 = a3(e, l3))[o5].apply(r3, Z2(c4));
            break;
          default:
            (t4 = a3(e, l3))[s3.type].apply(t4, Z2(s3.payload));
        }
      }), 3), h4 = d4[0], p4 = d4[1], y3 = d4[2];
      return u3 = h4, o4 && o4(h4, c3, { type: s3.type, params: s3.payload, patches: p4 }, l3, function(e) {
        var t4 = cn(h4, e);
        u3 = t4[0], p4 = [].concat(Z2(p4), Z2(t4[1])), y3 = [].concat(Z2(t4[2]), Z2(y3));
      }), [ae.UNDO, ae.REDO].includes(s3.type) && t3 && (u3 = immer_esm_default(u3, t3)), [].concat(Z2(n3), [ae.UNDO, ae.REDO, ae.IGNORE, ae.CLEAR]).includes(s3.type) || (s3.type === ae.THROTTLE ? f3.throttleAdd(p4, y3, s3.config && s3.config.rate) : s3.type === ae.MERGE ? f3.merge(p4, y3) : f3.add(p4, y3)), u3;
    };
  }, [f3, a3, r2]), b3 = (0, import_react.useCallback)(function() {
    return y2.current;
  }, []), m3 = (0, import_react.useMemo)(function() {
    return new le(b3);
  }, [b3]), g3 = (0, import_react.useCallback)(function(e) {
    var t3 = v2(y2.current, e);
    y2.current = t3, m3.notify();
  }, [v2, m3]);
  (0, import_react.useEffect)(function() {
    m3.notify();
  }, [m3]);
  var E3 = (0, import_react.useMemo)(function() {
    return r2 ? ue(r2, function() {
      return y2.current;
    }, f3) : [];
  }, [f3, r2]), w3 = (0, import_react.useMemo)(function() {
    var e = Object.keys(a3(null, null)), t3 = d3.current;
    return Y2(Y2({}, e.reduce(function(e2, t4) {
      return e2[t4] = function() {
        for (var e3 = arguments.length, n3 = new Array(e3), r3 = 0; r3 < e3; r3++)
          n3[r3] = arguments[r3];
        return g3({ type: t4, payload: n3 });
      }, e2;
    }, {})), {}, { history: { undo: function() {
      return g3({ type: ae.UNDO });
    }, redo: function() {
      return g3({ type: ae.REDO });
    }, clear: function() {
      return g3({ type: ae.CLEAR });
    }, throttle: function(n3) {
      return Y2({}, e.filter(function(e2) {
        return !t3.includes(e2);
      }).reduce(function(e2, t4) {
        return e2[t4] = function() {
          for (var e3 = arguments.length, r3 = new Array(e3), i2 = 0; i2 < e3; i2++)
            r3[i2] = arguments[i2];
          return g3({ type: ae.THROTTLE, payload: [t4].concat(r3), config: { rate: n3 } });
        }, e2;
      }, {}));
    }, ignore: function() {
      return Y2({}, e.filter(function(e2) {
        return !t3.includes(e2);
      }).reduce(function(e2, t4) {
        return e2[t4] = function() {
          for (var e3 = arguments.length, n3 = new Array(e3), r3 = 0; r3 < e3; r3++)
            n3[r3] = arguments[r3];
          return g3({ type: ae.IGNORE, payload: [t4].concat(n3) });
        }, e2;
      }, {}));
    }, merge: function() {
      return Y2({}, e.filter(function(e2) {
        return !t3.includes(e2);
      }).reduce(function(e2, t4) {
        return e2[t4] = function() {
          for (var e3 = arguments.length, n3 = new Array(e3), r3 = 0; r3 < e3; r3++)
            n3[r3] = arguments[r3];
          return g3({ type: ae.MERGE, payload: [t4].concat(n3) });
        }, e2;
      }, {}));
    } } });
  }, [g3, a3]);
  return (0, import_react.useMemo)(function() {
    return { getState: b3, subscribe: function(e, t3, n3) {
      return m3.subscribe(e, t3, n3);
    }, actions: w3, query: E3, history: f3 };
  }, [w3, E3, m3, b3, f3]);
}
function ue(e, t2, n2) {
  var r2 = Object.keys(e()).reduce(function(n3, r3) {
    return Y2(Y2({}, n3), {}, J({}, r3, function() {
      var n4;
      return (n4 = e(t2()))[r3].apply(n4, arguments);
    }));
  }, {});
  return Y2(Y2({}, r2), {}, { history: { canUndo: function() {
    return n2.canUndo();
  }, canRedo: function() {
    return n2.canRedo();
  } } });
}
C(), T();
var le = function() {
  function e(t2) {
    F(this, e), J(this, "getState", void 0), J(this, "subscribers", []), this.getState = t2;
  }
  return z2(e, [{ key: "subscribe", value: function(e2, t2, n2) {
    var r2 = this, i2 = new fe(function() {
      return e2(r2.getState());
    }, t2, n2);
    return this.subscribers.push(i2), this.unsubscribe.bind(this, i2);
  } }, { key: "unsubscribe", value: function(e2) {
    if (this.subscribers.length) {
      var t2 = this.subscribers.indexOf(e2);
      if (t2 > -1)
        return this.subscribers.splice(t2, 1);
    }
  } }, { key: "notify", value: function() {
    this.subscribers.forEach(function(e2) {
      return e2.collect();
    });
  } }]), e;
}();
var fe = function() {
  function e(t2, n2) {
    var r2 = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
    F(this, e), J(this, "collected", void 0), J(this, "collector", void 0), J(this, "onChange", void 0), J(this, "id", void 0), this.collector = t2, this.onChange = n2, r2 && this.collect();
  }
  return z2(e, [{ key: "collect", value: function() {
    try {
      var e2 = this.collector();
      (0, import_isEqualWith.default)(e2, this.collected) || (this.collected = e2, this.onChange && this.onChange(this.collected));
    } catch (e3) {
      console.warn(e3);
    }
  } }]), e;
}();
var de = function(e) {
  var t2 = e.getBoundingClientRect(), n2 = t2.x, r2 = t2.y, i2 = t2.top, o3 = t2.left, a3 = t2.bottom, c3 = t2.right, s3 = t2.width, u3 = t2.height, l3 = window.getComputedStyle(e), f3 = { left: parseInt(l3.marginLeft), right: parseInt(l3.marginRight), bottom: parseInt(l3.marginBottom), top: parseInt(l3.marginTop) }, d3 = { left: parseInt(l3.paddingLeft), right: parseInt(l3.paddingRight), bottom: parseInt(l3.paddingBottom), top: parseInt(l3.paddingTop) };
  return { x: n2, y: r2, top: i2, left: o3, bottom: a3, right: c3, width: s3, height: u3, outerWidth: Math.round(s3 + f3.left + f3.right), outerHeight: Math.round(u3 + f3.top + f3.bottom), margin: f3, padding: d3, inFlow: e.parentElement && !!function(t3) {
    var n3 = getComputedStyle(t3);
    if (!(l3.overflow && "visible" !== l3.overflow || "none" !== n3.float || "grid" === n3.display || "flex" === n3.display && "column" !== n3["flex-direction"])) {
      switch (l3.position) {
        case "static":
        case "relative":
          break;
        default:
          return;
      }
      switch (e.tagName) {
        case "TR":
        case "TBODY":
        case "THEAD":
        case "TFOOT":
          return true;
      }
      switch (l3.display) {
        case "block":
        case "list-item":
        case "table":
        case "flex":
        case "grid":
          return true;
      }
    }
  }(e.parentElement) };
};
function he(e, t2) {
  const { subscribe: n2, getState: r2, actions: i2, query: o3 } = e, a3 = (0, import_react.useRef)(true), c3 = (0, import_react.useRef)(null), d3 = (0, import_react.useRef)(t2);
  d3.current = t2;
  const h3 = (0, import_react.useCallback)((e2) => ({ ...e2, actions: i2, query: o3 }), [i2, o3]);
  a3.current && t2 && (c3.current = t2(r2(), o3), a3.current = false);
  const [p3, y2] = (0, import_react.useState)(h3(c3.current));
  return (0, import_react.useEffect)(() => {
    let e2;
    return d3.current && (e2 = n2((e3) => d3.current(e3, o3), (e3) => {
      y2(h3(e3));
    })), () => {
      e2 && e2();
    };
  }, [h3, o3, n2]), p3;
}
var pe;
var ye = function() {
  return nanoid(arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 10);
};
var ve = function() {
  function e() {
    F(this, e), J(this, "isEnabled", true), J(this, "elementIdMap", /* @__PURE__ */ new WeakMap()), J(this, "registry", /* @__PURE__ */ new Map());
  }
  return z2(e, [{ key: "getElementId", value: function(e2) {
    var t2 = this.elementIdMap.get(e2);
    if (t2)
      return t2;
    var n2 = ye();
    return this.elementIdMap.set(e2, n2), n2;
  } }, { key: "getConnectorId", value: function(e2, t2) {
    var n2 = this.getElementId(e2);
    return "".concat(t2, "--").concat(n2);
  } }, { key: "register", value: function(e2, t2) {
    var n2 = this, r2 = this.getByElement(e2, t2.name);
    if (r2) {
      if ((0, import_shallowequal.default)(t2.required, r2.required))
        return r2;
      this.getByElement(e2, t2.name).disable();
    }
    var i2 = null, o3 = this.getConnectorId(e2, t2.name);
    return this.registry.set(o3, { id: o3, required: t2.required, enable: function() {
      i2 && i2(), i2 = t2.connector(e2, t2.required, t2.options);
    }, disable: function() {
      i2 && i2();
    }, remove: function() {
      return n2.remove(o3);
    } }), this.isEnabled && this.registry.get(o3).enable(), this.registry.get(o3);
  } }, { key: "get", value: function(e2) {
    return this.registry.get(e2);
  } }, { key: "remove", value: function(e2) {
    var t2 = this.get(e2);
    t2 && (t2.disable(), this.registry.delete(t2.id));
  } }, { key: "enable", value: function() {
    this.isEnabled = true, this.registry.forEach(function(e2) {
      e2.enable();
    });
  } }, { key: "disable", value: function() {
    this.isEnabled = false, this.registry.forEach(function(e2) {
      e2.disable();
    });
  } }, { key: "getByElement", value: function(e2, t2) {
    return this.get(this.getConnectorId(e2, t2));
  } }, { key: "removeByElement", value: function(e2, t2) {
    return this.remove(this.getConnectorId(e2, t2));
  } }, { key: "clear", value: function() {
    this.disable(), this.elementIdMap = /* @__PURE__ */ new WeakMap(), this.registry = /* @__PURE__ */ new Map();
  } }]), e;
}();
!function(e) {
  e[e.HandlerDisabled = 0] = "HandlerDisabled", e[e.HandlerEnabled = 1] = "HandlerEnabled";
}(pe || (pe = {}));
var be = function() {
  function e(t2) {
    F(this, e), J(this, "options", void 0), J(this, "registry", new ve()), J(this, "subscribers", /* @__PURE__ */ new Set()), this.options = t2;
  }
  return z2(e, [{ key: "listen", value: function(e2) {
    var t2 = this;
    return this.subscribers.add(e2), function() {
      return t2.subscribers.delete(e2);
    };
  } }, { key: "disable", value: function() {
    this.onDisable && this.onDisable(), this.registry.disable(), this.subscribers.forEach(function(e2) {
      e2(pe.HandlerDisabled);
    });
  } }, { key: "enable", value: function() {
    this.onEnable && this.onEnable(), this.registry.enable(), this.subscribers.forEach(function(e2) {
      e2(pe.HandlerEnabled);
    });
  } }, { key: "cleanup", value: function() {
    this.disable(), this.subscribers.clear(), this.registry.clear();
  } }, { key: "addCraftEventListener", value: function(e2, t2, n2, r2) {
    var i2 = function(r3) {
      (function(e3, t3, n3) {
        e3.craft || (e3.craft = { stopPropagation: function() {
        }, blockedEvents: {} });
        for (var r4 = e3.craft && e3.craft.blockedEvents[t3] || [], i3 = 0; i3 < r4.length; i3++) {
          var o3 = r4[i3];
          if (n3 !== o3 && n3.contains(o3))
            return true;
        }
        return false;
      })(r3, t2, e2) || (r3.craft.stopPropagation = function() {
        r3.craft.blockedEvents[t2] || (r3.craft.blockedEvents[t2] = []), r3.craft.blockedEvents[t2].push(e2);
      }, n2(r3));
    };
    return e2.addEventListener(t2, i2, r2), function() {
      return e2.removeEventListener(t2, i2, r2);
    };
  } }, { key: "createConnectorsUsage", value: function() {
    var e2 = this, t2 = this.handlers(), n2 = /* @__PURE__ */ new Set(), r2 = false, i2 = /* @__PURE__ */ new Map();
    return { connectors: Object.entries(t2).reduce(function(t3, o3) {
      var a3 = X2(o3, 2), c3 = a3[0], s3 = a3[1];
      return Y2(Y2({}, t3), {}, J({}, c3, function(t4, o4, a4) {
        var u3 = function() {
          var r3 = e2.registry.register(t4, { required: o4, name: c3, options: a4, connector: s3 });
          return n2.add(r3.id), r3;
        };
        return i2.set(e2.registry.getConnectorId(t4, c3), u3), r2 && u3(), t4;
      }));
    }, {}), register: function() {
      r2 = true, i2.forEach(function(e3) {
        e3();
      });
    }, cleanup: function() {
      r2 = false, n2.forEach(function(t3) {
        return e2.registry.remove(t3);
      });
    } };
  } }, { key: "derive", value: function(e2, t2) {
    return new e2(this, t2);
  } }, { key: "createProxyHandlers", value: function(e2, t2) {
    var n2 = [], r2 = e2.handlers(), i2 = new Proxy(r2, { get: function(e3, t3, i3) {
      return t3 in r2 == 0 ? Reflect.get(e3, t3, i3) : function(e4) {
        for (var i4 = arguments.length, o3 = new Array(i4 > 1 ? i4 - 1 : 0), a3 = 1; a3 < i4; a3++)
          o3[a3 - 1] = arguments[a3];
        var c3 = r2[t3].apply(r2, [e4].concat(o3));
        c3 && n2.push(c3);
      };
    } });
    return t2(i2), function() {
      n2.forEach(function(e3) {
        e3();
      });
    };
  } }, { key: "reflect", value: function(e2) {
    return this.createProxyHandlers(this, e2);
  } }]), e;
}();
var me = function(e) {
  !function(e2, t3) {
    if ("function" != typeof t3 && null !== t3)
      throw new TypeError("Super expression must either be null or a function");
    e2.prototype = Object.create(t3 && t3.prototype, { constructor: { value: e2, writable: true, configurable: true } }), Object.defineProperty(e2, "prototype", { writable: false }), t3 && K(e2, t3);
  }(i2, be);
  var t2, n2, r2 = (t2 = i2, n2 = function() {
    if ("undefined" == typeof Reflect || !Reflect.construct)
      return false;
    if (Reflect.construct.sham)
      return false;
    if ("function" == typeof Proxy)
      return true;
    try {
      return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
      })), true;
    } catch (e2) {
      return false;
    }
  }(), function() {
    var e2, r3 = $(t2);
    if (n2) {
      var i3 = $(this).constructor;
      e2 = Reflect.construct(r3, arguments, i3);
    } else
      e2 = r3.apply(this, arguments);
    return function(e3, t3) {
      if (t3 && ("object" == typeof t3 || "function" == typeof t3))
        return t3;
      if (void 0 !== t3)
        throw new TypeError("Derived constructors may only return object or undefined");
      return Q2(e3);
    }(this, e2);
  });
  function i2(e2, t3) {
    var n3;
    return F(this, i2), J(Q2(n3 = r2.call(this, t3)), "derived", void 0), J(Q2(n3), "unsubscribeParentHandlerListener", void 0), n3.derived = e2, n3.options = t3, n3.unsubscribeParentHandlerListener = n3.derived.listen(function(e3) {
      switch (e3) {
        case pe.HandlerEnabled:
          return n3.enable();
        case pe.HandlerDisabled:
          return n3.disable();
        default:
          return;
      }
    }), n3;
  }
  return z2(i2, [{ key: "inherit", value: function(e2) {
    return this.createProxyHandlers(this.derived, e2);
  } }, { key: "cleanup", value: function() {
    V2($(i2.prototype), "cleanup", this).call(this), this.unsubscribeParentHandlerListener();
  } }]), i2;
}();
function ge(e, t2) {
  t2 && ("function" == typeof e ? e(t2) : e.current = t2);
}
function Ee(e, t2) {
  const n2 = e.ref;
  return invariant("string" != typeof n2, "Cannot connect to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://facebook.github.io/react/docs/more-about-refs.html#the-ref-callback-attribute"), (0, import_react.cloneElement)(e, n2 ? { ref: (e2) => {
    ge(n2, e2), ge(t2, e2);
  } } : { ref: t2 });
}
function we(e) {
  return (t2 = null, ...n2) => {
    if (!(0, import_react.isValidElement)(t2)) {
      if (!t2)
        return;
      const r3 = t2;
      return r3 && e(r3, ...n2), r3;
    }
    const r2 = t2;
    return function(e2) {
      if ("string" != typeof e2.type)
        throw new Error();
    }(r2), Ee(r2, e);
  };
}
function Oe(e) {
  return Object.keys(e).reduce((t2, n2) => (t2[n2] = we((...t3) => e[n2](...t3)), t2), {});
}
var Re = ({ style: e, className: t2, parentDom: n2 }) => {
  const r2 = import_react.default.createElement("div", { className: t2, style: { position: "fixed", display: "block", opacity: 1, borderStyle: "solid", borderWidth: "1px", borderColor: "transparent", zIndex: 99999, ...e } });
  return n2 && n2.ownerDocument !== document ? import_react_dom.default.createPortal(r2, n2.ownerDocument.body) : r2;
};
var Pe = function(e, t2) {
  var n2 = "Deprecation warning: ".concat(e, " will be deprecated in future relases."), r2 = t2.suggest, i2 = t2.doc;
  r2 && (n2 += " Please use ".concat(r2, " instead.")), i2 && (n2 += "(".concat(i2, ")")), console.warn(n2);
};
var je = function() {
  return "undefined" != typeof window;
};
var Te = function() {
  return je() && /Linux/i.test(window.navigator.userAgent);
};
var Ce = function() {
  return je() && /Chrome/i.test(window.navigator.userAgent);
};

// node_modules/@craftjs/core/dist/esm/index.js
var R3 = __toESM(require_react());
var import_react2 = __toESM(require_react());
var import_isFunction = __toESM(require_isFunction());
var import_cloneDeep = __toESM(require_cloneDeep());
"undefined" != typeof window && (window.__CRAFTJS__ || (window.__CRAFTJS__ = {}), window.__CRAFTJS__["@craftjs/core"] = "0.2.12");
var U3 = import_react2.default.createContext(null);
var V3 = ({ id: e, related: t2 = false, children: n2 }) => import_react2.default.createElement(U3.Provider, { value: { id: e, related: t2 } }, n2);
function X3(e, t2) {
  var n2 = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function Y3(e) {
  for (var t2 = 1; t2 < arguments.length; t2++) {
    var n2 = null != arguments[t2] ? arguments[t2] : {};
    t2 % 2 ? X3(Object(n2), true).forEach(function(t3) {
      ee2(e, t3, n2[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n2)) : X3(Object(n2)).forEach(function(t3) {
      Object.defineProperty(e, t3, Object.getOwnPropertyDescriptor(n2, t3));
    });
  }
  return e;
}
function G3(e) {
  return G3 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e2) {
    return typeof e2;
  } : function(e2) {
    return e2 && "function" == typeof Symbol && e2.constructor === Symbol && e2 !== Symbol.prototype ? "symbol" : typeof e2;
  }, G3(e);
}
function K2(e, t2) {
  if (!(e instanceof t2))
    throw new TypeError("Cannot call a class as a function");
}
function Q3(e, t2) {
  for (var n2 = 0; n2 < t2.length; n2++) {
    var r2 = t2[n2];
    r2.enumerable = r2.enumerable || false, r2.configurable = true, "value" in r2 && (r2.writable = true), Object.defineProperty(e, le2(r2.key), r2);
  }
}
function Z3(e, t2, n2) {
  return t2 && Q3(e.prototype, t2), n2 && Q3(e, n2), Object.defineProperty(e, "prototype", { writable: false }), e;
}
function ee2(e, t2, n2) {
  return (t2 = le2(t2)) in e ? Object.defineProperty(e, t2, { value: n2, enumerable: true, configurable: true, writable: true }) : e[t2] = n2, e;
}
function te2(e, t2) {
  if ("function" != typeof t2 && null !== t2)
    throw new TypeError("Super expression must either be null or a function");
  e.prototype = Object.create(t2 && t2.prototype, { constructor: { value: e, writable: true, configurable: true } }), Object.defineProperty(e, "prototype", { writable: false }), t2 && re2(e, t2);
}
function ne2(e) {
  return ne2 = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e2) {
    return e2.__proto__ || Object.getPrototypeOf(e2);
  }, ne2(e);
}
function re2(e, t2) {
  return re2 = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e2, t3) {
    return e2.__proto__ = t3, e2;
  }, re2(e, t2);
}
function oe2(e, t2) {
  if (null == e)
    return {};
  var n2, r2, o3 = function(e2, t3) {
    if (null == e2)
      return {};
    var n3, r3, o4 = {}, a4 = Object.keys(e2);
    for (r3 = 0; r3 < a4.length; r3++)
      t3.indexOf(n3 = a4[r3]) >= 0 || (o4[n3] = e2[n3]);
    return o4;
  }(e, t2);
  if (Object.getOwnPropertySymbols) {
    var a3 = Object.getOwnPropertySymbols(e);
    for (r2 = 0; r2 < a3.length; r2++)
      t2.indexOf(n2 = a3[r2]) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n2) && (o3[n2] = e[n2]);
  }
  return o3;
}
function ae2(e) {
  if (void 0 === e)
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  return e;
}
function ie2(e) {
  var t2 = function() {
    if ("undefined" == typeof Reflect || !Reflect.construct)
      return false;
    if (Reflect.construct.sham)
      return false;
    if ("function" == typeof Proxy)
      return true;
    try {
      return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
      })), true;
    } catch (e2) {
      return false;
    }
  }();
  return function() {
    var n2, r2 = ne2(e);
    if (t2) {
      var o3 = ne2(this).constructor;
      n2 = Reflect.construct(r2, arguments, o3);
    } else
      n2 = r2.apply(this, arguments);
    return function(e2, t3) {
      if (t3 && ("object" == typeof t3 || "function" == typeof t3))
        return t3;
      if (void 0 !== t3)
        throw new TypeError("Derived constructors may only return object or undefined");
      return ae2(e2);
    }(this, n2);
  };
}
function se2(e, t2) {
  return function(e2) {
    if (Array.isArray(e2))
      return e2;
  }(e) || function(e2, t3) {
    var n2 = null == e2 ? null : "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
    if (null != n2) {
      var r2, o3, a3, i2, s3 = [], d3 = true, c3 = false;
      try {
        if (a3 = (n2 = n2.call(e2)).next, 0 === t3) {
          if (Object(n2) !== n2)
            return;
          d3 = false;
        } else
          for (; !(d3 = (r2 = a3.call(n2)).done) && (s3.push(r2.value), s3.length !== t3); d3 = true)
            ;
      } catch (e3) {
        c3 = true, o3 = e3;
      } finally {
        try {
          if (!d3 && null != n2.return && (i2 = n2.return(), Object(i2) !== i2))
            return;
        } finally {
          if (c3)
            throw o3;
        }
      }
      return s3;
    }
  }(e, t2) || ce2(e, t2) || function() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function de2(e) {
  return function(e2) {
    if (Array.isArray(e2))
      return ue2(e2);
  }(e) || function(e2) {
    if ("undefined" != typeof Symbol && null != e2[Symbol.iterator] || null != e2["@@iterator"])
      return Array.from(e2);
  }(e) || ce2(e) || function() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function ce2(e, t2) {
  if (e) {
    if ("string" == typeof e)
      return ue2(e, t2);
    var n2 = Object.prototype.toString.call(e).slice(8, -1);
    return "Object" === n2 && e.constructor && (n2 = e.constructor.name), "Map" === n2 || "Set" === n2 ? Array.from(e) : "Arguments" === n2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n2) ? ue2(e, t2) : void 0;
  }
}
function ue2(e, t2) {
  (null == t2 || t2 > e.length) && (t2 = e.length);
  for (var n2 = 0, r2 = new Array(t2); n2 < t2; n2++)
    r2[n2] = e[n2];
  return r2;
}
function le2(e) {
  var t2 = function(e2, t3) {
    if ("object" != typeof e2 || null === e2)
      return e2;
    var n2 = e2[Symbol.toPrimitive];
    if (void 0 !== n2) {
      var r2 = n2.call(e2, "string");
      if ("object" != typeof r2)
        return r2;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return String(e2);
  }(e);
  return "symbol" == typeof t2 ? t2 : String(t2);
}
var fe2 = (0, import_react2.createContext)(null);
var pe2 = (0, import_react2.createContext)(null);
var ve2 = function() {
  return (0, import_react2.useContext)(pe2);
};
function he2(r2) {
  var o3 = ve2(), a3 = (0, import_react2.useContext)(fe2);
  invariant(a3, q2);
  var i2 = he(a3, r2), s3 = (0, import_react2.useMemo)(function() {
    return o3 && o3.createConnectorsUsage();
  }, [o3]);
  (0, import_react2.useEffect)(function() {
    return s3.register(), function() {
      s3.cleanup();
    };
  }, [s3]);
  var d3 = (0, import_react2.useMemo)(function() {
    return s3 && Oe(s3.connectors);
  }, [s3]);
  return Y3(Y3({}, i2), {}, { connectors: d3, inContext: !!a3, store: a3 });
}
var ye2 = ["actions", "query", "connectors"];
function ge2(e) {
  var t2 = (0, import_react2.useContext)(U3);
  invariant(t2, B2);
  var o3 = t2.id, a3 = t2.related, i2 = he2(function(t3) {
    return o3 && t3.nodes[o3] && e && e(t3.nodes[o3]);
  }), s3 = i2.actions, d3 = i2.connectors, c3 = oe2(i2, ye2), u3 = (0, import_react2.useMemo)(function() {
    return Oe({ connect: function(e2) {
      return d3.connect(e2, o3);
    }, drag: function(e2) {
      return d3.drag(e2, o3);
    } });
  }, [d3, o3]), l3 = (0, import_react2.useMemo)(function() {
    return { setProp: function(e2, t3) {
      t3 ? s3.history.throttle(t3).setProp(o3, e2) : s3.setProp(o3, e2);
    }, setCustom: function(e2, t3) {
      t3 ? s3.history.throttle(t3).setCustom(o3, e2) : s3.setCustom(o3, e2);
    }, setHidden: function(e2) {
      return s3.setHidden(o3, e2);
    } };
  }, [s3, o3]);
  return Y3(Y3({}, c3), {}, { id: o3, related: a3, inNodeContext: !!t2, actions: l3, connectors: u3 });
}
var me2 = ["id", "related", "actions", "inNodeContext", "connectors"];
function Ne(e) {
  var t2 = ge2(e), n2 = t2.id, r2 = t2.related, a3 = t2.actions, i2 = t2.inNodeContext, s3 = t2.connectors;
  return Y3(Y3({}, oe2(t2, me2)), {}, { actions: a3, id: n2, related: r2, setProp: function(e2, t3) {
    return Pe("useNode().setProp()", { suggest: "useNode().actions.setProp()" }), a3.setProp(e2, t3);
  }, inNodeContext: i2, connectors: s3 });
}
var be2 = ({ render: e }) => {
  const { connectors: { connect: t2, drag: n2 } } = Ne();
  return "string" == typeof e.type ? t2(n2(import_react2.default.cloneElement(e))) : e;
};
var Ee2 = () => {
  const { type: e, props: t2, nodes: n2, hydrationTimestamp: r2 } = ge2((e2) => ({ type: e2.data.type, props: e2.data.props, nodes: e2.data.nodes, hydrationTimestamp: e2._hydrationTimestamp }));
  return (0, import_react2.useMemo)(() => {
    let r3 = t2.children;
    n2 && n2.length > 0 && (r3 = import_react2.default.createElement(import_react2.default.Fragment, null, n2.map((e2) => import_react2.default.createElement(we2, { id: e2, key: e2 }))));
    const o3 = import_react2.default.createElement(e, t2, r3);
    return "string" == typeof e ? import_react2.default.createElement(be2, { render: o3 }) : o3;
  }, [e, t2, r2, n2]);
};
var Oe2 = ({ render: e }) => {
  const { hidden: t2 } = ge2((e2) => ({ hidden: e2.data.hidden })), { onRender: n2 } = he2((e2) => ({ onRender: e2.options.onRender }));
  return t2 ? null : import_react2.default.createElement(n2, { render: e || import_react2.default.createElement(Ee2, null) });
};
var we2 = ({ id: e, render: t2 }) => import_react2.default.createElement(V3, { id: e }, import_react2.default.createElement(Oe2, { render: t2 }));
var Te2 = { is: "div", canvas: false, custom: {}, hidden: false };
var Ce2 = { is: "type", canvas: "isCanvas" };
function ke({ id: e, children: t2, ...n2 }) {
  const { is: r2 } = { ...Te2, ...n2 }, { query: o3, actions: i2 } = he2(), { id: s3, inNodeContext: d3 } = ge2(), [c3] = (0, import_react2.useState)(() => {
    invariant(!!e, R2);
    const c4 = o3.node(s3).get();
    if (d3) {
      const a3 = c4.data.linkedNodes[e] ? o3.node(c4.data.linkedNodes[e]).get() : null;
      if (a3 && a3.data.type === r2)
        return a3.id;
      const d4 = import_react2.default.createElement(ke, n2, t2), u3 = o3.parseReactElement(d4).toNodeTree();
      return i2.history.ignore().addLinkedNodeFromTree(u3, s3, e), u3.rootNodeId;
    }
    return null;
  });
  return c3 ? import_react2.default.createElement(we2, { id: c3 }) : null;
}
var Se = () => Pe("<Canvas />", { suggest: "<Element canvas={true} />" });
function Canvas({ ...e }) {
  return (0, import_react2.useEffect)(() => Se(), []), import_react2.default.createElement(ke, { ...e, canvas: true });
}
var je2 = () => {
  const { timestamp: e } = he2((e2) => ({ timestamp: e2.nodes[m2] && e2.nodes[m2]._hydrationTimestamp }));
  return e ? import_react2.default.createElement(we2, { id: m2, key: e }) : null;
};
var De = ({ children: e, json: t2, data: n2 }) => {
  const { actions: r2, query: a3 } = he2();
  t2 && Pe("<Frame json={...} />", { suggest: "<Frame data={...} />" });
  const s3 = (0, import_react2.useRef)(false);
  if (!s3.current) {
    const o3 = n2 || t2;
    if (o3)
      r2.history.ignore().deserialize(o3);
    else if (e) {
      const t3 = import_react2.default.Children.only(e), n3 = a3.parseReactElement(t3).toNodeTree((e2, n4) => (n4 === t3 && (e2.id = m2), e2));
      r2.history.ignore().addNodeTree(n3);
    }
    s3.current = true;
  }
  return import_react2.default.createElement(je2, null);
};
var Ie;
!function(e) {
  e[e.Any = 0] = "Any", e[e.Id = 1] = "Id", e[e.Obj = 2] = "Obj";
}(Ie || (Ie = {}));
var xe = (e) => {
  const { addLinkedNodeFromTree: t2, setDOM: n2, setNodeEvent: r2, replaceNodes: o3, reset: a3, ...i2 } = e;
  return i2;
};
function Pe2(e) {
  const { connectors: t2, actions: n2, query: r2, store: o3, ...a3 } = he2(e), i2 = xe(n2);
  return { connectors: t2, actions: (0, import_react2.useMemo)(() => ({ ...i2, history: { ...i2.history, ignore: (...e2) => xe(i2.history.ignore(...e2)), throttle: (...e2) => xe(i2.history.throttle(...e2)) } }), [i2]), query: r2, store: o3, ...a3 };
}
function Re2(e) {
  return (t2) => (n2) => {
    const r2 = e ? Pe2(e) : Pe2();
    return import_react2.default.createElement(t2, { ...r2, ...n2 });
  };
}
function Ae(e) {
  return function(t2) {
    return (n2) => {
      const r2 = Ne(e);
      return import_react2.default.createElement(t2, { ...r2, ...n2 });
    };
  };
}
var Le = function(e) {
  return Object.fromEntries ? Object.fromEntries(e) : e.reduce(function(e2, t2) {
    var n2 = se2(t2, 2), r2 = n2[0], o3 = n2[1];
    return Y3(Y3({}, e2), {}, ee2({}, r2, o3));
  }, {});
};
var qe = function(e, t2, n2) {
  var r2 = Array.isArray(t2) ? t2 : [t2], o3 = Y3({ existOnly: false, idOnly: false }, n2 || {}), a3 = r2.filter(function(e2) {
    return !!e2;
  }).map(function(t3) {
    return "string" == typeof t3 ? { node: e[t3], exists: !!e[t3] } : "object" !== G3(t3) || o3.idOnly ? { node: null, exists: false } : { node: t3, exists: !!e[t3.id] };
  });
  return o3.existOnly && invariant(0 === a3.filter(function(e2) {
    return !e2.exists;
  }).length, O2), a3;
};
var _e = ["history"];
var Fe = null;
var Me = function(e, t2) {
  if ("string" == typeof t2)
    return t2;
  var n2, r2 = function(e2, t3) {
    var n3 = function(e3) {
      if (Fe && Fe.resolver === e3)
        return Fe.reversed;
      Fe = { resolver: e3, reversed: /* @__PURE__ */ new Map() };
      for (var t4 = 0, n4 = Object.entries(e3); t4 < n4.length; t4++) {
        var r3 = se2(n4[t4], 2);
        Fe.reversed.set(r3[1], r3[0]);
      }
      return Fe.reversed;
    }(e2).get(t3);
    return void 0 !== n3 ? n3 : null;
  }(e, t2);
  return invariant(r2, H2.replace("%node_type%", (n2 = t2).name || n2.displayName)), r2;
};
var ze = (e, t2) => "string" == typeof e ? e : { resolvedName: Me(t2, e) };
var Be = (e, t2) => {
  let { type: n2, isCanvas: r2, props: o3 } = e;
  return o3 = Object.keys(o3).reduce((e2, n3) => {
    const r3 = o3[n3];
    return null == r3 || "function" == typeof r3 || (e2[n3] = "children" === n3 && "string" != typeof r3 ? import_react2.Children.map(r3, (e3) => "string" == typeof e3 ? e3 : Be(e3, t2)) : "function" == typeof r3.type ? Be(r3, t2) : r3), e2;
  }, {}), { type: ze(n2, t2), isCanvas: !!r2, props: o3 };
};
var He = (e, t2) => {
  const { type: n2, props: r2, isCanvas: o3, name: a3, ...i2 } = e;
  return { ...Be({ type: n2, isCanvas: o3, props: r2 }, t2), ...i2 };
};
function $e(e, t2) {
  invariant("string" == typeof t2, N2);
  var n2 = e.nodes[t2], r2 = function(t3) {
    return $e(e, t3);
  };
  return { isCanvas: function() {
    return !!n2.data.isCanvas;
  }, isRoot: function() {
    return n2.id === m2;
  }, isLinkedNode: function() {
    return n2.data.parent && r2(n2.data.parent).linkedNodes().includes(n2.id);
  }, isTopLevelNode: function() {
    return this.isRoot() || this.isLinkedNode();
  }, isDeletable: function() {
    return !this.isTopLevelNode();
  }, isParentOfTopLevelNodes: function() {
    return n2.data.linkedNodes && Object.keys(n2.data.linkedNodes).length > 0;
  }, isParentOfTopLevelCanvas: function() {
    return Pe("query.node(id).isParentOfTopLevelCanvas", { suggest: "query.node(id).isParentOfTopLevelNodes" }), this.isParentOfTopLevelNodes();
  }, isSelected: function() {
    return e.events.selected.has(t2);
  }, isHovered: function() {
    return e.events.hovered.has(t2);
  }, isDragged: function() {
    return e.events.dragged.has(t2);
  }, get: function() {
    return n2;
  }, ancestors: function() {
    var t3 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
    return function n3(r3) {
      var o3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [], a3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, i2 = e.nodes[r3];
      return i2 ? (o3.push(r3), i2.data.parent ? ((t3 || !t3 && 0 === a3) && (o3 = n3(i2.data.parent, o3, a3 + 1)), o3) : o3) : o3;
    }(n2.data.parent);
  }, descendants: function() {
    var n3 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], o3 = arguments.length > 1 ? arguments[1] : void 0;
    return function t3(a3) {
      var i2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [], s3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0;
      return (n3 || !n3 && 0 === s3) && e.nodes[a3] ? ("childNodes" !== o3 && r2(a3).linkedNodes().forEach(function(e2) {
        i2.push(e2), i2 = t3(e2, i2, s3 + 1);
      }), "linkedNodes" !== o3 && r2(a3).childNodes().forEach(function(e2) {
        i2.push(e2), i2 = t3(e2, i2, s3 + 1);
      }), i2) : i2;
    }(t2);
  }, linkedNodes: function() {
    return Object.values(n2.data.linkedNodes || {});
  }, childNodes: function() {
    return n2.data.nodes || [];
  }, isDraggable: function(t3) {
    try {
      var o3 = n2;
      return invariant(!this.isTopLevelNode(), A2), invariant($e(e, o3.data.parent).isCanvas(), C2), invariant(o3.rules.canDrag(o3, r2), x2), true;
    } catch (e2) {
      return t3 && t3(e2), false;
    }
  }, isDroppable: function(t3, o3) {
    var a3 = qe(e.nodes, t3), i2 = n2;
    try {
      invariant(this.isCanvas(), I2), invariant(i2.rules.canMoveIn(a3.map(function(e2) {
        return e2.node;
      }), i2, r2), j2);
      var s3 = {};
      return a3.forEach(function(t4) {
        var n3 = t4.node, o4 = t4.exists;
        if (invariant(n3.rules.canDrop(i2, n3, r2), P2), o4) {
          invariant(!r2(n3.id).isTopLevelNode(), A2);
          var a4 = r2(n3.id).descendants(true);
          invariant(!a4.includes(i2.id) && i2.id !== n3.id, S2);
          var d3 = n3.data.parent && e.nodes[n3.data.parent];
          invariant(d3.data.isCanvas, C2), invariant(d3 || !d3 && !e.nodes[n3.id], w2), d3.id !== i2.id && (s3[d3.id] || (s3[d3.id] = []), s3[d3.id].push(n3));
        }
      }), Object.keys(s3).forEach(function(t4) {
        var n3 = e.nodes[t4];
        invariant(n3.rules.canMoveOut(s3[t4], n3, r2), T2);
      }), true;
    } catch (e2) {
      return o3 && o3(e2), false;
    }
  }, toSerializedNode: function() {
    return He(n2.data, e.options.resolver);
  }, toNodeTree: function(e2) {
    var n3 = [t2].concat(de2(this.descendants(true, e2))).reduce(function(e3, t3) {
      return e3[t3] = r2(t3).get(), e3;
    }, {});
    return { rootNodeId: t2, nodes: n3 };
  }, decendants: function() {
    var e2 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
    return Pe("query.node(id).decendants", { suggest: "query.node(id).descendants" }), this.descendants(e2);
  }, isTopLevelCanvas: function() {
    return !this.isRoot() && !n2.data.parent;
  } };
}
function Je(e, t2, n2, r2) {
  for (var o3 = { parent: e, index: 0, where: "before" }, a3 = 0, i2 = 0, s3 = 0, d3 = 0, c3 = 0, u3 = 0, l3 = 0, f3 = t2.length; l3 < f3; l3++) {
    var p3 = t2[l3];
    if (u3 = p3.top + p3.outerHeight, d3 = p3.left + p3.outerWidth / 2, c3 = p3.top + p3.outerHeight / 2, !(i2 && p3.left > i2 || s3 && c3 >= s3 || a3 && p3.left + p3.outerWidth < a3))
      if (o3.index = l3, p3.inFlow) {
        if (r2 < c3) {
          o3.where = "before";
          break;
        }
        o3.where = "after";
      } else
        r2 < u3 && (s3 = u3), n2 < d3 ? (i2 = d3, o3.where = "before") : (a3 = d3, o3.where = "after");
  }
  return o3;
}
var We = function(e) {
  return "string" == typeof e ? e : e.name;
};
function Ue(e, t2) {
  var n2 = e.data.type, r2 = { id: e.id || ye(), _hydrationTimestamp: Date.now(), data: Y3({ type: n2, name: We(n2), displayName: We(n2), props: {}, custom: {}, parent: null, isCanvas: false, hidden: false, nodes: [], linkedNodes: {} }, e.data), info: {}, related: {}, events: { selected: false, dragged: false, hovered: false }, rules: { canDrag: function() {
    return true;
  }, canDrop: function() {
    return true;
  }, canMoveIn: function() {
    return true;
  }, canMoveOut: function() {
    return true;
  } }, dom: null };
  if (r2.data.type === ke || r2.data.type === Canvas) {
    var o3 = Y3(Y3({}, Te2), r2.data.props);
    r2.data.props = Object.keys(r2.data.props).reduce(function(e2, t3) {
      return Object.keys(Te2).includes(t3) ? r2.data[Ce2[t3] || t3] = o3[t3] : e2[t3] = r2.data.props[t3], e2;
    }, {}), r2.data.name = We(n2 = r2.data.type), r2.data.displayName = We(n2), r2.data.type === Canvas && (r2.data.isCanvas = true, Se());
  }
  t2 && t2(r2);
  var a3 = n2.craft;
  if (a3) {
    if (r2.data.displayName = a3.displayName || a3.name || r2.data.displayName, r2.data.props = Y3(Y3({}, a3.props || a3.defaultProps || {}), r2.data.props), r2.data.custom = Y3(Y3({}, a3.custom || {}), r2.data.custom), null != a3.isCanvas && (r2.data.isCanvas = a3.isCanvas), a3.rules && Object.keys(a3.rules).forEach(function(e2) {
      ["canDrag", "canDrop", "canMoveIn", "canMoveOut"].includes(e2) && (r2.rules[e2] = a3.rules[e2]);
    }), a3.related) {
      var i2 = { id: r2.id, related: true };
      Object.keys(a3.related).forEach(function(e2) {
        r2.related[e2] = function(t3) {
          return import_react2.default.createElement(V3, i2, import_react2.default.createElement(a3.related[e2], t3));
        };
      });
    }
    a3.info && (r2.info = a3.info);
  }
  return r2;
}
var Ve = (e, t2, n2) => {
  let { type: r2, props: o3 } = e;
  const a3 = ((e2, t3) => "object" == typeof e2 && e2.resolvedName ? "Canvas" === e2.resolvedName ? Canvas : t3[e2.resolvedName] : "string" == typeof e2 ? e2 : null)(r2, t2);
  if (!a3)
    return;
  o3 = Object.keys(o3).reduce((e2, n3) => {
    const r3 = o3[n3];
    return e2[n3] = null == r3 ? null : "object" == typeof r3 && r3.resolvedName ? Ve(r3, t2) : "children" === n3 && Array.isArray(r3) ? r3.map((e3) => "string" == typeof e3 ? e3 : Ve(e3, t2)) : r3, e2;
  }, {}), n2 && (o3.key = n2);
  const i2 = { ...import_react2.default.createElement(a3, { ...o3 }) };
  return { ...i2, name: Me(t2, i2.type) };
};
var Xe = (e, t2) => {
  const { type: n2, props: r2, ...o3 } = e;
  invariant(void 0 !== n2 && "string" == typeof n2 || void 0 !== n2 && void 0 !== n2.resolvedName, U2.replace("%displayName%", e.displayName).replace("%availableComponents%", Object.keys(t2).join(", ")));
  const { type: a3, name: i2, props: s3 } = Ve(e, t2), { parent: d3, custom: c3, displayName: u3, isCanvas: l3, nodes: f3, hidden: p3 } = o3;
  return { type: a3, name: i2, displayName: u3 || i2, props: s3, custom: c3 || {}, isCanvas: !!l3, hidden: !!p3, parent: d3, linkedNodes: o3.linkedNodes || o3._childCanvas || {}, nodes: f3 || [] };
};
var Ye = (e, t2) => {
  if (t2.length < 1)
    return { [e.id]: e };
  const n2 = t2.map(({ rootNodeId: e2 }) => e2), r2 = { ...e, data: { ...e.data, nodes: n2 } };
  return t2.reduce((t3, n3) => {
    const r3 = n3.nodes[n3.rootNodeId];
    return { ...t3, ...n3.nodes, [r3.id]: { ...r3, data: { ...r3.data, parent: e.id } } };
  }, { [e.id]: r2 });
};
var Ge = (e, t2) => ({ rootNodeId: e.id, nodes: Ye(e, t2) });
function Ke(e) {
  const t2 = e && e.options, n2 = () => Ke(e);
  return { getDropPlaceholder: (t3, r2, o3, a3 = (t4) => e.nodes[t4.id].dom) => {
    const i2 = e.nodes[r2], s3 = n2().node(i2.id).isCanvas() ? i2 : e.nodes[i2.data.parent];
    if (!s3)
      return;
    const d3 = s3.data.nodes || [], c3 = Je(s3, d3 ? d3.reduce((t4, n3) => {
      const r3 = a3(e.nodes[n3]);
      if (r3) {
        const e2 = { id: n3, ...de(r3) };
        t4.push(e2);
      }
      return t4;
    }, []) : [], o3.x, o3.y), u3 = d3.length && e.nodes[d3[c3.index]], l3 = { placement: { ...c3, currentNode: u3 }, error: null };
    return qe(e.nodes, t3).forEach(({ node: e2, exists: t4 }) => {
      t4 && n2().node(e2.id).isDraggable((e3) => l3.error = e3);
    }), n2().node(s3.id).isDroppable(t3, (e2) => l3.error = e2), l3;
  }, getOptions: () => t2, getNodes: () => e.nodes, node: (t3) => $e(e, t3), getSerializedNodes() {
    const t3 = Object.keys(e.nodes).map((e2) => [e2, this.node(e2).toSerializedNode()]);
    return Le(t3);
  }, getEvent: (t3) => function(e2, t4) {
    var n3 = e2.events[t4];
    return { contains: function(e3) {
      return n3.has(e3);
    }, isEmpty: function() {
      return 0 === this.all().length;
    }, first: function() {
      return this.all()[0];
    }, last: function() {
      var e3 = this.all();
      return e3[e3.length - 1];
    }, all: function() {
      return Array.from(n3);
    }, size: function() {
      return this.all().length;
    }, at: function(e3) {
      return this.all()[e3];
    }, raw: function() {
      return n3;
    } };
  }(e, t3), serialize() {
    return JSON.stringify(this.getSerializedNodes());
  }, parseReactElement: (t3) => ({ toNodeTree(r2) {
    let o3 = function(e2, t4) {
      let n3 = e2;
      return "string" == typeof n3 && (n3 = import_react2.default.createElement(import_react2.Fragment, {}, n3)), Ue({ data: { type: n3.type, props: { ...n3.props } } }, (e3) => {
        t4 && t4(e3, n3);
      });
    }(t3, (t4, n3) => {
      const o4 = Me(e.options.resolver, t4.data.type);
      t4.data.displayName = t4.data.displayName || o4, t4.data.name = o4, r2 && r2(t4, n3);
    }), a3 = [];
    return t3.props && t3.props.children && (a3 = import_react2.default.Children.toArray(t3.props.children).reduce((e2, t4) => (import_react2.default.isValidElement(t4) && e2.push(n2().parseReactElement(t4).toNodeTree(r2)), e2), [])), Ge(o3, a3);
  } }), parseSerializedNode: (t3) => ({ toNode(r2) {
    const a3 = Xe(t3, e.options.resolver);
    invariant(a3.type, H2);
    const i2 = "string" == typeof r2 && r2;
    return i2 && Pe("query.parseSerializedNode(...).toNode(id)", { suggest: "query.parseSerializedNode(...).toNode(node => node.id = id)" }), n2().parseFreshNode({ ...i2 ? { id: i2 } : {}, data: a3 }).toNode(!i2 && r2);
  } }), parseFreshNode: (t3) => ({ toNode: (n3) => Ue(t3, (t4) => {
    t4.data.parent === g2 && (t4.data.parent = m2);
    const r2 = Me(e.options.resolver, t4.data.type);
    invariant(null !== r2, H2), t4.data.displayName = t4.data.displayName || r2, t4.data.name = r2, n3 && n3(t4);
  }) }), createNode(e2, t3) {
    Pe(`query.createNode(${e2})`, { suggest: `query.parseReactElement(${e2}).toNodeTree()` });
    const n3 = this.parseReactElement(e2).toNodeTree(), r2 = n3.nodes[n3.rootNodeId];
    return t3 ? (t3.id && (r2.id = t3.id), t3.data && (r2.data = { ...r2.data, ...t3.data }), r2) : r2;
  }, getState: () => e };
}
var Qe = function(e) {
  te2(n2, be);
  var t2 = ie2(n2);
  function n2() {
    return K2(this, n2), t2.apply(this, arguments);
  }
  return Z3(n2, [{ key: "handlers", value: function() {
    return { connect: function(e2, t3) {
    }, select: function(e2, t3) {
    }, hover: function(e2, t3) {
    }, drag: function(e2, t3) {
    }, drop: function(e2, t3) {
    }, create: function(e2, t3, n3) {
    } };
  } }]), n2;
}();
var Ze = function(e) {
  te2(n2, me);
  var t2 = ie2(n2);
  function n2() {
    return K2(this, n2), t2.apply(this, arguments);
  }
  return Z3(n2);
}();
var et = function(e) {
  e.preventDefault();
};
var tt = function() {
  function e(t2, n2) {
    K2(this, e), ee2(this, "store", void 0), ee2(this, "dragTarget", void 0), ee2(this, "currentDropTargetId", void 0), ee2(this, "currentDropTargetCanvasAncestorId", void 0), ee2(this, "currentIndicator", null), ee2(this, "currentTargetId", void 0), ee2(this, "currentTargetChildDimensions", void 0), ee2(this, "dragError", void 0), ee2(this, "draggedNodes", void 0), ee2(this, "onScrollListener", void 0), this.store = t2, this.dragTarget = n2, this.currentDropTargetId = null, this.currentDropTargetCanvasAncestorId = null, this.currentTargetId = null, this.currentTargetChildDimensions = null, this.currentIndicator = null, this.dragError = null, this.draggedNodes = this.getDraggedNodes(), this.validateDraggedNodes(), this.onScrollListener = this.onScroll.bind(this), window.addEventListener("scroll", this.onScrollListener, true), window.addEventListener("dragover", et, false);
  }
  return Z3(e, [{ key: "cleanup", value: function() {
    window.removeEventListener("scroll", this.onScrollListener, true), window.removeEventListener("dragover", et, false);
  } }, { key: "onScroll", value: function(e2) {
    var t2 = e2.target, n2 = this.store.query.node(m2).get();
    t2 instanceof Element && n2 && n2.dom && t2.contains(n2.dom) && (this.currentTargetChildDimensions = null);
  } }, { key: "getDraggedNodes", value: function() {
    return qe(this.store.query.getNodes(), "new" === this.dragTarget.type ? this.dragTarget.tree.nodes[this.dragTarget.tree.rootNodeId] : this.dragTarget.nodes);
  } }, { key: "validateDraggedNodes", value: function() {
    var e2 = this;
    "new" !== this.dragTarget.type && this.draggedNodes.forEach(function(t2) {
      t2.exists && e2.store.query.node(t2.node.id).isDraggable(function(t3) {
        e2.dragError = t3;
      });
    });
  } }, { key: "isNearBorders", value: function(t2, n2, r2) {
    return t2.top + e.BORDER_OFFSET > r2 || t2.bottom - e.BORDER_OFFSET < r2 || t2.left + e.BORDER_OFFSET > n2 || t2.right - e.BORDER_OFFSET < n2;
  } }, { key: "isDiff", value: function(e2) {
    return !this.currentIndicator || this.currentIndicator.placement.parent.id !== e2.parent.id || this.currentIndicator.placement.index !== e2.index || this.currentIndicator.placement.where !== e2.where;
  } }, { key: "getChildDimensions", value: function(e2) {
    var t2 = this, n2 = this.currentTargetChildDimensions;
    return this.currentTargetId === e2.id && n2 ? n2 : e2.data.nodes.reduce(function(e3, n3) {
      var r2 = t2.store.query.node(n3).get().dom;
      return r2 && e3.push(Y3({ id: n3 }, de(r2))), e3;
    }, []);
  } }, { key: "getCanvasAncestor", value: function(e2) {
    var t2 = this;
    if (e2 === this.currentDropTargetId && this.currentDropTargetCanvasAncestorId) {
      var n2 = this.store.query.node(this.currentDropTargetCanvasAncestorId).get();
      if (n2)
        return n2;
    }
    return function e3(n3) {
      var r2 = t2.store.query.node(n3).get();
      return r2 && r2.data.isCanvas ? r2 : r2.data.parent ? e3(r2.data.parent) : null;
    }(e2);
  } }, { key: "computeIndicator", value: function(e2, t2, n2) {
    var r2 = this.getCanvasAncestor(e2);
    if (r2 && (this.currentDropTargetId = e2, this.currentDropTargetCanvasAncestorId = r2.id, r2.data.parent && this.isNearBorders(de(r2.dom), t2, n2) && !this.store.query.node(r2.id).isLinkedNode() && (r2 = this.store.query.node(r2.data.parent).get()), r2)) {
      this.currentTargetChildDimensions = this.getChildDimensions(r2), this.currentTargetId = r2.id;
      var o3 = Je(r2, this.currentTargetChildDimensions, t2, n2);
      if (this.isDiff(o3)) {
        var a3 = this.dragError;
        a3 || this.store.query.node(r2.id).isDroppable(this.draggedNodes.map(function(e3) {
          return e3.node;
        }), function(e3) {
          a3 = e3;
        });
        var i2 = r2.data.nodes[o3.index], s3 = i2 && this.store.query.node(i2).get();
        return this.currentIndicator = { placement: Y3(Y3({}, o3), {}, { currentNode: s3 }), error: a3 }, this.currentIndicator;
      }
    }
  } }, { key: "getIndicator", value: function() {
    return this.currentIndicator;
  } }]), e;
}();
ee2(tt, "BORDER_OFFSET", 10);
var nt = function(e, t2) {
  if (1 === t2.length || arguments.length > 2 && void 0 !== arguments[2] && arguments[2]) {
    var n2 = t2[0].getBoundingClientRect(), r2 = n2.width, o3 = n2.height, a3 = t2[0].cloneNode(true);
    return a3.style.position = "absolute", a3.style.left = "-100%", a3.style.top = "-100%", a3.style.width = "".concat(r2, "px"), a3.style.height = "".concat(o3, "px"), a3.style.pointerEvents = "none", a3.classList.add("drag-shadow"), document.body.appendChild(a3), e.dataTransfer.setDragImage(a3, 0, 0), a3;
  }
  var i2 = document.createElement("div");
  return i2.style.position = "absolute", i2.style.left = "-100%", i2.style.top = "-100%", i2.style.width = "100%", i2.style.height = "100%", i2.style.pointerEvents = "none", i2.classList.add("drag-shadow-container"), t2.forEach(function(e2) {
    var t3 = e2.getBoundingClientRect(), n3 = t3.width, r3 = t3.height, o4 = t3.top, a4 = t3.left, s3 = e2.cloneNode(true);
    s3.style.position = "absolute", s3.style.left = "".concat(a4, "px"), s3.style.top = "".concat(o4, "px"), s3.style.width = "".concat(n3, "px"), s3.style.height = "".concat(r3, "px"), s3.classList.add("drag-shadow"), i2.appendChild(s3);
  }), document.body.appendChild(i2), e.dataTransfer.setDragImage(i2, e.clientX, e.clientY), i2;
};
var rt = function(e) {
  te2(n2, Qe);
  var t2 = ie2(n2);
  function n2() {
    var e2;
    K2(this, n2);
    for (var r2 = arguments.length, o3 = new Array(r2), a3 = 0; a3 < r2; a3++)
      o3[a3] = arguments[a3];
    return ee2(ae2(e2 = t2.call.apply(t2, [this].concat(o3))), "draggedElementShadow", void 0), ee2(ae2(e2), "dragTarget", void 0), ee2(ae2(e2), "positioner", null), ee2(ae2(e2), "currentSelectedElementIds", []), e2;
  }
  return Z3(n2, [{ key: "onDisable", value: function() {
    this.options.store.actions.clearEvents();
  } }, { key: "handlers", value: function() {
    var e2 = this, t3 = this.options.store;
    return { connect: function(n3, r2) {
      return t3.actions.setDOM(r2, n3), e2.reflect(function(e3) {
        e3.select(n3, r2), e3.hover(n3, r2), e3.drop(n3, r2);
      });
    }, select: function(n3, r2) {
      var o3 = e2.addCraftEventListener(n3, "mousedown", function(n4) {
        n4.craft.stopPropagation();
        var o4 = [];
        if (r2) {
          var a4 = t3.query, i2 = a4.getEvent("selected").all();
          (e2.options.isMultiSelectEnabled(n4) || i2.includes(r2)) && (o4 = i2.filter(function(e3) {
            var t4 = a4.node(e3).descendants(true), n5 = a4.node(e3).ancestors(true);
            return !t4.includes(r2) && !n5.includes(r2);
          })), o4.includes(r2) || o4.push(r2);
        }
        t3.actions.setNodeEvent("selected", o4);
      }), a3 = e2.addCraftEventListener(n3, "click", function(n4) {
        n4.craft.stopPropagation();
        var o4 = t3.query.getEvent("selected").all(), a4 = e2.options.isMultiSelectEnabled(n4), i2 = e2.currentSelectedElementIds.includes(r2), s3 = de2(o4);
        a4 && i2 ? (s3.splice(s3.indexOf(r2), 1), t3.actions.setNodeEvent("selected", s3)) : !a4 && o4.length > 1 && t3.actions.setNodeEvent("selected", s3 = [r2]), e2.currentSelectedElementIds = s3;
      });
      return function() {
        o3(), a3();
      };
    }, hover: function(n3, r2) {
      var o3 = e2.addCraftEventListener(n3, "mouseover", function(e3) {
        e3.craft.stopPropagation(), t3.actions.setNodeEvent("hovered", r2);
      }), a3 = null;
      return e2.options.removeHoverOnMouseleave && (a3 = e2.addCraftEventListener(n3, "mouseleave", function(e3) {
        e3.craft.stopPropagation(), t3.actions.setNodeEvent("hovered", null);
      })), function() {
        o3(), a3 && a3();
      };
    }, drop: function(n3, r2) {
      var o3 = e2.addCraftEventListener(n3, "dragover", function(n4) {
        if (n4.craft.stopPropagation(), n4.preventDefault(), e2.positioner) {
          var o4 = e2.positioner.computeIndicator(r2, n4.clientX, n4.clientY);
          o4 && t3.actions.setIndicator(o4);
        }
      }), a3 = e2.addCraftEventListener(n3, "dragenter", function(e3) {
        e3.craft.stopPropagation(), e3.preventDefault();
      });
      return function() {
        a3(), o3();
      };
    }, drag: function(r2, o3) {
      if (!t3.query.node(o3).isDraggable())
        return function() {
        };
      r2.setAttribute("draggable", "true");
      var a3 = e2.addCraftEventListener(r2, "dragstart", function(r3) {
        r3.craft.stopPropagation();
        var a4 = t3.query, i3 = t3.actions, s3 = a4.getEvent("selected").all(), d3 = e2.options.isMultiSelectEnabled(r3);
        e2.currentSelectedElementIds.includes(o3) || (s3 = d3 ? [].concat(de2(s3), [o3]) : [o3], t3.actions.setNodeEvent("selected", s3)), i3.setNodeEvent("dragged", s3);
        var c3 = s3.map(function(e3) {
          return a4.node(e3).get().dom;
        });
        e2.draggedElementShadow = nt(r3, c3, n2.forceSingleDragShadow), e2.dragTarget = { type: "existing", nodes: s3 }, e2.positioner = new tt(e2.options.store, e2.dragTarget);
      }), i2 = e2.addCraftEventListener(r2, "dragend", function(n3) {
        n3.craft.stopPropagation(), e2.dropElement(function(e3, n4) {
          "new" !== e3.type && t3.actions.move(e3.nodes, n4.placement.parent.id, n4.placement.index + ("after" === n4.placement.where ? 1 : 0));
        });
      });
      return function() {
        r2.setAttribute("draggable", "false"), a3(), i2();
      };
    }, create: function(r2, o3, a3) {
      r2.setAttribute("draggable", "true");
      var i2 = e2.addCraftEventListener(r2, "dragstart", function(r3) {
        var a4;
        if (r3.craft.stopPropagation(), "function" == typeof o3) {
          var i3 = o3();
          a4 = import_react2.default.isValidElement(i3) ? t3.query.parseReactElement(i3).toNodeTree() : i3;
        } else
          a4 = t3.query.parseReactElement(o3).toNodeTree();
        e2.draggedElementShadow = nt(r3, [r3.currentTarget], n2.forceSingleDragShadow), e2.dragTarget = { type: "new", tree: a4 }, e2.positioner = new tt(e2.options.store, e2.dragTarget);
      }), s3 = e2.addCraftEventListener(r2, "dragend", function(n3) {
        n3.craft.stopPropagation(), e2.dropElement(function(e3, n4) {
          "existing" !== e3.type && (t3.actions.addNodeTree(e3.tree, n4.placement.parent.id, n4.placement.index + ("after" === n4.placement.where ? 1 : 0)), a3 && (0, import_isFunction.default)(a3.onCreate) && a3.onCreate(e3.tree));
        });
      });
      return function() {
        r2.removeAttribute("draggable"), i2(), s3();
      };
    } };
  } }, { key: "dropElement", value: function(e2) {
    var t3 = this.options.store;
    if (this.positioner) {
      var n3 = this.draggedElementShadow, r2 = this.positioner.getIndicator();
      this.dragTarget && r2 && !r2.error && e2(this.dragTarget, r2), n3 && (n3.parentNode.removeChild(n3), this.draggedElementShadow = null), this.dragTarget = null, t3.actions.setIndicator(null), t3.actions.setNodeEvent("dragged", null), this.positioner.cleanup(), this.positioner = null;
    }
  } }]), n2;
}();
function ot(e, t2, n2) {
  var r2 = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 2, o3 = 0, a3 = 0, i2 = 0, s3 = 0, d3 = e.where;
  return n2 ? n2.inFlow ? (i2 = n2.outerWidth, s3 = r2, o3 = "before" === d3 ? n2.top : n2.bottom, a3 = n2.left) : (i2 = r2, s3 = n2.outerHeight, o3 = n2.top, a3 = "before" === d3 ? n2.left : n2.left + n2.outerWidth) : t2 && (o3 = t2.top + t2.padding.top, a3 = t2.left + t2.padding.left, i2 = t2.outerWidth - t2.padding.right - t2.padding.left - t2.margin.left - t2.margin.right, s3 = r2), { top: "".concat(o3, "px"), left: "".concat(a3, "px"), width: "".concat(i2, "px"), height: "".concat(s3, "px") };
}
ee2(rt, "forceSingleDragShadow", Ce() && Te());
var at = () => {
  const { indicator: e, indicatorOptions: t2, enabled: n2 } = he2((e2) => ({ indicator: e2.indicator, indicatorOptions: e2.options.indicator, enabled: e2.options.enabled })), r2 = ve2();
  return (0, import_react2.useEffect)(() => {
    r2 && (n2 ? r2.enable() : r2.disable());
  }, [n2, r2]), e ? import_react2.default.createElement(Re, { className: t2.className, style: { ...ot(e.placement, de(e.placement.parent.dom), e.placement.currentNode && de(e.placement.currentNode.dom), t2.thickness), backgroundColor: e.error ? t2.error : t2.success, transition: t2.transition || "0.2s ease-in", ...t2.style ?? {} }, parentDom: e.placement.parent.dom }) : null;
};
var it = ({ children: e }) => {
  const t2 = (0, import_react2.useContext)(fe2), n2 = (0, import_react2.useMemo)(() => t2.query.getOptions().handlers(t2), [t2]);
  return n2 ? import_react2.default.createElement(pe2.Provider, { value: n2 }, import_react2.default.createElement(at, null), e) : null;
};
var st = { nodes: {}, events: { dragged: /* @__PURE__ */ new Set(), selected: /* @__PURE__ */ new Set(), hovered: /* @__PURE__ */ new Set() }, indicator: null, options: { onNodesChange: () => null, onRender: ({ render: e }) => e, onBeforeMoveEnd: () => null, resolver: {}, enabled: true, indicator: { error: "red", success: "rgb(98, 196, 98)" }, handlers: (e) => new rt({ store: e, removeHoverOnMouseleave: false, isMultiSelectEnabled: (e2) => !!e2.metaKey }), normalizeNodes: () => {
} } };
var dt = { methods: function(e, t2) {
  return Y3(Y3({}, function(e2, t3) {
    var n2 = function(t4, n3, o3) {
      if (function n4(r3, o4) {
        var a5 = t4.nodes[r3];
        "string" != typeof a5.data.type && invariant(e2.options.resolver[a5.data.name], H2.replace("%node_type%", "".concat(a5.data.type.name))), e2.nodes[r3] = Y3(Y3({}, a5), {}, { data: Y3(Y3({}, a5.data), {}, { parent: o4 }) }), a5.data.nodes.length > 0 && (delete e2.nodes[r3].data.props.children, a5.data.nodes.forEach(function(e3) {
          return n4(e3, a5.id);
        })), Object.values(a5.data.linkedNodes).forEach(function(e3) {
          return n4(e3, a5.id);
        });
      }(t4.rootNodeId, n3), n3 || t4.rootNodeId !== m2) {
        var a4 = r2(n3);
        if ("child" !== o3.type)
          a4.data.linkedNodes[o3.id] = t4.rootNodeId;
        else {
          var s3 = o3.index;
          null != s3 ? a4.data.nodes.splice(s3, 0, t4.rootNodeId) : a4.data.nodes.push(t4.rootNodeId);
        }
      }
    }, r2 = function(t4) {
      invariant(t4, E2);
      var n3 = e2.nodes[t4];
      return invariant(n3, O2), n3;
    }, a3 = function t4(n3) {
      var r3 = e2.nodes[n3], o3 = e2.nodes[r3.data.parent];
      if (r3.data.nodes && de2(r3.data.nodes).forEach(function(e3) {
        return t4(e3);
      }), r3.data.linkedNodes && Object.values(r3.data.linkedNodes).map(function(e3) {
        return t4(e3);
      }), o3.data.nodes.includes(n3)) {
        var a4 = o3.data.nodes;
        a4.splice(a4.indexOf(n3), 1);
      } else {
        var i2 = Object.keys(o3.data.linkedNodes).find(function(e3) {
          return o3.data.linkedNodes[e3] === e3;
        });
        i2 && delete o3.data.linkedNodes[i2];
      }
      !function(e3, t5) {
        Object.keys(e3.events).forEach(function(n4) {
          var r4 = e3.events[n4];
          r4 && r4.has && r4.has(t5) && (e3.events[n4] = new Set(Array.from(r4).filter(function(e4) {
            return t5 !== e4;
          })));
        });
      }(e2, n3), delete e2.nodes[n3];
    };
    return { addLinkedNodeFromTree: function(e3, t4, o3) {
      var i2 = r2(t4).data.linkedNodes[o3];
      i2 && a3(i2), n2(e3, t4, { type: "linked", id: o3 });
    }, add: function(e3, t4, r3) {
      var a4 = [e3];
      Array.isArray(e3) && (Pe("actions.add(node: Node[])", { suggest: "actions.add(node: Node)" }), a4 = e3), a4.forEach(function(e4) {
        n2({ nodes: ee2({}, e4.id, e4), rootNodeId: e4.id }, t4, { type: "child", index: r3 });
      });
    }, addNodeTree: function(e3, t4, r3) {
      n2(e3, t4, { type: "child", index: r3 });
    }, delete: function(n3) {
      qe(e2.nodes, n3, { existOnly: true, idOnly: true }).forEach(function(e3) {
        var n4 = e3.node;
        invariant(!t3.node(n4.id).isTopLevelNode(), L2), a3(n4.id);
      });
    }, deserialize: function(e3) {
      var n3 = "string" == typeof e3 ? JSON.parse(e3) : e3, r3 = Object.keys(n3).map(function(e4) {
        var r4 = e4;
        return e4 === g2 && (r4 = m2), [r4, t3.parseSerializedNode(n3[e4]).toNode(function(e5) {
          return e5.id = r4;
        })];
      });
      this.replaceNodes(Le(r3));
    }, move: function(n3, r3, o3) {
      var a4 = qe(e2.nodes, n3, { existOnly: true }), i2 = e2.nodes[r3], s3 = /* @__PURE__ */ new Set();
      a4.forEach(function(n4, a5) {
        var d3 = n4.node, c3 = d3.id, u3 = d3.data.parent;
        t3.node(r3).isDroppable([c3], function(e3) {
          throw new Error(e3);
        }), e2.options.onBeforeMoveEnd(d3, i2, e2.nodes[u3]);
        var l3 = e2.nodes[u3].data.nodes;
        s3.add(l3);
        var f3 = l3.indexOf(c3);
        l3[f3] = "$$", i2.data.nodes.splice(o3 + a5, 0, c3), e2.nodes[c3].data.parent = r3;
      }), s3.forEach(function(e3) {
        var t4 = e3.length;
        de2(e3).reverse().forEach(function(n4, r4) {
          "$$" === n4 && e3.splice(t4 - 1 - r4, 1);
        });
      });
    }, replaceNodes: function(t4) {
      this.clearEvents(), e2.nodes = t4;
    }, clearEvents: function() {
      this.setNodeEvent("selected", null), this.setNodeEvent("hovered", null), this.setNodeEvent("dragged", null), this.setIndicator(null);
    }, reset: function() {
      this.clearEvents(), this.replaceNodes({});
    }, setOptions: function(t4) {
      t4(e2.options);
    }, setNodeEvent: function(t4, n3) {
      if (e2.events[t4].forEach(function(n4) {
        e2.nodes[n4] && (e2.nodes[n4].events[t4] = false);
      }), e2.events[t4] = /* @__PURE__ */ new Set(), n3) {
        var r3 = qe(e2.nodes, n3, { idOnly: true, existOnly: true }), o3 = new Set(r3.map(function(e3) {
          return e3.node.id;
        }));
        o3.forEach(function(n4) {
          e2.nodes[n4].events[t4] = true;
        }), e2.events[t4] = o3;
      }
    }, setCustom: function(t4, n3) {
      qe(e2.nodes, t4, { idOnly: true, existOnly: true }).forEach(function(t5) {
        return n3(e2.nodes[t5.node.id].data.custom);
      });
    }, setDOM: function(t4, n3) {
      e2.nodes[t4] && (e2.nodes[t4].dom = n3);
    }, setIndicator: function(t4) {
      t4 && (!t4.placement.parent.dom || t4.placement.currentNode && !t4.placement.currentNode.dom) || (e2.indicator = t4);
    }, setHidden: function(t4, n3) {
      e2.nodes[t4].data.hidden = n3;
    }, setProp: function(t4, n3) {
      qe(e2.nodes, t4, { idOnly: true, existOnly: true }).forEach(function(t5) {
        return n3(e2.nodes[t5.node.id].data.props);
      });
    }, selectNode: function(t4) {
      if (t4) {
        var n3 = qe(e2.nodes, t4, { idOnly: true, existOnly: true });
        this.setNodeEvent("selected", n3.map(function(e3) {
          return e3.node.id;
        }));
      } else
        this.setNodeEvent("selected", null);
      this.setNodeEvent("hovered", null);
    } };
  }(e, t2)), {}, { setState: function(t3) {
    var n2 = oe2(this, _e);
    t3(e, n2);
  } });
}, ignoreHistoryForActions: ["setDOM", "setNodeEvent", "selectNode", "clearEvents", "setOptions", "setIndicator"], normalizeHistory: (e) => {
  Object.keys(e.events).forEach((t2) => {
    Array.from(e.events[t2] || []).forEach((n2) => {
      e.nodes[n2] || e.events[t2].delete(n2);
    });
  }), Object.keys(e.nodes).forEach((t2) => {
    const n2 = e.nodes[t2];
    Object.keys(n2.events).forEach((t3) => {
      n2.events[t3] && e.events[t3] && !e.events[t3].has(n2.id) && (n2.events[t3] = false);
    });
  });
} };
var ct = (e, t2) => se(dt, { ...st, options: { ...st.options, ...e } }, Ke, t2);
var ut = ({ children: e, ...t2 }) => {
  void 0 !== t2.resolver && invariant("object" == typeof t2.resolver && !Array.isArray(t2.resolver) && null !== t2.resolver, M2);
  const n2 = R3.useRef(t2), r2 = ct(n2.current, (e2, t3, n3, r3, o3) => {
    if (!n3)
      return;
    const { patches: a3, ...i2 } = n3;
    for (let n4 = 0; n4 < a3.length; n4++) {
      const { path: s3 } = a3[n4], d3 = s3.length > 2 && "nodes" === s3[0] && "data" === s3[2];
      if ([ae.IGNORE, ae.THROTTLE].includes(i2.type) && i2.params && (i2.type = i2.params[0]), ["setState", "deserialize"].includes(i2.type) || d3) {
        o3((n5) => {
          e2.options.normalizeNodes && e2.options.normalizeNodes(n5, t3, i2, r3);
        });
        break;
      }
    }
  });
  return R3.useEffect(() => {
    r2 && void 0 !== t2.enabled && r2.query.getOptions().enabled !== t2.enabled && r2.actions.setOptions((e2) => {
      e2.enabled = t2.enabled;
    });
  }, [r2, t2.enabled]), R3.useEffect(() => {
    r2.subscribe((e2) => ({ json: r2.query.serialize() }), () => {
      r2.query.getOptions().onNodesChange(r2.query);
    });
  }, [r2]), r2 ? R3.createElement(fe2.Provider, { value: r2 }, R3.createElement(it, null, e)) : null;
};
var lt = ["events", "data"];
var ft = ["nodes"];
var pt = ["nodes"];
var vt = ["_hydrationTimestamp", "rules"];
var ht = ["_hydrationTimestamp", "rules"];
var yt = function(e) {
  var t2 = e.events, n2 = e.data, r2 = n2.nodes, o3 = n2.linkedNodes, a3 = oe2(e, lt), i2 = Ue((0, import_cloneDeep.default)(e));
  return { node: e = Y3(Y3(Y3({}, i2), a3), {}, { events: Y3(Y3({}, i2.events), t2), dom: e.dom || i2.dom }), childNodes: r2, linkedNodes: o3 };
};
var gt = function(e, t2) {
  var n2 = t2.nodes, r2 = oe2(t2, ft), o3 = e.nodes, a3 = oe2(e, pt);
  expect(a3).toEqual(r2);
  var i2 = Object.keys(n2).reduce(function(e2, t3) {
    var r3 = oe2(n2[t3], vt);
    return e2[t3] = r3, e2;
  }, {}), s3 = Object.keys(o3).reduce(function(e2, t3) {
    var n3 = oe2(o3[t3], ht);
    return e2[t3] = n3, e2;
  }, {});
  expect(s3).toEqual(i2);
};
var mt = function(e) {
  var t2 = {};
  return function e2(n2) {
    var r2 = yt(n2), o3 = r2.node, a3 = r2.childNodes, i2 = r2.linkedNodes;
    t2[o3.id] = o3, a3 && a3.forEach(function(n3, r3) {
      var a4 = yt(n3), i3 = a4.node, s3 = a4.childNodes, d3 = a4.linkedNodes;
      i3.data.parent = o3.id, t2[i3.id] = i3, o3.data.nodes[r3] = i3.id, e2(Y3(Y3({}, i3), {}, { data: Y3(Y3({}, i3.data), {}, { nodes: s3 || [], linkedNodes: d3 || {} }) }));
    }), i2 && Object.keys(i2).forEach(function(n3) {
      var r3 = yt(i2[n3]), a4 = r3.node, s3 = r3.childNodes, d3 = r3.linkedNodes;
      o3.data.linkedNodes[n3] = a4.id, a4.data.parent = o3.id, t2[a4.id] = a4, e2(Y3(Y3({}, a4), {}, { data: Y3(Y3({}, a4.data), {}, { nodes: s3 || [], linkedNodes: d3 || {} }) }));
    });
  }(e), t2;
};
var Nt = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t2 = e.nodes, n2 = e.events;
  return Y3(Y3(Y3({}, st), e), {}, { nodes: t2 ? mt(t2) : {}, events: Y3(Y3({}, st.events), n2 || {}) });
};
export {
  dt as ActionMethodsWithConfig,
  Canvas,
  Qe as CoreEventHandlers,
  rt as DefaultEventHandlers,
  Ze as DerivedCoreEventHandlers,
  ut as Editor,
  ke as Element,
  it as Events,
  De as Frame,
  we2 as NodeElement,
  $e as NodeHelpers,
  V3 as NodeProvider,
  Ie as NodeSelectorType,
  tt as Positioner,
  Ke as QueryMethods,
  m2 as ROOT_NODE,
  Re2 as connectEditor,
  Ae as connectNode,
  nt as createShadow,
  mt as createTestNodes,
  Nt as createTestState,
  Te2 as defaultElementProps,
  Se as deprecateCanvasComponent,
  st as editorInitialState,
  Ce2 as elementPropToNodeData,
  gt as expectEditorState,
  He as serializeNode,
  Pe2 as useEditor,
  ct as useEditorStore,
  ve2 as useEventHandler,
  Ne as useNode
};
//# sourceMappingURL=@craftjs_core.js.map
