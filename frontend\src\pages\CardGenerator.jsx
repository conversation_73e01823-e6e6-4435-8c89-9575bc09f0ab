import { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { Download, Eye, CreditCard, User } from 'lucide-react'
import { studentAPI, templateAPI, cardAPI } from '../services/api'
import { CardPreview } from '../components/craft/CardPreview'
import toast from 'react-hot-toast'

export default function CardGenerator() {
  const { studentId } = useParams()
  const [searchParams] = useSearchParams()
  const templateId = searchParams.get('template')
  
  const [students, setStudents] = useState([])
  const [templates, setTemplates] = useState([])
  const [selectedStudent, setSelectedStudent] = useState(studentId || '')
  const [selectedTemplate, setSelectedTemplate] = useState(templateId || '')
  const [generating, setGenerating] = useState(false)
  const [generatedCards, setGeneratedCards] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    if (selectedStudent) {
      fetchGeneratedCards()
    }
  }, [selectedStudent])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch students
      const studentsResponse = await studentAPI.getAll({ limit: 100 })
      setStudents(studentsResponse.data.students || [])
      
      // Fetch templates
      const templatesResponse = await templateAPI.getAll()
      setTemplates(templatesResponse.data)
      
      // Set default template if not specified
      if (!selectedTemplate && templatesResponse.data.length > 0) {
        const defaultTemplate = templatesResponse.data.find(t => t.is_default) || templatesResponse.data[0]
        setSelectedTemplate(defaultTemplate.id.toString())
      }
      
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const fetchGeneratedCards = async () => {
    if (!selectedStudent) return
    
    try {
      const response = await cardAPI.getGenerated(selectedStudent)
      setGeneratedCards(response.data)
    } catch (error) {
      console.error('Error fetching generated cards:', error)
    }
  }

  const handleGenerateCard = async () => {
    if (!selectedStudent) {
      toast.error('Please select a student')
      return
    }

    if (!selectedTemplate) {
      toast.error('Please select a template')
      return
    }

    try {
      setGenerating(true)

      const response = await cardAPI.generateWithTemplate(selectedStudent, selectedTemplate)
      toast.success('ID card generated successfully!')

      // Auto-download the generated card
      if (response.data.cardUrl) {
        const downloadUrl = `http://localhost:5001${response.data.cardUrl}`
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = `id_card_${selectedStudentData?.student_id || selectedStudent}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        toast.success('Card downloaded successfully!')
      }

      // Refresh generated cards list
      fetchGeneratedCards()

    } catch (error) {
      console.error('Error generating card:', error)
      toast.error('Failed to generate ID card')
    } finally {
      setGenerating(false)
    }
  }

  const handleBulkGenerate = async () => {
    if (!selectedTemplate) {
      toast.error('Please select a template')
      return
    }

    if (students.length === 0) {
      toast.error('No students found')
      return
    }

    try {
      setGenerating(true)
      let successCount = 0
      let errorCount = 0

      toast.info(`Starting bulk generation for ${students.length} students...`)

      for (const student of students) {
        try {
          const response = await cardAPI.generateWithTemplate(student.id, selectedTemplate)
          successCount++

          // Auto-download each card
          if (response.data.cardUrl) {
            const downloadUrl = `http://localhost:5001${response.data.cardUrl}`
            const link = document.createElement('a')
            link.href = downloadUrl
            link.download = `id_card_${student.student_id}.png`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }

          // Small delay to prevent overwhelming the server
          await new Promise(resolve => setTimeout(resolve, 500))
        } catch (error) {
          console.error(`Error generating card for student ${student.student_id}:`, error)
          errorCount++
        }
      }

      if (successCount > 0) {
        toast.success(`Successfully generated ${successCount} ID cards!`)
      }
      if (errorCount > 0) {
        toast.error(`Failed to generate ${errorCount} cards`)
      }

      // Refresh generated cards list
      fetchGeneratedCards()

    } catch (error) {
      console.error('Error in bulk generation:', error)
      toast.error('Failed to start bulk generation')
    } finally {
      setGenerating(false)
    }
  }

  const handlePreview = async () => {
    if (!selectedStudent || !selectedTemplate) {
      toast.error('Please select both student and template')
      return
    }

    try {
      const studentData = selectedStudentData
      const templateData = selectedTemplateData

      if (!studentData || !templateData) {
        toast.error('Student or template data not found')
        return
      }

      // Create preview window with CardPreview component
      const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes')
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>ID Card Preview</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: Arial, sans-serif;
              background: #f5f5f5;
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
            }
            .preview-container {
              background: white;
              padding: 30px;
              border-radius: 12px;
              box-shadow: 0 8px 25px rgba(0,0,0,0.15);
              text-align: center;
            }
            .preview-title {
              margin-bottom: 20px;
              color: #333;
              font-size: 24px;
              font-weight: bold;
            }
            .card-preview {
              border: 2px solid #ddd;
              background: ${templateData.template_data?.backgroundColor || '#ffffff'};
              width: ${templateData.template_data?.width || 400}px;
              height: ${templateData.template_data?.height || 250}px;
              position: relative;
              margin: 0 auto 20px;
              box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            .student-info {
              position: absolute;
              top: 20px;
              left: 20px;
              right: 20px;
            }
            .student-name {
              font-size: 18px;
              font-weight: bold;
              color: #333;
              margin-bottom: 8px;
            }
            .student-details {
              font-size: 14px;
              color: #666;
              line-height: 1.4;
            }
            .photo-placeholder {
              position: absolute;
              top: 20px;
              right: 20px;
              width: 80px;
              height: 100px;
              border: 2px dashed #ccc;
              background: #f9f9f9;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              color: #999;
            }
            .note {
              color: #666;
              font-size: 14px;
              margin-top: 15px;
              font-style: italic;
            }
          </style>
        </head>
        <body>
          <div class="preview-container">
            <h2 class="preview-title">ID Card Preview</h2>
            <div class="card-preview">
              <div class="student-info">
                <div class="student-name">${studentData.name}</div>
                <div class="student-details">
                  <div>ID: ${studentData.student_id}</div>
                  <div>Course: ${studentData.course}</div>
                  <div>Email: ${studentData.email}</div>
                </div>
              </div>
              <div class="photo-placeholder">
                ${studentData.photo_url ? 'Photo' : 'No Photo'}
              </div>
            </div>
            <p class="note">
              This is a simplified preview. The actual generated card will use the full template design.
            </p>
          </div>
        </body>
        </html>
      `)
      previewWindow.document.close()

      toast.success('Preview opened in new window')

    } catch (error) {
      console.error('Error generating preview:', error)
      toast.error('Failed to generate preview')
    }
  }

  const selectedStudentData = students.find(s => s.id.toString() === selectedStudent)
  const selectedTemplateData = templates.find(t => t.id.toString() === selectedTemplate)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <div className="space-y-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
            <div className="card p-6">
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">ID Card Generator</h1>
        <p className="text-gray-600">Generate ID cards for individual students or bulk generation</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Selection Panel */}
        <div className="space-y-6">
          {/* Student Selection */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Student</h3>
            <div className="space-y-4">
              <select
                value={selectedStudent}
                onChange={(e) => setSelectedStudent(e.target.value)}
                className="input"
              >
                <option value="">Choose a student...</option>
                {students.map(student => (
                  <option key={student.id} value={student.id}>
                    {student.student_id} - {student.name}
                  </option>
                ))}
              </select>
              
              {selectedStudentData && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-4">
                    {selectedStudentData.photo_url ? (
                      <img
                        src={`http://localhost:5001${selectedStudentData.photo_url}`}
                        alt={selectedStudentData.name}
                        className="h-16 w-16 rounded-lg object-cover"
                      />
                    ) : (
                      <div className="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center">
                        <User className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {selectedStudentData.name}
                      </h4>
                      <p className="text-sm text-gray-600">ID: {selectedStudentData.student_id}</p>
                      <p className="text-sm text-gray-600">{selectedStudentData.course}</p>
                      <p className="text-sm text-gray-600">{selectedStudentData.email}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Template Selection */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Template</h3>
            <div className="space-y-4">
              <select
                value={selectedTemplate}
                onChange={(e) => setSelectedTemplate(e.target.value)}
                className="input"
              >
                <option value="">Choose a template...</option>
                {templates.map(template => (
                  <option key={template.id} value={template.id}>
                    {template.name} {template.is_default ? '(Default)' : ''}
                  </option>
                ))}
              </select>
              
              {selectedTemplateData && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">{selectedTemplateData.name}</h4>
                  <div className="text-sm text-gray-600">
                    <p>Size: {selectedTemplateData.template_data.width} × {selectedTemplateData.template_data.height}px</p>
                    <p>Elements: {selectedTemplateData.template_data.elements?.length || 0}</p>
                  </div>
                  
                  {/* Template Preview */}
                  <div className="mt-3 flex justify-center">
                    <div className="transform scale-50 origin-top">
                      <CardPreview
                        templateData={selectedTemplateData.template_data}
                        studentData={selectedStudentData}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
            <div className="space-y-3">
              <button
                onClick={handlePreview}
                disabled={!selectedStudent || !selectedTemplate}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  minWidth: '140px',
                  whiteSpace: 'nowrap',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '0.75rem 1.5rem',
                  gap: '0.5rem',
                  width: '100%'
                }}
              >
                <Eye className="h-4 w-4" />
                Preview Card
              </button>

              <button
                onClick={handleGenerateCard}
                disabled={!selectedStudent || !selectedTemplate || generating}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  minWidth: '160px',
                  whiteSpace: 'nowrap',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '0.75rem 1.5rem',
                  gap: '0.5rem',
                  width: '100%'
                }}
              >
                <CreditCard className="h-4 w-4" />
                {generating ? 'Generating...' : 'Generate Card'}
              </button>

              <div className="border-t border-gray-200 pt-3 mt-3">
                <p className="text-sm text-gray-600 mb-3">Generate cards for all students:</p>
                <button
                  onClick={handleBulkGenerate}
                  disabled={!selectedTemplate || generating}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    minWidth: '180px',
                    whiteSpace: 'nowrap',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0.75rem 1.5rem',
                    gap: '0.5rem',
                    width: '100%'
                  }}
                >
                  <CreditCard className="h-4 w-4" />
                  {generating ? 'Generating...' : 'Generate All Cards'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Generated Cards History */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Generated Cards</h3>
          </div>
          <div className="card-content">
            {selectedStudent ? (
              generatedCards.length > 0 ? (
                <div className="space-y-4">
                  {generatedCards.map((card) => (
                    <div key={card.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">
                          {card.template_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          Generated on {new Date(card.generated_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                      {card.card_url && (
                        <div className="flex items-center gap-2">
                          <a
                            href={`http://localhost:5001${card.card_url}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="btn-secondary btn-sm"
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </a>
                          <a
                            href={`http://localhost:5001${card.card_url}`}
                            download
                            className="btn-primary btn-sm"
                          >
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </a>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No cards generated</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Generate the first ID card for this student.
                  </p>
                </div>
              )
            ) : (
              <div className="text-center py-8">
                <User className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Select a student</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Choose a student to view their generated cards.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6 text-center">
          <div className="text-2xl font-bold text-blue-600">{students.length}</div>
          <div className="text-sm text-gray-600">Total Students</div>
        </div>
        <div className="card p-6 text-center">
          <div className="text-2xl font-bold text-green-600">{templates.length}</div>
          <div className="text-sm text-gray-600">Available Templates</div>
        </div>
        <div className="card p-6 text-center">
          <div className="text-2xl font-bold text-purple-600">{generatedCards.length}</div>
          <div className="text-sm text-gray-600">Cards Generated</div>
        </div>
      </div>
    </div>
  )
}
