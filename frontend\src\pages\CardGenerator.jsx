import { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { Download, Eye, CreditCard, User } from 'lucide-react'
import { studentAPI, templateAPI, cardAPI } from '../services/api'
import { CardPreview } from '../components/craft/CardPreview'
import toast from 'react-hot-toast'

export default function CardGenerator() {
  const { studentId } = useParams()
  const [searchParams] = useSearchParams()
  const templateId = searchParams.get('template')
  
  const [students, setStudents] = useState([])
  const [templates, setTemplates] = useState([])
  const [selectedTemplate, setSelectedTemplate] = useState(templateId || '')
  const [generating, setGenerating] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Fetch students
      const studentsResponse = await studentAPI.getAll({ limit: 100 })
      setStudents(studentsResponse.data.students || [])
      
      // Fetch templates
      const templatesResponse = await templateAPI.getAll()
      setTemplates(templatesResponse.data)
      
      // Set default template if not specified
      if (!selectedTemplate && templatesResponse.data.length > 0) {
        const defaultTemplate = templatesResponse.data.find(t => t.is_default) || templatesResponse.data[0]
        setSelectedTemplate(defaultTemplate.id.toString())
      }
      
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load data')
    } finally {
      setLoading(false)
    }
  }



  const handleGenerateCard = async () => {
    if (!selectedTemplate) {
      toast.error('Please select a template')
      return
    }

    if (students.length === 0) {
      toast.error('No students found')
      return
    }

    try {
      setGenerating(true)
      let successCount = 0
      let errorCount = 0

      toast(`Starting generation for ${students.length} students...`)

      for (const student of students) {
        try {
          const response = await cardAPI.generateWithTemplate(student.id, selectedTemplate)
          successCount++

          // Auto-download each card
          if (response.data.cardUrl) {
            const downloadUrl = `http://localhost:5001${response.data.cardUrl}`
            const link = document.createElement('a')
            link.href = downloadUrl
            link.download = `id_card_${student.student_id}.png`
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
          }

          // Small delay to prevent overwhelming the server
          await new Promise(resolve => setTimeout(resolve, 500))
        } catch (error) {
          console.error(`Error generating card for student ${student.student_id}:`, error)
          errorCount++
        }
      }

      if (successCount > 0) {
        toast.success(`Successfully generated ${successCount} ID cards!`)
      }
      if (errorCount > 0) {
        toast.error(`Failed to generate ${errorCount} cards`)
      }

    } catch (error) {
      console.error('Error in generation:', error)
      toast.error('Failed to start generation')
    } finally {
      setGenerating(false)
    }
  }

  const selectedTemplateData = templates.find(t => t.id.toString() === selectedTemplate)

  const handlePreview = async () => {
    if (!selectedTemplate) {
      toast.error('Please select a template')
      return
    }

    try {
      const templateData = selectedTemplateData

      if (!templateData) {
        toast.error('Template data not found')
        return
      }

      // Show modal preview with sample data
      setShowPreview(true)
      toast.success('Preview opened')

    } catch (error) {
      console.error('Error generating preview:', error)
      toast.error('Failed to generate preview')
    }
  }



  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <div className="space-y-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
            <div className="card p-6">
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">ID Card Generator</h1>
        <p className="text-gray-600">Choose a template and generate ID cards</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Template Selection */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Template</h3>
          <div className="space-y-4">
            <select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value)}
              className="input"
            >
              <option value="">Choose a template...</option>
              {templates.map(template => (
                <option key={template.id} value={template.id}>
                  {template.name} {template.is_default ? '(Default)' : ''}
                </option>
              ))}
            </select>

            {selectedTemplateData && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">{selectedTemplateData.name}</h4>
                <div className="text-sm text-gray-600 mb-4">
                  <p>Size: {selectedTemplateData.template_data.width} × {selectedTemplateData.template_data.height}px</p>
                </div>

                {/* Generate Button */}
                <button
                  onClick={handleGenerateCard}
                  disabled={!selectedTemplate || generating}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    minWidth: '160px',
                    whiteSpace: 'nowrap',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '0.75rem 1.5rem',
                    gap: '0.5rem',
                    width: '100%'
                  }}
                >
                  <CreditCard className="h-4 w-4" />
                  {generating ? 'Generating...' : 'Generate Cards'}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Template Preview */}
        <div className="card p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Template Preview</h3>
            {selectedTemplateData && (
              <button
                onClick={handlePreview}
                className="btn-secondary"
                style={{
                  minWidth: '120px',
                  whiteSpace: 'nowrap',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '0.5rem 1rem',
                  gap: '0.5rem'
                }}
              >
                <Eye className="h-4 w-4" />
                Preview
              </button>
            )}
          </div>

          {selectedTemplateData ? (
            <div className="flex justify-center">
              <div className="transform scale-75 origin-center">
                <CardPreview
                  templateData={selectedTemplateData.template_data}
                  studentData={{
                    name: 'Sample Student',
                    student_id: 'STU001',
                    course: 'Computer Science',
                    email: '<EMAIL>'
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <CreditCard className="mx-auto h-16 w-16 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">No Template Selected</h3>
              <p className="mt-2 text-sm text-gray-500">
                Choose a template to see the preview
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">ID Card Preview</h2>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
                >
                  ×
                </button>
              </div>

              <div className="flex justify-center">
                <div className="transform scale-100">
                  <CardPreview
                    templateData={selectedTemplateData?.template_data}
                    studentData={{
                      name: 'Sample Student',
                      student_id: 'STU001',
                      course: 'Computer Science',
                      email: '<EMAIL>'
                    }}
                  />
                </div>
              </div>

              <div className="mt-6 text-center">
                <p className="text-gray-600 mb-4">
                  This is a simplified preview. The actual generated card will use the full template design.
                </p>
                <div className="flex justify-center gap-3">
                  <button
                    onClick={() => setShowPreview(false)}
                    className="btn-secondary"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => {
                      setShowPreview(false)
                      handleGenerateCard()
                    }}
                    disabled={generating}
                    className="btn-primary"
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    Generate & Download
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
