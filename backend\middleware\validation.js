const Joi = require('joi');

// Student validation schema
const studentSchema = Joi.object({
  student_id: Joi.string().alphanum().min(3).max(20).required(),
  first_name: Joi.string().min(2).max(50).required(),
  last_name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  phone: Joi.string().pattern(/^[\+]?[1-9][\d]{0,15}$/).optional().allow(''),
  date_of_birth: Joi.date().iso().max('now').optional().allow(''),
  address: Joi.string().max(500).optional().allow(''),
  course: Joi.string().min(2).max(100).required(),
  year_of_study: Joi.number().integer().min(1).max(10).required(),
  status: Joi.string().valid('active', 'inactive', 'graduated', 'suspended').default('active')
});

// Student update validation schema (all fields optional except required ones)
const studentUpdateSchema = Joi.object({
  student_id: Joi.string().alphanum().min(3).max(20).optional(),
  first_name: Joi.string().min(2).max(50).optional(),
  last_name: Joi.string().min(2).max(50).optional(),
  email: Joi.string().email().optional(),
  phone: Joi.string().pattern(/^[\+]?[1-9][\d]{0,15}$/).optional().allow(''),
  date_of_birth: Joi.date().iso().max('now').optional().allow(''),
  address: Joi.string().max(500).optional().allow(''),
  course: Joi.string().min(2).max(100).optional(),
  year_of_study: Joi.number().integer().min(1).max(10).optional(),
  status: Joi.string().valid('active', 'inactive', 'graduated', 'suspended').optional()
});

// Card template validation schema
const cardTemplateSchema = Joi.object({
  name: Joi.string().min(3).max(100).required(),
  template_data: Joi.object({
    width: Joi.number().min(100).max(1000).required(),
    height: Joi.number().min(100).max(1000).required(),
    backgroundColor: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).required(),
    elements: Joi.array().items(
      Joi.object({
        type: Joi.string().valid('text', 'image', 'shape').required(),
        x: Joi.number().required(),
        y: Joi.number().required(),
        width: Joi.number().when('type', {
          is: Joi.string().valid('image', 'shape'),
          then: Joi.required(),
          otherwise: Joi.optional()
        }),
        height: Joi.number().when('type', {
          is: Joi.string().valid('image', 'shape'),
          then: Joi.required(),
          otherwise: Joi.optional()
        }),
        content: Joi.string().when('type', {
          is: 'text',
          then: Joi.required(),
          otherwise: Joi.optional()
        }),
        fontSize: Joi.number().when('type', {
          is: 'text',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        }),
        fontWeight: Joi.string().when('type', {
          is: 'text',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        }),
        color: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).when('type', {
          is: 'text',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        }),
        textAlign: Joi.string().valid('left', 'center', 'right').when('type', {
          is: 'text',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        }),
        placeholder: Joi.string().when('type', {
          is: 'image',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        }),
        shapeType: Joi.string().valid('rectangle', 'circle', 'line').when('type', {
          is: 'shape',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        }),
        fillColor: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).when('type', {
          is: 'shape',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        }),
        strokeColor: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).when('type', {
          is: 'shape',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        }),
        strokeWidth: Joi.number().when('type', {
          is: 'shape',
          then: Joi.optional(),
          otherwise: Joi.forbidden()
        })
      })
    ).required()
  }).required(),
  is_default: Joi.boolean().optional()
});

// Validation middleware functions
const validateStudent = (req, res, next) => {
  const { error, value } = studentSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    });
  }
  
  req.body = value;
  next();
};

const validateStudentUpdate = (req, res, next) => {
  const { error, value } = studentUpdateSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    });
  }
  
  req.body = value;
  next();
};

const validateCardTemplate = (req, res, next) => {
  const { error, value } = cardTemplateSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      error: 'Validation error',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    });
  }
  
  req.body = value;
  next();
};

module.exports = {
  validateStudent,
  validateStudentUpdate,
  validateCardTemplate
};
