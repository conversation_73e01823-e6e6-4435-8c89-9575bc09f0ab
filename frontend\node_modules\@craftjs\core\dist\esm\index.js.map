{"version": 3, "file": "index.js", "sources": ["../../src/nodes/NodeContext.tsx", "../../src/editor/EditorContext.tsx", "../../src/events/EventContext.ts", "../../src/editor/useInternalEditor.ts", "../../src/nodes/useInternalNode.ts", "../../src/hooks/useNode.ts", "../../src/render/SimpleElement.tsx", "../../src/render/DefaultRender.tsx", "../../src/render/RenderNode.tsx", "../../src/nodes/NodeElement.tsx", "../../src/nodes/Element.tsx", "../../src/nodes/Canvas.tsx", "../../src/render/Frame.tsx", "../../src/interfaces/nodes.ts", "../../src/hooks/useEditor.tsx", "../../src/hooks/legacy/connectEditor.tsx", "../../src/hooks/legacy/connectNode.tsx", "../../src/utils/fromEntries.ts", "../../src/utils/getNodesFromSelector.ts", "../../src/utils/resolveComponent.ts", "../../src/utils/serializeNode.tsx", "../../src/editor/NodeHelpers.ts", "../../src/events/findPosition.ts", "../../src/utils/createNode.ts", "../../src/utils/deserializeNode.tsx", "../../src/utils/mergeTrees.tsx", "../../src/editor/query.tsx", "../../src/editor/EventHelpers.ts", "../../src/utils/parseNodeFromJSX.tsx", "../../src/events/CoreEventHandlers.ts", "../../src/events/Positioner.ts", "../../src/events/createShadow.ts", "../../src/events/DefaultEventHandlers.ts", "../../src/events/movePlaceholder.ts", "../../src/events/RenderEditorIndicator.tsx", "../../src/events/Events.tsx", "../../src/editor/store.tsx", "../../src/editor/actions.ts", "../../src/utils/removeNodeFromEvents.ts", "../../src/editor/Editor.tsx", "../../src/utils/testHelpers.ts"], "sourcesContent": ["import React from 'react';\n\nimport { NodeId } from '../interfaces';\n\nexport type NodeContextType = {\n  id: NodeId;\n  related?: boolean;\n};\n\nexport const NodeContext = React.createContext<NodeContextType>(null);\n\nexport type NodeProviderProps = Omit<NodeContextType, 'connectors'> & {\n  children?: React.ReactNode;\n};\n\nexport const NodeProvider = ({\n  id,\n  related = false,\n  children,\n}: NodeProviderProps) => {\n  return (\n    <NodeContext.Provider value={{ id, related }}>\n      {children}\n    </NodeContext.Provider>\n  );\n};\n", "import { createContext } from 'react';\n\nimport { EditorStore } from './store';\n\nexport type EditorContextType = EditorStore;\nexport const EditorContext = createContext<EditorContextType>(null);\n", "import { createContext, useContext } from 'react';\n\nimport { CoreEventHandlers } from './CoreEventHandlers';\n\nexport const EventHandlerContext = createContext<CoreEventHandlers>(null);\n\nexport const useEventHandler = () => useContext(EventHandlerContext);\n", "import {\n  useCollector,\n  useCollectorReturnType,\n  QueryCallbacksFor,\n  wrapConnector<PERSON><PERSON>s,\n  EventHandlerConnectors,\n  ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT,\n} from '@craftjs/utils';\nimport { useContext, useEffect, useMemo } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EditorContext } from './EditorContext';\nimport { QueryMethods } from './query';\nimport { EditorStore } from './store';\n\nimport { CoreEventHandlers } from '../events/CoreEventHandlers';\nimport { useEventHandler } from '../events/EventContext';\nimport { EditorState } from '../interfaces';\n\nexport type EditorCollector<C> = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => C;\n\nexport type useInternalEditorReturnType<C = null> = useCollectorReturnType<\n  EditorStore,\n  C\n> & {\n  inContext: boolean;\n  store: EditorStore;\n  connectors: EventHandlerConnectors<CoreEventHandlers, React.ReactElement>;\n};\n\nexport function useInternalEditor<C>(\n  collector?: EditorCollector<C>\n): useInternalEditorReturnType<C> {\n  const handler = useEventHandler();\n  const store = useContext(EditorContext);\n  invariant(store, ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT);\n\n  const collected = useCollector(store, collector);\n\n  const connectorsUsage = useMemo(\n    () => handler && handler.createConnectorsUsage(),\n    [handler]\n  );\n\n  useEffect(() => {\n    connectorsUsage.register();\n\n    return () => {\n      connectorsUsage.cleanup();\n    };\n  }, [connectorsUsage]);\n\n  const connectors = useMemo(\n    () => connectorsUsage && wrapConnectorHooks(connectorsUsage.connectors),\n    [connectorsUsage]\n  );\n\n  return {\n    ...collected,\n    connectors,\n    inContext: !!store,\n    store,\n  };\n}\n", "import {\n  wrapConnectorHooks,\n  ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT,\n} from '@craftjs/utils';\nimport { useMemo, useContext } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { NodeContext } from './NodeContext';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { Node } from '../interfaces';\n\nexport function useInternalNode<S = null>(collect?: (node: Node) => S) {\n  const context = useContext(NodeContext);\n  invariant(context, ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT);\n\n  const { id, related } = context;\n\n  const {\n    actions: EditorActions,\n    query,\n    connectors: editorConnectors,\n    ...collected\n  } = useInternalEditor(\n    (state) => id && state.nodes[id] && collect && collect(state.nodes[id])\n  );\n\n  const connectors = useMemo(\n    () =>\n      wrapConnectorHooks({\n        connect: (dom: HTMLElement) => editorConnectors.connect(dom, id),\n        drag: (dom: HTMLElement) => editorConnectors.drag(dom, id),\n      }),\n    [editorConnectors, id]\n  );\n\n  const actions = useMemo(() => {\n    return {\n      setProp: (cb: any, throttleRate?: number) => {\n        if (throttleRate) {\n          EditorActions.history.throttle(throttleRate).setProp(id, cb);\n        } else {\n          EditorActions.setProp(id, cb);\n        }\n      },\n      setCustom: (cb: any, throttleRate?: number) => {\n        if (throttleRate) {\n          EditorActions.history.throttle(throttleRate).setCustom(id, cb);\n        } else {\n          EditorActions.setCustom(id, cb);\n        }\n      },\n      setHidden: (bool: boolean) => EditorActions.setHidden(id, bool),\n    };\n  }, [EditorActions, id]);\n\n  return {\n    ...collected,\n    id,\n    related,\n    inNodeContext: !!context,\n    actions,\n    connectors,\n  };\n}\n", "import { deprecationWarning } from '@craftjs/utils';\n\nimport { Node } from '../interfaces';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\n/**\n * A Hook to that provides methods and state information related to the corresponding Node that manages the current component.\n * @param collect - Collector function to consume values from the corresponding Node's state\n */\nexport function useNode<S = null>(collect?: (node: Node) => S) {\n  const {\n    id,\n    related,\n    actions,\n    inNodeContext,\n    connectors,\n    ...collected\n  } = useInternalNode(collect);\n\n  return {\n    ...collected,\n    actions,\n    id,\n    related,\n    setProp: (\n      cb: (props: Record<string, any>) => void,\n      throttleRate?: number\n    ) => {\n      deprecationWarning('useNode().setProp()', {\n        suggest: 'useNode().actions.setProp()',\n      });\n      return actions.setProp(cb, throttleRate);\n    },\n    inNodeContext,\n    connectors,\n  };\n}\n", "import React from 'react';\n\nimport { useNode } from '../hooks/useNode';\n\nexport const SimpleElement = ({ render }: any) => {\n  const {\n    connectors: { connect, drag },\n  } = useNode();\n\n  return typeof render.type === 'string'\n    ? connect(drag(React.cloneElement(render)))\n    : render;\n};\n", "import React, { useMemo } from 'react';\n\nimport { SimpleElement } from './SimpleElement';\n\nimport { NodeId } from '../interfaces';\nimport { NodeElement } from '../nodes/NodeElement';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\nexport const DefaultRender = () => {\n  const { type, props, nodes, hydrationTimestamp } = useInternalNode(\n    (node) => ({\n      type: node.data.type,\n      props: node.data.props,\n      nodes: node.data.nodes,\n      hydrationTimestamp: node._hydrationTimestamp,\n    })\n  );\n\n  return useMemo(() => {\n    let children = props.children;\n\n    if (nodes && nodes.length > 0) {\n      children = (\n        <React.Fragment>\n          {nodes.map((id: NodeId) => (\n            <NodeElement id={id} key={id} />\n          ))}\n        </React.Fragment>\n      );\n    }\n\n    const render = React.createElement(type, props, children);\n\n    if (typeof type == 'string') {\n      return <SimpleElement render={render} />;\n    }\n\n    return render;\n    // eslint-disable-next-line  react-hooks/exhaustive-deps\n  }, [type, props, hydrationTimestamp, nodes]);\n};\n", "import React from 'react';\n\nimport { DefaultRender } from './DefaultRender';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\ntype RenderNodeToElementProps = {\n  render?: React.ReactElement;\n  children?: React.ReactNode;\n};\nexport const RenderNodeToElement = ({ render }: RenderNodeToElementProps) => {\n  const { hidden } = useInternalNode((node) => ({\n    hidden: node.data.hidden,\n  }));\n\n  const { onRender } = useInternalEditor((state) => ({\n    onRender: state.options.onRender,\n  }));\n\n  // don't display the node since it's hidden\n  if (hidden) {\n    return null;\n  }\n\n  return React.createElement(onRender, { render: render || <DefaultRender /> });\n};\n", "import React from 'react';\n\nimport { NodeProvider } from './NodeContext';\n\nimport { NodeId } from '../interfaces';\nimport { RenderNodeToElement } from '../render/RenderNode';\n\nexport type NodeElementProps = {\n  id: NodeId;\n  render?: React.ReactElement;\n};\n\nexport const NodeElement = ({ id, render }: NodeElementProps) => {\n  return (\n    <NodeProvider id={id}>\n      <RenderNodeToElement render={render} />\n    </NodeProvider>\n  );\n};\n", "import { ERROR_TOP_LEVEL_ELEMENT_NO_ID } from '@craftjs/utils';\nimport React, { useState } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { NodeElement } from './NodeElement';\nimport { useInternalNode } from './useInternalNode';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { NodeId } from '../interfaces';\n\nexport const defaultElementProps = {\n  is: 'div',\n  canvas: false,\n  custom: {},\n  hidden: false,\n};\n\nexport const elementPropToNodeData = {\n  is: 'type',\n  canvas: 'isCanvas',\n};\n\nexport type ElementProps<T extends React.ElementType> = {\n  id?: NodeId;\n  is?: T;\n  custom?: Record<string, any>;\n  children?: React.ReactNode;\n  canvas?: boolean;\n  hidden?: boolean;\n} & React.ComponentProps<T>;\n\nexport function Element<T extends React.ElementType>({\n  id,\n  children,\n  ...elementProps\n}: ElementProps<T>) {\n  const { is } = {\n    ...defaultElementProps,\n    ...elementProps,\n  };\n\n  const { query, actions } = useInternalEditor();\n  const { id: nodeId, inNodeContext } = useInternalNode();\n\n  const [linkedNodeId] = useState<NodeId | null>(() => {\n    invariant(!!id, ERROR_TOP_LEVEL_ELEMENT_NO_ID);\n    const node = query.node(nodeId).get();\n\n    if (inNodeContext) {\n      const existingNode = node.data.linkedNodes[id]\n        ? query.node(node.data.linkedNodes[id]).get()\n        : null;\n\n      // Render existing linked Node if it already exists (and is the same type as the JSX)\n      if (existingNode && existingNode.data.type === is) {\n        return existingNode.id;\n      }\n\n      // otherwise, create and render a new linked Node\n      const linkedElement = React.createElement(\n        Element,\n        elementProps,\n        children\n      );\n\n      const tree = query.parseReactElement(linkedElement).toNodeTree();\n\n      actions.history.ignore().addLinkedNodeFromTree(tree, nodeId, id);\n      return tree.rootNodeId;\n    }\n    return null;\n  });\n\n  return linkedNodeId ? <NodeElement id={linkedNodeId} /> : null;\n}\n", "import { deprecationWarning } from '@craftjs/utils';\nimport React, { useEffect } from 'react';\n\nimport { Element, ElementProps } from './Element';\n\nexport type CanvasProps<T extends React.ElementType> = ElementProps<T>;\n\nexport const deprecateCanvasComponent = () =>\n  deprecationWarning('<Canvas />', {\n    suggest: '<Element canvas={true} />',\n  });\n\nexport function Canvas<T extends React.ElementType>({\n  ...props\n}: CanvasProps<T>) {\n  useEffect(() => deprecateCanvasComponent(), []);\n\n  return <Element {...props} canvas={true} />;\n}\n", "import { deprecationWarning, ROOT_NODE } from '@craftjs/utils';\nimport React, { useRef } from 'react';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { SerializedNodes } from '../interfaces';\nimport { NodeElement } from '../nodes/NodeElement';\n\nexport type FrameProps = {\n  children?: React.ReactNode;\n  json?: string;\n  data?: string | SerializedNodes;\n};\n\nconst RenderRootNode = () => {\n  const { timestamp } = useInternalEditor((state) => ({\n    timestamp:\n      state.nodes[ROOT_NODE] && state.nodes[ROOT_NODE]._hydrationTimestamp,\n  }));\n\n  if (!timestamp) {\n    return null;\n  }\n\n  return <NodeElement id={ROOT_NODE} key={timestamp} />;\n};\n\n/**\n * A React Component that defines the editable area\n */\nexport const Frame = ({ children, json, data }: FrameProps) => {\n  const { actions, query } = useInternalEditor();\n\n  if (!!json) {\n    deprecationWarning('<Frame json={...} />', {\n      suggest: '<Frame data={...} />',\n    });\n  }\n\n  const isLoaded = useRef(false);\n\n  if (!isLoaded.current) {\n    const initialData = data || json;\n\n    if (initialData) {\n      actions.history.ignore().deserialize(initialData);\n    } else if (children) {\n      const rootNode = React.Children.only(children) as React.ReactElement;\n\n      const node = query.parseReactElement(rootNode).toNodeTree((node, jsx) => {\n        if (jsx === rootNode) {\n          node.id = ROOT_NODE;\n        }\n        return node;\n      });\n\n      actions.history.ignore().addNodeTree(node);\n    }\n\n    isLoaded.current = true;\n  }\n\n  return <RenderRootNode />;\n};\n", "import { QueryCallbacksFor } from '@craftjs/utils';\nimport React from 'react';\n\nimport { QueryMethods } from '../editor/query';\n\nexport type UserComponentConfig<T> = {\n  displayName: string;\n  rules: Partial<NodeRules>;\n  related: Partial<NodeRelated>;\n  props: Partial<T>;\n  custom: Record<string, any>;\n  info: Record<string, any>;\n  isCanvas: boolean;\n\n  // TODO: Deprecate\n  name: string;\n  defaultProps: Partial<T>;\n};\n\nexport type UserComponent<T = any> = React.ComponentType<T> & {\n  craft?: Partial<UserComponentConfig<T>>;\n};\n\nexport type NodeId = string;\nexport type NodeEventTypes = 'selected' | 'dragged' | 'hovered';\n\nexport type Node = {\n  id: NodeId;\n  data: NodeData;\n  info: Record<string, any>;\n  events: Record<NodeEventTypes, boolean>;\n  dom: HTMLElement | null;\n  related: Record<string, React.ElementType>;\n  rules: NodeRules;\n  _hydrationTimestamp: number;\n};\n\nexport type NodeHelpersType = QueryCallbacksFor<typeof QueryMethods>['node'];\nexport type NodeRules = {\n  canDrag(node: Node, helpers: NodeHelpersType): boolean;\n  canDrop(dropTarget: Node, self: Node, helpers: NodeHelpersType): boolean;\n  canMoveIn(canMoveIn: Node[], self: Node, helpers: NodeHelpersType): boolean;\n  canMoveOut(canMoveOut: Node[], self: Node, helpers: NodeHelpersType): boolean;\n};\nexport type NodeRelated = Record<string, React.ElementType>;\n\nexport type NodeData = {\n  props: Record<string, any>;\n  type: string | React.ElementType;\n  name: string;\n  displayName: string;\n  isCanvas: boolean;\n  parent: NodeId | null;\n  linkedNodes: Record<string, NodeId>;\n  nodes: NodeId[];\n  hidden: boolean;\n  custom?: any;\n  _childCanvas?: Record<string, NodeId>; // TODO: Deprecate in favour of linkedNodes\n};\n\nexport type FreshNode = {\n  id?: NodeId;\n  data: Partial<NodeData> & Required<Pick<NodeData, 'type'>>;\n};\n\nexport type ReduceCompType =\n  | string\n  | {\n      resolvedName: string;\n    };\n\nexport type ReducedComp = {\n  type: ReduceCompType;\n  isCanvas: boolean;\n  props: any;\n};\n\nexport type SerializedNode = Omit<\n  NodeData,\n  'type' | 'subtype' | 'name' | 'event'\n> &\n  ReducedComp;\n\nexport type SerializedNodes = Record<NodeId, SerializedNode>;\n\n// TODO: Deprecate in favor of SerializedNode\nexport type SerializedNodeData = SerializedNode;\n\nexport type Nodes = Record<NodeId, Node>;\n\n/**\n * A NodeTree is an internal data structure for CRUD operations that involve\n * more than a single node.\n *\n * For example, when we drop a component we use a tree because we\n * need to drop more than a single component.\n */\nexport interface NodeTree {\n  rootNodeId: NodeId;\n  nodes: Nodes;\n}\n\ntype NodeIdSelector = NodeId | NodeId[];\ntype NodeObjSelector = Node | Node[];\n\nexport enum NodeSelectorType {\n  Any,\n  Id,\n  Obj,\n}\n\nexport type NodeSelector<\n  T extends NodeSelectorType = NodeSelectorType.Any\n> = T extends NodeSelectorType.Id\n  ? NodeIdSelector\n  : T extends NodeSelectorType.Obj\n  ? NodeObjSelector\n  : NodeIdSelector | NodeObjSelector;\n\nexport type NodeSelectorWrapper = {\n  node: Node;\n  exists: boolean;\n};\n", "import { Overwrite, Delete, OverwriteFnReturnType } from '@craftjs/utils';\nimport { useMemo } from 'react';\n\nimport {\n  useInternalEditor,\n  EditorCollector,\n  useInternalEditorReturnType,\n} from '../editor/useInternalEditor';\n\ntype PrivateActions =\n  | 'addLinkedNodeFromTree'\n  | 'setNodeEvent'\n  | 'setDOM'\n  | 'replaceNodes'\n  | 'reset';\n\nconst getPublicActions = (actions) => {\n  const {\n    addLinkedNodeFromTree,\n    setDOM,\n    setNodeEvent,\n    replaceNodes,\n    reset,\n    ...EditorActions\n  } = actions;\n\n  return EditorActions;\n};\n\nexport type WithoutPrivateActions<S = null> = Delete<\n  useInternalEditorReturnType<S>['actions'],\n  PrivateActions | 'history'\n> & {\n  history: Overwrite<\n    useInternalEditorReturnType<S>['actions']['history'],\n    {\n      ignore: OverwriteFnReturnType<\n        useInternalEditorReturnType<S>['actions']['history']['ignore'],\n        PrivateActions\n      >;\n      throttle: OverwriteFnReturnType<\n        useInternalEditorReturnType<S>['actions']['history']['throttle'],\n        PrivateActions\n      >;\n    }\n  >;\n};\n\nexport type useEditorReturnType<S = null> = Overwrite<\n  useInternalEditorReturnType<S>,\n  {\n    actions: WithoutPrivateActions;\n    query: Delete<useInternalEditorReturnType<S>['query'], 'deserialize'>;\n  }\n>;\n\n/**\n * A Hook that that provides methods and information related to the entire editor state.\n * @param collector Collector function to consume values from the editor's state\n */\nexport function useEditor(): useEditorReturnType;\nexport function useEditor<S>(\n  collect: EditorCollector<S>\n): useEditorReturnType<S>;\n\nexport function useEditor<S>(collect?: any): useEditorReturnType<S> {\n  const {\n    connectors,\n    actions: internalActions,\n    query,\n    store,\n    ...collected\n  } = useInternalEditor(collect);\n\n  const EditorActions = getPublicActions(internalActions);\n\n  const actions = useMemo(() => {\n    return {\n      ...EditorActions,\n      history: {\n        ...EditorActions.history,\n        ignore: (...args) =>\n          getPublicActions(EditorActions.history.ignore(...args)),\n        throttle: (...args) =>\n          getPublicActions(EditorActions.history.throttle(...args)),\n      },\n    };\n  }, [EditorActions]);\n\n  return {\n    connectors,\n    actions,\n    query,\n    store,\n    ...(collected as any),\n  };\n}\n", "import React from 'react';\n\nimport { EditorState } from '../../interfaces';\nimport { useEditor } from '../useEditor';\n\nexport function connectEditor<C>(collect?: (state: EditorState) => C) {\n  return (WrappedComponent: React.ElementType) => {\n    return (props: any) => {\n      const Editor = collect ? useEditor(collect) : useEditor();\n      return <WrappedComponent {...Editor} {...props} />;\n    };\n  };\n}\n", "import React from 'react';\n\nimport { Node } from '../../interfaces';\nimport { useNode } from '../useNode';\n\nexport function connectNode<C>(collect?: (state: Node) => C) {\n  return function (WrappedComponent: React.ElementType) {\n    return (props: any) => {\n      const node = useNode(collect);\n      return <WrappedComponent {...node} {...props} />;\n    };\n  };\n}\n", "export const fromEntries = (pairs) => {\n  if (Object.fromEntries) {\n    return Object.fromEntries(pairs);\n  }\n  return pairs.reduce(\n    (accum, [id, value]) => ({\n      ...accum,\n      [id]: value,\n    }),\n    {}\n  );\n};\n", "import { ERROR_INVALID_NODEID } from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { Nodes, Node, NodeSelectorWrapper, NodeSelector } from '../interfaces';\n\ntype config = { existOnly: boolean; idOnly: boolean };\nexport const getNodesFromSelector = (\n  nodes: Nodes,\n  selector: NodeSelector,\n  config?: Partial<config>\n): NodeSelectorWrapper[] => {\n  const items = Array.isArray(selector) ? selector : [selector];\n\n  const mergedConfig = {\n    existOnly: false,\n    idOnly: false,\n    ...(config || {}),\n  };\n\n  const nodeSelectors = items\n    .filter((item) => !!item)\n    .map((item) => {\n      if (typeof item === 'string') {\n        return {\n          node: nodes[item],\n          exists: !!nodes[item],\n        };\n      }\n\n      if (typeof item === 'object' && !mergedConfig.idOnly) {\n        const node = item as Node;\n        return {\n          node,\n          exists: !!nodes[node.id],\n        };\n      }\n\n      return {\n        node: null,\n        exists: false,\n      };\n    });\n\n  if (mergedConfig.existOnly) {\n    invariant(\n      nodeSelectors.filter((selector) => !selector.exists).length === 0,\n      ERROR_INVALID_NODEID\n    );\n  }\n\n  return nodeSelectors;\n};\n", "import { ERROR_NOT_IN_RESOLVER } from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { Resolver } from '../interfaces';\n\ntype ReversedResolver = Map<React.ComponentType | string, string>;\n\ntype CachedResolverData = {\n  resolver: Resolver;\n  reversed: ReversedResolver;\n};\n\nlet CACHED_RESOLVER_DATA: CachedResolverData | null = null;\n\nconst getReversedResolver = (resolver: Resolver): ReversedResolver => {\n  if (CACHED_RESOLVER_DATA && CACHED_RESOLVER_DATA.resolver === resolver) {\n    return CACHED_RESOLVER_DATA.reversed;\n  }\n\n  CACHED_RESOLVER_DATA = {\n    resolver,\n    reversed: new Map(),\n  };\n\n  for (const [name, comp] of Object.entries(resolver)) {\n    CACHED_RESOLVER_DATA.reversed.set(comp, name);\n  }\n\n  return CACHED_RESOLVER_DATA.reversed;\n};\n\nconst getComponentName = (component: React.ElementType): string | undefined => {\n  return (component as any).name || (component as any).displayName;\n};\n\nconst searchComponentInResolver = (\n  resolver: Resolver,\n  comp: React.ElementType\n): string | null => {\n  const name = getReversedResolver(resolver).get(comp);\n  return name !== undefined ? name : null;\n};\n\nexport const resolveComponent = (\n  resolver: Resolver,\n  comp: React.ElementType | string\n): string => {\n  if (typeof comp === 'string') {\n    return comp;\n  }\n\n  const resolvedName = searchComponentInResolver(resolver, comp);\n\n  invariant(\n    resolvedName,\n    ERROR_NOT_IN_RESOLVER.replace('%node_type%', getComponentName(comp))\n  );\n\n  return resolvedName;\n};\n", "import React, { Children } from 'react';\n\nimport { resolveComponent } from './resolveComponent';\n\nimport { NodeData, ReducedComp, SerializedNode } from '../interfaces';\nimport { Resolver } from '../interfaces';\n\nconst reduceType = (type: React.ElementType | string, resolver: Resolver) => {\n  if (typeof type === 'string') {\n    return type;\n  }\n  return { resolvedName: resolveComponent(resolver, type) };\n};\n\nexport const serializeComp = (\n  data: Pick<NodeData, 'type' | 'isCanvas' | 'props'>,\n  resolver: Resolver\n): ReducedComp => {\n  let { type, isCanvas, props } = data;\n  props = Object.keys(props).reduce((result: Record<string, any>, key) => {\n    const prop = props[key];\n\n    if (prop === undefined || prop === null || typeof prop === 'function') {\n      return result;\n    }\n\n    if (key === 'children' && typeof prop !== 'string') {\n      result[key] = Children.map(prop, (child) => {\n        if (typeof child === 'string') {\n          return child;\n        }\n        return serializeComp(child, resolver);\n      });\n    } else if (typeof prop.type === 'function') {\n      result[key] = serializeComp(prop, resolver);\n    } else {\n      result[key] = prop;\n    }\n    return result;\n  }, {});\n\n  return {\n    type: reduceType(type, resolver),\n    isCanvas: !!isCanvas,\n    props,\n  };\n};\n\nexport const serializeNode = (\n  data: Omit<NodeData, 'event'>,\n  resolver: Resolver\n): SerializedNode => {\n  const { type, props, isCanvas, name, ...nodeData } = data;\n\n  const reducedComp = serializeComp({ type, isCanvas, props }, resolver);\n\n  return {\n    ...reducedComp,\n    ...nodeData,\n  };\n};\n", "import {\n  deprecationWarning,\n  ERROR_CANNOT_DRAG,\n  ERROR_DUPLICATE_NODEID,\n  ERROR_INVALID_NODE_ID,\n  ERROR_MOVE_INCOMING_PARENT,\n  ERROR_MOVE_NONCANVAS_CHILD,\n  ERROR_MOVE_OUTGOING_PARENT,\n  ERROR_MOVE_TO_DESCENDANT,\n  ERROR_MOVE_TO_NONCANVAS_PARENT,\n  ERROR_MOVE_TOP_LEVEL_NODE,\n  ERROR_MOVE_CANNOT_DROP,\n  ROOT_NODE,\n} from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { EditorState, NodeId, NodeSelector } from '../interfaces';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { serializeNode } from '../utils/serializeNode';\n\nexport function NodeHelpers(state: EditorState, id: NodeId) {\n  invariant(typeof id == 'string', ERROR_INVALID_NODE_ID);\n\n  const node = state.nodes[id];\n\n  const nodeHelpers = (id) => NodeHelpers(state, id);\n\n  return {\n    isCanvas() {\n      return !!node.data.isCanvas;\n    },\n    isRoot() {\n      return node.id === ROOT_NODE;\n    },\n    isLinkedNode() {\n      return (\n        node.data.parent &&\n        nodeHelpers(node.data.parent).linkedNodes().includes(node.id)\n      );\n    },\n    isTopLevelNode() {\n      return this.isRoot() || this.isLinkedNode();\n    },\n    isDeletable() {\n      return !this.isTopLevelNode();\n    },\n    isParentOfTopLevelNodes: () =>\n      node.data.linkedNodes && Object.keys(node.data.linkedNodes).length > 0,\n    isParentOfTopLevelCanvas() {\n      deprecationWarning('query.node(id).isParentOfTopLevelCanvas', {\n        suggest: 'query.node(id).isParentOfTopLevelNodes',\n      });\n      return this.isParentOfTopLevelNodes();\n    },\n    isSelected() {\n      return state.events.selected.has(id);\n    },\n    isHovered() {\n      return state.events.hovered.has(id);\n    },\n    isDragged() {\n      return state.events.dragged.has(id);\n    },\n    get() {\n      return node;\n    },\n    ancestors(deep = false): NodeId[] {\n      function appendParentNode(\n        id: NodeId,\n        ancestors: NodeId[] = [],\n        depth: number = 0\n      ) {\n        const node = state.nodes[id];\n        if (!node) {\n          return ancestors;\n        }\n\n        ancestors.push(id);\n\n        if (!node.data.parent) {\n          return ancestors;\n        }\n\n        if (deep || (!deep && depth === 0)) {\n          ancestors = appendParentNode(node.data.parent, ancestors, depth + 1);\n        }\n        return ancestors;\n      }\n      return appendParentNode(node.data.parent);\n    },\n    descendants(\n      deep = false,\n      includeOnly?: 'linkedNodes' | 'childNodes'\n    ): NodeId[] {\n      function appendChildNode(\n        id: NodeId,\n        descendants: NodeId[] = [],\n        depth: number = 0\n      ) {\n        if (deep || (!deep && depth === 0)) {\n          const node = state.nodes[id];\n\n          if (!node) {\n            return descendants;\n          }\n\n          if (includeOnly !== 'childNodes') {\n            // Include linkedNodes if any\n            const linkedNodes = nodeHelpers(id).linkedNodes();\n\n            linkedNodes.forEach((nodeId) => {\n              descendants.push(nodeId);\n              descendants = appendChildNode(nodeId, descendants, depth + 1);\n            });\n          }\n\n          if (includeOnly !== 'linkedNodes') {\n            const childNodes = nodeHelpers(id).childNodes();\n\n            childNodes.forEach((nodeId) => {\n              descendants.push(nodeId);\n              descendants = appendChildNode(nodeId, descendants, depth + 1);\n            });\n          }\n\n          return descendants;\n        }\n        return descendants;\n      }\n      return appendChildNode(id);\n    },\n    linkedNodes() {\n      return Object.values(node.data.linkedNodes || {});\n    },\n    childNodes() {\n      return node.data.nodes || [];\n    },\n    isDraggable(onError?: (err: string) => void) {\n      try {\n        const targetNode = node;\n        invariant(!this.isTopLevelNode(), ERROR_MOVE_TOP_LEVEL_NODE);\n        invariant(\n          NodeHelpers(state, targetNode.data.parent).isCanvas(),\n          ERROR_MOVE_NONCANVAS_CHILD\n        );\n        invariant(\n          targetNode.rules.canDrag(targetNode, nodeHelpers),\n          ERROR_CANNOT_DRAG\n        );\n        return true;\n      } catch (err) {\n        if (onError) {\n          onError(err);\n        }\n        return false;\n      }\n    },\n    isDroppable(selector: NodeSelector, onError?: (err: string) => void) {\n      const targets = getNodesFromSelector(state.nodes, selector);\n\n      const newParentNode = node;\n\n      try {\n        invariant(this.isCanvas(), ERROR_MOVE_TO_NONCANVAS_PARENT);\n        invariant(\n          newParentNode.rules.canMoveIn(\n            targets.map((selector) => selector.node),\n            newParentNode,\n            nodeHelpers\n          ),\n          ERROR_MOVE_INCOMING_PARENT\n        );\n\n        const parentNodes = {};\n\n        targets.forEach(({ node: targetNode, exists }) => {\n          invariant(\n            targetNode.rules.canDrop(newParentNode, targetNode, nodeHelpers),\n            ERROR_MOVE_CANNOT_DROP\n          );\n\n          // Ignore other checking if the Node is new\n          if (!exists) {\n            return;\n          }\n\n          invariant(\n            !nodeHelpers(targetNode.id).isTopLevelNode(),\n            ERROR_MOVE_TOP_LEVEL_NODE\n          );\n\n          const targetDeepNodes = nodeHelpers(targetNode.id).descendants(true);\n\n          invariant(\n            !targetDeepNodes.includes(newParentNode.id) &&\n              newParentNode.id !== targetNode.id,\n            ERROR_MOVE_TO_DESCENDANT\n          );\n\n          const currentParentNode =\n            targetNode.data.parent && state.nodes[targetNode.data.parent];\n\n          invariant(\n            currentParentNode.data.isCanvas,\n            ERROR_MOVE_NONCANVAS_CHILD\n          );\n\n          invariant(\n            currentParentNode ||\n              (!currentParentNode && !state.nodes[targetNode.id]),\n            ERROR_DUPLICATE_NODEID\n          );\n\n          if (currentParentNode.id !== newParentNode.id) {\n            if (!parentNodes[currentParentNode.id]) {\n              parentNodes[currentParentNode.id] = [];\n            }\n\n            parentNodes[currentParentNode.id].push(targetNode);\n          }\n        });\n\n        Object.keys(parentNodes).forEach((parentNodeId) => {\n          const childNodes = parentNodes[parentNodeId];\n          const parentNode = state.nodes[parentNodeId];\n\n          invariant(\n            parentNode.rules.canMoveOut(childNodes, parentNode, nodeHelpers),\n            ERROR_MOVE_OUTGOING_PARENT\n          );\n        });\n\n        return true;\n      } catch (err) {\n        if (onError) {\n          onError(err);\n        }\n        return false;\n      }\n    },\n    toSerializedNode() {\n      return serializeNode(node.data, state.options.resolver);\n    },\n    toNodeTree(includeOnly?: 'linkedNodes' | 'childNodes') {\n      const nodes = [id, ...this.descendants(true, includeOnly)].reduce(\n        (accum, descendantId) => {\n          accum[descendantId] = nodeHelpers(descendantId).get();\n          return accum;\n        },\n        {}\n      );\n\n      return {\n        rootNodeId: id,\n        nodes,\n      };\n    },\n\n    /**\n     Deprecated NodeHelpers\n     **/\n\n    decendants(deep = false) {\n      deprecationWarning('query.node(id).decendants', {\n        suggest: 'query.node(id).descendants',\n      });\n      return this.descendants(deep);\n    },\n    isTopLevelCanvas() {\n      return !this.isRoot() && !node.data.parent;\n    },\n  };\n}\n", "import { Node, NodeInfo, DropPosition } from '../interfaces';\n\nexport default function findPosition(\n  parent: Node,\n  dims: NodeInfo[],\n  posX: number,\n  posY: number\n) {\n  let result: DropPosition = {\n    parent,\n    index: 0,\n    where: 'before',\n  };\n\n  let leftLimit = 0,\n    xLimit = 0,\n    dimRight = 0,\n    yLimit = 0,\n    xCenter = 0,\n    yCenter = 0,\n    dimDown = 0;\n\n  // Each dim is: Top, Left, Height, Width\n  for (let i = 0, len = dims.length; i < len; i++) {\n    const dim = dims[i];\n\n    // Right position of the element. Left + Width\n    dimRight = dim.left + dim.outerWidth;\n    // Bottom position of the element. Top + Height\n    dimDown = dim.top + dim.outerHeight;\n    // X center position of the element. Left + (Width / 2)\n    xCenter = dim.left + dim.outerWidth / 2;\n    // Y center position of the element. Top + (Height / 2)\n    yCenter = dim.top + dim.outerHeight / 2;\n    // Skip if over the limits\n    if (\n      (xLimit && dim.left > xLimit) ||\n      (yLimit && yCenter >= yLimit) || // >= avoid issue with clearfixes\n      (leftLimit && dimRight < leftLimit)\n    )\n      continue;\n\n    result.index = i;\n    // If it's not in flow (like 'float' element)\n    if (!dim.inFlow) {\n      if (posY < dimDown) yLimit = dimDown;\n      //If x lefter than center\n      if (posX < xCenter) {\n        xLimit = xCenter;\n        result.where = 'before';\n      } else {\n        leftLimit = xCenter;\n        result.where = 'after';\n      }\n    } else {\n      // If y upper than center\n      if (posY < yCenter) {\n        result.where = 'before';\n        break;\n      } else result.where = 'after'; // After last element\n    }\n  }\n\n  return result;\n}\n", "import { getRandomId as getRandomNodeId } from '@craftjs/utils';\nimport React from 'react';\n\nimport { Node, FreshNode, UserComponentConfig } from '../interfaces';\nimport {\n  defaultElementProps,\n  Element,\n  Canvas,\n  elementPropToNodeData,\n  deprecateCanvasComponent,\n} from '../nodes';\nimport { NodeProvider } from '../nodes/NodeContext';\n\nconst getNodeTypeName = (type: string | { name: string }) =>\n  typeof type == 'string' ? type : type.name;\n\nexport function createNode(\n  newNode: FreshNode,\n  normalize?: (node: Node) => void\n) {\n  let actualType = newNode.data.type as any;\n  let id = newNode.id || getRandomNodeId();\n\n  const node: Node = {\n    id,\n    _hydrationTimestamp: Date.now(),\n    data: {\n      type: actualType,\n      name: getNodeTypeName(actualType),\n      displayName: getNodeTypeName(actualType),\n      props: {},\n      custom: {},\n      parent: null,\n      isCanvas: false,\n      hidden: false,\n      nodes: [],\n      linkedNodes: {},\n      ...newNode.data,\n    },\n    info: {},\n    related: {},\n    events: {\n      selected: false,\n      dragged: false,\n      hovered: false,\n    },\n    rules: {\n      canDrag: () => true,\n      canDrop: () => true,\n      canMoveIn: () => true,\n      canMoveOut: () => true,\n    },\n    dom: null,\n  };\n\n  // @ts-ignore\n  if (node.data.type === Element || node.data.type === Canvas) {\n    const mergedProps = {\n      ...defaultElementProps,\n      ...node.data.props,\n    };\n\n    node.data.props = Object.keys(node.data.props).reduce((props, key) => {\n      if (Object.keys(defaultElementProps).includes(key)) {\n        // If a <Element /> specific props is found (ie: \"is\", \"canvas\")\n        // Replace the node.data with the value specified in the prop\n        node.data[elementPropToNodeData[key] || key] = mergedProps[key];\n      } else {\n        // Otherwise include the props in the node as usual\n        props[key] = node.data.props[key];\n      }\n\n      return props;\n    }, {});\n\n    actualType = node.data.type;\n    node.data.name = getNodeTypeName(actualType);\n    node.data.displayName = getNodeTypeName(actualType);\n\n    const usingDeprecatedCanvas = node.data.type === Canvas;\n    if (usingDeprecatedCanvas) {\n      node.data.isCanvas = true;\n      deprecateCanvasComponent();\n    }\n  }\n\n  if (normalize) {\n    normalize(node);\n  }\n\n  // TODO: use UserComponentConfig type\n  const userComponentConfig = actualType.craft as UserComponentConfig<any>;\n\n  if (userComponentConfig) {\n    node.data.displayName =\n      userComponentConfig.displayName ||\n      userComponentConfig.name ||\n      node.data.displayName;\n\n    node.data.props = {\n      ...(userComponentConfig.props || userComponentConfig.defaultProps || {}),\n      ...node.data.props,\n    };\n\n    node.data.custom = {\n      ...(userComponentConfig.custom || {}),\n      ...node.data.custom,\n    };\n\n    if (\n      userComponentConfig.isCanvas !== undefined &&\n      userComponentConfig.isCanvas !== null\n    ) {\n      node.data.isCanvas = userComponentConfig.isCanvas;\n    }\n\n    if (userComponentConfig.rules) {\n      Object.keys(userComponentConfig.rules).forEach((key) => {\n        if (['canDrag', 'canDrop', 'canMoveIn', 'canMoveOut'].includes(key)) {\n          node.rules[key] = userComponentConfig.rules[key];\n        }\n      });\n    }\n\n    if (userComponentConfig.related) {\n      const relatedNodeContext = {\n        id: node.id,\n        related: true,\n      };\n\n      Object.keys(userComponentConfig.related).forEach((comp) => {\n        node.related[comp] = (props) =>\n          React.createElement(\n            NodeProvider,\n            relatedNodeContext,\n            React.createElement(userComponentConfig.related[comp], props)\n          );\n      });\n    }\n\n    if (userComponentConfig.info) {\n      node.info = userComponentConfig.info;\n    }\n  }\n\n  return node;\n}\n", "import { ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER } from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { resolveComponent } from './resolveComponent';\n\nimport {\n  NodeData,\n  SerializedNode,\n  ReducedComp,\n  ReduceCompType,\n} from '../interfaces';\nimport { Resolver } from '../interfaces';\nimport { Canvas } from '../nodes/Canvas';\n\ntype DeserialisedType = React.JSX.Element & { name: string };\n\nconst restoreType = (type: ReduceCompType, resolver: Resolver) =>\n  typeof type === 'object' && type.resolvedName\n    ? type.resolvedName === 'Canvas'\n      ? Canvas\n      : resolver[type.resolvedName]\n    : typeof type === 'string'\n    ? type\n    : null;\n\nexport const deserializeComp = (\n  data: ReducedComp,\n  resolver: Resolver,\n  index?: number\n): DeserialisedType | void => {\n  let { type, props } = data;\n\n  const main = restoreType(type, resolver);\n\n  if (!main) {\n    return;\n  }\n\n  props = Object.keys(props).reduce((result: Record<string, any>, key) => {\n    const prop = props[key];\n    if (prop === null || prop === undefined) {\n      result[key] = null;\n    } else if (typeof prop === 'object' && prop.resolvedName) {\n      result[key] = deserializeComp(prop, resolver);\n    } else if (key === 'children' && Array.isArray(prop)) {\n      result[key] = prop.map((child) => {\n        if (typeof child === 'string') {\n          return child;\n        }\n        return deserializeComp(child, resolver);\n      });\n    } else {\n      result[key] = prop;\n    }\n    return result;\n  }, {});\n\n  if (index) {\n    props.key = index;\n  }\n\n  const jsx = {\n    ...React.createElement(main, {\n      ...props,\n    }),\n  };\n\n  return {\n    ...jsx,\n    name: resolveComponent(resolver, jsx.type),\n  };\n};\n\nexport const deserializeNode = (\n  data: SerializedNode,\n  resolver: Resolver\n): Omit<NodeData, 'event'> => {\n  const { type: Comp, props: Props, ...nodeData } = data;\n\n  const isCompAnHtmlElement = Comp !== undefined && typeof Comp === 'string';\n  const isCompAUserComponent =\n    Comp !== undefined &&\n    (Comp as { resolvedName?: string }).resolvedName !== undefined;\n\n  invariant(\n    isCompAnHtmlElement || isCompAUserComponent,\n    ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER.replace(\n      '%displayName%',\n      data.displayName\n    ).replace('%availableComponents%', Object.keys(resolver).join(', '))\n  );\n\n  const { type, name, props } = (deserializeComp(\n    data,\n    resolver\n  ) as unknown) as NodeData;\n\n  const { parent, custom, displayName, isCanvas, nodes, hidden } = nodeData;\n\n  const linkedNodes = nodeData.linkedNodes || nodeData._childCanvas;\n\n  return {\n    type,\n    name,\n    displayName: displayName || name,\n    props,\n    custom: custom || {},\n    isCanvas: !!isCanvas,\n    hidden: !!hidden,\n    parent,\n    linkedNodes: linkedNodes || {},\n    nodes: nodes || [],\n  };\n};\n", "import { Node, NodeTree } from '../interfaces';\n\nconst mergeNodes = (rootNode: Node, childrenNodes: NodeTree[]) => {\n  if (childrenNodes.length < 1) {\n    return { [rootNode.id]: rootNode };\n  }\n  const nodes = childrenNodes.map(({ rootNodeId }) => rootNodeId);\n  const nodeWithChildren = { ...rootNode, data: { ...rootNode.data, nodes } };\n  const rootNodes = { [rootNode.id]: nodeWithChildren };\n  return childrenNodes.reduce((accum, tree) => {\n    const currentNode = tree.nodes[tree.rootNodeId];\n    return {\n      ...accum,\n      ...tree.nodes,\n      // set the parent id for the current node\n      [currentNode.id]: {\n        ...currentNode,\n        data: {\n          ...currentNode.data,\n          parent: rootNode.id,\n        },\n      },\n    };\n  }, rootNodes);\n};\n\nexport const mergeTrees = (\n  rootNode: Node,\n  childrenNodes: NodeTree[]\n): NodeTree => ({\n  rootNodeId: rootNode.id,\n  nodes: mergeNodes(rootNode, childrenNodes),\n});\n", "import {\n  QueryCallbacksFor,\n  ERROR_NOT_IN_RESOLVER,\n  getDOMInfo,\n  deprecationWarning,\n  DEPRECATED_ROOT_NODE,\n  ROOT_NODE,\n} from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EventHelpers } from './EventHelpers';\nimport { NodeHelpers } from './NodeHelpers';\n\nimport findPosition from '../events/findPosition';\nimport {\n  NodeId,\n  EditorState,\n  Indicator,\n  Node,\n  Options,\n  NodeEventTypes,\n  NodeInfo,\n  NodeSelector,\n  NodeTree,\n  SerializedNodes,\n  SerializedNode,\n  FreshNode,\n} from '../interfaces';\nimport { createNode } from '../utils/createNode';\nimport { deserializeNode } from '../utils/deserializeNode';\nimport { fromEntries } from '../utils/fromEntries';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { mergeTrees } from '../utils/mergeTrees';\nimport { parseNodeFromJSX } from '../utils/parseNodeFromJSX';\nimport { resolveComponent } from '../utils/resolveComponent';\n\nexport function QueryMethods(state: EditorState) {\n  const options = state && state.options;\n\n  const _: () => QueryCallbacksFor<typeof QueryMethods> = () =>\n    QueryMethods(state) as any;\n\n  return {\n    /**\n     * Determine the best possible location to drop the source Node relative to the target Node\n     *\n     * TODO: replace with Positioner.computeIndicator();\n     */\n    getDropPlaceholder: (\n      source: NodeSelector,\n      target: NodeId,\n      pos: { x: number; y: number },\n      nodesToDOM: (node: Node) => HTMLElement = (node) =>\n        state.nodes[node.id].dom\n    ) => {\n      const targetNode = state.nodes[target],\n        isTargetCanvas = _().node(targetNode.id).isCanvas();\n\n      const targetParent = isTargetCanvas\n        ? targetNode\n        : state.nodes[targetNode.data.parent];\n\n      if (!targetParent) return;\n\n      const targetParentNodes = targetParent.data.nodes || [];\n\n      const dimensionsInContainer = targetParentNodes\n        ? targetParentNodes.reduce((result, id: NodeId) => {\n            const dom = nodesToDOM(state.nodes[id]);\n            if (dom) {\n              const info: NodeInfo = {\n                id,\n                ...getDOMInfo(dom),\n              };\n\n              result.push(info);\n            }\n            return result;\n          }, [] as NodeInfo[])\n        : [];\n\n      const dropAction = findPosition(\n        targetParent,\n        dimensionsInContainer,\n        pos.x,\n        pos.y\n      );\n      const currentNode =\n        targetParentNodes.length &&\n        state.nodes[targetParentNodes[dropAction.index]];\n\n      const output: Indicator = {\n        placement: {\n          ...dropAction,\n          currentNode,\n        },\n        error: null,\n      };\n\n      const sourceNodes = getNodesFromSelector(state.nodes, source);\n\n      sourceNodes.forEach(({ node, exists }) => {\n        // If source Node is already in the editor, check if it's draggable\n        if (exists) {\n          _()\n            .node(node.id)\n            .isDraggable((err) => (output.error = err));\n        }\n      });\n\n      // Check if source Node is droppable in target\n      _()\n        .node(targetParent.id)\n        .isDroppable(source, (err) => (output.error = err));\n\n      return output;\n    },\n\n    /**\n     * Get the current Editor options\n     */\n    getOptions(): Options {\n      return options;\n    },\n\n    getNodes() {\n      return state.nodes;\n    },\n\n    /**\n     * Helper methods to describe the specified Node\n     * @param id\n     */\n    node(id: NodeId) {\n      return NodeHelpers(state, id);\n    },\n\n    /**\n     * Returns all the `nodes` in a serialized format\n     */\n    getSerializedNodes(): SerializedNodes {\n      const nodePairs = Object.keys(state.nodes).map((id: NodeId) => [\n        id,\n        this.node(id).toSerializedNode(),\n      ]);\n      return fromEntries(nodePairs);\n    },\n\n    getEvent(eventType: NodeEventTypes) {\n      return EventHelpers(state, eventType);\n    },\n\n    /**\n     * Retrieve the JSON representation of the editor's Nodes\n     */\n    serialize(): string {\n      return JSON.stringify(this.getSerializedNodes());\n    },\n\n    parseReactElement: (reactElement: React.ReactElement<any>) => ({\n      toNodeTree(\n        normalize?: (node: Node, jsx: React.ReactElement<any>) => void\n      ): NodeTree {\n        let node = parseNodeFromJSX(reactElement, (node, jsx) => {\n          const name = resolveComponent(state.options.resolver, node.data.type);\n\n          node.data.displayName = node.data.displayName || name;\n          node.data.name = name;\n\n          if (normalize) {\n            normalize(node, jsx);\n          }\n        });\n\n        let childrenNodes: NodeTree[] = [];\n\n        if (reactElement.props && reactElement.props.children) {\n          childrenNodes = React.Children.toArray(\n            reactElement.props.children\n          ).reduce<NodeTree[]>((accum, child: any) => {\n            if (React.isValidElement(child)) {\n              accum.push(_().parseReactElement(child).toNodeTree(normalize));\n            }\n            return accum;\n          }, []);\n        }\n\n        return mergeTrees(node, childrenNodes);\n      },\n    }),\n\n    parseSerializedNode: (serializedNode: SerializedNode) => ({\n      toNode(normalize?: (node: Node) => void): Node {\n        const data = deserializeNode(serializedNode, state.options.resolver);\n        invariant(data.type, ERROR_NOT_IN_RESOLVER);\n\n        const id = typeof normalize === 'string' && normalize;\n\n        if (id) {\n          deprecationWarning(`query.parseSerializedNode(...).toNode(id)`, {\n            suggest: `query.parseSerializedNode(...).toNode(node => node.id = id)`,\n          });\n        }\n\n        return _()\n          .parseFreshNode({\n            ...(id ? { id } : {}),\n            data,\n          })\n          .toNode(!id && normalize);\n      },\n    }),\n\n    parseFreshNode: (node: FreshNode) => ({\n      toNode(normalize?: (node: Node) => void): Node {\n        return createNode(node, (node) => {\n          if (node.data.parent === DEPRECATED_ROOT_NODE) {\n            node.data.parent = ROOT_NODE;\n          }\n\n          const name = resolveComponent(state.options.resolver, node.data.type);\n          invariant(name !== null, ERROR_NOT_IN_RESOLVER);\n          node.data.displayName = node.data.displayName || name;\n          node.data.name = name;\n\n          if (normalize) {\n            normalize(node);\n          }\n        });\n      },\n    }),\n\n    createNode(reactElement: React.ReactElement, extras?: any) {\n      deprecationWarning(`query.createNode(${reactElement})`, {\n        suggest: `query.parseReactElement(${reactElement}).toNodeTree()`,\n      });\n\n      const tree = this.parseReactElement(reactElement).toNodeTree();\n\n      const node = tree.nodes[tree.rootNodeId];\n\n      if (!extras) {\n        return node;\n      }\n\n      if (extras.id) {\n        node.id = extras.id;\n      }\n\n      if (extras.data) {\n        node.data = {\n          ...node.data,\n          ...extras.data,\n        };\n      }\n\n      return node;\n    },\n\n    getState() {\n      return state;\n    },\n  };\n}\n", "import { EditorState, NodeId, NodeEventTypes } from '../interfaces';\n\nexport function EventHelpers(state: EditorState, eventType: NodeEventTypes) {\n  const event = state.events[eventType];\n  return {\n    contains(id: NodeId) {\n      return event.has(id);\n    },\n    isEmpty() {\n      return this.all().length === 0;\n    },\n    first() {\n      const values = this.all();\n      return values[0];\n    },\n    last() {\n      const values = this.all();\n      return values[values.length - 1];\n    },\n    all() {\n      return Array.from(event);\n    },\n    size() {\n      return this.all().length;\n    },\n    at(i: number) {\n      return this.all()[i];\n    },\n    raw() {\n      return event;\n    },\n  };\n}\n", "import React, { Fragment } from 'react';\n\nimport { createNode } from './createNode';\n\nimport { Node } from '../interfaces';\n\nexport function parseNodeFromJSX(\n  jsx: React.ReactElement<any> | string,\n  normalize?: (node: Node, jsx: React.ReactElement<any>) => void\n) {\n  let element = jsx as React.ReactElement<any>;\n\n  if (typeof element === 'string') {\n    element = React.createElement(Fragment, {}, element) as React.ReactElement<\n      any\n    >;\n  }\n\n  let actualType = element.type as any;\n\n  return createNode(\n    {\n      data: {\n        type: actualType,\n        props: { ...element.props },\n      },\n    },\n    (node) => {\n      if (normalize) {\n        normalize(node, element);\n      }\n    }\n  );\n}\n", "import { DerivedEventHandlers, EventHandlers } from '@craftjs/utils';\n\nimport { EditorStore } from '../editor/store';\nimport { NodeId, NodeTree } from '../interfaces/nodes';\n\nexport interface CreateHandlerOptions {\n  onCreate: (nodeTree: NodeTree) => void;\n}\n\nexport class CoreEventHandlers<O = {}> extends EventHandlers<\n  { store: EditorStore; removeHoverOnMouseleave: boolean } & O\n> {\n  handlers() {\n    return {\n      connect: (el: HTMLElement, id: NodeId) => {},\n      select: (el: HTMLElement, id: NodeId) => {},\n      hover: (el: HTMLElement, id: NodeId) => {},\n      drag: (el: HTMLElement, id: NodeId) => {},\n      drop: (el: HTMLElement, id: NodeId) => {},\n      create: (\n        el: HTMLElement,\n        UserElement: React.ReactElement | (() => NodeTree | React.ReactElement),\n        options?: Partial<CreateHandlerOptions>\n      ) => {},\n    };\n  }\n}\n\nexport abstract class DerivedCoreEventHandlers<\n  O = {}\n> extends DerivedEventHandlers<CoreEventHandlers, O> {}\n", "import { getDOMInfo, ROOT_NODE } from '@craftjs/utils';\n\nimport findPosition from './findPosition';\n\nimport { EditorStore } from '../editor/store';\nimport {\n  DragTarget,\n  DropPosition,\n  Indicator,\n  Node,\n  NodeId,\n  NodeInfo,\n  NodeSelectorWrapper,\n} from '../interfaces';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\n\n// Hack: to trigger dragend event immediate\n// Otherwise we would have to wait until the native animation is completed before we can actually drop an block\nconst documentDragoverEventHandler = (e: DragEvent) => {\n  e.preventDefault();\n};\n\n/**\n * Positioner is responsible for computing the drop Indicator during a sequence of drag-n-drop events\n */\nexport class Positioner {\n  static BORDER_OFFSET = 10;\n\n  // Current Node being hovered on\n  private currentDropTargetId: NodeId | null;\n  // Current closest Canvas Node relative to the currentDropTarget\n  private currentDropTargetCanvasAncestorId: NodeId | null;\n\n  private currentIndicator: Indicator | null = null;\n\n  private currentTargetId: NodeId | null;\n  private currentTargetChildDimensions: NodeInfo[] | null;\n\n  private dragError: string | null;\n  private draggedNodes: NodeSelectorWrapper[];\n\n  private onScrollListener: (e: Event) => void;\n\n  constructor(readonly store: EditorStore, readonly dragTarget: DragTarget) {\n    this.currentDropTargetId = null;\n    this.currentDropTargetCanvasAncestorId = null;\n\n    this.currentTargetId = null;\n    this.currentTargetChildDimensions = null;\n\n    this.currentIndicator = null;\n\n    this.dragError = null;\n    this.draggedNodes = this.getDraggedNodes();\n\n    this.validateDraggedNodes();\n\n    this.onScrollListener = this.onScroll.bind(this);\n    window.addEventListener('scroll', this.onScrollListener, true);\n    window.addEventListener('dragover', documentDragoverEventHandler, false);\n  }\n\n  cleanup() {\n    window.removeEventListener('scroll', this.onScrollListener, true);\n    window.removeEventListener('dragover', documentDragoverEventHandler, false);\n  }\n\n  private onScroll(e: Event) {\n    const scrollBody = e.target;\n    const rootNode = this.store.query.node(ROOT_NODE).get();\n\n    // Clear the currentTargetChildDimensions if the user has scrolled\n    // Because we will have to recompute new dimensions relative to the new scroll pos\n    const shouldClearChildDimensionsCache =\n      scrollBody instanceof Element &&\n      rootNode &&\n      rootNode.dom &&\n      scrollBody.contains(rootNode.dom);\n\n    if (!shouldClearChildDimensionsCache) {\n      return;\n    }\n\n    this.currentTargetChildDimensions = null;\n  }\n\n  private getDraggedNodes() {\n    if (this.dragTarget.type === 'new') {\n      return getNodesFromSelector(\n        this.store.query.getNodes(),\n        this.dragTarget.tree.nodes[this.dragTarget.tree.rootNodeId]\n      );\n    }\n\n    return getNodesFromSelector(\n      this.store.query.getNodes(),\n      this.dragTarget.nodes\n    );\n  }\n\n  // Check if the elements being dragged are allowed to be dragged\n  private validateDraggedNodes() {\n    // We don't need to check for dragTarget.type = \"new\" because those nodes are not yet in the state (ie: via the .create() connector)\n    if (this.dragTarget.type === 'new') {\n      return;\n    }\n\n    this.draggedNodes.forEach(({ node, exists }) => {\n      if (!exists) {\n        return;\n      }\n\n      this.store.query.node(node.id).isDraggable((err) => {\n        this.dragError = err;\n      });\n    });\n  }\n\n  private isNearBorders(\n    domInfo: ReturnType<typeof getDOMInfo>,\n    x: number,\n    y: number\n  ) {\n    const { top, bottom, left, right } = domInfo;\n\n    if (\n      top + Positioner.BORDER_OFFSET > y ||\n      bottom - Positioner.BORDER_OFFSET < y ||\n      left + Positioner.BORDER_OFFSET > x ||\n      right - Positioner.BORDER_OFFSET < x\n    ) {\n      return true;\n    }\n\n    return false;\n  }\n\n  private isDiff(newPosition: DropPosition) {\n    if (\n      this.currentIndicator &&\n      this.currentIndicator.placement.parent.id === newPosition.parent.id &&\n      this.currentIndicator.placement.index === newPosition.index &&\n      this.currentIndicator.placement.where === newPosition.where\n    ) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Get dimensions of every child Node in the specified parent Node\n   */\n  private getChildDimensions(newParentNode: Node) {\n    // Use previously computed child dimensions if newParentNode is the same as the previous one\n    const existingTargetChildDimensions = this.currentTargetChildDimensions;\n    if (\n      this.currentTargetId === newParentNode.id &&\n      existingTargetChildDimensions\n    ) {\n      return existingTargetChildDimensions;\n    }\n\n    return newParentNode.data.nodes.reduce((result, id: NodeId) => {\n      const dom = this.store.query.node(id).get().dom;\n\n      if (dom) {\n        result.push({\n          id,\n          ...getDOMInfo(dom),\n        });\n      }\n\n      return result;\n    }, [] as NodeInfo[]);\n  }\n\n  /**\n   * Get closest Canvas node relative to the dropTargetId\n   * Return dropTargetId if it itself is a Canvas node\n   *\n   * In most cases it will be the dropTarget itself or its immediate parent.\n   * We typically only need to traverse 2 levels or more if the dropTarget is a linked node\n   *\n   * TODO: We should probably have some special rules to handle linked nodes\n   */\n  private getCanvasAncestor(dropTargetId: NodeId) {\n    // If the dropTargetId is the same as the previous one\n    // Return the canvas ancestor node that we found previuously\n    if (\n      dropTargetId === this.currentDropTargetId &&\n      this.currentDropTargetCanvasAncestorId\n    ) {\n      const node = this.store.query\n        .node(this.currentDropTargetCanvasAncestorId)\n        .get();\n\n      if (node) {\n        return node;\n      }\n    }\n\n    const getCanvas = (nodeId: NodeId): Node => {\n      const node = this.store.query.node(nodeId).get();\n\n      if (node && node.data.isCanvas) {\n        return node;\n      }\n\n      if (!node.data.parent) {\n        return null;\n      }\n\n      return getCanvas(node.data.parent);\n    };\n\n    return getCanvas(dropTargetId);\n  }\n\n  /**\n   * Compute a new Indicator object based on the dropTarget and x,y coords\n   * Returns null if theres no change from the previous Indicator\n   */\n  computeIndicator(dropTargetId: NodeId, x: number, y: number): Indicator {\n    let newParentNode = this.getCanvasAncestor(dropTargetId);\n\n    if (!newParentNode) {\n      return;\n    }\n\n    this.currentDropTargetId = dropTargetId;\n    this.currentDropTargetCanvasAncestorId = newParentNode.id;\n\n    // Get parent if we're hovering at the border of the current node\n    if (\n      newParentNode.data.parent &&\n      this.isNearBorders(getDOMInfo(newParentNode.dom), x, y) &&\n      // Ignore if linked node because there's won't be an adjacent sibling anyway\n      !this.store.query.node(newParentNode.id).isLinkedNode()\n    ) {\n      newParentNode = this.store.query.node(newParentNode.data.parent).get();\n    }\n\n    if (!newParentNode) {\n      return;\n    }\n\n    this.currentTargetChildDimensions = this.getChildDimensions(newParentNode);\n    this.currentTargetId = newParentNode.id;\n\n    const position = findPosition(\n      newParentNode,\n      this.currentTargetChildDimensions,\n      x,\n      y\n    );\n\n    // Ignore if the position is similar as the previous one\n    if (!this.isDiff(position)) {\n      return;\n    }\n\n    let error = this.dragError;\n\n    // Last thing to check for is if the dragged nodes can be dropped in the target area\n    if (!error) {\n      this.store.query.node(newParentNode.id).isDroppable(\n        this.draggedNodes.map((sourceNode) => sourceNode.node),\n        (dropError) => {\n          error = dropError;\n        }\n      );\n    }\n\n    const currentNodeId = newParentNode.data.nodes[position.index];\n    const currentNode =\n      currentNodeId && this.store.query.node(currentNodeId).get();\n\n    this.currentIndicator = {\n      placement: {\n        ...position,\n        currentNode,\n      },\n      error,\n    };\n\n    return this.currentIndicator;\n  }\n\n  getIndicator() {\n    return this.currentIndicator;\n  }\n}\n", "// Works partially with Linux (except on Chrome)\n// We'll need an alternate way to create drag shadows\nexport const createShadow = (\n  e: DragEvent,\n  shadowsToCreate: HTMLElement[],\n  forceSingleShadow: boolean = false\n) => {\n  if (shadowsToCreate.length === 1 || forceSingleShadow) {\n    const { width, height } = shadowsToCreate[0].getBoundingClientRect();\n    const shadow = shadowsToCreate[0].cloneNode(true) as HTMLElement;\n\n    shadow.style.position = `absolute`;\n    shadow.style.left = `-100%`;\n    shadow.style.top = `-100%`;\n    shadow.style.width = `${width}px`;\n    shadow.style.height = `${height}px`;\n    shadow.style.pointerEvents = 'none';\n    shadow.classList.add('drag-shadow');\n\n    document.body.appendChild(shadow);\n    e.dataTransfer.setDragImage(shadow, 0, 0);\n\n    return shadow;\n  }\n\n  /**\n   * If there's supposed to be multiple drag shadows, we will create a single container div to store them\n   * That container will be used as the drag shadow for the current drag event\n   */\n  const container = document.createElement('div');\n  container.style.position = 'absolute';\n  container.style.left = '-100%';\n  container.style.top = `-100%`;\n  container.style.width = '100%';\n  container.style.height = '100%';\n  container.style.pointerEvents = 'none';\n  container.classList.add('drag-shadow-container');\n\n  shadowsToCreate.forEach((dom) => {\n    const { width, height, top, left } = dom.getBoundingClientRect();\n    const shadow = dom.cloneNode(true) as HTMLElement;\n\n    shadow.style.position = `absolute`;\n    shadow.style.left = `${left}px`;\n    shadow.style.top = `${top}px`;\n    shadow.style.width = `${width}px`;\n    shadow.style.height = `${height}px`;\n    shadow.classList.add('drag-shadow');\n\n    container.appendChild(shadow);\n  });\n\n  document.body.appendChild(container);\n  e.dataTransfer.setDragImage(container, e.clientX, e.clientY);\n\n  return container;\n};\n", "import { isChromium, isLinux } from '@craftjs/utils';\nimport isFunction from 'lodash/isFunction';\nimport React from 'react';\n\nimport { CoreEventHandlers, CreateHandlerOptions } from './CoreEventHandlers';\nimport { Positioner } from './Positioner';\nimport { createShadow } from './createShadow';\n\nimport { Indicator, NodeId, DragTarget, NodeTree } from '../interfaces';\n\nexport type DefaultEventHandlersOptions = {\n  isMultiSelectEnabled: (e: MouseEvent) => boolean;\n  removeHoverOnMouseleave: boolean;\n};\n\n/**\n * Specifies Editor-wide event handlers and connectors\n */\nexport class DefaultEventHandlers<O = {}> extends CoreEventHandlers<\n  DefaultEventHandlersOptions & O\n> {\n  /**\n   * Note: Multiple drag shadows (ie: via multiselect in v0.2 and higher) do not look good on Linux Chromium due to way it renders drag shadows in general,\n   * so will have to fallback to the single shadow approach above for the time being\n   * see: https://bugs.chromium.org/p/chromium/issues/detail?id=550999\n   */\n  static forceSingleDragShadow = isChromium() && isLinux();\n\n  draggedElementShadow: HTMLElement;\n  dragTarget: DragTarget;\n  positioner: Positioner | null = null;\n  currentSelectedElementIds = [];\n\n  onDisable() {\n    this.options.store.actions.clearEvents();\n  }\n\n  handlers() {\n    const store = this.options.store;\n\n    return {\n      connect: (el: HTMLElement, id: NodeId) => {\n        store.actions.setDOM(id, el);\n\n        return this.reflect((connectors) => {\n          connectors.select(el, id);\n          connectors.hover(el, id);\n          connectors.drop(el, id);\n        });\n      },\n      select: (el: HTMLElement, id: NodeId) => {\n        const unbindOnMouseDown = this.addCraftEventListener(\n          el,\n          'mousedown',\n          (e) => {\n            e.craft.stopPropagation();\n\n            let newSelectedElementIds = [];\n\n            if (id) {\n              const { query } = store;\n              const selectedElementIds = query.getEvent('selected').all();\n              const isMultiSelect = this.options.isMultiSelectEnabled(e);\n\n              /**\n               * Retain the previously select elements if the multi-select condition is enabled\n               * or if the currentNode is already selected\n               *\n               * so users can just click to drag the selected elements around without holding the multi-select key\n               */\n\n              if (isMultiSelect || selectedElementIds.includes(id)) {\n                newSelectedElementIds = selectedElementIds.filter(\n                  (selectedId) => {\n                    const descendants = query\n                      .node(selectedId)\n                      .descendants(true);\n                    const ancestors = query.node(selectedId).ancestors(true);\n\n                    // Deselect ancestors/descendants\n                    if (descendants.includes(id) || ancestors.includes(id)) {\n                      return false;\n                    }\n\n                    return true;\n                  }\n                );\n              }\n\n              if (!newSelectedElementIds.includes(id)) {\n                newSelectedElementIds.push(id);\n              }\n            }\n\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          }\n        );\n\n        const unbindOnClick = this.addCraftEventListener(el, 'click', (e) => {\n          e.craft.stopPropagation();\n\n          const { query } = store;\n          const selectedElementIds = query.getEvent('selected').all();\n\n          const isMultiSelect = this.options.isMultiSelectEnabled(e);\n          const isNodeAlreadySelected = this.currentSelectedElementIds.includes(\n            id\n          );\n\n          let newSelectedElementIds = [...selectedElementIds];\n\n          if (isMultiSelect && isNodeAlreadySelected) {\n            newSelectedElementIds.splice(newSelectedElementIds.indexOf(id), 1);\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          } else if (!isMultiSelect && selectedElementIds.length > 1) {\n            newSelectedElementIds = [id];\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          }\n\n          this.currentSelectedElementIds = newSelectedElementIds;\n        });\n\n        return () => {\n          unbindOnMouseDown();\n          unbindOnClick();\n        };\n      },\n      hover: (el: HTMLElement, id: NodeId) => {\n        const unbindMouseover = this.addCraftEventListener(\n          el,\n          'mouseover',\n          (e) => {\n            e.craft.stopPropagation();\n            store.actions.setNodeEvent('hovered', id);\n          }\n        );\n\n        let unbindMouseleave: (() => void) | null = null;\n\n        if (this.options.removeHoverOnMouseleave) {\n          unbindMouseleave = this.addCraftEventListener(\n            el,\n            'mouseleave',\n            (e) => {\n              e.craft.stopPropagation();\n              store.actions.setNodeEvent('hovered', null);\n            }\n          );\n        }\n\n        return () => {\n          unbindMouseover();\n\n          if (!unbindMouseleave) {\n            return;\n          }\n\n          unbindMouseleave();\n        };\n      },\n      drop: (el: HTMLElement, targetId: NodeId) => {\n        const unbindDragOver = this.addCraftEventListener(\n          el,\n          'dragover',\n          (e) => {\n            e.craft.stopPropagation();\n            e.preventDefault();\n\n            if (!this.positioner) {\n              return;\n            }\n\n            const indicator = this.positioner.computeIndicator(\n              targetId,\n              e.clientX,\n              e.clientY\n            );\n\n            if (!indicator) {\n              return;\n            }\n\n            store.actions.setIndicator(indicator);\n          }\n        );\n\n        const unbindDragEnter = this.addCraftEventListener(\n          el,\n          'dragenter',\n          (e) => {\n            e.craft.stopPropagation();\n            e.preventDefault();\n          }\n        );\n\n        return () => {\n          unbindDragEnter();\n          unbindDragOver();\n        };\n      },\n      drag: (el: HTMLElement, id: NodeId) => {\n        if (!store.query.node(id).isDraggable()) {\n          return () => {};\n        }\n\n        el.setAttribute('draggable', 'true');\n\n        const unbindDragStart = this.addCraftEventListener(\n          el,\n          'dragstart',\n          (e) => {\n            e.craft.stopPropagation();\n\n            const { query, actions } = store;\n\n            let selectedElementIds = query.getEvent('selected').all();\n\n            const isMultiSelect = this.options.isMultiSelectEnabled(e);\n            const isNodeAlreadySelected = this.currentSelectedElementIds.includes(\n              id\n            );\n\n            if (!isNodeAlreadySelected) {\n              if (isMultiSelect) {\n                selectedElementIds = [...selectedElementIds, id];\n              } else {\n                selectedElementIds = [id];\n              }\n              store.actions.setNodeEvent('selected', selectedElementIds);\n            }\n\n            actions.setNodeEvent('dragged', selectedElementIds);\n\n            const selectedDOMs = selectedElementIds.map(\n              (id) => query.node(id).get().dom\n            );\n\n            this.draggedElementShadow = createShadow(\n              e,\n              selectedDOMs,\n              DefaultEventHandlers.forceSingleDragShadow\n            );\n\n            this.dragTarget = {\n              type: 'existing',\n              nodes: selectedElementIds,\n            };\n\n            this.positioner = new Positioner(\n              this.options.store,\n              this.dragTarget\n            );\n          }\n        );\n\n        const unbindDragEnd = this.addCraftEventListener(el, 'dragend', (e) => {\n          e.craft.stopPropagation();\n\n          this.dropElement((dragTarget, indicator) => {\n            if (dragTarget.type === 'new') {\n              return;\n            }\n\n            const index =\n              indicator.placement.index +\n              (indicator.placement.where === 'after' ? 1 : 0);\n\n            store.actions.move(\n              dragTarget.nodes,\n              indicator.placement.parent.id,\n              index\n            );\n          });\n        });\n\n        return () => {\n          el.setAttribute('draggable', 'false');\n          unbindDragStart();\n          unbindDragEnd();\n        };\n      },\n      create: (\n        el: HTMLElement,\n        userElement: React.ReactElement | (() => NodeTree | React.ReactElement),\n        options?: Partial<CreateHandlerOptions>\n      ) => {\n        el.setAttribute('draggable', 'true');\n\n        const unbindDragStart = this.addCraftEventListener(\n          el,\n          'dragstart',\n          (e) => {\n            e.craft.stopPropagation();\n            let tree;\n            if (typeof userElement === 'function') {\n              const result = userElement();\n              if (React.isValidElement(result)) {\n                tree = store.query.parseReactElement(result).toNodeTree();\n              } else {\n                tree = result;\n              }\n            } else {\n              tree = store.query.parseReactElement(userElement).toNodeTree();\n            }\n\n            const dom = e.currentTarget as HTMLElement;\n            this.draggedElementShadow = createShadow(\n              e,\n              [dom],\n              DefaultEventHandlers.forceSingleDragShadow\n            );\n            this.dragTarget = {\n              type: 'new',\n              tree,\n            };\n\n            this.positioner = new Positioner(\n              this.options.store,\n              this.dragTarget\n            );\n          }\n        );\n\n        const unbindDragEnd = this.addCraftEventListener(el, 'dragend', (e) => {\n          e.craft.stopPropagation();\n          this.dropElement((dragTarget, indicator) => {\n            if (dragTarget.type === 'existing') {\n              return;\n            }\n\n            const index =\n              indicator.placement.index +\n              (indicator.placement.where === 'after' ? 1 : 0);\n            store.actions.addNodeTree(\n              dragTarget.tree,\n              indicator.placement.parent.id,\n              index\n            );\n\n            if (options && isFunction(options.onCreate)) {\n              options.onCreate(dragTarget.tree);\n            }\n          });\n        });\n\n        return () => {\n          el.removeAttribute('draggable');\n          unbindDragStart();\n          unbindDragEnd();\n        };\n      },\n    };\n  }\n\n  private dropElement(\n    onDropNode: (dragTarget: DragTarget, placement: Indicator) => void\n  ) {\n    const store = this.options.store;\n\n    if (!this.positioner) {\n      return;\n    }\n\n    const draggedElementShadow = this.draggedElementShadow;\n\n    const indicator = this.positioner.getIndicator();\n\n    if (this.dragTarget && indicator && !indicator.error) {\n      onDropNode(this.dragTarget, indicator);\n    }\n\n    if (draggedElementShadow) {\n      draggedElementShadow.parentNode.removeChild(draggedElementShadow);\n      this.draggedElementShadow = null;\n    }\n\n    this.dragTarget = null;\n\n    store.actions.setIndicator(null);\n    store.actions.setNodeEvent('dragged', null);\n    this.positioner.cleanup();\n\n    this.positioner = null;\n  }\n}\n", "import { DropPosition, DOMInfo } from '../interfaces';\n\nexport default function movePlaceholder(\n  pos: DropPosition,\n  canvasDOMInfo: DOMInfo, // which canvas is cursor at\n  bestTargetDomInfo: DOMInfo | null, // closest element in canvas (null if canvas is empty)\n  thickness: number = 2\n) {\n  let t = 0,\n    l = 0,\n    w = 0,\n    h = 0,\n    where = pos.where;\n\n  const elDim = bestTargetDomInfo;\n\n  if (elDim) {\n    // If it's not in flow (like 'float' element)\n    if (!elDim.inFlow) {\n      w = thickness;\n      h = elDim.outerHeight;\n      t = elDim.top;\n      l = where === 'before' ? elDim.left : elDim.left + elDim.outerWidth;\n    } else {\n      w = elDim.outerWidth;\n      h = thickness;\n      t = where === 'before' ? elDim.top : elDim.bottom;\n      l = elDim.left;\n    }\n  } else {\n    if (canvasDOMInfo) {\n      t = canvasDOMInfo.top + canvasDOMInfo.padding.top;\n      l = canvasDOMInfo.left + canvasDOMInfo.padding.left;\n      w =\n        canvasDOMInfo.outerWidth -\n        canvasDOMInfo.padding.right -\n        canvasDOMInfo.padding.left -\n        canvasDOMInfo.margin.left -\n        canvasDOMInfo.margin.right;\n      h = thickness;\n    }\n  }\n  return {\n    top: `${t}px`,\n    left: `${l}px`,\n    width: `${w}px`,\n    height: `${h}px`,\n  };\n}\n", "import { RenderIndicator, getDOMInfo } from '@craftjs/utils';\nimport React, { useEffect } from 'react';\n\nimport { useEventHandler } from './EventContext';\nimport movePlaceholder from './movePlaceholder';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\n\nexport const RenderEditorIndicator = () => {\n  const { indicator, indicatorOptions, enabled } = useInternalEditor(\n    (state) => ({\n      indicator: state.indicator,\n      indicatorOptions: state.options.indicator,\n      enabled: state.options.enabled,\n    })\n  );\n\n  const handler = useEventHandler();\n\n  useEffect(() => {\n    if (!handler) {\n      return;\n    }\n\n    if (!enabled) {\n      handler.disable();\n      return;\n    }\n\n    handler.enable();\n  }, [enabled, handler]);\n\n  if (!indicator) {\n    return null;\n  }\n\n  return React.createElement(RenderIndicator, {\n    className: indicatorOptions.className,\n    style: {\n      ...movePlaceholder(\n        indicator.placement,\n        getDOMInfo(indicator.placement.parent.dom),\n        indicator.placement.currentNode &&\n          getDOMInfo(indicator.placement.currentNode.dom),\n        indicatorOptions.thickness\n      ),\n      backgroundColor: indicator.error\n        ? indicatorOptions.error\n        : indicatorOptions.success,\n      transition: indicatorOptions.transition || '0.2s ease-in',\n      ...(indicatorOptions.style ?? {}),\n    },\n    parentDom: indicator.placement.parent.dom,\n  });\n};\n", "import React, { useContext, useMemo } from 'react';\n\nimport { EventHandlerContext } from './EventContext';\nimport { RenderEditorIndicator } from './RenderEditorIndicator';\n\nimport { EditorContext } from '../editor/EditorContext';\n\ntype EventsProps = {\n  children?: React.ReactNode;\n};\n\nexport const Events = ({ children }: EventsProps) => {\n  const store = useContext(EditorContext);\n\n  const handler = useMemo(() => store.query.getOptions().handlers(store), [\n    store,\n  ]);\n\n  if (!handler) {\n    return null;\n  }\n\n  return (\n    <EventHandlerContext.Provider value={handler}>\n      <RenderEditorIndicator />\n      {children}\n    </EventHandlerContext.Provider>\n  );\n};\n", "import {\n  useMethods,\n  SubscriberAndCallbacksFor,\n  PatchListener,\n} from '@craftjs/utils';\n\nimport { ActionMethods } from './actions';\nimport { QueryMethods } from './query';\n\nimport { DefaultEventHandlers } from '../events';\nimport { EditorState, Options, NodeEventTypes, NodeId } from '../interfaces';\n\nexport const editorInitialState: EditorState = {\n  nodes: {},\n  events: {\n    dragged: new Set<NodeId>(),\n    selected: new Set<NodeId>(),\n    hovered: new Set<NodeId>(),\n  },\n  indicator: null,\n  options: {\n    onNodesChange: () => null,\n    onRender: ({ render }) => render,\n    onBeforeMoveEnd: () => null,\n    resolver: {},\n    enabled: true,\n    indicator: {\n      error: 'red',\n      success: 'rgb(98, 196, 98)',\n    },\n    handlers: (store) =>\n      new DefaultEventHandlers({\n        store,\n        removeHoverOnMouseleave: false,\n        isMultiSelectEnabled: (e: MouseEvent) => !!e.meta<PERSON>ey,\n      }),\n    normalizeNodes: () => {},\n  },\n};\n\nexport const ActionMethodsWithConfig = {\n  methods: ActionMethods,\n  ignoreHistoryForActions: [\n    'setDOM',\n    'setNodeEvent',\n    'selectNode',\n    'clearEvents',\n    'setOptions',\n    'setIndicator',\n  ] as const,\n  normalizeHistory: (state: EditorState) => {\n    /**\n     * On every undo/redo, we remove events pointing to deleted Nodes\n     */\n    Object.keys(state.events).forEach((eventName: NodeEventTypes) => {\n      const nodeIds = Array.from(state.events[eventName] || []);\n\n      nodeIds.forEach((id) => {\n        if (!state.nodes[id]) {\n          state.events[eventName].delete(id);\n        }\n      });\n    });\n\n    // Remove any invalid node[nodeId].events\n    // TODO(prev): it's really cumbersome to have to ensure state.events and state.nodes[nodeId].events are in sync\n    // Find a way to make it so that once state.events is set, state.nodes[nodeId] automatically reflects that (maybe using proxies?)\n    Object.keys(state.nodes).forEach((id) => {\n      const node = state.nodes[id];\n\n      Object.keys(node.events).forEach((eventName: NodeEventTypes) => {\n        const isEventActive = !!node.events[eventName];\n\n        if (\n          isEventActive &&\n          state.events[eventName] &&\n          !state.events[eventName].has(node.id)\n        ) {\n          node.events[eventName] = false;\n        }\n      });\n    });\n  },\n};\n\nexport type EditorStore = SubscriberAndCallbacksFor<\n  typeof ActionMethodsWithConfig,\n  typeof QueryMethods\n>;\n\nexport const useEditorStore = (\n  options: Partial<Options>,\n  patchListener: PatchListener<\n    EditorState,\n    typeof ActionMethodsWithConfig,\n    typeof QueryMethods\n  >\n): EditorStore => {\n  // TODO: fix type\n  return useMethods(\n    ActionMethodsWithConfig,\n    {\n      ...editorInitialState,\n      options: {\n        ...editorInitialState.options,\n        ...options,\n      },\n    },\n    QueryMethods,\n    patchListener\n  ) as EditorStore;\n};\n", "import {\n  deprecationWarning,\n  ERROR_INVALID_NODEID,\n  ROOT_NODE,\n  DEPRECATED_ROOT_NODE,\n  QueryCallbacksFor,\n  ERROR_NOPARENT,\n  ERROR_DELETE_TOP_LEVEL_NODE,\n  CallbacksFor,\n  Delete,\n  ERROR_NOT_IN_RESOLVER,\n} from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { QueryMethods } from './query';\n\nimport {\n  EditorState,\n  Indicator,\n  NodeId,\n  Node,\n  Nodes,\n  Options,\n  NodeEventTypes,\n  NodeTree,\n  SerializedNodes,\n  NodeSelector,\n  NodeSelectorType,\n} from '../interfaces';\nimport { fromEntries } from '../utils/fromEntries';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { removeNodeFromEvents } from '../utils/removeNodeFromEvents';\n\nconst Methods = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => {\n  /** Helper functions */\n  const addNodeTreeToParent = (\n    tree: NodeTree,\n    parentId?: NodeId,\n    addNodeType?:\n      | {\n          type: 'child';\n          index: number;\n        }\n      | {\n          type: 'linked';\n          id: string;\n        }\n  ) => {\n    const iterateChildren = (id: NodeId, parentId?: NodeId) => {\n      const node = tree.nodes[id];\n\n      if (typeof node.data.type !== 'string') {\n        invariant(\n          state.options.resolver[node.data.name],\n          ERROR_NOT_IN_RESOLVER.replace(\n            '%node_type%',\n            `${(node.data.type as any).name}`\n          )\n        );\n      }\n\n      state.nodes[id] = {\n        ...node,\n        data: {\n          ...node.data,\n          parent: parentId,\n        },\n      };\n\n      if (node.data.nodes.length > 0) {\n        delete state.nodes[id].data.props.children;\n        node.data.nodes.forEach((childNodeId) =>\n          iterateChildren(childNodeId, node.id)\n        );\n      }\n\n      Object.values(node.data.linkedNodes).forEach((linkedNodeId) =>\n        iterateChildren(linkedNodeId, node.id)\n      );\n    };\n\n    iterateChildren(tree.rootNodeId, parentId);\n\n    if (!parentId && tree.rootNodeId === ROOT_NODE) {\n      return;\n    }\n\n    const parent = getParentAndValidate(parentId);\n\n    if (addNodeType.type === 'child') {\n      const index = addNodeType.index;\n\n      if (index != null) {\n        parent.data.nodes.splice(index, 0, tree.rootNodeId);\n      } else {\n        parent.data.nodes.push(tree.rootNodeId);\n      }\n\n      return;\n    }\n\n    parent.data.linkedNodes[addNodeType.id] = tree.rootNodeId;\n  };\n\n  const getParentAndValidate = (parentId: NodeId): Node => {\n    invariant(parentId, ERROR_NOPARENT);\n    const parent = state.nodes[parentId];\n    invariant(parent, ERROR_INVALID_NODEID);\n    return parent;\n  };\n\n  const deleteNode = (id: NodeId) => {\n    const targetNode = state.nodes[id],\n      parentNode = state.nodes[targetNode.data.parent];\n\n    if (targetNode.data.nodes) {\n      // we deep clone here because otherwise immer will mutate the node\n      // object as we remove nodes\n      [...targetNode.data.nodes].forEach((childId) => deleteNode(childId));\n    }\n\n    if (targetNode.data.linkedNodes) {\n      Object.values(targetNode.data.linkedNodes).map((linkedNodeId) =>\n        deleteNode(linkedNodeId)\n      );\n    }\n\n    const isChildNode = parentNode.data.nodes.includes(id);\n\n    if (isChildNode) {\n      const parentChildren = parentNode.data.nodes;\n      parentChildren.splice(parentChildren.indexOf(id), 1);\n    } else {\n      const linkedId = Object.keys(parentNode.data.linkedNodes).find(\n        (id) => parentNode.data.linkedNodes[id] === id\n      );\n      if (linkedId) {\n        delete parentNode.data.linkedNodes[linkedId];\n      }\n    }\n\n    removeNodeFromEvents(state, id);\n    delete state.nodes[id];\n  };\n\n  return {\n    /**\n     * @private\n     * Add a new linked Node to the editor.\n     * Only used internally by the <Element /> component\n     *\n     * @param tree\n     * @param parentId\n     * @param id\n     */\n    addLinkedNodeFromTree(tree: NodeTree, parentId: NodeId, id: string) {\n      const parent = getParentAndValidate(parentId);\n\n      const existingLinkedNode = parent.data.linkedNodes[id];\n\n      if (existingLinkedNode) {\n        deleteNode(existingLinkedNode);\n      }\n\n      addNodeTreeToParent(tree, parentId, { type: 'linked', id });\n    },\n\n    /**\n     * Add a new Node to the editor.\n     *\n     * @param nodeToAdd\n     * @param parentId\n     * @param index\n     */\n    add(nodeToAdd: Node | Node[], parentId?: NodeId, index?: number) {\n      // TODO: Deprecate adding array of Nodes to keep implementation simpler\n      let nodes = [nodeToAdd];\n      if (Array.isArray(nodeToAdd)) {\n        deprecationWarning('actions.add(node: Node[])', {\n          suggest: 'actions.add(node: Node)',\n        });\n        nodes = nodeToAdd;\n      }\n      nodes.forEach((node: Node) => {\n        addNodeTreeToParent(\n          {\n            nodes: {\n              [node.id]: node,\n            },\n            rootNodeId: node.id,\n          },\n          parentId,\n          { type: 'child', index }\n        );\n      });\n    },\n\n    /**\n     * Add a NodeTree to the editor\n     *\n     * @param tree\n     * @param parentId\n     * @param index\n     */\n    addNodeTree(tree: NodeTree, parentId?: NodeId, index?: number) {\n      addNodeTreeToParent(tree, parentId, { type: 'child', index });\n    },\n\n    /**\n     * Delete a Node\n     * @param id\n     */\n    delete(selector: NodeSelector<NodeSelectorType.Id>) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        existOnly: true,\n        idOnly: true,\n      });\n\n      targets.forEach(({ node }) => {\n        invariant(\n          !query.node(node.id).isTopLevelNode(),\n          ERROR_DELETE_TOP_LEVEL_NODE\n        );\n        deleteNode(node.id);\n      });\n    },\n\n    deserialize(input: SerializedNodes | string) {\n      const dehydratedNodes =\n        typeof input == 'string' ? JSON.parse(input) : input;\n\n      const nodePairs = Object.keys(dehydratedNodes).map((id) => {\n        let nodeId = id;\n\n        if (id === DEPRECATED_ROOT_NODE) {\n          nodeId = ROOT_NODE;\n        }\n\n        return [\n          nodeId,\n          query\n            .parseSerializedNode(dehydratedNodes[id])\n            .toNode((node) => (node.id = nodeId)),\n        ];\n      });\n\n      this.replaceNodes(fromEntries(nodePairs));\n    },\n\n    /**\n     * Move a target Node to a new Parent at a given index\n     * @param targetId\n     * @param newParentId\n     * @param index\n     */\n    move(selector: NodeSelector, newParentId: NodeId, index: number) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        existOnly: true,\n      });\n\n      const newParent = state.nodes[newParentId];\n\n      const nodesArrToCleanup = new Set<string[]>();\n\n      targets.forEach(({ node: targetNode }, i) => {\n        const targetId = targetNode.id;\n        const currentParentId = targetNode.data.parent;\n\n        query.node(newParentId).isDroppable([targetId], (err) => {\n          throw new Error(err);\n        });\n\n        // modify node props\n        state.options.onBeforeMoveEnd(\n          targetNode,\n          newParent,\n          state.nodes[currentParentId]\n        );\n\n        const currentParent = state.nodes[currentParentId];\n        const currentParentNodes = currentParent.data.nodes;\n\n        nodesArrToCleanup.add(currentParentNodes);\n\n        const oldIndex = currentParentNodes.indexOf(targetId);\n        currentParentNodes[oldIndex] = '$$'; // mark for deletion\n\n        newParent.data.nodes.splice(index + i, 0, targetId);\n\n        state.nodes[targetId].data.parent = newParentId;\n      });\n\n      nodesArrToCleanup.forEach((nodes) => {\n        const length = nodes.length;\n\n        [...nodes].reverse().forEach((value, index) => {\n          if (value !== '$$') {\n            return;\n          }\n\n          nodes.splice(length - 1 - index, 1);\n        });\n      });\n    },\n\n    replaceNodes(nodes: Nodes) {\n      this.clearEvents();\n      state.nodes = nodes;\n    },\n\n    clearEvents() {\n      this.setNodeEvent('selected', null);\n      this.setNodeEvent('hovered', null);\n      this.setNodeEvent('dragged', null);\n      this.setIndicator(null);\n    },\n\n    /**\n     * Resets all the editor state.\n     */\n    reset() {\n      this.clearEvents();\n      this.replaceNodes({});\n    },\n\n    /**\n     * Set editor options via a callback function\n     *\n     * @param cb: function used to set the options.\n     */\n    setOptions(cb: (options: Partial<Options>) => void) {\n      cb(state.options);\n    },\n\n    setNodeEvent(\n      eventType: NodeEventTypes,\n      nodeIdSelector: NodeSelector<NodeSelectorType.Id>\n    ) {\n      state.events[eventType].forEach((id) => {\n        if (state.nodes[id]) {\n          state.nodes[id].events[eventType] = false;\n        }\n      });\n\n      state.events[eventType] = new Set();\n\n      if (!nodeIdSelector) {\n        return;\n      }\n\n      const targets = getNodesFromSelector(state.nodes, nodeIdSelector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      const nodeIds: Set<NodeId> = new Set(targets.map(({ node }) => node.id));\n      nodeIds.forEach((id) => {\n        state.nodes[id].events[eventType] = true;\n      });\n      state.events[eventType] = nodeIds;\n    },\n\n    /**\n     * Set custom values to a Node\n     * @param id\n     * @param cb\n     */\n    setCustom<T extends NodeId>(\n      selector: NodeSelector<NodeSelectorType.Id>,\n      cb: (data: EditorState['nodes'][T]['data']['custom']) => void\n    ) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      targets.forEach(({ node }) => cb(state.nodes[node.id].data.custom));\n    },\n\n    /**\n     * Given a `id`, it will set the `dom` porperty of that node.\n     *\n     * @param id of the node we want to set\n     * @param dom\n     */\n    setDOM(id: NodeId, dom: HTMLElement) {\n      if (!state.nodes[id]) {\n        return;\n      }\n\n      state.nodes[id].dom = dom;\n    },\n\n    setIndicator(indicator: Indicator | null) {\n      if (\n        indicator &&\n        (!indicator.placement.parent.dom ||\n          (indicator.placement.currentNode &&\n            !indicator.placement.currentNode.dom))\n      )\n        return;\n      state.indicator = indicator;\n    },\n\n    /**\n     * Hide a Node\n     * @param id\n     * @param bool\n     */\n    setHidden(id: NodeId, bool: boolean) {\n      state.nodes[id].data.hidden = bool;\n    },\n\n    /**\n     * Update the props of a Node\n     * @param id\n     * @param cb\n     */\n    setProp(\n      selector: NodeSelector<NodeSelectorType.Id>,\n      cb: (props: any) => void\n    ) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      targets.forEach(({ node }) => cb(state.nodes[node.id].data.props));\n    },\n\n    selectNode(nodeIdSelector?: NodeSelector<NodeSelectorType.Id>) {\n      if (nodeIdSelector) {\n        const targets = getNodesFromSelector(state.nodes, nodeIdSelector, {\n          idOnly: true,\n          existOnly: true,\n        });\n\n        this.setNodeEvent(\n          'selected',\n          targets.map(({ node }) => node.id)\n        );\n      } else {\n        this.setNodeEvent('selected', null);\n      }\n\n      this.setNodeEvent('hovered', null);\n    },\n  };\n};\n\nexport const ActionMethods = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => {\n  return {\n    ...Methods(state, query),\n    // Note: Beware: advanced method! You most likely don't need to use this\n    // TODO: fix parameter types and cleanup the method\n    setState(\n      cb: (\n        state: EditorState,\n        actions: Delete<CallbacksFor<typeof Methods>, 'history'>\n      ) => void\n    ) {\n      const { history, ...actions } = this;\n\n      // We pass the other actions as the second parameter, so that devs could still make use of the predefined actions\n      cb(state, actions);\n    },\n  };\n};\n", "import { EditorState, NodeId } from '../interfaces';\n\nexport const removeNodeFromEvents = (state: EditorState, nodeId: NodeId) =>\n  Object.keys(state.events).forEach((key) => {\n    const eventSet = state.events[key];\n    if (eventSet && eventSet.has && eventSet.has(nodeId)) {\n      state.events[key] = new Set(\n        Array.from(eventSet).filter((id) => nodeId !== id)\n      );\n    }\n  });\n", "import { ERROR_RESOLVER_NOT_AN_OBJECT, HISTORY_ACTIONS } from '@craftjs/utils';\nimport * as React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EditorContext } from './EditorContext';\nimport { useEditorStore } from './store';\n\nimport { Events } from '../events';\nimport { Options } from '../interfaces';\n\ntype EditorProps = Partial<Options> & {\n  children?: React.ReactNode;\n};\n\n/**\n * A React Component that provides the Editor context\n */\nexport const Editor = ({ children, ...options }: EditorProps) => {\n  // we do not want to warn the user if no resolver was supplied\n  if (options.resolver !== undefined) {\n    invariant(\n      typeof options.resolver === 'object' &&\n        !Array.isArray(options.resolver) &&\n        options.resolver !== null,\n      ERROR_RESOLVER_NOT_AN_OBJECT\n    );\n  }\n\n  const optionsRef = React.useRef(options);\n\n  const context = useEditorStore(\n    optionsRef.current,\n    (state, previousState, actionPerformedWithPatches, query, normalizer) => {\n      if (!actionPerformedWithPatches) {\n        return;\n      }\n\n      const { patches, ...actionPerformed } = actionPerformedWithPatches;\n\n      for (let i = 0; i < patches.length; i++) {\n        const { path } = patches[i];\n        const isModifyingNodeData =\n          path.length > 2 && path[0] === 'nodes' && path[2] === 'data';\n\n        let actionType = actionPerformed.type;\n\n        if (\n          [HISTORY_ACTIONS.IGNORE, HISTORY_ACTIONS.THROTTLE].includes(\n            actionType\n          ) &&\n          actionPerformed.params\n        ) {\n          actionPerformed.type = actionPerformed.params[0];\n        }\n\n        if (\n          ['setState', 'deserialize'].includes(actionPerformed.type) ||\n          isModifyingNodeData\n        ) {\n          normalizer((draft) => {\n            if (state.options.normalizeNodes) {\n              state.options.normalizeNodes(\n                draft,\n                previousState,\n                actionPerformed,\n                query\n              );\n            }\n          });\n          break; // we exit the loop as soon as we find a change in node.data\n        }\n      }\n    }\n  );\n\n  // sync enabled prop with editor store options\n  React.useEffect(() => {\n    if (!context) {\n      return;\n    }\n\n    if (\n      options.enabled === undefined ||\n      context.query.getOptions().enabled === options.enabled\n    ) {\n      return;\n    }\n\n    context.actions.setOptions((editorOptions) => {\n      editorOptions.enabled = options.enabled;\n    });\n  }, [context, options.enabled]);\n\n  React.useEffect(() => {\n    context.subscribe(\n      (_) => ({\n        json: context.query.serialize(),\n      }),\n      () => {\n        context.query.getOptions().onNodesChange(context.query);\n      }\n    );\n  }, [context]);\n\n  if (!context) {\n    return null;\n  }\n\n  return (\n    <EditorContext.Provider value={context}>\n      <Events>{children}</Events>\n    </EditorContext.Provider>\n  );\n};\n", "import cloneDeep from 'lodash/cloneDeep';\n\nimport { createNode } from './createNode';\n\nimport { editorInitialState } from '../editor/store';\nimport { Nodes } from '../interfaces';\n\nconst getTestNode = (parentNode) => {\n  const {\n    events,\n    data: { nodes: childNodes, linkedNodes },\n    ...restParentNode\n  } = parentNode;\n  const validParentNode = createNode(cloneDeep(parentNode));\n  parentNode = {\n    ...validParentNode,\n    ...restParentNode,\n    events: {\n      ...validParentNode.events,\n      ...events,\n    },\n    dom: parentNode.dom || validParentNode.dom,\n  };\n\n  return {\n    node: parentNode,\n    childNodes,\n    linkedNodes,\n  };\n};\n\nexport const expectEditorState = (lhs, rhs) => {\n  const { nodes: nodesRhs, ...restRhs } = rhs;\n  const { nodes: nodesLhs, ...restLhs } = lhs;\n  expect(restLhs).toEqual(restRhs);\n\n  const nodesRhsSimplified = Object.keys(nodesRhs).reduce((accum, id) => {\n    const { _hydrationTimestamp, rules, ...node } = nodesRhs[id];\n    accum[id] = node;\n    return accum;\n  }, {});\n\n  const nodesLhsSimplified = Object.keys(nodesLhs).reduce((accum, id) => {\n    const { _hydrationTimestamp, rules, ...node } = nodesLhs[id];\n    accum[id] = node;\n    return accum;\n  }, {});\n\n  expect(nodesLhsSimplified).toEqual(nodesRhsSimplified);\n};\n\nexport const createTestNodes = (rootNode): Nodes => {\n  const nodes = {};\n  const iterateNodes = (testNode) => {\n    const { node: parentNode, childNodes, linkedNodes } = getTestNode(testNode);\n    nodes[parentNode.id] = parentNode;\n\n    if (childNodes) {\n      childNodes.forEach((childTestNode, i) => {\n        const {\n          node: childNode,\n          childNodes: grandChildNodes,\n          linkedNodes: grandChildLinkedNodes,\n        } = getTestNode(childTestNode);\n        childNode.data.parent = parentNode.id;\n        nodes[childNode.id] = childNode;\n        parentNode.data.nodes[i] = childNode.id;\n        iterateNodes({\n          ...childNode,\n          data: {\n            ...childNode.data,\n            nodes: grandChildNodes || [],\n            linkedNodes: grandChildLinkedNodes || {},\n          },\n        });\n      });\n    }\n\n    if (linkedNodes) {\n      Object.keys(linkedNodes).forEach((linkedId) => {\n        const {\n          node: childNode,\n          childNodes: grandChildNodes,\n          linkedNodes: grandChildLinkedNodes,\n        } = getTestNode(linkedNodes[linkedId]);\n        parentNode.data.linkedNodes[linkedId] = childNode.id;\n\n        childNode.data.parent = parentNode.id;\n        nodes[childNode.id] = childNode;\n        iterateNodes({\n          ...childNode,\n          data: {\n            ...childNode.data,\n            nodes: grandChildNodes || [],\n            linkedNodes: grandChildLinkedNodes || {},\n          },\n        });\n      });\n    }\n  };\n\n  iterateNodes(rootNode);\n\n  return nodes;\n};\n\nexport const createTestState = (state = {} as any) => {\n  const { nodes: rootNode, events } = state;\n\n  return {\n    ...editorInitialState,\n    ...state,\n    nodes: rootNode ? createTestNodes(rootNode) : {},\n    events: {\n      ...editorInitialState.events,\n      ...(events || {}),\n    },\n  };\n};\n"], "names": ["NodeContext", "React", "createContext", "NodeProvider", "id", "related", "children", "createElement", "Provider", "value", "EditorContext", "EventHandlerContext", "useEventHandler", "useContext", "useInternalEditor", "collector", "handler", "store", "invariant", "ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT", "collected", "useCollector", "connectorsUsage", "useMemo", "createConnectorsUsage", "useEffect", "register", "cleanup", "connectors", "wrapConnectorHooks", "_objectSpread", "inContext", "useInternalNode", "collect", "context", "ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT", "state", "nodes", "EditorActions", "actions", "editorConnectors", "_objectWithoutProperties", "_useInternalEditor", "_excluded", "connect", "dom", "drag", "setProp", "cb", "throttleRate", "history", "throttle", "setCustom", "setHidden", "bool", "inNodeContext", "useNode", "_useInternalNode", "deprecationWarning", "suggest", "SimpleElement", "render", "type", "cloneElement", "De<PERSON>ult<PERSON>ender", "props", "hydrationTimestamp", "node", "data", "_hydrationTimestamp", "length", "Fragment", "map", "NodeElement", "key", "RenderNodeToElement", "hidden", "onRender", "options", "defaultElementProps", "is", "canvas", "custom", "elementPropToNodeData", "Element", "elementProps", "query", "nodeId", "linkedNodeId", "useState", "ERROR_TOP_LEVEL_ELEMENT_NO_ID", "get", "existingNode", "linkedNodes", "linkedElement", "tree", "parseReactElement", "toNodeTree", "ignore", "addLinkedNodeFromTree", "rootNodeId", "deprecateCanvasComponent", "<PERSON><PERSON>", "RenderRootNode", "timestamp", "ROOT_NODE", "<PERSON>ame", "json", "isLoaded", "useRef", "current", "initialData", "deserialize", "rootNode", "Children", "only", "jsx", "addNodeTree", "NodeSelectorType", "getPublicActions", "setDOM", "setNodeEvent", "replaceNodes", "reset", "useEditor", "internalActions", "args", "connectEditor", "WrappedComponent", "Editor", "connectNode", "fromEntries", "pairs", "Object", "reduce", "accum", "_ref", "_ref2", "_slicedToArray", "_defineProperty", "getNodesFromSelector", "selector", "config", "items", "Array", "isArray", "mergedConfig", "existOnly", "idOnly", "nodeSelectors", "filter", "item", "exists", "_typeof", "ERROR_INVALID_NODEID", "CACHED_RESOLVER_DATA", "resolveComponent", "resolver", "comp", "component", "resolvedName", "name", "reversed", "Map", "_i", "_Object$entries", "entries", "_Object$entries$_i", "set", "getReversedResolver", "undefined", "searchComponentInResolver", "ERROR_NOT_IN_RESOLVER", "replace", "displayName", "reduceType", "serializeComp", "isCanvas", "keys", "result", "prop", "child", "serializeNode", "nodeData", "NodeHelpers", "ERROR_INVALID_NODE_ID", "nodeHelpers", "isRoot", "isLinkedNode", "parent", "includes", "isTopLevelNode", "this", "isDeletable", "isParentOfTopLevelNodes", "isParentOfTopLevelCanvas", "isSelected", "events", "selected", "has", "isHovered", "hovered", "isDragged", "dragged", "ancestors", "deep", "appendParentNode", "depth", "push", "descendants", "include<PERSON>nly", "arguments", "appendChildNode", "for<PERSON>ach", "childNodes", "values", "isDraggable", "onError", "targetNode", "ERROR_MOVE_TOP_LEVEL_NODE", "ERROR_MOVE_NONCANVAS_CHILD", "rules", "canDrag", "ERROR_CANNOT_DRAG", "err", "isDroppable", "targets", "newParentNode", "ERROR_MOVE_TO_NONCANVAS_PARENT", "canMoveIn", "ERROR_MOVE_INCOMING_PARENT", "parentNodes", "canDrop", "ERROR_MOVE_CANNOT_DROP", "targetDeepNodes", "ERROR_MOVE_TO_DESCENDANT", "currentParentNode", "ERROR_DUPLICATE_NODEID", "parentNodeId", "parentNode", "canMoveOut", "ERROR_MOVE_OUTGOING_PARENT", "toSerializedNode", "descendantId", "decendants", "isTopLevelCanvas", "findPosition", "dims", "posX", "posY", "index", "where", "leftLimit", "xLimit", "yLimit", "xCenter", "yCenter", "dimDown", "i", "len", "dim", "top", "outerHeight", "left", "outerWidth", "inFlow", "getNodeTypeName", "createNode", "newNode", "normalize", "actualType", "getRandomNodeId", "Date", "now", "info", "mergedProps", "userComponentConfig", "craft", "defaultProps", "relatedNodeContext", "deserializeComp", "main", "restoreType", "deserializeNode", "Comp", "Props", "ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER", "join", "_child<PERSON><PERSON><PERSON>", "mergeNodes", "childrenNodes", "nodeWithChildren", "currentNode", "mergeTrees", "QueryMethods", "_", "getDropPlaceholder", "source", "target", "pos", "nodesToDOM", "targetParent", "targetParentNodes", "dropAction", "getDOMInfo", "x", "y", "output", "placement", "error", "getOptions", "getNodes", "getSerializedNodes", "nodePairs", "getEvent", "eventType", "event", "contains", "isEmpty", "all", "first", "last", "from", "size", "at", "raw", "EventHelpers", "serialize", "JSON", "stringify", "reactElement", "element", "parseNodeFromJSX", "toArray", "isValidElement", "parseSerializedNode", "serializedNode", "toNode", "parseFreshNode", "DEPRECATED_ROOT_NODE", "extras", "getState", "CoreEventHandlers", "_EventHandlers", "_inherits", "EventHandlers", "_super", "_createSuper", "_classCallCheck", "apply", "_createClass", "el", "select", "hover", "drop", "create", "UserElement", "DerivedCoreEventHandlers", "_DerivedEventHandlers", "DerivedEventHandlers", "_super2", "documentDragoverEventHandler", "e", "preventDefault", "Positioner", "dragTarget", "currentDropTargetId", "currentDropTargetCanvasAncestorId", "currentTargetId", "currentTargetChildDimensions", "currentIndicator", "dragError", "draggedNodes", "getDraggedNodes", "validateDraggedNodes", "onScrollListener", "onScroll", "bind", "window", "addEventListener", "removeEventListener", "scrollBody", "_this", "domInfo", "BORDER_OFFSET", "bottom", "right", "newPosition", "_this2", "existingTargetChildDimensions", "dropTargetId", "_this3", "get<PERSON>anvas", "getCanvasAncestor", "isNearBorders", "getChildDimensions", "position", "isDiff", "sourceNode", "dropError", "currentNodeId", "createShadow", "shadowsToCreate", "_shadowsToCreate$0$ge", "getBoundingClientRect", "width", "height", "shadow", "cloneNode", "style", "concat", "pointerEvents", "classList", "add", "document", "body", "append<PERSON><PERSON><PERSON>", "dataTransfer", "setDragImage", "container", "clientX", "clientY", "DefaultEventHandlers", "_CoreEventHandlers", "_len", "_key", "_assertThisInitialized", "call", "clearEvents", "reflect", "unbindOnMouseDown", "addCraftEventListener", "stopPropagation", "newSelectedElementIds", "selectedElementIds", "isMultiSelectEnabled", "selectedId", "unbindOnClick", "isMultiSelect", "isNodeAlreadySelected", "currentSelectedElementIds", "splice", "indexOf", "unbindMouseover", "unbindMouseleave", "removeHoverOnMouseleave", "targetId", "unbindDragOver", "positioner", "indicator", "computeIndicator", "setIndicator", "unbindDragEnter", "setAttribute", "unbindDragStart", "selectedDOMs", "draggedElementShadow", "forceSingleDragShadow", "unbindDragEnd", "dropElement", "move", "userElement", "currentTarget", "isFunction", "onCreate", "removeAttribute", "onDropNode", "getIndicator", "<PERSON><PERSON><PERSON><PERSON>", "movePlaceholder", "canvasDOMInfo", "bestTargetDomInfo", "thickness", "t", "l", "w", "h", "padding", "margin", "isChromium", "isLinux", "RenderEditorIndicator", "indicatorOptions", "enabled", "enable", "disable", "RenderIndicator", "className", "backgroundColor", "success", "transition", "parentDom", "Events", "handlers", "editorInitialState", "Set", "onNodesChange", "onBeforeMoveEnd", "metaKey", "normalizeNodes", "ActionMethodsWithConfig", "methods", "addNodeTreeToParent", "parentId", "addNodeType", "iterateChildren", "childNodeId", "getParentAndValidate", "ERROR_NOPARENT", "deleteNode", "_toConsumableArray", "childId", "parent<PERSON><PERSON><PERSON><PERSON>", "linkedId", "find", "eventSet", "removeNodeFromEvents", "existingLinkedNode", "nodeToAdd", "delete", "ERROR_DELETE_TOP_LEVEL_NODE", "input", "dehydratedNodes", "parse", "newParentId", "newParent", "nodesArrToCleanup", "currentParentId", "Error", "currentParentNodes", "oldIndex", "reverse", "setOptions", "nodeIdSelector", "nodeIds", "_ref3", "_ref4", "_ref5", "selectNode", "_ref6", "Methods", "setState", "ignoreHistoryForActions", "normalizeHistory", "eventName", "useEditorStore", "patchListener", "useMethods", "ERROR_RESOLVER_NOT_AN_OBJECT", "optionsRef", "previousState", "actionPerformedWithPatches", "normalizer", "patches", "actionPerformed", "path", "isModifyingNodeData", "HISTORY_ACTIONS", "IGNORE", "THROTTLE", "params", "draft", "editorOptions", "subscribe", "getTestNode", "_parentNode$data", "restParentNode", "validParentNode", "cloneDeep", "expectEditorState", "lhs", "rhs", "nodesRhs", "restRhs", "_excluded2", "nodesLhs", "restLhs", "_excluded3", "expect", "toEqual", "nodesRhsSimplified", "_excluded4", "nodesLhsSimplified", "_excluded5", "createTestNodes", "iterateNodes", "testNode", "childTestNode", "childNode", "grandChildNodes", "grandChildLinkedNodes", "_getTestNode3", "createTestState"], "mappings": "ozCASO,MAAMA,EAAcC,EAAMC,cAA+B,MAMnDC,EAAe,EAC1BC,KACAC,WAAU,EACVC,cAGEL,EAACM,cAAAP,EAAYQ,UAASC,MAAO,CAAEL,KAAIC,YAChCC,4jJCjBA,MAAMI,GAAgBR,EAAiC,MCDvD,IAAMS,GAAsBT,EAAiC,MAEvDU,GAAkB,WAAH,OAASC,EAAWF,GAAoB,EC2B9D,SAAUG,GACdC,GAEA,IAAMC,EAAUJ,KACVK,EAAQJ,EAAWH,IACzBQ,EAAUD,EAAOE,GAEjB,IAAMC,EAAYC,EAAaJ,EAAOF,GAEhCO,EAAkBC,GACtB,WAAA,OAAMP,GAAWA,EAAQQ,0BACzB,CAACR,IAGHS,GAAU,WAGR,OAFAH,EAAgBI,WAET,WACLJ,EAAgBK,UAEpB,GAAG,CAACL,IAEJ,IAAMM,EAAaL,GACjB,WAAA,OAAMD,GAAmBO,EAAmBP,EAAgBM,cAC5D,CAACN,IAGH,OAAAQ,EAAAA,EAAA,CAAA,EACKV,GAAS,CAAA,EAAA,CACZQ,WAAAA,EACAG,YAAad,EACbA,MAAAA,GAEJ,yCCtDM,SAAUe,GAA0BC,GACxC,IAAMC,EAAUrB,EAAWb,GAC3BkB,EAAUgB,EAASC,GAEnB,IAAQ/B,EAAgB8B,EAAhB9B,GAAIC,EAAY6B,EAAZ7B,QAORS,EAAAA,IACF,SAACsB,GAAK,OAAKhC,GAAMgC,EAAMC,MAAMjC,IAAO6B,GAAWA,EAAQG,EAAMC,MAAMjC,OAL1DkC,IAATC,QAEYC,IAAZZ,WACGR,EAASqB,GAAAC,EAAAC,IAKRf,EAAaL,GACjB,WAAA,OACEM,EAAmB,CACjBe,QAAS,SAACC,GAAgB,OAAKL,EAAiBI,QAAQC,EAAKzC,EAAG,EAChE0C,KAAM,SAACD,GAAgB,OAAKL,EAAiBM,KAAKD,EAAKzC,EAAG,GAC1D,GACJ,CAACoC,EAAkBpC,IAGfmC,EAAUhB,GAAQ,WACtB,MAAO,CACLwB,QAAS,SAACC,EAASC,GACbA,EACFX,EAAcY,QAAQC,SAASF,GAAcF,QAAQ3C,EAAI4C,GAEzDV,EAAcS,QAAQ3C,EAAI4C,EAE7B,EACDI,UAAW,SAACJ,EAASC,GACfA,EACFX,EAAcY,QAAQC,SAASF,GAAcG,UAAUhD,EAAI4C,GAE3DV,EAAcc,UAAUhD,EAAI4C,EAE/B,EACDK,UAAW,SAACC,GAAa,OAAKhB,EAAce,UAAUjD,EAAIkD,EAAK,EAEnE,GAAG,CAAChB,EAAelC,IAEnB,OAAA0B,EAAAA,EAAA,CAAA,EACKV,GAAS,CAAA,EAAA,CACZhB,GAAAA,EACAC,QAAAA,EACAkD,gBAAiBrB,EACjBK,QAAAA,EACAX,WAAAA,GAEJ,gECvDM,SAAU4B,GAAkBvB,GAChC,IAOID,EAAAA,GAAgBC,GANlB7B,IAAAA,GACAC,IAAAA,QACAkC,IAAAA,QACAgB,IAAAA,cACA3B,IAAAA,WAIF,OAAAE,EAAAA,EAAA,CAAA,EAHcW,GAAAgB,EAAAd,KAIA,CAAA,EAAA,CACZJ,QAAAA,EACAnC,GAAAA,EACAC,QAAAA,EACA0C,QAAS,SACPC,EACAC,GAKA,OAHAS,EAAmB,sBAAuB,CACxCC,QAAS,gCAEJpB,EAAQQ,QAAQC,EAAIC,EAC5B,EACDM,cAAAA,EACA3B,WAAAA,GAEJ,CChCO,MAAMgC,GAAgB,EAAGC,aAC9B,MACEjC,YAAYgB,QAAEA,EAAOE,KAAEA,IACrBU,KAEJ,MAA8B,iBAAhBK,EAAOC,KACjBlB,EAAQE,EAAK7C,EAAM8D,aAAaF,KAChCA,CAAM,ECHCG,GAAgB,KAC3B,MAAMF,KAAEA,EAAIG,MAAEA,EAAK5B,MAAEA,EAAK6B,mBAAEA,GAAuBlC,IAChDmC,IAAU,CACTL,KAAMK,EAAKC,KAAKN,KAChBG,MAAOE,EAAKC,KAAKH,MACjB5B,MAAO8B,EAAKC,KAAK/B,MACjB6B,mBAAoBC,EAAKE,wBAI7B,OAAO9C,GAAQ,KACb,IAAIjB,EAAW2D,EAAM3D,SAEjB+B,GAASA,EAAMiC,OAAS,IAC1BhE,EACEL,EAACM,cAAAN,EAAMsE,SAAQ,KACZlC,EAAMmC,KAAKpE,GACVH,EAAAM,cAACkE,GAAW,CAACrE,GAAIA,EAAIsE,IAAKtE,QAMlC,MAAMyD,EAAS5D,EAAMM,cAAcuD,EAAMG,EAAO3D,GAEhD,MAAmB,iBAARwD,EACF7D,gBAAC2D,GAAa,CAACC,OAAQA,IAGzBA,CAAM,GAEZ,CAACC,EAAMG,EAAOC,EAAoB7B,GAAO,EC5BjCsC,GAAsB,EAAGd,aACpC,MAAMe,OAAEA,GAAW5C,IAAiBmC,IAAU,CAC5CS,OAAQT,EAAKC,KAAKQ,YAGdC,SAAEA,GAAa/D,IAAmBsB,IAAW,CACjDyC,SAAUzC,EAAM0C,QAAQD,aAI1B,OAAID,EACK,KAGF3E,EAAMM,cAAcsE,EAAU,CAAEhB,OAAQA,GAAU5D,EAACM,cAAAyD,GAAgB,OAAG,ECblES,GAAc,EAAGrE,KAAIyD,YAE9B5D,EAACM,cAAAJ,EAAa,CAAAC,GAAIA,GAChBH,EAACM,cAAAoE,IAAoBd,OAAQA,KCLtBkB,GAAsB,CACjCC,GAAI,MACJC,QAAQ,EACRC,OAAQ,CAAE,EACVN,QAAQ,GAGGO,GAAwB,CACnCH,GAAI,OACJC,OAAQ,YAYJ,SAAUG,IAAqChF,GACnDA,EAAEE,SACFA,KACG+E,IAEH,MAAML,GAAEA,GAAO,IACVD,MACAM,IAGCC,MAAEA,EAAK/C,QAAEA,GAAYzB,MACnBV,GAAImF,EAAMhC,cAAEA,GAAkBvB,MAE/BwD,GAAgBC,GAAwB,KAC7CvE,IAAYd,EAAIsF,GAChB,MAAMvB,EAAOmB,EAAMnB,KAAKoB,GAAQI,MAEhC,GAAIpC,EAAe,CACjB,MAAMqC,EAAezB,EAAKC,KAAKyB,YAAYzF,GACvCkF,EAAMnB,KAAKA,EAAKC,KAAKyB,YAAYzF,IAAKuF,MACtC,KAGJ,GAAIC,GAAgBA,EAAaxB,KAAKN,OAASkB,EAC7C,OAAOY,EAAaxF,GAItB,MAAM0F,EAAgB7F,EAAMM,cAC1B6E,GACAC,EACA/E,GAGIyF,EAAOT,EAAMU,kBAAkBF,GAAeG,aAGpD,OADA1D,EAAQW,QAAQgD,SAASC,sBAAsBJ,EAAMR,EAAQnF,GACtD2F,EAAKK,UACb,CACD,OAAO,IAAI,IAGb,OAAOZ,EAAevF,gBAACwE,GAAW,CAACrE,GAAIoF,IAAmB,IAC5D,CCnEa,MAAAa,GAA2B,IACtC3C,EAAmB,aAAc,CAC/BC,QAAS,uCAGG2C,WACXrC,IAIH,OAFAxC,GAAU,IAAM4E,MAA4B,IAErCpG,EAAAM,cAAC6E,GAAY,IAAAnB,EAAOgB,QAAQ,GACrC,CCLA,MAAMsB,GAAiB,KACrB,MAAMC,UAAEA,GAAc1F,IAAmBsB,IAAW,CAClDoE,UACEpE,EAAMC,MAAMoE,IAAcrE,EAAMC,MAAMoE,GAAWpC,wBAGrD,OAAKmC,EAIEvG,EAAAM,cAACkE,GAAW,CAACrE,GAAIqG,EAAW/B,IAAK8B,IAH/B,IAG4C,EAM1CE,GAAQ,EAAGpG,WAAUqG,OAAMvC,WACtC,MAAM7B,QAAEA,EAAO+C,MAAEA,GAAUxE,KAErB6F,GACJjD,EAAmB,uBAAwB,CACzCC,QAAS,yBAIb,MAAMiD,EAAWC,GAAO,GAExB,IAAKD,EAASE,QAAS,CACrB,MAAMC,EAAc3C,GAAQuC,EAE5B,GAAII,EACFxE,EAAQW,QAAQgD,SAASc,YAAYD,QAChC,GAAIzG,EAAU,CACnB,MAAM2G,EAAWhH,EAAMiH,SAASC,KAAK7G,GAE/B6D,EAAOmB,EAAMU,kBAAkBiB,GAAUhB,YAAW,CAAC9B,EAAMiD,KAC3DA,IAAQH,IACV9C,EAAK/D,GAAKqG,GAELtC,KAGT5B,EAAQW,QAAQgD,SAASmB,YAAYlD,EACtC,CAEDyC,EAASE,SAAU,CACpB,CAED,OAAO7G,EAAAM,cAACgG,GAAc,KAAG,MC4Cfe,IAAZ,SAAYA,GACVA,EAAAA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,GAAA,GAAA,KACAA,EAAAA,EAAA,IAAA,GAAA,KACD,CAJD,CAAYA,KAAAA,GAIX,CAAA,IC7FD,MAAMC,GAAoBhF,IACxB,MAAM4D,sBACJA,EAAqBqB,OACrBA,EAAMC,aACNA,EAAYC,aACZA,EAAYC,MACZA,KACGrF,GACDC,EAEJ,OAAOD,CAAa,EAuChB,SAAUsF,GAAa3F,GAC3B,MAAML,WACJA,EACAW,QAASsF,EAAevC,MACxBA,EAAKrE,MACLA,KACGG,GACDN,GAAkBmB,GAEhBK,EAAgBiF,GAAiBM,GAevC,MAAO,CACLjG,aACAW,QAfchB,GAAQ,KACf,IACFe,EACHY,QAAS,IACJZ,EAAcY,QACjBgD,OAAQ,IAAI4B,IACVP,GAAiBjF,EAAcY,QAAQgD,UAAU4B,IACnD3E,SAAU,IAAI2E,IACZP,GAAiBjF,EAAcY,QAAQC,YAAY2E,QAGxD,CAACxF,IAKFgD,QACArE,WACIG,EAER,CC3FM,SAAU2G,GAAiB9F,GAC/B,OAAQ+F,GACE/D,IACN,MAAMgE,EAAShG,EAAU2F,GAAU3F,GAAW2F,KAC9C,OAAO3H,gBAAC+H,EAAgB,IAAKC,KAAYhE,GAAS,CAGxD,CCPM,SAAUiE,GAAejG,GAC7B,OAAO,SAAU+F,GACf,OAAQ/D,IACN,MAAME,EAAOX,GAAQvB,GACrB,OAAOhC,gBAAC+H,EAAgB,IAAK7D,KAAUF,GAAS,CAEpD,CACF,CCZO,IAAMkE,GAAc,SAACC,GAC1B,OAAIC,OAAOF,YACFE,OAAOF,YAAYC,GAErBA,EAAME,QACX,SAACC,EAAKC,GAAA,IAAAC,EAAAC,GAAAF,EAAA,GAAGpI,EAAEqI,EAAA,GAAEhI,EAAKgI,EAAA,GAAA,OAAA3G,EAAAA,EAAA,CAAA,EACbyG,GAAK,GAAAI,GAAA,CAAA,EACPvI,EAAKK,GACN,GACF,CAAE,EAEN,ECLamI,GAAuB,SAClCvG,EACAwG,EACAC,GAEA,IAAMC,EAAQC,MAAMC,QAAQJ,GAAYA,EAAW,CAACA,GAE9CK,EAAYpH,EAAA,CAChBqH,WAAW,EACXC,QAAQ,GACJN,GAAU,CAAA,GAGVO,EAAgBN,EACnBO,QAAO,SAACC,GAAI,QAAOA,CAAI,IACvB/E,KAAI,SAAC+E,GACJ,MAAoB,iBAATA,EACF,CACLpF,KAAM9B,EAAMkH,GACZC,SAAUnH,EAAMkH,IAIA,WAAhBE,EAAOF,IAAsBL,EAAaE,OAQvC,CACLjF,KAAM,KACNqF,QAAQ,GARD,CACLrF,KAFWoF,EAGXC,SAAUnH,EAHCkH,EAGUnJ,IAQ3B,IASF,OAPI8I,EAAaC,WACfjI,EACkE,IAAhEmI,EAAcC,QAAO,SAACT,GAAQ,OAAMA,EAASW,MAAM,IAAElF,OACrDoF,GAIGL,CACT,iBCtCIM,GAAkD,KA+BzCC,GAAmB,SAC9BC,EACAC,GAEA,GAAoB,iBAATA,EACT,OAAOA,EAGT,IApBwBC,EAoBlBC,EAhB0B,SAChCH,EACAC,GAEA,IAAMG,EAzBoB,SAACJ,GAC3B,GAAIF,IAAwBA,GAAqBE,WAAaA,EAC5D,OAAOF,GAAqBO,SAG9BP,GAAuB,CACrBE,SAAAA,EACAK,SAAU,IAAIC,KAGhB,IAAA,IAAAC,EAAA,EAAAC,EAA2BhC,OAAOiC,QAAQT,GAAWO,EAAAC,EAAA/F,OAAA8F,IAAA,CAAhD,IAAAG,EAAA7B,GAAA2B,EAAAD,GAAA,GACHT,GAAqBO,SAASM,IADVD,EAAA,GAANA,EAAA,GAEhB,CAEA,OAAOZ,GAAqBO,QAC9B,CAUeO,CAAoBZ,GAAUlE,IAAImE,GAC/C,YAAgBY,IAATT,EAAqBA,EAAO,IACrC,CAUuBU,CAA0Bd,EAAUC,GAOzD,OALA5I,EACE8I,EACAY,EAAsBC,QAAQ,eAxBRd,EAwBwCD,GAvBtCG,MAASF,EAAkBe,cA0B9Cd,CACT,ECrDA,MAAMe,GAAa,CAACjH,EAAkC+F,IAChC,iBAAT/F,EACFA,EAEF,CAAEkG,aAAcJ,GAAiBC,EAAU/F,IAGvCkH,GAAgB,CAC3B5G,EACAyF,KAEA,IAAI/F,KAAEA,EAAImH,SAAEA,EAAQhH,MAAEA,GAAUG,EAuBhC,OAtBAH,EAAQoE,OAAO6C,KAAKjH,GAAOqE,QAAO,CAAC6C,EAA6BzG,KAC9D,MAAM0G,EAAOnH,EAAMS,GAEnB,OAAI0G,SAAuD,mBAATA,IAKhDD,EAAOzG,GADG,aAARA,GAAsC,iBAAT0G,EACjBlE,EAAS1C,IAAI4G,GAAOC,GACX,iBAAVA,EACFA,EAEFL,GAAcK,EAAOxB,KAEA,mBAAduB,EAAKtH,KACPkH,GAAcI,EAAMvB,GAEpBuB,GAbPD,CAeI,GACZ,CAAE,GAEE,CACLrH,KAAMiH,GAAWjH,EAAM+F,GACvBoB,WAAYA,EACZhH,QACD,EAGUqH,GAAgB,CAC3BlH,EACAyF,KAEA,MAAM/F,KAAEA,EAAIG,MAAEA,EAAKgH,SAAEA,EAAQhB,KAAEA,KAASsB,GAAanH,EAIrD,MAAO,IAFa4G,GAAc,CAAElH,OAAMmH,WAAUhH,SAAS4F,MAIxD0B,EACJ,ECvCa,SAAAC,GAAYpJ,EAAoBhC,GAC9Cc,EAAuB,iBAANd,EAAgBqL,GAEjC,IAAMtH,EAAO/B,EAAMC,MAAMjC,GAEnBsL,EAAc,SAACtL,GAAE,OAAKoL,GAAYpJ,EAAOhC,EAAG,EAElD,MAAO,CACL6K,SAAQ,WACN,QAAS9G,EAAKC,KAAK6G,QACpB,EACDU,OAAM,WACJ,OAAOxH,EAAK/D,KAAOqG,CACpB,EACDmF,aAAY,WACV,OACEzH,EAAKC,KAAKyH,QACVH,EAAYvH,EAAKC,KAAKyH,QAAQhG,cAAciG,SAAS3H,EAAK/D,GAE7D,EACD2L,eAAc,WACZ,OAAOC,KAAKL,UAAYK,KAAKJ,cAC9B,EACDK,YAAW,WACT,OAAQD,KAAKD,gBACd,EACDG,wBAAyB,WAAA,OACvB/H,EAAKC,KAAKyB,aAAewC,OAAO6C,KAAK/G,EAAKC,KAAKyB,aAAavB,OAAS,CAAC,EACxE6H,yBAAwB,WAItB,OAHAzI,EAAmB,0CAA2C,CAC5DC,QAAS,2CAEJqI,KAAKE,yBACb,EACDE,WAAU,WACR,OAAOhK,EAAMiK,OAAOC,SAASC,IAAInM,EAClC,EACDoM,UAAS,WACP,OAAOpK,EAAMiK,OAAOI,QAAQF,IAAInM,EACjC,EACDsM,UAAS,WACP,OAAOtK,EAAMiK,OAAOM,QAAQJ,IAAInM,EACjC,EACDuF,IAAG,WACD,OAAOxB,CACR,EACDyI,UAAsB,WAAA,IAAZC,0DAsBR,OArBA,SAASC,EACP1M,GAEiB,IADjBwM,yDAAsB,GACtBG,yDAAgB,EAEV5I,EAAO/B,EAAMC,MAAMjC,GACzB,OAAK+D,GAILyI,EAAUI,KAAK5M,GAEV+D,EAAKC,KAAKyH,SAIXgB,IAAUA,GAAkB,IAAVE,KACpBH,EAAYE,EAAiB3I,EAAKC,KAAKyH,OAAQe,EAAWG,EAAQ,IAE7DH,GANEA,GANAA,CAaX,CACOE,CAAiB3I,EAAKC,KAAKyH,OACnC,EACDoB,YAE4C,WAAA,IAD1CJ,0DACAK,EAA0CC,UAAA7I,OAAA,EAAA6I,UAAA,QAAAzC,EAqC1C,OAnCA,SAAS0C,EACPhN,GAEiB,IADjB6M,yDAAwB,GACxBF,yDAAgB,EAEhB,OAAIF,IAAUA,GAAkB,IAAVE,IACP3K,EAAMC,MAAMjC,IAML,eAAhB8M,GAEkBxB,EAAYtL,GAAIyF,cAExBwH,SAAQ,SAAC9H,GACnB0H,EAAYD,KAAKzH,GACjB0H,EAAcG,EAAgB7H,EAAQ0H,EAAaF,EAAQ,EAC7D,IAGkB,gBAAhBG,GACiBxB,EAAYtL,GAAIkN,aAExBD,SAAQ,SAAC9H,GAClB0H,EAAYD,KAAKzH,GACjB0H,EAAcG,EAAgB7H,EAAQ0H,EAAaF,EAAQ,EAC7D,IAGKE,GAEFA,CACT,CACOG,CAAgBhN,EACxB,EACDyF,YAAW,WACT,OAAOwC,OAAOkF,OAAOpJ,EAAKC,KAAKyB,aAAe,CAAA,EAC/C,EACDyH,WAAU,WACR,OAAOnJ,EAAKC,KAAK/B,OAAS,EAC3B,EACDmL,YAAW,SAACC,GACV,IACE,IAAMC,EAAavJ,EAUnB,OATAjD,GAAW8K,KAAKD,iBAAkB4B,GAClCzM,EACEsK,GAAYpJ,EAAOsL,EAAWtJ,KAAKyH,QAAQZ,WAC3C2C,GAEF1M,EACEwM,EAAWG,MAAMC,QAAQJ,EAAYhC,GACrCqC,IAEK,CAMT,CALE,MAAOC,GAIP,OAHIP,GACFA,EAAQO,IAEH,CACT,CACD,EACDC,YAAYpF,SAAAA,EAAwB4E,GAClC,IAAMS,EAAUtF,GAAqBxG,EAAMC,MAAOwG,GAE5CsF,EAAgBhK,EAEtB,IACEjD,EAAU8K,KAAKf,WAAYmD,GAC3BlN,EACEiN,EAAcN,MAAMQ,UAClBH,EAAQ1J,KAAI,SAACqE,GAAQ,OAAKA,EAAS1E,IAAI,IACvCgK,EACAzC,GAEF4C,GAGF,IAAMC,EAAc,CAAA,EA2DpB,OAzDAL,EAAQb,SAAQ,SAAiC7E,GAAA,IAAxBkF,IAANvJ,KAAkBqF,IAAAA,OAOnC,GANAtI,EACEwM,EAAWG,MAAMW,QAAQL,EAAeT,EAAYhC,GACpD+C,GAIGjF,EAAL,CAIAtI,GACGwK,EAAYgC,EAAWtN,IAAI2L,iBAC5B4B,GAGF,IAAMe,EAAkBhD,EAAYgC,EAAWtN,IAAI6M,aAAY,GAE/D/L,GACGwN,EAAgB5C,SAASqC,EAAc/N,KACtC+N,EAAc/N,KAAOsN,EAAWtN,GAClCuO,GAGF,IAAMC,EACJlB,EAAWtJ,KAAKyH,QAAUzJ,EAAMC,MAAMqL,EAAWtJ,KAAKyH,QAExD3K,EACE0N,EAAkBxK,KAAK6G,SACvB2C,GAGF1M,EACE0N,IACIA,IAAsBxM,EAAMC,MAAMqL,EAAWtN,IACjDyO,GAGED,EAAkBxO,KAAO+N,EAAc/N,KACpCmO,EAAYK,EAAkBxO,MACjCmO,EAAYK,EAAkBxO,IAAM,IAGtCmO,EAAYK,EAAkBxO,IAAI4M,KAAKU,GAlCzC,CAoCF,IAEArF,OAAO6C,KAAKqD,GAAalB,SAAQ,SAACyB,GAChC,IACMC,EAAa3M,EAAMC,MAAMyM,GAE/B5N,EACE6N,EAAWlB,MAAMmB,WAJAT,EAAYO,GAIWC,EAAYrD,GACpDuD,EAEJ,KAEO,CAMT,CALE,MAAOjB,GAIP,OAHIP,GACFA,EAAQO,IAEH,CACT,CACD,EACDkB,iBAAgB,WACd,OAAO5D,GAAcnH,EAAKC,KAAMhC,EAAM0C,QAAQ+E,SAC/C,EACD5D,WAAU,SAACiH,GACT,IAAM7K,EAAQ,CAACjC,aAAO4L,KAAKiB,aAAY,EAAMC,KAAc5E,QACzD,SAACC,EAAO4G,GAEN,OADA5G,EAAM4G,GAAgBzD,EAAYyD,GAAcxJ,MACzC4C,CACR,GACD,CAAE,GAGJ,MAAO,CACLnC,WAAYhG,EACZiC,MAAAA,EAEH,EAMD+M,WAAuB,WAAA,IAAZvC,0DAIT,OAHAnJ,EAAmB,4BAA6B,CAC9CC,QAAS,+BAEJqI,KAAKiB,YAAYJ,EACzB,EACDwC,iBAAgB,WACd,OAAQrD,KAAKL,WAAaxH,EAAKC,KAAKyH,MACtC,EAEJ,CC9Qc,SAAUyD,GACtBzD,EACA0D,EACAC,EACAC,GAiBA,IAfA,IAAItE,EAAuB,CACzBU,OAAAA,EACA6D,MAAO,EACPC,MAAO,UAGLC,EAAY,EACdC,EAAS,EAETC,EAAS,EACTC,EAAU,EACVC,EAAU,EACVC,EAAU,EAGHC,EAAI,EAAGC,EAAMZ,EAAKjL,OAAQ4L,EAAIC,EAAKD,IAAK,CAC/C,IAAME,EAAMb,EAAKW,GAWjB,GANAD,EAAUG,EAAIC,IAAMD,EAAIE,YAExBP,EAAUK,EAAIG,KAAOH,EAAII,WAAa,EAEtCR,EAAUI,EAAIC,IAAMD,EAAIE,YAAc,IAGnCT,GAAUO,EAAIG,KAAOV,GACrBC,GAAUE,GAAWF,GACrBF,GAXQQ,EAAIG,KAAOH,EAAII,WAWCZ,GAM3B,GAFAzE,EAAOuE,MAAQQ,EAEVE,EAAIK,OAUF,CAEL,GAAIhB,EAAOO,EAAS,CAClB7E,EAAOwE,MAAQ,SACf,KACF,CAAOxE,EAAOwE,MAAQ,OACxB,MAfMF,EAAOQ,IAASH,EAASG,GAEzBT,EAAOO,GACTF,EAASE,EACT5E,EAAOwE,MAAQ,WAEfC,EAAYG,EACZ5E,EAAOwE,MAAQ,QASrB,CAEA,OAAOxE,CACT,CCnDA,IAAMuF,GAAkB,SAAC5M,GAA+B,MACvC,iBAARA,EAAmBA,EAAOA,EAAKmG,IAAI,EAE5B,SAAA0G,GACdC,EACAC,GAEA,IAAIC,EAAaF,EAAQxM,KAAKN,KAGxBK,EAAa,CACjB/D,GAHOwQ,EAAQxQ,IAAM2Q,IAIrB1M,oBAAqB2M,KAAKC,MAC1B7M,KAAItC,EAAA,CACFgC,KAAMgN,EACN7G,KAAMyG,GAAgBI,GACtBhG,YAAa4F,GAAgBI,GAC7B7M,MAAO,CAAE,EACTiB,OAAQ,CAAE,EACV2G,OAAQ,KACRZ,UAAU,EACVrG,QAAQ,EACRvC,MAAO,GACPwD,YAAa,CAAE,GACZ+K,EAAQxM,MAEb8M,KAAM,CAAE,EACR7Q,QAAS,CAAE,EACXgM,OAAQ,CACNC,UAAU,EACVK,SAAS,EACTF,SAAS,GAEXoB,MAAO,CACLC,QAAS,WAAA,OAAM,CAAI,EACnBU,QAAS,WAAA,OAAM,CAAI,EACnBH,UAAW,WAAA,OAAM,CAAI,EACrBW,WAAY,WAAA,OAAM,CAAI,GAExBnM,IAAK,MAIP,GAAIsB,EAAKC,KAAKN,OAASsB,IAAWjB,EAAKC,KAAKN,OAASwC,OAAQ,CAC3D,IAAM6K,SACDpM,IACAZ,EAAKC,KAAKH,OAGfE,EAAKC,KAAKH,MAAQoE,OAAO6C,KAAK/G,EAAKC,KAAKH,OAAOqE,QAAO,SAACrE,EAAOS,GAU5D,OATI2D,OAAO6C,KAAKnG,IAAqB+G,SAASpH,GAG5CP,EAAKC,KAAKe,GAAsBT,IAAQA,GAAOyM,EAAYzM,GAG3DT,EAAMS,GAAOP,EAAKC,KAAKH,MAAMS,GAGxBT,CACR,GAAE,CAAE,GAGLE,EAAKC,KAAK6F,KAAOyG,GADjBI,EAAa3M,EAAKC,KAAKN,MAEvBK,EAAKC,KAAK0G,YAAc4F,GAAgBI,GAEV3M,EAAKC,KAAKN,OAASwC,SAE/CnC,EAAKC,KAAK6G,UAAW,EACrB5E,KAEJ,CAEIwK,GACFA,EAAU1M,GAIZ,IAAMiN,EAAsBN,EAAWO,MAEvC,GAAID,EAAqB,CA+BvB,GA9BAjN,EAAKC,KAAK0G,YACRsG,EAAoBtG,aACpBsG,EAAoBnH,MACpB9F,EAAKC,KAAK0G,YAEZ3G,EAAKC,KAAKH,aACJmN,EAAoBnN,OAASmN,EAAoBE,cAAgB,CAAE,GACpEnN,EAAKC,KAAKH,OAGfE,EAAKC,KAAKc,OAAMpD,EAAAA,EAAA,CAAA,EACVsP,EAAoBlM,QAAU,CAAA,GAC/Bf,EAAKC,KAAKc,QAIbkM,QAAoBnG,WAGpB9G,EAAKC,KAAK6G,SAAWmG,EAAoBnG,UAGvCmG,EAAoBvD,OACtBxF,OAAO6C,KAAKkG,EAAoBvD,OAAOR,SAAQ,SAAC3I,GAC1C,CAAC,UAAW,UAAW,YAAa,cAAcoH,SAASpH,KAC7DP,EAAK0J,MAAMnJ,GAAO0M,EAAoBvD,MAAMnJ,GAEhD,IAGE0M,EAAoB/Q,QAAS,CAC/B,IAAMkR,EAAqB,CACzBnR,GAAI+D,EAAK/D,GACTC,SAAS,GAGXgI,OAAO6C,KAAKkG,EAAoB/Q,SAASgN,SAAQ,SAACvD,GAChD3F,EAAK9D,QAAQyJ,GAAQ,SAAC7F,GAAK,OACzBhE,EAAMM,cACJJ,EACAoR,EACAtR,EAAMM,cAAc6Q,EAAoB/Q,QAAQyJ,GAAO7F,GACxD,CACL,GACF,CAEImN,EAAoBF,OACtB/M,EAAK+M,KAAOE,EAAoBF,KAEpC,CAEA,OAAO/M,CACT,CCjIA,MASaqN,GAAkB,CAC7BpN,EACAyF,EACA6F,KAEA,IAAI5L,KAAEA,EAAIG,MAAEA,GAAUG,EAEtB,MAAMqN,EAhBY,EAAC3N,EAAsB+F,IACzB,iBAAT/F,GAAqBA,EAAKkG,aACP,WAAtBlG,EAAKkG,aACH1D,OACAuD,EAAS/F,EAAKkG,cACA,iBAATlG,EACPA,EACA,KASS4N,CAAY5N,EAAM+F,GAE/B,IAAK4H,EACH,OAGFxN,EAAQoE,OAAO6C,KAAKjH,GAAOqE,QAAO,CAAC6C,EAA6BzG,KAC9D,MAAM0G,EAAOnH,EAAMS,GAenB,OAbEyG,EAAOzG,GADL0G,QACY,KACW,iBAATA,GAAqBA,EAAKpB,aAC5BwH,GAAgBpG,EAAMvB,GACnB,aAARnF,GAAsBsE,MAAMC,QAAQmC,GAC/BA,EAAK5G,KAAK6G,GACD,iBAAVA,EACFA,EAEFmG,GAAgBnG,EAAOxB,KAGlBuB,EAETD,CAAM,GACZ,CAAE,GAEDuE,IACFzL,EAAMS,IAAMgL,GAGd,MAAMtI,EAAM,IACPnH,EAAMM,cAAckR,EAAM,IACxBxN,KAIP,MAAO,IACFmD,EACH6C,KAAML,GAAiBC,EAAUzC,EAAItD,MACtC,EAGU6N,GAAkB,CAC7BvN,EACAyF,KAEA,MAAQ/F,KAAM8N,EAAM3N,MAAO4N,KAAUtG,GAAanH,EAOlDlD,OALqCwJ,IAATkH,GAAsC,iBAATA,QAE9ClH,IAATkH,QACqDlH,IAApDkH,EAAmC5H,aAIpC8H,EAA4CjH,QAC1C,gBACAzG,EAAK0G,aACLD,QAAQ,wBAAyBxC,OAAO6C,KAAKrB,GAAUkI,KAAK,QAGhE,MAAMjO,KAAEA,EAAImG,KAAEA,EAAIhG,MAAEA,GAAWuN,GAC7BpN,EACAyF,IAGIgC,OAAEA,EAAM3G,OAAEA,EAAM4F,YAAEA,EAAWG,SAAEA,EAAQ5I,MAAEA,EAAKuC,OAAEA,GAAW2G,EAIjE,MAAO,CACLzH,OACAmG,OACAa,YAAaA,GAAeb,EAC5BhG,QACAiB,OAAQA,GAAU,CAAE,EACpB+F,WAAYA,EACZrG,SAAUA,EACViH,SACAhG,YAXkB0F,EAAS1F,aAAe0F,EAASyG,cAWvB,CAAE,EAC9B3P,MAAOA,GAAS,GACjB,EC/GG4P,GAAa,CAAChL,EAAgBiL,KAClC,GAAIA,EAAc5N,OAAS,EACzB,MAAO,CAAE,CAAC2C,EAAS7G,IAAK6G,GAE1B,MAAM5E,EAAQ6P,EAAc1N,KAAI,EAAG4B,gBAAiBA,IAC9C+L,EAAmB,IAAKlL,EAAU7C,KAAM,IAAK6C,EAAS7C,KAAM/B,UAElE,OAAO6P,EAAc5J,QAAO,CAACC,EAAOxC,KAClC,MAAMqM,EAAcrM,EAAK1D,MAAM0D,EAAKK,YACpC,MAAO,IACFmC,KACAxC,EAAK1D,MAER,CAAC+P,EAAYhS,IAAK,IACbgS,EACHhO,KAAM,IACDgO,EAAYhO,KACfyH,OAAQ5E,EAAS7G,KAGtB,GAde,CAAE,CAAC6G,EAAS7G,IAAK+R,GAetB,EAGFE,GAAa,CACxBpL,EACAiL,KACc,CACd9L,WAAYa,EAAS7G,GACrBiC,MAAO4P,GAAWhL,EAAUiL,KCMxB,SAAUI,GAAalQ,GAC3B,MAAM0C,EAAU1C,GAASA,EAAM0C,QAEzByN,EAAkD,IACtDD,GAAalQ,GAEf,MAAO,CAMLoQ,mBAAoB,CAClBC,EACAC,EACAC,EACAC,EAA0C,CAACzO,GACzC/B,EAAMC,MAAM8B,EAAK/D,IAAIyC,QAEvB,MAAM6K,EAAatL,EAAMC,MAAMqQ,GAGzBG,EAFaN,IAAIpO,KAAKuJ,EAAWtN,IAAI6K,WAGvCyC,EACAtL,EAAMC,MAAMqL,EAAWtJ,KAAKyH,QAEhC,IAAKgH,EAAc,OAEnB,MAAMC,EAAoBD,EAAazO,KAAK/B,OAAS,GAiB/C0Q,EAAazD,GACjBuD,EAhB4BC,EAC1BA,EAAkBxK,QAAO,CAAC6C,EAAQ/K,KAChC,MAAMyC,EAAM+P,EAAWxQ,EAAMC,MAAMjC,IACnC,GAAIyC,EAAK,CACP,MAAMqO,EAAiB,CACrB9Q,QACG4S,EAAWnQ,IAGhBsI,EAAO6B,KAAKkE,EACb,CACD,OAAO/F,CAAM,GACZ,IACH,GAKFwH,EAAIM,EACJN,EAAIO,GAEAd,EACJU,EAAkBxO,QAClBlC,EAAMC,MAAMyQ,EAAkBC,EAAWrD,QAErCyD,EAAoB,CACxBC,UAAW,IACNL,EACHX,eAEFiB,MAAO,MAmBT,OAhBoBzK,GAAqBxG,EAAMC,MAAOoQ,GAE1CpF,SAAQ,EAAGlJ,OAAMqF,aAEvBA,GACF+I,IACGpO,KAAKA,EAAK/D,IACVoN,aAAaQ,GAASmF,EAAOE,MAAQrF,GACzC,IAIHuE,IACGpO,KAAK0O,EAAazS,IAClB6N,YAAYwE,GAASzE,GAASmF,EAAOE,MAAQrF,IAEzCmF,CAAM,EAMfG,WAAU,IACDxO,EAGTyO,SAAQ,IACCnR,EAAMC,MAOf8B,KAAK/D,GACIoL,GAAYpJ,EAAOhC,GAM5BoT,qBACE,MAAMC,EAAYpL,OAAO6C,KAAK9I,EAAMC,OAAOmC,KAAKpE,GAAe,CAC7DA,EACA4L,KAAK7H,KAAK/D,GAAI8O,sBAEhB,OAAO/G,GAAYsL,EACpB,EAEDC,SAASC,GCnJG,SAAavR,EAAoBuR,GAC/C,IAAMC,EAAQxR,EAAMiK,OAAOsH,GAC3B,MAAO,CACLE,SAAQ,SAACzT,GACP,OAAOwT,EAAMrH,IAAInM,EAClB,EACD0T,QAAO,WACL,OAA6B,IAAtB9H,KAAK+H,MAAMzP,MACnB,EACD0P,MAAK,WAEH,OADehI,KAAK+H,MACN,EACf,EACDE,KAAI,WACF,IAAM1G,EAASvB,KAAK+H,MACpB,OAAOxG,EAAOA,EAAOjJ,OAAS,EAC/B,EACDyP,IAAG,WACD,OAAO/K,MAAMkL,KAAKN,EACnB,EACDO,KAAI,WACF,OAAOnI,KAAK+H,MAAMzP,MACnB,EACD8P,GAAE,SAAClE,GACD,OAAOlE,KAAK+H,MAAM7D,EACnB,EACDmE,IAAG,WACD,OAAOT,CACT,EAEJ,CDsHaU,CAAalS,EAAOuR,GAM7BY,YACE,OAAOC,KAAKC,UAAUzI,KAAKwH,qBAC5B,EAEDxN,kBAAoB0O,IAA2C,CAC7DzO,WACE4K,GAEA,IAAI1M,EE9JI,SACdiD,EACAyJ,GAEA,IAAI8D,EAAUvN,EAUd,MARuB,iBAAZuN,IACTA,EAAU1U,EAAMM,cAAcgE,EAAU,CAAE,EAAEoQ,IAOvChE,GACL,CACEvM,KAAM,CACJN,KALW6Q,EAAQ7Q,KAMnBG,MAAO,IAAK0Q,EAAQ1Q,UAGvBE,IACK0M,GACFA,EAAU1M,EAAMwQ,EACjB,GAGP,CFmImBC,CAAiBF,GAAc,CAACvQ,EAAMiD,KAC/C,MAAM6C,EAAOL,GAAiBxH,EAAM0C,QAAQ+E,SAAU1F,EAAKC,KAAKN,MAEhEK,EAAKC,KAAK0G,YAAc3G,EAAKC,KAAK0G,aAAeb,EACjD9F,EAAKC,KAAK6F,KAAOA,EAEb4G,GACFA,EAAU1M,EAAMiD,EACjB,IAGC8K,EAA4B,GAahC,OAXIwC,EAAazQ,OAASyQ,EAAazQ,MAAM3D,WAC3C4R,EAAgBjS,EAAMiH,SAAS2N,QAC7BH,EAAazQ,MAAM3D,UACnBgI,QAAmB,CAACC,EAAO8C,KACvBpL,EAAM6U,eAAezJ,IACvB9C,EAAMyE,KAAKuF,IAAIvM,kBAAkBqF,GAAOpF,WAAW4K,IAE9CtI,IACN,KAGE8J,GAAWlO,EAAM+N,EACzB,IAGH6C,oBAAsBC,IAAoC,CACxDC,OAAOpE,GACL,MAAMzM,EAAOuN,GAAgBqD,EAAgB5S,EAAM0C,QAAQ+E,UAC3D3I,EAAUkD,EAAKN,KAAM8G,GAErB,MAAMxK,EAA0B,iBAAdyQ,GAA0BA,EAQ5C,OANIzQ,GACFsD,EAAmB,4CAA6C,CAC9DC,QAAS,gEAIN4O,IACJ2C,eAAe,IACV9U,EAAK,CAAEA,MAAO,GAClBgE,SAED6Q,QAAQ7U,GAAMyQ,EAClB,IAGHqE,eAAiB/Q,IAAqB,CACpC8Q,OAAOpE,GACEF,GAAWxM,GAAOA,IACnBA,EAAKC,KAAKyH,SAAWsJ,IACvBhR,EAAKC,KAAKyH,OAASpF,GAGrB,MAAMwD,EAAOL,GAAiBxH,EAAM0C,QAAQ+E,SAAU1F,EAAKC,KAAKN,MAChE5C,EAAmB,OAAT+I,EAAeW,GACzBzG,EAAKC,KAAK0G,YAAc3G,EAAKC,KAAK0G,aAAeb,EACjD9F,EAAKC,KAAK6F,KAAOA,EAEb4G,GACFA,EAAU1M,EACX,MAKPwM,WAAW+D,EAAkCU,GAC3C1R,EAAmB,oBAAoBgR,KAAiB,CACtD/Q,QAAS,2BAA2B+Q,oBAGtC,MAAM3O,EAAOiG,KAAKhG,kBAAkB0O,GAAczO,aAE5C9B,EAAO4B,EAAK1D,MAAM0D,EAAKK,YAE7B,OAAKgP,GAIDA,EAAOhV,KACT+D,EAAK/D,GAAKgV,EAAOhV,IAGfgV,EAAOhR,OACTD,EAAKC,KAAO,IACPD,EAAKC,QACLgR,EAAOhR,OAIPD,GAdEA,CAeV,EAEDkR,SAAQ,IACCjT,EAGb,CG/PA,IAAakT,GAA0B,SAAAC,GAAAC,GAAAF,EAAQG,GAAR,IAAAC,EAAAC,GAAAL,GAAA,SAAAA,IAAA,OAAAM,EAAA5J,KAAAsJ,GAAAI,EAAAG,MAAA7J,KAAAmB,UAAA,CAgBpC,OAhBoC2I,EAAAR,EAAA,CAAA,CAAA5Q,IAAA,WAAAjE,MAGrC,WACE,MAAO,CACLmC,QAAS,SAACmT,EAAiB3V,GAAiB,EAC5C4V,OAAQ,SAACD,EAAiB3V,GAAiB,EAC3C6V,MAAO,SAACF,EAAiB3V,GAAiB,EAC1C0C,KAAM,SAACiT,EAAiB3V,GAAiB,EACzC8V,KAAM,SAACH,EAAiB3V,GAAiB,EACzC+V,OAAQ,SACNJ,EACAK,EACAtR,GACI,EAEV,KAACwQ,CAAA,CAhBoC,GAmBjBe,GAEpB,SAAAC,GAAAd,GAAAa,EAAQE,GAAR,IAAAC,EAAAb,GAAAU,GAAA,SAAAA,IAAA,OAAAT,EAAA5J,KAAAqK,GAAAG,EAAAX,MAAA7J,KAAAmB,UAAA,CAAA,OAAA2I,EAAAO,EAAA,CAAA,GCZII,GAA+B,SAACC,GACpCA,EAAEC,gBACJ,EAKaC,GAAU,WAkBrB,SAAqB3V,EAAAA,EAA6B4V,GAAsBjB,EAAA5J,KAAA4K,GAAAjO,GAAAqD,KAAA,aAAA,GAAArD,GAAAqD,KAAA,kBAAA,GAAArD,GAAAqD,KAAA,2BAAA,GAAArD,GAAAqD,KAAA,yCAAA,GAAArD,GAAAqD,KAAA,mBAV3B,MAAIrD,GAAAqD,KAAA,uBAAA,GAAArD,GAAAqD,KAAA,oCAAA,GAAArD,GAAAqD,KAAA,iBAAA,GAAArD,GAAAqD,KAAA,oBAAA,GAAArD,GAAAqD,KAAA,wBAAA,GAU5BA,KAAK/K,MAALA,EAA6B+K,KAAU6K,WAAVA,EAChD7K,KAAK8K,oBAAsB,KAC3B9K,KAAK+K,kCAAoC,KAEzC/K,KAAKgL,gBAAkB,KACvBhL,KAAKiL,6BAA+B,KAEpCjL,KAAKkL,iBAAmB,KAExBlL,KAAKmL,UAAY,KACjBnL,KAAKoL,aAAepL,KAAKqL,kBAEzBrL,KAAKsL,uBAELtL,KAAKuL,iBAAmBvL,KAAKwL,SAASC,KAAKzL,MAC3C0L,OAAOC,iBAAiB,SAAU3L,KAAKuL,kBAAkB,GACzDG,OAAOC,iBAAiB,WAAYlB,IAA8B,EACpE,CAuOC,OAvOAX,EAAAc,EAAA,CAAA,CAAAlS,IAAA,UAAAjE,MAED,WACEiX,OAAOE,oBAAoB,SAAU5L,KAAKuL,kBAAkB,GAC5DG,OAAOE,oBAAoB,WAAYnB,IAA8B,EACvE,GAAC,CAAA/R,IAAA,WAAAjE,MAEO,SAASiW,GACf,IAAMmB,EAAanB,EAAEhE,OACfzL,EAAW+E,KAAK/K,MAAMqE,MAAMnB,KAAKsC,GAAWd,MAKhDkS,aAAsBzS,SACtB6B,GACAA,EAASpE,KACTgV,EAAWhE,SAAS5M,EAASpE,OAM/BmJ,KAAKiL,6BAA+B,KACtC,GAAC,CAAAvS,IAAA,kBAAAjE,MAEO,WACN,OACSmI,GACLoD,KAAK/K,MAAMqE,MAAMiO,WAFQ,QAAzBvH,KAAK6K,WAAW/S,KAGhBkI,KAAK6K,WAAW9Q,KAAK1D,MAAM2J,KAAK6K,WAAW9Q,KAAKK,YAMlD4F,KAAK6K,WAAWxU,MAEpB,GAEA,CAAAqC,IAAA,uBAAAjE,MACQ,WAAoB,IAAAqX,EAAA9L,KAEG,QAAzBA,KAAK6K,WAAW/S,MAIpBkI,KAAKoL,aAAa/J,SAAQ,SAAqB7E,KAAZgB,QAKjCsO,EAAK7W,MAAMqE,MAAMnB,OALUA,KAKA/D,IAAIoN,aAAY,SAACQ,GAC1C8J,EAAKX,UAAYnJ,CACnB,GACF,GACF,GAAC,CAAAtJ,IAAA,gBAAAjE,MAEO,SACNsX,EACA9E,EACAC,GAIA,OAFqC6E,EAA7B1H,IAGAuG,EAAWoB,cAAgB9E,GAHE6E,EAAxBE,OAIFrB,EAAWoB,cAAgB9E,GAJD6E,EAAhBxH,KAKZqG,EAAWoB,cAAgB/E,GALC8E,EAAVG,MAMjBtB,EAAWoB,cAAgB/E,CAMvC,GAAC,CAAAvO,IAAA,SAAAjE,MAEO,SAAO0X,GACb,OACEnM,KAAKkL,kBACLlL,KAAKkL,iBAAiB9D,UAAUvH,OAAOzL,KAAO+X,EAAYtM,OAAOzL,IACjE4L,KAAKkL,iBAAiB9D,UAAU1D,QAAUyI,EAAYzI,OACtD1D,KAAKkL,iBAAiB9D,UAAUzD,QAAUwI,EAAYxI,KAM1D,GAEA,CAAAjL,IAAA,qBAAAjE,MAGQ,SAAmB0N,GAAmB,IAAAiK,EAAApM,KAEtCqM,EAAgCrM,KAAKiL,6BAC3C,OACEjL,KAAKgL,kBAAoB7I,EAAc/N,IACvCiY,EAEOA,EAGFlK,EAAc/J,KAAK/B,MAAMiG,QAAO,SAAC6C,EAAQ/K,GAC9C,IAAMyC,EAAMuV,EAAKnX,MAAMqE,MAAMnB,KAAK/D,GAAIuF,MAAM9C,IAS5C,OAPIA,GACFsI,EAAO6B,KAAIlL,EAAA,CACT1B,GAAAA,GACG4S,EAAWnQ,KAIXsI,CACR,GAAE,GACL,GAEA,CAAAzG,IAAA,oBAAAjE,MASQ,SAAkB6X,GAAoB,IAAAC,EAAAvM,KAG5C,GACEsM,IAAiBtM,KAAK8K,qBACtB9K,KAAK+K,kCACL,CACA,IAAM5S,EAAO6H,KAAK/K,MAAMqE,MACrBnB,KAAK6H,KAAK+K,mCACVpR,MAEH,GAAIxB,EACF,OAAOA,CAEX,CAgBA,OAdkB,SAAZqU,EAAajT,GACjB,IAAMpB,EAAOoU,EAAKtX,MAAMqE,MAAMnB,KAAKoB,GAAQI,MAE3C,OAAIxB,GAAQA,EAAKC,KAAK6G,SACb9G,EAGJA,EAAKC,KAAKyH,OAIR2M,EAAUrU,EAAKC,KAAKyH,QAHlB,KAMJ2M,CAAUF,EACnB,GAEA,CAAA5T,IAAA,mBAAAjE,MAIA,SAAiB6X,EAAsBrF,EAAWC,GAChD,IAAI/E,EAAgBnC,KAAKyM,kBAAkBH,GAE3C,GAAKnK,IAILnC,KAAK8K,oBAAsBwB,EAC3BtM,KAAK+K,kCAAoC5I,EAAc/N,GAIrD+N,EAAc/J,KAAKyH,QACnBG,KAAK0M,cAAc1F,EAAW7E,EAActL,KAAMoQ,EAAGC,KAEpDlH,KAAK/K,MAAMqE,MAAMnB,KAAKgK,EAAc/N,IAAIwL,iBAEzCuC,EAAgBnC,KAAK/K,MAAMqE,MAAMnB,KAAKgK,EAAc/J,KAAKyH,QAAQlG,OAG9DwI,GAAL,CAIAnC,KAAKiL,6BAA+BjL,KAAK2M,mBAAmBxK,GAC5DnC,KAAKgL,gBAAkB7I,EAAc/N,GAErC,IAAMwY,EAAWtJ,GACfnB,EACAnC,KAAKiL,6BACLhE,EACAC,GAIF,GAAKlH,KAAK6M,OAAOD,GAAjB,CAIA,IAAIvF,EAAQrH,KAAKmL,UAGZ9D,GACHrH,KAAK/K,MAAMqE,MAAMnB,KAAKgK,EAAc/N,IAAI6N,YACtCjC,KAAKoL,aAAa5S,KAAI,SAACsU,GAAU,OAAKA,EAAW3U,SACjD,SAAC4U,GACC1F,EAAQ0F,CACV,IAIJ,IAAMC,EAAgB7K,EAAc/J,KAAK/B,MAAMuW,EAASlJ,OAClD0C,EACJ4G,GAAiBhN,KAAK/K,MAAMqE,MAAMnB,KAAK6U,GAAerT,MAUxD,OARAqG,KAAKkL,iBAAmB,CACtB9D,iBACKwF,GAAQ,CAAA,EAAA,CACXxG,YAAAA,IAEFiB,MAAAA,GAGKrH,KAAKkL,gBA1BZ,CAfA,CA0CF,GAAC,CAAAxS,IAAA,eAAAjE,MAED,WACE,OAAOuL,KAAKkL,gBACd,KAACN,CAAA,CA1QoB,MAAVA,mBACY,ICxBlB,IAAMqC,GAAe,SAC1BvC,EACAwC,GAGA,GAA+B,IAA3BA,EAAgB5U,gEAAmC,CACrD,IAAA6U,EAA0BD,EAAgB,GAAGE,wBAArCC,IAAAA,MAAOC,IAAAA,OACTC,EAASL,EAAgB,GAAGM,WAAU,GAa5C,OAXAD,EAAOE,MAAMb,SAAqB,WAClCW,EAAOE,MAAMlJ,KAAc,QAC3BgJ,EAAOE,MAAMpJ,IAAa,QAC1BkJ,EAAOE,MAAMJ,MAAK,GAAAK,OAAML,EAAS,MACjCE,EAAOE,MAAMH,OAAM,GAAAI,OAAMJ,EAAU,MACnCC,EAAOE,MAAME,cAAgB,OAC7BJ,EAAOK,UAAUC,IAAI,eAErBC,SAASC,KAAKC,YAAYT,GAC1B7C,EAAEuD,aAAaC,aAAaX,EAAQ,EAAG,GAEhCA,CACT,CAMA,IAAMY,EAAYL,SAASvZ,cAAc,OA0BzC,OAzBA4Z,EAAUV,MAAMb,SAAW,WAC3BuB,EAAUV,MAAMlJ,KAAO,QACvB4J,EAAUV,MAAMpJ,IAAa,QAC7B8J,EAAUV,MAAMJ,MAAQ,OACxBc,EAAUV,MAAMH,OAAS,OACzBa,EAAUV,MAAME,cAAgB,OAChCQ,EAAUP,UAAUC,IAAI,yBAExBX,EAAgB7L,SAAQ,SAACxK,GACvB,IAAqCA,EAAAA,EAAIuW,wBAAjCC,IAAAA,MAAOC,IAAAA,OAAQjJ,IAAAA,IAAKE,IAAAA,KACtBgJ,EAAS1W,EAAI2W,WAAU,GAE7BD,EAAOE,MAAMb,SAAqB,WAClCW,EAAOE,MAAMlJ,KAAI,GAAAmJ,OAAMnJ,EAAQ,MAC/BgJ,EAAOE,MAAMpJ,IAAG,GAAAqJ,OAAMrJ,EAAO,MAC7BkJ,EAAOE,MAAMJ,MAAK,GAAAK,OAAML,EAAS,MACjCE,EAAOE,MAAMH,OAAM,GAAAI,OAAMJ,EAAU,MACnCC,EAAOK,UAAUC,IAAI,eAErBM,EAAUH,YAAYT,EACxB,IAEAO,SAASC,KAAKC,YAAYG,GAC1BzD,EAAEuD,aAAaC,aAAaC,EAAWzD,EAAE0D,QAAS1D,EAAE2D,SAE7CF,CACT,ECtCaG,GAA6B,SAAAC,GAAA/E,GAAA8E,EAAQhF,IAAR,IAAAI,EAAAC,GAAA2E,GAAA,SAAAA,IAAA,IAAAxC,EAAAlC,EAAA5J,KAAAsO,GAAA,IAAA,IAAAE,EAAArN,UAAA7I,OAAAwD,EAAA,IAAAkB,MAAAwR,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAA3S,EAAA2S,GAAAtN,UAAAsN,GAaV,OAbU9R,GAAA+R,GAAA5C,EAAApC,EAAAiF,KAAA9E,MAAAH,EAAA,CAAA1J,MAAA0N,OAAA5R,KAAA,4BAAA,GAAAa,GAAA+R,GAAA5C,GAAA,kBAAA,GAAAnP,GAAA+R,GAAA5C,GAAA,aAYR,MAAInP,GAAA+R,GAAA5C,GAAA,4BACR,IAAEA,CAAA,CAgW7B,OAhW6BhC,EAAAwE,EAAA,CAAA,CAAA5V,IAAA,YAAAjE,MAE9B,WACEuL,KAAKlH,QAAQ7D,MAAMsB,QAAQqY,aAC7B,GAAC,CAAAlW,IAAA,WAAAjE,MAED,WAAQ,IAAA2X,EAAApM,KACA/K,EAAQ+K,KAAKlH,QAAQ7D,MAE3B,MAAO,CACL2B,QAAS,SAACmT,EAAiB3V,GAGzB,OAFAa,EAAMsB,QAAQiF,OAAOpH,EAAI2V,GAElBqC,EAAKyC,SAAQ,SAACjZ,GACnBA,EAAWoU,OAAOD,EAAI3V,GACtBwB,EAAWqU,MAAMF,EAAI3V,GACrBwB,EAAWsU,KAAKH,EAAI3V,EACtB,GACD,EACD4V,OAAQ,SAACD,EAAiB3V,GACxB,IAAM0a,EAAoB1C,EAAK2C,sBAC7BhF,EACA,aACA,SAACW,GACCA,EAAErF,MAAM2J,kBAER,IAAIC,EAAwB,GAE5B,GAAI7a,EAAI,CACN,IAAQkF,EAAUrE,EAAVqE,MACF4V,EAAqB5V,EAAMoO,SAAS,YAAYK,OAChCqE,EAAKtT,QAAQqW,qBAAqBzE,IASnCwE,EAAmBpP,SAAS1L,MAC/C6a,EAAwBC,EAAmB5R,QACzC,SAAC8R,GACC,IAAMnO,EAAc3H,EACjBnB,KAAKiX,GACLnO,aAAY,GACTL,EAAYtH,EAAMnB,KAAKiX,GAAYxO,WAAU,GAGnD,OAAIK,EAAYnB,SAAS1L,KAAOwM,EAAUd,SAAS1L,EAKrD,KAIC6a,EAAsBnP,SAAS1L,IAClC6a,EAAsBjO,KAAK5M,EAE/B,CAEAa,EAAMsB,QAAQkF,aAAa,WAAYwT,EACzC,IAGII,EAAgBjD,EAAK2C,sBAAsBhF,EAAI,SAAS,SAACW,GAC7DA,EAAErF,MAAM2J,kBAER,IACME,EADYja,EAAVqE,MACyBoO,SAAS,YAAYK,MAEhDuH,EAAgBlD,EAAKtT,QAAQqW,qBAAqBzE,GAClD6E,EAAwBnD,EAAKoD,0BAA0B1P,SAC3D1L,GAGE6a,EAA4BC,GAAAA,GAE5BI,GAAiBC,GACnBN,EAAsBQ,OAAOR,EAAsBS,QAAQtb,GAAK,GAChEa,EAAMsB,QAAQkF,aAAa,WAAYwT,KAC7BK,GAAiBJ,EAAmB5W,OAAS,GAEvDrD,EAAMsB,QAAQkF,aAAa,WAD3BwT,EAAwB,CAAC7a,IAI3BgY,EAAKoD,0BAA4BP,CACnC,IAEA,OAAO,WACLH,IACAO,IAEH,EACDpF,MAAO,SAACF,EAAiB3V,GACvB,IAAMub,EAAkBvD,EAAK2C,sBAC3BhF,EACA,aACA,SAACW,GACCA,EAAErF,MAAM2J,kBACR/Z,EAAMsB,QAAQkF,aAAa,UAAWrH,EACxC,IAGEwb,EAAwC,KAa5C,OAXIxD,EAAKtT,QAAQ+W,0BACfD,EAAmBxD,EAAK2C,sBACtBhF,EACA,cACA,SAACW,GACCA,EAAErF,MAAM2J,kBACR/Z,EAAMsB,QAAQkF,aAAa,UAAW,KACxC,KAIG,WACLkU,IAEKC,GAILA,IAEH,EACD1F,KAAM,SAACH,EAAiB+F,GACtB,IAAMC,EAAiB3D,EAAK2C,sBAC1BhF,EACA,YACA,SAACW,GAIC,GAHAA,EAAErF,MAAM2J,kBACRtE,EAAEC,iBAEGyB,EAAK4D,WAAV,CAIA,IAAMC,EAAY7D,EAAK4D,WAAWE,iBAChCJ,EACApF,EAAE0D,QACF1D,EAAE2D,SAGC4B,GAILhb,EAAMsB,QAAQ4Z,aAAaF,EAZ3B,CAaF,IAGIG,EAAkBhE,EAAK2C,sBAC3BhF,EACA,aACA,SAACW,GACCA,EAAErF,MAAM2J,kBACRtE,EAAEC,gBACJ,IAGF,OAAO,WACLyF,IACAL,IAEH,EACDjZ,KAAM,SAACiT,EAAiB3V,GACtB,IAAKa,EAAMqE,MAAMnB,KAAK/D,GAAIoN,cACxB,OAAO,WAAO,EAGhBuI,EAAGsG,aAAa,YAAa,QAE7B,IAAMC,EAAkBlE,EAAK2C,sBAC3BhF,EACA,aACA,SAACW,GACCA,EAAErF,MAAM2J,kBAER,IAAQ1V,EAAmBrE,EAAnBqE,MAAO/C,EAAYtB,EAAZsB,QAEX2Y,EAAqB5V,EAAMoO,SAAS,YAAYK,MAE9CuH,EAAgBlD,EAAKtT,QAAQqW,qBAAqBzE,GAC1B0B,EAAKoD,0BAA0B1P,SAC3D1L,KAKE8a,EADEI,EACuBJ,GAAAA,OAAAA,GAAAA,GAAoB9a,CAAAA,IAExB,CAACA,GAExBa,EAAMsB,QAAQkF,aAAa,WAAYyT,IAGzC3Y,EAAQkF,aAAa,UAAWyT,GAEhC,IAAMqB,EAAerB,EAAmB1W,KACtC,SAACpE,GAAE,OAAKkF,EAAMnB,KAAK/D,GAAIuF,MAAM9C,OAG/BuV,EAAKoE,qBAAuBvD,GAC1BvC,EACA6F,EACAjC,EAAqBmC,uBAGvBrE,EAAKvB,WAAa,CAChB/S,KAAM,WACNzB,MAAO6Y,GAGT9C,EAAK4D,WAAa,IAAIpF,GACpBwB,EAAKtT,QAAQ7D,MACbmX,EAAKvB,WAET,IAGI6F,EAAgBtE,EAAK2C,sBAAsBhF,EAAI,WAAW,SAACW,GAC/DA,EAAErF,MAAM2J,kBAER5C,EAAKuE,aAAY,SAAC9F,EAAYoF,GACJ,QAApBpF,EAAW/S,MAQf7C,EAAMsB,QAAQqa,KACZ/F,EAAWxU,MACX4Z,EAAU7I,UAAUvH,OAAOzL,GAL3B6b,EAAU7I,UAAU1D,OACW,UAA9BuM,EAAU7I,UAAUzD,MAAoB,EAAI,GAOjD,GACF,IAEA,OAAO,WACLoG,EAAGsG,aAAa,YAAa,SAC7BC,IACAI,IAEH,EACDvG,OAAQ,SACNJ,EACA8G,EACA/X,GAEAiR,EAAGsG,aAAa,YAAa,QAE7B,IAAMC,EAAkBlE,EAAK2C,sBAC3BhF,EACA,aACA,SAACW,GAEC,IAAI3Q,EACJ,GAFA2Q,EAAErF,MAAM2J,kBAEmB,mBAAhB6B,EAA4B,CACrC,IAAM1R,EAAS0R,IAEb9W,EADE9F,EAAM6U,eAAe3J,GAChBlK,EAAMqE,MAAMU,kBAAkBmF,GAAQlF,aAEtCkF,CAEX,MACEpF,EAAO9E,EAAMqE,MAAMU,kBAAkB6W,GAAa5W,aAIpDmS,EAAKoE,qBAAuBvD,GAC1BvC,EACA,CAHUA,EAAEoG,eAIZxC,EAAqBmC,uBAEvBrE,EAAKvB,WAAa,CAChB/S,KAAM,MACNiC,KAAAA,GAGFqS,EAAK4D,WAAa,IAAIpF,GACpBwB,EAAKtT,QAAQ7D,MACbmX,EAAKvB,WAET,IAGI6F,EAAgBtE,EAAK2C,sBAAsBhF,EAAI,WAAW,SAACW,GAC/DA,EAAErF,MAAM2J,kBACR5C,EAAKuE,aAAY,SAAC9F,EAAYoF,GACJ,aAApBpF,EAAW/S,OAOf7C,EAAMsB,QAAQ8E,YACZwP,EAAW9Q,KACXkW,EAAU7I,UAAUvH,OAAOzL,GAJ3B6b,EAAU7I,UAAU1D,OACW,UAA9BuM,EAAU7I,UAAUzD,MAAoB,EAAI,IAO3C7K,GAAWiY,EAAWjY,EAAQkY,WAChClY,EAAQkY,SAASnG,EAAW9Q,MAEhC,GACF,IAEA,OAAO,WACLgQ,EAAGkH,gBAAgB,aACnBX,IACAI,IAEJ,EAEJ,GAAC,CAAAhY,IAAA,cAAAjE,MAEO,SACNyc,GAEA,IAAMjc,EAAQ+K,KAAKlH,QAAQ7D,MAE3B,GAAK+K,KAAKgQ,WAAV,CAIA,IAAMQ,EAAuBxQ,KAAKwQ,qBAE5BP,EAAYjQ,KAAKgQ,WAAWmB,eAE9BnR,KAAK6K,YAAcoF,IAAcA,EAAU5I,OAC7C6J,EAAWlR,KAAK6K,WAAYoF,GAG1BO,IACFA,EAAqBzN,WAAWqO,YAAYZ,GAC5CxQ,KAAKwQ,qBAAuB,MAG9BxQ,KAAK6K,WAAa,KAElB5V,EAAMsB,QAAQ4Z,aAAa,MAC3Blb,EAAMsB,QAAQkF,aAAa,UAAW,MACtCuE,KAAKgQ,WAAWra,UAEhBqK,KAAKgQ,WAAa,IArBlB,CAsBF,KAAC1B,CAAA,CA7WuC,GChBlB,SAAA+C,GACtB1K,EACA2K,EACAC,GACqB,IAArBC,yDAAoB,EAEhBC,EAAI,EACNC,EAAI,EACJC,EAAI,EACJC,EAAI,EACJjO,EAAQgD,EAAIhD,MA8Bd,OA5Bc4N,IAID9M,QAMTkN,EAVUJ,EAUA/M,WACVoN,EAAIJ,EACJC,EAAc,WAAV9N,EAZM4N,EAYqBlN,IAZrBkN,EAYiCtF,OAC3CyF,EAbUH,EAaAhN,OARVoN,EAAIH,EACJI,EANUL,EAMAjN,YACVmN,EAPUF,EAOAlN,IACVqN,EAAc,WAAV/N,EARM4N,EAQqBhN,KARrBgN,EAQkChN,KARlCgN,EAQ+C/M,YAQvD8M,IACFG,EAAIH,EAAcjN,IAAMiN,EAAcO,QAAQxN,IAC9CqN,EAAIJ,EAAc/M,KAAO+M,EAAcO,QAAQtN,KAC/CoN,EACEL,EAAc9M,WACd8M,EAAcO,QAAQ3F,MACtBoF,EAAcO,QAAQtN,KACtB+M,EAAcQ,OAAOvN,KACrB+M,EAAcQ,OAAO5F,MACvB0F,EAAIJ,GAGD,CACLnN,IAAG,GAAAqJ,OAAK+D,EAAK,MACblN,KAAI,GAAAmJ,OAAKgE,EAAK,MACdrE,MAAK,GAAAK,OAAKiE,EAAK,MACfrE,iBAAWsE,EAAC,MAEhB,ID9BatD,GAQoByD,wBAAAA,KAAgBC,KElB1C,MAAMC,GAAwB,KACnC,MAAMhC,UAAEA,EAASiC,iBAAEA,EAAgBC,QAAEA,GAAYrd,IAC9CsB,IAAW,CACV6Z,UAAW7Z,EAAM6Z,UACjBiC,iBAAkB9b,EAAM0C,QAAQmX,UAChCkC,QAAS/b,EAAM0C,QAAQqZ,YAIrBnd,EAAUJ,KAehB,OAbAa,GAAU,KACHT,IAIAmd,EAKLnd,EAAQod,SAJNpd,EAAQqd,UAIM,GACf,CAACF,EAASnd,IAERib,EAIEhc,EAAMM,cAAc+d,EAAiB,CAC1CC,UAAWL,EAAiBK,UAC5B9E,MAAO,IACF4D,GACDpB,EAAU7I,UACVJ,EAAWiJ,EAAU7I,UAAUvH,OAAOhJ,KACtCoZ,EAAU7I,UAAUhB,aAClBY,EAAWiJ,EAAU7I,UAAUhB,YAAYvP,KAC7Cqb,EAAiBV,WAEnBgB,gBAAiBvC,EAAU5I,MACvB6K,EAAiB7K,MACjB6K,EAAiBO,QACrBC,WAAYR,EAAiBQ,YAAc,kBACvCR,EAAiBzE,OAAS,IAEhCkF,UAAW1C,EAAU7I,UAAUvH,OAAOhJ,MAnB/B,IAoBP,EC1CS+b,GAAS,EAAGte,eACvB,MAAMW,EAAQJ,EAAWH,IAEnBM,EAAUO,GAAQ,IAAMN,EAAMqE,MAAMgO,aAAauL,SAAS5d,IAAQ,CACtEA,IAGF,OAAKD,EAKHf,gBAACU,GAAoBH,SAAS,CAAAC,MAAOO,GACnCf,EAAAM,cAAC0d,GAAwB,MACxB3d,GANI,IAQP,ECfSwe,GAAkC,CAC7Czc,MAAO,CAAE,EACTgK,OAAQ,CACNM,QAAS,IAAIoS,IACbzS,SAAU,IAAIyS,IACdtS,QAAS,IAAIsS,KAEf9C,UAAW,KACXnX,QAAS,CACPka,cAAe,IAAM,KACrBna,SAAU,EAAGhB,YAAaA,EAC1Bob,gBAAiB,IAAM,KACvBpV,SAAU,CAAE,EACZsU,SAAS,EACTlC,UAAW,CACT5I,MAAO,MACPoL,QAAS,oBAEXI,SAAW5d,GACT,IAAIqZ,GAAqB,CACvBrZ,QACA4a,yBAAyB,EACzBV,qBAAuBzE,KAAoBA,EAAEwI,UAEjDC,eAAgB,SAIPC,GAA0B,CACrCC,QC4Z2B,SAC3Bjd,EACAkD,GAEA,OAAAxD,EAAAA,EAAA,GAxac,SACdM,EACAkD,GAGA,IAAMga,EAAsB,SAC1BvZ,EACAwZ,EACAC,GA6CA,GAnCwB,SAAlBC,EAAmBrf,EAAYmf,GACnC,IAAMpb,EAAO4B,EAAK1D,MAAMjC,GAEM,iBAAnB+D,EAAKC,KAAKN,MACnB5C,EACEkB,EAAM0C,QAAQ+E,SAAS1F,EAAKC,KAAK6F,MACjCW,EAAsBC,QACpB,cAAa,GAAA6O,OACTvV,EAAKC,KAAKN,KAAamG,QAKjC7H,EAAMC,MAAMjC,UACP+D,GAAI,GAAA,CACPC,KAAItC,EAAAA,EAAA,CAAA,EACCqC,EAAKC,MAAI,GAAA,CACZyH,OAAQ0T,MAIRpb,EAAKC,KAAK/B,MAAMiC,OAAS,WACpBlC,EAAMC,MAAMjC,GAAIgE,KAAKH,MAAM3D,SAClC6D,EAAKC,KAAK/B,MAAMgL,SAAQ,SAACqS,GAAW,OAClCD,EAAgBC,EAAavb,EAAK/D,QAItCiI,OAAOkF,OAAOpJ,EAAKC,KAAKyB,aAAawH,SAAQ,SAAC7H,GAAY,OACxDia,EAAgBja,EAAcrB,EAAK/D,OAIvCqf,CAAgB1Z,EAAKK,WAAYmZ,GAE5BA,GAAYxZ,EAAKK,aAAeK,EAArC,CAIA,IAAMoF,EAAS8T,EAAqBJ,GAEpC,GAAyB,UAArBC,EAAY1b,KAYhB+H,EAAOzH,KAAKyB,YAAY2Z,EAAYpf,IAAM2F,EAAKK,eAZ/C,CACE,IAAMsJ,EAAQ8P,EAAY9P,MAEb,MAATA,EACF7D,EAAOzH,KAAK/B,MAAMoZ,OAAO/L,EAAO,EAAG3J,EAAKK,YAExCyF,EAAOzH,KAAK/B,MAAM2K,KAAKjH,EAAKK,WAIhC,CAdA,GAmBIuZ,EAAuB,SAACJ,GAC5Bre,EAAUqe,EAAUK,GACpB,IAAM/T,EAASzJ,EAAMC,MAAMkd,GAE3B,OADAre,EAAU2K,EAAQnC,GACXmC,GAGHgU,EAAa,SAAbA,EAAczf,GAClB,IAAMsN,EAAatL,EAAMC,MAAMjC,GAC7B2O,EAAa3M,EAAMC,MAAMqL,EAAWtJ,KAAKyH,QAgB3C,GAdI6B,EAAWtJ,KAAK/B,OAGlByd,GAAIpS,EAAWtJ,KAAK/B,OAAOgL,SAAQ,SAAC0S,GAAO,OAAKF,EAAWE,MAGzDrS,EAAWtJ,KAAKyB,aAClBwC,OAAOkF,OAAOG,EAAWtJ,KAAKyB,aAAarB,KAAI,SAACgB,GAAY,OAC1Dqa,EAAWra,MAIKuJ,EAAW3K,KAAK/B,MAAMyJ,SAAS1L,GAElC,CACf,IAAM4f,EAAiBjR,EAAW3K,KAAK/B,MACvC2d,EAAevE,OAAOuE,EAAetE,QAAQtb,GAAK,EACpD,KAAO,CACL,IAAM6f,EAAW5X,OAAO6C,KAAK6D,EAAW3K,KAAKyB,aAAaqa,MACxD,SAAC9f,GAAE,OAAK2O,EAAW3K,KAAKyB,YAAYzF,KAAQA,KAE1C6f,UACKlR,EAAW3K,KAAKyB,YAAYoa,EAEvC,EC5IgC,SAAC7d,EAAoBmD,GACvD8C,OAAO6C,KAAK9I,EAAMiK,QAAQgB,SAAQ,SAAC3I,GACjC,IAAMyb,EAAW/d,EAAMiK,OAAO3H,GAC1Byb,GAAYA,EAAS5T,KAAO4T,EAAS5T,IAAIhH,KAC3CnD,EAAMiK,OAAO3H,GAAO,IAAIqa,IACtB/V,MAAMkL,KAAKiM,GAAU7W,QAAO,SAAClJ,GAAE,OAAKmF,IAAWnF,CAAE,KAGvD,GAAE,CDsIAggB,CAAqBhe,EAAOhC,UACrBgC,EAAMC,MAAMjC,IAGrB,MAAO,CAUL+F,+BAAsBJ,EAAgBwZ,EAAkBnf,GACtD,IAEMigB,EAFSV,EAAqBJ,GAEFnb,KAAKyB,YAAYzF,GAE/CigB,GACFR,EAAWQ,GAGbf,EAAoBvZ,EAAMwZ,EAAU,CAAEzb,KAAM,SAAU1D,GAAAA,GACvD,EASDyZ,aAAIyG,EAA0Bf,EAAmB7P,GAE/C,IAAIrN,EAAQ,CAACie,GACTtX,MAAMC,QAAQqX,KAChB5c,EAAmB,4BAA6B,CAC9CC,QAAS,4BAEXtB,EAAQie,GAEVje,EAAMgL,SAAQ,SAAClJ,GACbmb,EACE,CACEjd,YACG8B,EAAK/D,GAAK+D,GAEbiC,WAAYjC,EAAK/D,IAEnBmf,EACA,CAAEzb,KAAM,QAAS4L,MAAAA,GAErB,GACD,EASDrI,qBAAYtB,EAAgBwZ,EAAmB7P,GAC7C4P,EAAoBvZ,EAAMwZ,EAAU,CAAEzb,KAAM,QAAS4L,MAAAA,GACtD,EAMD6Q,OAAM,SAAC1X,GACWD,GAAqBxG,EAAMC,MAAOwG,EAAU,CAC1DM,WAAW,EACXC,QAAQ,IAGFiE,SAAQ,SAAa7E,GAAA,IAAVrE,IAAAA,KACjBjD,GACGoE,EAAMnB,KAAKA,EAAK/D,IAAI2L,iBACrByU,GAEFX,EAAW1b,EAAK/D,GAClB,GACD,EAED4G,YAAW,SAACyZ,GACV,IAAMC,EACY,iBAATD,EAAoBjM,KAAKmM,MAAMF,GAASA,EAE3ChN,EAAYpL,OAAO6C,KAAKwV,GAAiBlc,KAAI,SAACpE,GAClD,IAAImF,EAASnF,EAMb,OAJIA,IAAO+U,IACT5P,EAASkB,GAGJ,CACLlB,EACAD,EACGyP,oBAAoB2L,EAAgBtgB,IACpC6U,QAAO,SAAC9Q,GAAI,OAAMA,EAAK/D,GAAKmF,CAAO,IAE1C,IAEAyG,KAAKtE,aAAaS,GAAYsL,GAC/B,EAQDmJ,cAAK/T,EAAwB+X,EAAqBlR,GAChD,IAAMxB,EAAUtF,GAAqBxG,EAAMC,MAAOwG,EAAU,CAC1DM,WAAW,IAGP0X,EAAYze,EAAMC,MAAMue,GAExBE,EAAoB,IAAI/B,IAE9B7Q,EAAQb,SAAQ,SAAA5E,EAAuByH,GAAK,IAAnBxC,IAANvJ,KACX2X,EAAWpO,EAAWtN,GACtB2gB,EAAkBrT,EAAWtJ,KAAKyH,OAExCvG,EAAMnB,KAAKyc,GAAa3S,YAAY,CAAC6N,IAAW,SAAC9N,GAC/C,MAAM,IAAIgT,MAAMhT,EAClB,IAGA5L,EAAM0C,QAAQma,gBACZvR,EACAmT,EACAze,EAAMC,MAAM0e,IAGd,IACME,EADgB7e,EAAMC,MAAM0e,GACO3c,KAAK/B,MAE9Cye,EAAkBjH,IAAIoH,GAEtB,IAAMC,EAAWD,EAAmBvF,QAAQI,GAC5CmF,EAAmBC,GAAY,KAE/BL,EAAUzc,KAAK/B,MAAMoZ,OAAO/L,EAAQQ,EAAG,EAAG4L,GAE1C1Z,EAAMC,MAAMyZ,GAAU1X,KAAKyH,OAAS+U,CACtC,IAEAE,EAAkBzT,SAAQ,SAAChL,GACzB,IAAMiC,EAASjC,EAAMiC,OAErBwb,GAAIzd,GAAO8e,UAAU9T,SAAQ,SAAC5M,EAAOiP,GACrB,OAAVjP,GAIJ4B,EAAMoZ,OAAOnX,EAAS,EAAIoL,EAAO,EACnC,GACF,GACD,EAEDhI,aAAY,SAACrF,GACX2J,KAAK4O,cACLxY,EAAMC,MAAQA,CACf,EAEDuY,YAAW,WACT5O,KAAKvE,aAAa,WAAY,MAC9BuE,KAAKvE,aAAa,UAAW,MAC7BuE,KAAKvE,aAAa,UAAW,MAC7BuE,KAAKmQ,aAAa,KACnB,EAKDxU,MAAK,WACHqE,KAAK4O,cACL5O,KAAKtE,aAAa,CAAA,EACnB,EAOD0Z,WAAU,SAACpe,GACTA,EAAGZ,EAAM0C,QACV,EAED2C,aACEkM,SAAAA,EACA0N,GAUA,GARAjf,EAAMiK,OAAOsH,GAAWtG,SAAQ,SAACjN,GAC3BgC,EAAMC,MAAMjC,KACdgC,EAAMC,MAAMjC,GAAIiM,OAAOsH,IAAa,EAExC,IAEAvR,EAAMiK,OAAOsH,GAAa,IAAIoL,IAEzBsC,EAAL,CAIA,IAAMnT,EAAUtF,GAAqBxG,EAAMC,MAAOgf,EAAgB,CAChEjY,QAAQ,EACRD,WAAW,IAGPmY,EAAuB,IAAIvC,IAAI7Q,EAAQ1J,KAAI,SAAA+c,GAAO,SAAJpd,KAAgB/D,EAAE,KACtEkhB,EAAQjU,SAAQ,SAACjN,GACfgC,EAAMC,MAAMjC,GAAIiM,OAAOsH,IAAa,CACtC,IACAvR,EAAMiK,OAAOsH,GAAa2N,CAX1B,CAYD,EAODle,UACEyF,SAAAA,EACA7F,GAEgB4F,GAAqBxG,EAAMC,MAAOwG,EAAU,CAC1DO,QAAQ,EACRD,WAAW,IAGLkE,SAAQ,SAAAmU,GAAO,OAAOxe,EAAGZ,EAAMC,QAApB8B,KAA+B/D,IAAIgE,KAAKc,UAC5D,EAQDsC,OAAOpH,SAAAA,EAAYyC,GACZT,EAAMC,MAAMjC,KAIjBgC,EAAMC,MAAMjC,GAAIyC,IAAMA,EACvB,EAEDsZ,aAAY,SAACF,GAETA,KACEA,EAAU7I,UAAUvH,OAAOhJ,KAC1BoZ,EAAU7I,UAAUhB,cAClB6J,EAAU7I,UAAUhB,YAAYvP,OAGvCT,EAAM6Z,UAAYA,EACnB,EAOD5Y,UAAUjD,SAAAA,EAAYkD,GACpBlB,EAAMC,MAAMjC,GAAIgE,KAAKQ,OAAStB,CAC/B,EAODP,QACE8F,SAAAA,EACA7F,GAEgB4F,GAAqBxG,EAAMC,MAAOwG,EAAU,CAC1DO,QAAQ,EACRD,WAAW,IAGLkE,SAAQ,SAAAoU,GAAO,OAAOze,EAAGZ,EAAMC,QAApB8B,KAA+B/D,IAAIgE,KAAKH,SAC5D,EAEDyd,WAAU,SAACL,GACT,GAAIA,EAAgB,CAClB,IAAMnT,EAAUtF,GAAqBxG,EAAMC,MAAOgf,EAAgB,CAChEjY,QAAQ,EACRD,WAAW,IAGb6C,KAAKvE,aACH,WACAyG,EAAQ1J,KAAI,SAAAmd,GAAO,SAAJxd,KAAgB/D,EAAE,IAErC,MACE4L,KAAKvE,aAAa,WAAY,MAGhCuE,KAAKvE,aAAa,UAAW,KAC/B,EAEJ,CAOOma,CAAQxf,EAAOkD,IAAM,GAAA,CAGxBuc,SAAQ,SACN7e,GAKoBT,IAAAA,KAAYyJ,KAAIrJ,IAGpCK,EAAGZ,EAAOG,EACZ,GAEJ,ED/aEuf,wBAAyB,CACvB,SACA,eACA,aACA,cACA,aACA,gBAEFC,iBAAmB3f,IAIjBiG,OAAO6C,KAAK9I,EAAMiK,QAAQgB,SAAS2U,IACjBhZ,MAAMkL,KAAK9R,EAAMiK,OAAO2V,IAAc,IAE9C3U,SAASjN,IACVgC,EAAMC,MAAMjC,IACfgC,EAAMiK,OAAO2V,GAAWzB,OAAOngB,EAChC,GACD,IAMJiI,OAAO6C,KAAK9I,EAAMC,OAAOgL,SAASjN,IAChC,MAAM+D,EAAO/B,EAAMC,MAAMjC,GAEzBiI,OAAO6C,KAAK/G,EAAKkI,QAAQgB,SAAS2U,IACR7d,EAAKkI,OAAO2V,IAIlC5f,EAAMiK,OAAO2V,KACZ5f,EAAMiK,OAAO2V,GAAWzV,IAAIpI,EAAK/D,MAElC+D,EAAKkI,OAAO2V,IAAa,EAC1B,GACD,GACF,GASOC,GAAiB,CAC5Bnd,EACAod,IAOOC,EACL/C,GACA,IACKN,GACHha,QAAS,IACJga,GAAmBha,WACnBA,IAGPwN,GACA4P,GG5FSja,GAAS,EAAG3H,cAAawE,WAEX4F,IAArB5F,EAAQ+E,UACV3I,EAC8B,iBAArB4D,EAAQ+E,WACZb,MAAMC,QAAQnE,EAAQ+E,WACF,OAArB/E,EAAQ+E,SACVuY,GAIJ,MAAMC,EAAapiB,EAAM4G,OAAO/B,GAE1B5C,EAAU+f,GACdI,EAAWvb,SACX,CAAC1E,EAAOkgB,EAAeC,EAA4Bjd,EAAOkd,KACxD,IAAKD,EACH,OAGF,MAAME,QAAEA,KAAYC,GAAoBH,EAExC,IAAK,IAAIrS,EAAI,EAAGA,EAAIuS,EAAQne,OAAQ4L,IAAK,CACvC,MAAMyS,KAAEA,GAASF,EAAQvS,GACnB0S,EACJD,EAAKre,OAAS,GAAiB,UAAZqe,EAAK,IAA8B,SAAZA,EAAK,GAajD,GARE,CAACE,EAAgBC,OAAQD,EAAgBE,UAAUjX,SAHpC4W,EAAgB5e,OAM/B4e,EAAgBM,SAEhBN,EAAgB5e,KAAO4e,EAAgBM,OAAO,IAI9C,CAAC,WAAY,eAAelX,SAAS4W,EAAgB5e,OACrD8e,EACA,CACAJ,GAAYS,IACN7gB,EAAM0C,QAAQqa,gBAChB/c,EAAM0C,QAAQqa,eACZ8D,EACAX,EACAI,EACApd,EAEH,IAEH,KACD,CACF,KAiCL,OA5BArF,EAAMwB,WAAU,KACTS,QAKiBwI,IAApB5F,EAAQqZ,SACRjc,EAAQoD,MAAMgO,aAAa6K,UAAYrZ,EAAQqZ,SAKjDjc,EAAQK,QAAQ6e,YAAY8B,IAC1BA,EAAc/E,QAAUrZ,EAAQqZ,OAAO,GACvC,GACD,CAACjc,EAAS4C,EAAQqZ,UAErBle,EAAMwB,WAAU,KACdS,EAAQihB,WACL5Q,IAAO,CACN5L,KAAMzE,EAAQoD,MAAMiP,gBAEtB,KACErS,EAAQoD,MAAMgO,aAAa0L,cAAc9c,EAAQoD,MAAM,GAE1D,GACA,CAACpD,IAECA,EAKHjC,gBAACS,GAAcF,SAAS,CAAAC,MAAOyB,GAC7BjC,EAAAM,cAACqe,GAAQ,KAAAte,IALJ,IAOP,2HCzGE8iB,GAAc,SAACrU,GACnB,IACE1C,EAGE0C,EAHF1C,OAAMgX,EAGJtU,EAFF3K,KAAekJ,IAAPjL,MAAmBwD,IAAAA,YACxByd,EAAc7gB,GACfsM,EADepM,IAEb4gB,EAAkB5S,GAAW6S,EAAUzU,IAW7C,MAAO,CACL5K,KAXF4K,EAAUjN,EAAAA,EAAAA,EAAA,CAAA,EACLyhB,GACAD,GAAc,GAAA,CACjBjX,cACKkX,EAAgBlX,QAChBA,GAELxJ,IAAKkM,EAAWlM,KAAO0gB,EAAgB1gB,MAKvCyK,WAAAA,EACAzH,YAAAA,EAEJ,EAEa4d,GAAoB,SAACC,EAAKC,GACrC,IAAeC,EAAyBD,EAAhCthB,MAAoBwhB,KAAYF,EAAGG,IAC5BC,EAAyBL,EAAhCrhB,MAAoB2hB,KAAYN,EAAGO,IAC3CC,OAAOF,GAASG,QAAQN,GAExB,IAAMO,EAAqB/b,OAAO6C,KAAK0Y,GAAUtb,QAAO,SAACC,EAAOnI,GACdwjB,IAATzf,EAAI1B,GAAKmhB,EAASxjB,GAAdikB,IAE3C,OADA9b,EAAMnI,GAAM+D,EACLoE,CACR,GAAE,CAAE,GAEC+b,EAAqBjc,OAAO6C,KAAK6Y,GAAUzb,QAAO,SAACC,EAAOnI,GACd2jB,IAAT5f,EAAI1B,GAAKshB,EAAS3jB,GAAdmkB,IAE3C,OADAhc,EAAMnI,GAAM+D,EACLoE,CACR,GAAE,CAAE,GAEL2b,OAAOI,GAAoBH,QAAQC,EACrC,EAEaI,GAAkB,SAACvd,GAC9B,IAAM5E,EAAQ,CAAA,EAmDd,OAlDqB,SAAfoiB,EAAgBC,GACpB,IAAsDtB,EAAAA,GAAYsB,GAApD3V,IAAN5K,KAAkBmJ,IAAAA,WAAYzH,IAAAA,YACtCxD,EAAM0M,EAAW3O,IAAM2O,EAEnBzB,GACFA,EAAWD,SAAQ,SAACsX,EAAezU,GACjC,IAIIkT,EAAAA,GAAYuB,GAHRC,IAANzgB,KACY0gB,IAAZvX,WACawX,IAAbjf,YAEF+e,EAAUxgB,KAAKyH,OAASkD,EAAW3O,GACnCiC,EAAMuiB,EAAUxkB,IAAMwkB,EACtB7V,EAAW3K,KAAK/B,MAAM6N,GAAK0U,EAAUxkB,GACrCqkB,SACKG,GAAS,CAAA,EAAA,CACZxgB,KAAItC,EAAAA,EAAA,CAAA,EACC8iB,EAAUxgB,MAAI,GAAA,CACjB/B,MAAOwiB,GAAmB,GAC1Bhf,YAAaif,GAAyB,CAAE,MAG9C,IAGEjf,GACFwC,OAAO6C,KAAKrF,GAAawH,SAAQ,SAAC4S,GAChC,IAAA8E,EAII3B,GAAYvd,EAAYoa,IAHpB2E,IAANzgB,KACY0gB,IAAZvX,WACawX,IAAbjf,YAEFkJ,EAAW3K,KAAKyB,YAAYoa,GAAY2E,EAAUxkB,GAElDwkB,EAAUxgB,KAAKyH,OAASkD,EAAW3O,GACnCiC,EAAMuiB,EAAUxkB,IAAMwkB,EACtBH,SACKG,GAAS,CAAA,EAAA,CACZxgB,KAAItC,EAAAA,EAAA,CAAA,EACC8iB,EAAUxgB,MAAI,GAAA,CACjB/B,MAAOwiB,GAAmB,GAC1Bhf,YAAaif,GAAyB,CAAE,MAG9C,IAIJL,CAAaxd,GAEN5E,CACT,EAEa2iB,GAAkB,WAAsB,IAArB5iB,EAAQ+K,UAAA7I,OAAA,QAAAoG,IAAAyC,UAAA,GAAAA,UAAA,GAAA,CAAA,EACvBlG,EAAqB7E,EAA5BC,MAAiBgK,EAAWjK,EAAXiK,OAEzB,OACKyS,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,IACA1c,GAAK,GAAA,CACRC,MAAO4E,EAAWud,GAAgBvd,GAAY,CAAE,EAChDoF,OAAMvK,EAAAA,EAAA,CAAA,EACDgd,GAAmBzS,QAClBA,GAAU,KAGpB"}