# Simple Student Management System

## Overview
A streamlined student management system focused on:
- ✅ **Student CRUD operations** (Create, Read, Update, Delete)
- ✅ **Template creation and management**
- ✅ **Bulk ID card generation** (generate cards for ALL students with one click)
- ✅ **MySQL database** with environment-based configuration

## Quick Setup

### 1. Database Configuration

**Option A: Use SQLite (Default - No setup required)**
```bash
# Keep DB_TYPE=sqlite in backend/.env (already configured)
```

**Option B: Use MySQL**
```bash
# Update backend/.env:
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=student_management
DB_USER=root
DB_PASSWORD=your_password
```

### 2. Install Dependencies
```bash
# Backend
cd backend
npm install

# Frontend  
cd frontend
npm install
```

### 3. Setup Database
```bash
cd backend
npm run setup-db
```

### 4. Start Servers
```bash
# Terminal 1 - Backend
cd backend
node server.js

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### 5. Access Application
- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:5001

## Key Features

### 📊 Student Management
- **Add Students**: Complete form with photo upload
- **View Students**: Paginated list with search/filter
- **Edit Students**: Update any student information
- **Delete Students**: Remove students from system

### 🎨 Template System
- **Browse Templates**: Pre-made professional templates
- **Create Templates**: Simple template builder
- **Save Templates**: Store for reuse

### 🚀 Bulk Card Generation
- **One-Click Generation**: Generate cards for ALL students
- **Template Selection**: Choose any saved template
- **Background Processing**: Large batches processed automatically
- **Progress Tracking**: Monitor generation status

## Usage Workflow

### Adding Students
1. Go to **Students** page
2. Click **"Add Student"**
3. Fill form and upload photo
4. Save student

### Creating Templates
1. Go to **Template Gallery**
2. Browse existing templates OR
3. Go to **Templates** → **"Create Template"**
4. Design template and save

### Bulk Card Generation
1. Go to **Bulk Generator**
2. Select a template
3. Click **"Generate Cards for X Students"**
4. Monitor progress in jobs list

## Database Schema

### Students Table
```sql
- id (Primary Key)
- student_id (Unique)
- first_name, last_name
- email (Unique)
- phone, date_of_birth, address
- course, year_of_study
- photo_url, status
- created_at, updated_at
```

### Templates Table
```sql
- id (Primary Key)
- name
- template_data (JSON)
- is_default
- created_at, updated_at
```

### Bulk Jobs Table
```sql
- id (Primary Key)
- template_id
- total_students, completed_students
- status (pending/processing/completed)
- created_at, completed_at
```

## API Endpoints

### Students
- `GET /api/students` - List students
- `POST /api/students` - Create student
- `PUT /api/students/:id` - Update student
- `DELETE /api/students/:id` - Delete student

### Templates
- `GET /api/cards/templates` - List templates
- `POST /api/cards/templates` - Create template

### Bulk Generation
- `POST /api/cards/bulk-generate/:templateId` - Start bulk generation
- `GET /api/cards/bulk-jobs` - List generation jobs
- `GET /api/cards/bulk-jobs/:jobId` - Get job status

## Environment Variables

### Backend (.env)
```
PORT=5001
NODE_ENV=development

# Database (Choose one)
DB_TYPE=sqlite  # OR mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=student_management
DB_USER=root
DB_PASSWORD=your_password

FRONTEND_URL=http://localhost:5173
```

### Frontend (.env)
```
VITE_API_BASE_URL=http://localhost:5001/api
```

## File Structure
```
Student Management/
├── backend/
│   ├── config/
│   │   ├── database.js (SQLite)
│   │   └── mysqlDatabase.js (MySQL)
│   ├── controllers/
│   ├── routes/
│   ├── middleware/
│   └── server.js
├── frontend/
│   ├── src/
│   │   ├── pages/
│   │   │   ├── Students.jsx
│   │   │   ├── StudentForm.jsx
│   │   │   ├── Templates.jsx
│   │   │   └── BulkCardGenerator.jsx
│   │   └── components/
└── README.md
```

## Key Benefits

1. **Simple CRUD**: Easy student management
2. **Bulk Processing**: Generate hundreds of cards at once
3. **Template Reuse**: Create once, use many times
4. **Database Flexibility**: SQLite or MySQL
5. **Background Jobs**: No waiting for large batches
6. **Progress Tracking**: Monitor generation status

## Troubleshooting

### MySQL Connection Issues
1. Ensure MySQL server is running
2. Check credentials in `.env`
3. Create database: `CREATE DATABASE student_management;`

### File Upload Issues
1. Check `backend/uploads/` folder exists
2. Verify file permissions
3. Check file size limits in code

### Bulk Generation Issues
1. Check active students exist
2. Verify template is valid
3. Monitor backend logs for errors

This system is designed to be simple, efficient, and focused on the core requirements: student management and bulk ID card generation.
