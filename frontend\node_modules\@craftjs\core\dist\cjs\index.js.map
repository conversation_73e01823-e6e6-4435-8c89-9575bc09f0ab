{"version": 3, "file": "index.js", "sources": ["../../src/nodes/NodeContext.tsx", "../../src/editor/EditorContext.tsx", "../../src/events/EventContext.ts", "../../src/editor/useInternalEditor.ts", "../../src/nodes/useInternalNode.ts", "../../src/hooks/useNode.ts", "../../src/render/SimpleElement.tsx", "../../src/render/DefaultRender.tsx", "../../src/render/RenderNode.tsx", "../../src/nodes/NodeElement.tsx", "../../src/nodes/Element.tsx", "../../src/nodes/Canvas.tsx", "../../src/render/Frame.tsx", "../../src/interfaces/nodes.ts", "../../src/hooks/useEditor.tsx", "../../src/utils/fromEntries.ts", "../../src/utils/getNodesFromSelector.ts", "../../src/utils/resolveComponent.ts", "../../src/utils/serializeNode.tsx", "../../src/editor/NodeHelpers.ts", "../../src/events/findPosition.ts", "../../src/utils/createNode.ts", "../../src/utils/deserializeNode.tsx", "../../src/utils/mergeTrees.tsx", "../../src/editor/query.tsx", "../../src/editor/EventHelpers.ts", "../../src/utils/parseNodeFromJSX.tsx", "../../src/events/CoreEventHandlers.ts", "../../src/events/Positioner.ts", "../../src/events/createShadow.ts", "../../src/events/DefaultEventHandlers.ts", "../../src/events/movePlaceholder.ts", "../../src/events/RenderEditorIndicator.tsx", "../../src/events/Events.tsx", "../../src/editor/store.tsx", "../../src/editor/actions.ts", "../../src/utils/removeNodeFromEvents.ts", "../../src/utils/testHelpers.ts", "../../src/editor/Editor.tsx", "../../src/hooks/legacy/connectEditor.tsx", "../../src/hooks/legacy/connectNode.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { NodeId } from '../interfaces';\n\nexport type NodeContextType = {\n  id: NodeId;\n  related?: boolean;\n};\n\nexport const NodeContext = React.createContext<NodeContextType>(null);\n\nexport type NodeProviderProps = Omit<NodeContextType, 'connectors'> & {\n  children?: React.ReactNode;\n};\n\nexport const NodeProvider = ({\n  id,\n  related = false,\n  children,\n}: NodeProviderProps) => {\n  return (\n    <NodeContext.Provider value={{ id, related }}>\n      {children}\n    </NodeContext.Provider>\n  );\n};\n", "import { createContext } from 'react';\n\nimport { EditorStore } from './store';\n\nexport type EditorContextType = EditorStore;\nexport const EditorContext = createContext<EditorContextType>(null);\n", "import { createContext, useContext } from 'react';\n\nimport { CoreEventHandlers } from './CoreEventHandlers';\n\nexport const EventHandlerContext = createContext<CoreEventHandlers>(null);\n\nexport const useEventHandler = () => useContext(EventHandlerContext);\n", "import {\n  useCollector,\n  useCollectorReturnType,\n  QueryCallbacksFor,\n  wrapConnector<PERSON><PERSON>s,\n  EventHandlerConnectors,\n  ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT,\n} from '@craftjs/utils';\nimport { useContext, useEffect, useMemo } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EditorContext } from './EditorContext';\nimport { QueryMethods } from './query';\nimport { EditorStore } from './store';\n\nimport { CoreEventHandlers } from '../events/CoreEventHandlers';\nimport { useEventHandler } from '../events/EventContext';\nimport { EditorState } from '../interfaces';\n\nexport type EditorCollector<C> = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => C;\n\nexport type useInternalEditorReturnType<C = null> = useCollectorReturnType<\n  EditorStore,\n  C\n> & {\n  inContext: boolean;\n  store: EditorStore;\n  connectors: EventHandlerConnectors<CoreEventHandlers, React.ReactElement>;\n};\n\nexport function useInternalEditor<C>(\n  collector?: EditorCollector<C>\n): useInternalEditorReturnType<C> {\n  const handler = useEventHandler();\n  const store = useContext(EditorContext);\n  invariant(store, ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT);\n\n  const collected = useCollector(store, collector);\n\n  const connectorsUsage = useMemo(\n    () => handler && handler.createConnectorsUsage(),\n    [handler]\n  );\n\n  useEffect(() => {\n    connectorsUsage.register();\n\n    return () => {\n      connectorsUsage.cleanup();\n    };\n  }, [connectorsUsage]);\n\n  const connectors = useMemo(\n    () => connectorsUsage && wrapConnectorHooks(connectorsUsage.connectors),\n    [connectorsUsage]\n  );\n\n  return {\n    ...collected,\n    connectors,\n    inContext: !!store,\n    store,\n  };\n}\n", "import {\n  wrapConnectorHooks,\n  ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT,\n} from '@craftjs/utils';\nimport { useMemo, useContext } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { NodeContext } from './NodeContext';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { Node } from '../interfaces';\n\nexport function useInternalNode<S = null>(collect?: (node: Node) => S) {\n  const context = useContext(NodeContext);\n  invariant(context, ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT);\n\n  const { id, related } = context;\n\n  const {\n    actions: EditorActions,\n    query,\n    connectors: editorConnectors,\n    ...collected\n  } = useInternalEditor(\n    (state) => id && state.nodes[id] && collect && collect(state.nodes[id])\n  );\n\n  const connectors = useMemo(\n    () =>\n      wrapConnectorHooks({\n        connect: (dom: HTMLElement) => editorConnectors.connect(dom, id),\n        drag: (dom: HTMLElement) => editorConnectors.drag(dom, id),\n      }),\n    [editorConnectors, id]\n  );\n\n  const actions = useMemo(() => {\n    return {\n      setProp: (cb: any, throttleRate?: number) => {\n        if (throttleRate) {\n          EditorActions.history.throttle(throttleRate).setProp(id, cb);\n        } else {\n          EditorActions.setProp(id, cb);\n        }\n      },\n      setCustom: (cb: any, throttleRate?: number) => {\n        if (throttleRate) {\n          EditorActions.history.throttle(throttleRate).setCustom(id, cb);\n        } else {\n          EditorActions.setCustom(id, cb);\n        }\n      },\n      setHidden: (bool: boolean) => EditorActions.setHidden(id, bool),\n    };\n  }, [EditorActions, id]);\n\n  return {\n    ...collected,\n    id,\n    related,\n    inNodeContext: !!context,\n    actions,\n    connectors,\n  };\n}\n", "import { deprecationWarning } from '@craftjs/utils';\n\nimport { Node } from '../interfaces';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\n/**\n * A Hook to that provides methods and state information related to the corresponding Node that manages the current component.\n * @param collect - Collector function to consume values from the corresponding Node's state\n */\nexport function useNode<S = null>(collect?: (node: Node) => S) {\n  const {\n    id,\n    related,\n    actions,\n    inNodeContext,\n    connectors,\n    ...collected\n  } = useInternalNode(collect);\n\n  return {\n    ...collected,\n    actions,\n    id,\n    related,\n    setProp: (\n      cb: (props: Record<string, any>) => void,\n      throttleRate?: number\n    ) => {\n      deprecationWarning('useNode().setProp()', {\n        suggest: 'useNode().actions.setProp()',\n      });\n      return actions.setProp(cb, throttleRate);\n    },\n    inNodeContext,\n    connectors,\n  };\n}\n", "import React from 'react';\n\nimport { useNode } from '../hooks/useNode';\n\nexport const SimpleElement = ({ render }: any) => {\n  const {\n    connectors: { connect, drag },\n  } = useNode();\n\n  return typeof render.type === 'string'\n    ? connect(drag(React.cloneElement(render)))\n    : render;\n};\n", "import React, { useMemo } from 'react';\n\nimport { SimpleElement } from './SimpleElement';\n\nimport { NodeId } from '../interfaces';\nimport { NodeElement } from '../nodes/NodeElement';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\nexport const DefaultRender = () => {\n  const { type, props, nodes, hydrationTimestamp } = useInternalNode(\n    (node) => ({\n      type: node.data.type,\n      props: node.data.props,\n      nodes: node.data.nodes,\n      hydrationTimestamp: node._hydrationTimestamp,\n    })\n  );\n\n  return useMemo(() => {\n    let children = props.children;\n\n    if (nodes && nodes.length > 0) {\n      children = (\n        <React.Fragment>\n          {nodes.map((id: NodeId) => (\n            <NodeElement id={id} key={id} />\n          ))}\n        </React.Fragment>\n      );\n    }\n\n    const render = React.createElement(type, props, children);\n\n    if (typeof type == 'string') {\n      return <SimpleElement render={render} />;\n    }\n\n    return render;\n    // eslint-disable-next-line  react-hooks/exhaustive-deps\n  }, [type, props, hydrationTimestamp, nodes]);\n};\n", "import React from 'react';\n\nimport { DefaultRender } from './DefaultRender';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { useInternalNode } from '../nodes/useInternalNode';\n\ntype RenderNodeToElementProps = {\n  render?: React.ReactElement;\n  children?: React.ReactNode;\n};\nexport const RenderNodeToElement = ({ render }: RenderNodeToElementProps) => {\n  const { hidden } = useInternalNode((node) => ({\n    hidden: node.data.hidden,\n  }));\n\n  const { onRender } = useInternalEditor((state) => ({\n    onRender: state.options.onRender,\n  }));\n\n  // don't display the node since it's hidden\n  if (hidden) {\n    return null;\n  }\n\n  return React.createElement(onRender, { render: render || <DefaultRender /> });\n};\n", "import React from 'react';\n\nimport { NodeProvider } from './NodeContext';\n\nimport { NodeId } from '../interfaces';\nimport { RenderNodeToElement } from '../render/RenderNode';\n\nexport type NodeElementProps = {\n  id: NodeId;\n  render?: React.ReactElement;\n};\n\nexport const NodeElement = ({ id, render }: NodeElementProps) => {\n  return (\n    <NodeProvider id={id}>\n      <RenderNodeToElement render={render} />\n    </NodeProvider>\n  );\n};\n", "import { ERROR_TOP_LEVEL_ELEMENT_NO_ID } from '@craftjs/utils';\nimport React, { useState } from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { NodeElement } from './NodeElement';\nimport { useInternalNode } from './useInternalNode';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { NodeId } from '../interfaces';\n\nexport const defaultElementProps = {\n  is: 'div',\n  canvas: false,\n  custom: {},\n  hidden: false,\n};\n\nexport const elementPropToNodeData = {\n  is: 'type',\n  canvas: 'isCanvas',\n};\n\nexport type ElementProps<T extends React.ElementType> = {\n  id?: NodeId;\n  is?: T;\n  custom?: Record<string, any>;\n  children?: React.ReactNode;\n  canvas?: boolean;\n  hidden?: boolean;\n} & React.ComponentProps<T>;\n\nexport function Element<T extends React.ElementType>({\n  id,\n  children,\n  ...elementProps\n}: ElementProps<T>) {\n  const { is } = {\n    ...defaultElementProps,\n    ...elementProps,\n  };\n\n  const { query, actions } = useInternalEditor();\n  const { id: nodeId, inNodeContext } = useInternalNode();\n\n  const [linkedNodeId] = useState<NodeId | null>(() => {\n    invariant(!!id, ERROR_TOP_LEVEL_ELEMENT_NO_ID);\n    const node = query.node(nodeId).get();\n\n    if (inNodeContext) {\n      const existingNode = node.data.linkedNodes[id]\n        ? query.node(node.data.linkedNodes[id]).get()\n        : null;\n\n      // Render existing linked Node if it already exists (and is the same type as the JSX)\n      if (existingNode && existingNode.data.type === is) {\n        return existingNode.id;\n      }\n\n      // otherwise, create and render a new linked Node\n      const linkedElement = React.createElement(\n        Element,\n        elementProps,\n        children\n      );\n\n      const tree = query.parseReactElement(linkedElement).toNodeTree();\n\n      actions.history.ignore().addLinkedNodeFromTree(tree, nodeId, id);\n      return tree.rootNodeId;\n    }\n    return null;\n  });\n\n  return linkedNodeId ? <NodeElement id={linkedNodeId} /> : null;\n}\n", "import { deprecationWarning } from '@craftjs/utils';\nimport React, { useEffect } from 'react';\n\nimport { Element, ElementProps } from './Element';\n\nexport type CanvasProps<T extends React.ElementType> = ElementProps<T>;\n\nexport const deprecateCanvasComponent = () =>\n  deprecationWarning('<Canvas />', {\n    suggest: '<Element canvas={true} />',\n  });\n\nexport function Canvas<T extends React.ElementType>({\n  ...props\n}: CanvasProps<T>) {\n  useEffect(() => deprecateCanvasComponent(), []);\n\n  return <Element {...props} canvas={true} />;\n}\n", "import { deprecationWarning, ROOT_NODE } from '@craftjs/utils';\nimport React, { useRef } from 'react';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\nimport { SerializedNodes } from '../interfaces';\nimport { NodeElement } from '../nodes/NodeElement';\n\nexport type FrameProps = {\n  children?: React.ReactNode;\n  json?: string;\n  data?: string | SerializedNodes;\n};\n\nconst RenderRootNode = () => {\n  const { timestamp } = useInternalEditor((state) => ({\n    timestamp:\n      state.nodes[ROOT_NODE] && state.nodes[ROOT_NODE]._hydrationTimestamp,\n  }));\n\n  if (!timestamp) {\n    return null;\n  }\n\n  return <NodeElement id={ROOT_NODE} key={timestamp} />;\n};\n\n/**\n * A React Component that defines the editable area\n */\nexport const Frame = ({ children, json, data }: FrameProps) => {\n  const { actions, query } = useInternalEditor();\n\n  if (!!json) {\n    deprecationWarning('<Frame json={...} />', {\n      suggest: '<Frame data={...} />',\n    });\n  }\n\n  const isLoaded = useRef(false);\n\n  if (!isLoaded.current) {\n    const initialData = data || json;\n\n    if (initialData) {\n      actions.history.ignore().deserialize(initialData);\n    } else if (children) {\n      const rootNode = React.Children.only(children) as React.ReactElement;\n\n      const node = query.parseReactElement(rootNode).toNodeTree((node, jsx) => {\n        if (jsx === rootNode) {\n          node.id = ROOT_NODE;\n        }\n        return node;\n      });\n\n      actions.history.ignore().addNodeTree(node);\n    }\n\n    isLoaded.current = true;\n  }\n\n  return <RenderRootNode />;\n};\n", "import { QueryCallbacksFor } from '@craftjs/utils';\nimport React from 'react';\n\nimport { QueryMethods } from '../editor/query';\n\nexport type UserComponentConfig<T> = {\n  displayName: string;\n  rules: Partial<NodeRules>;\n  related: Partial<NodeRelated>;\n  props: Partial<T>;\n  custom: Record<string, any>;\n  info: Record<string, any>;\n  isCanvas: boolean;\n\n  // TODO: Deprecate\n  name: string;\n  defaultProps: Partial<T>;\n};\n\nexport type UserComponent<T = any> = React.ComponentType<T> & {\n  craft?: Partial<UserComponentConfig<T>>;\n};\n\nexport type NodeId = string;\nexport type NodeEventTypes = 'selected' | 'dragged' | 'hovered';\n\nexport type Node = {\n  id: NodeId;\n  data: NodeData;\n  info: Record<string, any>;\n  events: Record<NodeEventTypes, boolean>;\n  dom: HTMLElement | null;\n  related: Record<string, React.ElementType>;\n  rules: NodeRules;\n  _hydrationTimestamp: number;\n};\n\nexport type NodeHelpersType = QueryCallbacksFor<typeof QueryMethods>['node'];\nexport type NodeRules = {\n  canDrag(node: Node, helpers: NodeHelpersType): boolean;\n  canDrop(dropTarget: Node, self: Node, helpers: NodeHelpersType): boolean;\n  canMoveIn(canMoveIn: Node[], self: Node, helpers: NodeHelpersType): boolean;\n  canMoveOut(canMoveOut: Node[], self: Node, helpers: NodeHelpersType): boolean;\n};\nexport type NodeRelated = Record<string, React.ElementType>;\n\nexport type NodeData = {\n  props: Record<string, any>;\n  type: string | React.ElementType;\n  name: string;\n  displayName: string;\n  isCanvas: boolean;\n  parent: NodeId | null;\n  linkedNodes: Record<string, NodeId>;\n  nodes: NodeId[];\n  hidden: boolean;\n  custom?: any;\n  _childCanvas?: Record<string, NodeId>; // TODO: Deprecate in favour of linkedNodes\n};\n\nexport type FreshNode = {\n  id?: NodeId;\n  data: Partial<NodeData> & Required<Pick<NodeData, 'type'>>;\n};\n\nexport type ReduceCompType =\n  | string\n  | {\n      resolvedName: string;\n    };\n\nexport type ReducedComp = {\n  type: ReduceCompType;\n  isCanvas: boolean;\n  props: any;\n};\n\nexport type SerializedNode = Omit<\n  NodeData,\n  'type' | 'subtype' | 'name' | 'event'\n> &\n  ReducedComp;\n\nexport type SerializedNodes = Record<NodeId, SerializedNode>;\n\n// TODO: Deprecate in favor of SerializedNode\nexport type SerializedNodeData = SerializedNode;\n\nexport type Nodes = Record<NodeId, Node>;\n\n/**\n * A NodeTree is an internal data structure for CRUD operations that involve\n * more than a single node.\n *\n * For example, when we drop a component we use a tree because we\n * need to drop more than a single component.\n */\nexport interface NodeTree {\n  rootNodeId: NodeId;\n  nodes: Nodes;\n}\n\ntype NodeIdSelector = NodeId | NodeId[];\ntype NodeObjSelector = Node | Node[];\n\nexport enum NodeSelectorType {\n  Any,\n  Id,\n  Obj,\n}\n\nexport type NodeSelector<\n  T extends NodeSelectorType = NodeSelectorType.Any\n> = T extends NodeSelectorType.Id\n  ? NodeIdSelector\n  : T extends NodeSelectorType.Obj\n  ? NodeObjSelector\n  : NodeIdSelector | NodeObjSelector;\n\nexport type NodeSelectorWrapper = {\n  node: Node;\n  exists: boolean;\n};\n", "import { Overwrite, Delete, OverwriteFnReturnType } from '@craftjs/utils';\nimport { useMemo } from 'react';\n\nimport {\n  useInternalEditor,\n  EditorCollector,\n  useInternalEditorReturnType,\n} from '../editor/useInternalEditor';\n\ntype PrivateActions =\n  | 'addLinkedNodeFromTree'\n  | 'setNodeEvent'\n  | 'setDOM'\n  | 'replaceNodes'\n  | 'reset';\n\nconst getPublicActions = (actions) => {\n  const {\n    addLinkedNodeFromTree,\n    setDOM,\n    setNodeEvent,\n    replaceNodes,\n    reset,\n    ...EditorActions\n  } = actions;\n\n  return EditorActions;\n};\n\nexport type WithoutPrivateActions<S = null> = Delete<\n  useInternalEditorReturnType<S>['actions'],\n  PrivateActions | 'history'\n> & {\n  history: Overwrite<\n    useInternalEditorReturnType<S>['actions']['history'],\n    {\n      ignore: OverwriteFnReturnType<\n        useInternalEditorReturnType<S>['actions']['history']['ignore'],\n        PrivateActions\n      >;\n      throttle: OverwriteFnReturnType<\n        useInternalEditorReturnType<S>['actions']['history']['throttle'],\n        PrivateActions\n      >;\n    }\n  >;\n};\n\nexport type useEditorReturnType<S = null> = Overwrite<\n  useInternalEditorReturnType<S>,\n  {\n    actions: WithoutPrivateActions;\n    query: Delete<useInternalEditorReturnType<S>['query'], 'deserialize'>;\n  }\n>;\n\n/**\n * A Hook that that provides methods and information related to the entire editor state.\n * @param collector Collector function to consume values from the editor's state\n */\nexport function useEditor(): useEditorReturnType;\nexport function useEditor<S>(\n  collect: EditorCollector<S>\n): useEditorReturnType<S>;\n\nexport function useEditor<S>(collect?: any): useEditorReturnType<S> {\n  const {\n    connectors,\n    actions: internalActions,\n    query,\n    store,\n    ...collected\n  } = useInternalEditor(collect);\n\n  const EditorActions = getPublicActions(internalActions);\n\n  const actions = useMemo(() => {\n    return {\n      ...EditorActions,\n      history: {\n        ...EditorActions.history,\n        ignore: (...args) =>\n          getPublicActions(EditorActions.history.ignore(...args)),\n        throttle: (...args) =>\n          getPublicActions(EditorActions.history.throttle(...args)),\n      },\n    };\n  }, [EditorActions]);\n\n  return {\n    connectors,\n    actions,\n    query,\n    store,\n    ...(collected as any),\n  };\n}\n", "export const fromEntries = (pairs) => {\n  if (Object.fromEntries) {\n    return Object.fromEntries(pairs);\n  }\n  return pairs.reduce(\n    (accum, [id, value]) => ({\n      ...accum,\n      [id]: value,\n    }),\n    {}\n  );\n};\n", "import { ERROR_INVALID_NODEID } from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { Nodes, Node, NodeSelectorWrapper, NodeSelector } from '../interfaces';\n\ntype config = { existOnly: boolean; idOnly: boolean };\nexport const getNodesFromSelector = (\n  nodes: Nodes,\n  selector: NodeSelector,\n  config?: Partial<config>\n): NodeSelectorWrapper[] => {\n  const items = Array.isArray(selector) ? selector : [selector];\n\n  const mergedConfig = {\n    existOnly: false,\n    idOnly: false,\n    ...(config || {}),\n  };\n\n  const nodeSelectors = items\n    .filter((item) => !!item)\n    .map((item) => {\n      if (typeof item === 'string') {\n        return {\n          node: nodes[item],\n          exists: !!nodes[item],\n        };\n      }\n\n      if (typeof item === 'object' && !mergedConfig.idOnly) {\n        const node = item as Node;\n        return {\n          node,\n          exists: !!nodes[node.id],\n        };\n      }\n\n      return {\n        node: null,\n        exists: false,\n      };\n    });\n\n  if (mergedConfig.existOnly) {\n    invariant(\n      nodeSelectors.filter((selector) => !selector.exists).length === 0,\n      ERROR_INVALID_NODEID\n    );\n  }\n\n  return nodeSelectors;\n};\n", "import { ERROR_NOT_IN_RESOLVER } from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { Resolver } from '../interfaces';\n\ntype ReversedResolver = Map<React.ComponentType | string, string>;\n\ntype CachedResolverData = {\n  resolver: Resolver;\n  reversed: ReversedResolver;\n};\n\nlet CACHED_RESOLVER_DATA: CachedResolverData | null = null;\n\nconst getReversedResolver = (resolver: Resolver): ReversedResolver => {\n  if (CACHED_RESOLVER_DATA && CACHED_RESOLVER_DATA.resolver === resolver) {\n    return CACHED_RESOLVER_DATA.reversed;\n  }\n\n  CACHED_RESOLVER_DATA = {\n    resolver,\n    reversed: new Map(),\n  };\n\n  for (const [name, comp] of Object.entries(resolver)) {\n    CACHED_RESOLVER_DATA.reversed.set(comp, name);\n  }\n\n  return CACHED_RESOLVER_DATA.reversed;\n};\n\nconst getComponentName = (component: React.ElementType): string | undefined => {\n  return (component as any).name || (component as any).displayName;\n};\n\nconst searchComponentInResolver = (\n  resolver: Resolver,\n  comp: React.ElementType\n): string | null => {\n  const name = getReversedResolver(resolver).get(comp);\n  return name !== undefined ? name : null;\n};\n\nexport const resolveComponent = (\n  resolver: Resolver,\n  comp: React.ElementType | string\n): string => {\n  if (typeof comp === 'string') {\n    return comp;\n  }\n\n  const resolvedName = searchComponentInResolver(resolver, comp);\n\n  invariant(\n    resolvedName,\n    ERROR_NOT_IN_RESOLVER.replace('%node_type%', getComponentName(comp))\n  );\n\n  return resolvedName;\n};\n", "import React, { Children } from 'react';\n\nimport { resolveComponent } from './resolveComponent';\n\nimport { NodeData, ReducedComp, SerializedNode } from '../interfaces';\nimport { Resolver } from '../interfaces';\n\nconst reduceType = (type: React.ElementType | string, resolver: Resolver) => {\n  if (typeof type === 'string') {\n    return type;\n  }\n  return { resolvedName: resolveComponent(resolver, type) };\n};\n\nexport const serializeComp = (\n  data: Pick<NodeData, 'type' | 'isCanvas' | 'props'>,\n  resolver: Resolver\n): ReducedComp => {\n  let { type, isCanvas, props } = data;\n  props = Object.keys(props).reduce((result: Record<string, any>, key) => {\n    const prop = props[key];\n\n    if (prop === undefined || prop === null || typeof prop === 'function') {\n      return result;\n    }\n\n    if (key === 'children' && typeof prop !== 'string') {\n      result[key] = Children.map(prop, (child) => {\n        if (typeof child === 'string') {\n          return child;\n        }\n        return serializeComp(child, resolver);\n      });\n    } else if (typeof prop.type === 'function') {\n      result[key] = serializeComp(prop, resolver);\n    } else {\n      result[key] = prop;\n    }\n    return result;\n  }, {});\n\n  return {\n    type: reduceType(type, resolver),\n    isCanvas: !!isCanvas,\n    props,\n  };\n};\n\nexport const serializeNode = (\n  data: Omit<NodeData, 'event'>,\n  resolver: Resolver\n): SerializedNode => {\n  const { type, props, isCanvas, name, ...nodeData } = data;\n\n  const reducedComp = serializeComp({ type, isCanvas, props }, resolver);\n\n  return {\n    ...reducedComp,\n    ...nodeData,\n  };\n};\n", "import {\n  deprecationWarning,\n  ERROR_CANNOT_DRAG,\n  ERROR_DUPLICATE_NODEID,\n  ERROR_INVALID_NODE_ID,\n  ERROR_MOVE_INCOMING_PARENT,\n  ERROR_MOVE_NONCANVAS_CHILD,\n  ERROR_MOVE_OUTGOING_PARENT,\n  ERROR_MOVE_TO_DESCENDANT,\n  ERROR_MOVE_TO_NONCANVAS_PARENT,\n  ERROR_MOVE_TOP_LEVEL_NODE,\n  ERROR_MOVE_CANNOT_DROP,\n  ROOT_NODE,\n} from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { EditorState, NodeId, NodeSelector } from '../interfaces';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { serializeNode } from '../utils/serializeNode';\n\nexport function NodeHelpers(state: EditorState, id: NodeId) {\n  invariant(typeof id == 'string', ERROR_INVALID_NODE_ID);\n\n  const node = state.nodes[id];\n\n  const nodeHelpers = (id) => NodeHelpers(state, id);\n\n  return {\n    isCanvas() {\n      return !!node.data.isCanvas;\n    },\n    isRoot() {\n      return node.id === ROOT_NODE;\n    },\n    isLinkedNode() {\n      return (\n        node.data.parent &&\n        nodeHelpers(node.data.parent).linkedNodes().includes(node.id)\n      );\n    },\n    isTopLevelNode() {\n      return this.isRoot() || this.isLinkedNode();\n    },\n    isDeletable() {\n      return !this.isTopLevelNode();\n    },\n    isParentOfTopLevelNodes: () =>\n      node.data.linkedNodes && Object.keys(node.data.linkedNodes).length > 0,\n    isParentOfTopLevelCanvas() {\n      deprecationWarning('query.node(id).isParentOfTopLevelCanvas', {\n        suggest: 'query.node(id).isParentOfTopLevelNodes',\n      });\n      return this.isParentOfTopLevelNodes();\n    },\n    isSelected() {\n      return state.events.selected.has(id);\n    },\n    isHovered() {\n      return state.events.hovered.has(id);\n    },\n    isDragged() {\n      return state.events.dragged.has(id);\n    },\n    get() {\n      return node;\n    },\n    ancestors(deep = false): NodeId[] {\n      function appendParentNode(\n        id: NodeId,\n        ancestors: NodeId[] = [],\n        depth: number = 0\n      ) {\n        const node = state.nodes[id];\n        if (!node) {\n          return ancestors;\n        }\n\n        ancestors.push(id);\n\n        if (!node.data.parent) {\n          return ancestors;\n        }\n\n        if (deep || (!deep && depth === 0)) {\n          ancestors = appendParentNode(node.data.parent, ancestors, depth + 1);\n        }\n        return ancestors;\n      }\n      return appendParentNode(node.data.parent);\n    },\n    descendants(\n      deep = false,\n      includeOnly?: 'linkedNodes' | 'childNodes'\n    ): NodeId[] {\n      function appendChildNode(\n        id: NodeId,\n        descendants: NodeId[] = [],\n        depth: number = 0\n      ) {\n        if (deep || (!deep && depth === 0)) {\n          const node = state.nodes[id];\n\n          if (!node) {\n            return descendants;\n          }\n\n          if (includeOnly !== 'childNodes') {\n            // Include linkedNodes if any\n            const linkedNodes = nodeHelpers(id).linkedNodes();\n\n            linkedNodes.forEach((nodeId) => {\n              descendants.push(nodeId);\n              descendants = appendChildNode(nodeId, descendants, depth + 1);\n            });\n          }\n\n          if (includeOnly !== 'linkedNodes') {\n            const childNodes = nodeHelpers(id).childNodes();\n\n            childNodes.forEach((nodeId) => {\n              descendants.push(nodeId);\n              descendants = appendChildNode(nodeId, descendants, depth + 1);\n            });\n          }\n\n          return descendants;\n        }\n        return descendants;\n      }\n      return appendChildNode(id);\n    },\n    linkedNodes() {\n      return Object.values(node.data.linkedNodes || {});\n    },\n    childNodes() {\n      return node.data.nodes || [];\n    },\n    isDraggable(onError?: (err: string) => void) {\n      try {\n        const targetNode = node;\n        invariant(!this.isTopLevelNode(), ERROR_MOVE_TOP_LEVEL_NODE);\n        invariant(\n          NodeHelpers(state, targetNode.data.parent).isCanvas(),\n          ERROR_MOVE_NONCANVAS_CHILD\n        );\n        invariant(\n          targetNode.rules.canDrag(targetNode, nodeHelpers),\n          ERROR_CANNOT_DRAG\n        );\n        return true;\n      } catch (err) {\n        if (onError) {\n          onError(err);\n        }\n        return false;\n      }\n    },\n    isDroppable(selector: NodeSelector, onError?: (err: string) => void) {\n      const targets = getNodesFromSelector(state.nodes, selector);\n\n      const newParentNode = node;\n\n      try {\n        invariant(this.isCanvas(), ERROR_MOVE_TO_NONCANVAS_PARENT);\n        invariant(\n          newParentNode.rules.canMoveIn(\n            targets.map((selector) => selector.node),\n            newParentNode,\n            nodeHelpers\n          ),\n          ERROR_MOVE_INCOMING_PARENT\n        );\n\n        const parentNodes = {};\n\n        targets.forEach(({ node: targetNode, exists }) => {\n          invariant(\n            targetNode.rules.canDrop(newParentNode, targetNode, nodeHelpers),\n            ERROR_MOVE_CANNOT_DROP\n          );\n\n          // Ignore other checking if the Node is new\n          if (!exists) {\n            return;\n          }\n\n          invariant(\n            !nodeHelpers(targetNode.id).isTopLevelNode(),\n            ERROR_MOVE_TOP_LEVEL_NODE\n          );\n\n          const targetDeepNodes = nodeHelpers(targetNode.id).descendants(true);\n\n          invariant(\n            !targetDeepNodes.includes(newParentNode.id) &&\n              newParentNode.id !== targetNode.id,\n            ERROR_MOVE_TO_DESCENDANT\n          );\n\n          const currentParentNode =\n            targetNode.data.parent && state.nodes[targetNode.data.parent];\n\n          invariant(\n            currentParentNode.data.isCanvas,\n            ERROR_MOVE_NONCANVAS_CHILD\n          );\n\n          invariant(\n            currentParentNode ||\n              (!currentParentNode && !state.nodes[targetNode.id]),\n            ERROR_DUPLICATE_NODEID\n          );\n\n          if (currentParentNode.id !== newParentNode.id) {\n            if (!parentNodes[currentParentNode.id]) {\n              parentNodes[currentParentNode.id] = [];\n            }\n\n            parentNodes[currentParentNode.id].push(targetNode);\n          }\n        });\n\n        Object.keys(parentNodes).forEach((parentNodeId) => {\n          const childNodes = parentNodes[parentNodeId];\n          const parentNode = state.nodes[parentNodeId];\n\n          invariant(\n            parentNode.rules.canMoveOut(childNodes, parentNode, nodeHelpers),\n            ERROR_MOVE_OUTGOING_PARENT\n          );\n        });\n\n        return true;\n      } catch (err) {\n        if (onError) {\n          onError(err);\n        }\n        return false;\n      }\n    },\n    toSerializedNode() {\n      return serializeNode(node.data, state.options.resolver);\n    },\n    toNodeTree(includeOnly?: 'linkedNodes' | 'childNodes') {\n      const nodes = [id, ...this.descendants(true, includeOnly)].reduce(\n        (accum, descendantId) => {\n          accum[descendantId] = nodeHelpers(descendantId).get();\n          return accum;\n        },\n        {}\n      );\n\n      return {\n        rootNodeId: id,\n        nodes,\n      };\n    },\n\n    /**\n     Deprecated NodeHelpers\n     **/\n\n    decendants(deep = false) {\n      deprecationWarning('query.node(id).decendants', {\n        suggest: 'query.node(id).descendants',\n      });\n      return this.descendants(deep);\n    },\n    isTopLevelCanvas() {\n      return !this.isRoot() && !node.data.parent;\n    },\n  };\n}\n", "import { Node, NodeInfo, DropPosition } from '../interfaces';\n\nexport default function findPosition(\n  parent: Node,\n  dims: NodeInfo[],\n  posX: number,\n  posY: number\n) {\n  let result: DropPosition = {\n    parent,\n    index: 0,\n    where: 'before',\n  };\n\n  let leftLimit = 0,\n    xLimit = 0,\n    dimRight = 0,\n    yLimit = 0,\n    xCenter = 0,\n    yCenter = 0,\n    dimDown = 0;\n\n  // Each dim is: Top, Left, Height, Width\n  for (let i = 0, len = dims.length; i < len; i++) {\n    const dim = dims[i];\n\n    // Right position of the element. Left + Width\n    dimRight = dim.left + dim.outerWidth;\n    // Bottom position of the element. Top + Height\n    dimDown = dim.top + dim.outerHeight;\n    // X center position of the element. Left + (Width / 2)\n    xCenter = dim.left + dim.outerWidth / 2;\n    // Y center position of the element. Top + (Height / 2)\n    yCenter = dim.top + dim.outerHeight / 2;\n    // Skip if over the limits\n    if (\n      (xLimit && dim.left > xLimit) ||\n      (yLimit && yCenter >= yLimit) || // >= avoid issue with clearfixes\n      (leftLimit && dimRight < leftLimit)\n    )\n      continue;\n\n    result.index = i;\n    // If it's not in flow (like 'float' element)\n    if (!dim.inFlow) {\n      if (posY < dimDown) yLimit = dimDown;\n      //If x lefter than center\n      if (posX < xCenter) {\n        xLimit = xCenter;\n        result.where = 'before';\n      } else {\n        leftLimit = xCenter;\n        result.where = 'after';\n      }\n    } else {\n      // If y upper than center\n      if (posY < yCenter) {\n        result.where = 'before';\n        break;\n      } else result.where = 'after'; // After last element\n    }\n  }\n\n  return result;\n}\n", "import { getRandomId as getRandomNodeId } from '@craftjs/utils';\nimport React from 'react';\n\nimport { Node, FreshNode, UserComponentConfig } from '../interfaces';\nimport {\n  defaultElementProps,\n  Element,\n  Canvas,\n  elementPropToNodeData,\n  deprecateCanvasComponent,\n} from '../nodes';\nimport { NodeProvider } from '../nodes/NodeContext';\n\nconst getNodeTypeName = (type: string | { name: string }) =>\n  typeof type == 'string' ? type : type.name;\n\nexport function createNode(\n  newNode: FreshNode,\n  normalize?: (node: Node) => void\n) {\n  let actualType = newNode.data.type as any;\n  let id = newNode.id || getRandomNodeId();\n\n  const node: Node = {\n    id,\n    _hydrationTimestamp: Date.now(),\n    data: {\n      type: actualType,\n      name: getNodeTypeName(actualType),\n      displayName: getNodeTypeName(actualType),\n      props: {},\n      custom: {},\n      parent: null,\n      isCanvas: false,\n      hidden: false,\n      nodes: [],\n      linkedNodes: {},\n      ...newNode.data,\n    },\n    info: {},\n    related: {},\n    events: {\n      selected: false,\n      dragged: false,\n      hovered: false,\n    },\n    rules: {\n      canDrag: () => true,\n      canDrop: () => true,\n      canMoveIn: () => true,\n      canMoveOut: () => true,\n    },\n    dom: null,\n  };\n\n  // @ts-ignore\n  if (node.data.type === Element || node.data.type === Canvas) {\n    const mergedProps = {\n      ...defaultElementProps,\n      ...node.data.props,\n    };\n\n    node.data.props = Object.keys(node.data.props).reduce((props, key) => {\n      if (Object.keys(defaultElementProps).includes(key)) {\n        // If a <Element /> specific props is found (ie: \"is\", \"canvas\")\n        // Replace the node.data with the value specified in the prop\n        node.data[elementPropToNodeData[key] || key] = mergedProps[key];\n      } else {\n        // Otherwise include the props in the node as usual\n        props[key] = node.data.props[key];\n      }\n\n      return props;\n    }, {});\n\n    actualType = node.data.type;\n    node.data.name = getNodeTypeName(actualType);\n    node.data.displayName = getNodeTypeName(actualType);\n\n    const usingDeprecatedCanvas = node.data.type === Canvas;\n    if (usingDeprecatedCanvas) {\n      node.data.isCanvas = true;\n      deprecateCanvasComponent();\n    }\n  }\n\n  if (normalize) {\n    normalize(node);\n  }\n\n  // TODO: use UserComponentConfig type\n  const userComponentConfig = actualType.craft as UserComponentConfig<any>;\n\n  if (userComponentConfig) {\n    node.data.displayName =\n      userComponentConfig.displayName ||\n      userComponentConfig.name ||\n      node.data.displayName;\n\n    node.data.props = {\n      ...(userComponentConfig.props || userComponentConfig.defaultProps || {}),\n      ...node.data.props,\n    };\n\n    node.data.custom = {\n      ...(userComponentConfig.custom || {}),\n      ...node.data.custom,\n    };\n\n    if (\n      userComponentConfig.isCanvas !== undefined &&\n      userComponentConfig.isCanvas !== null\n    ) {\n      node.data.isCanvas = userComponentConfig.isCanvas;\n    }\n\n    if (userComponentConfig.rules) {\n      Object.keys(userComponentConfig.rules).forEach((key) => {\n        if (['canDrag', 'canDrop', 'canMoveIn', 'canMoveOut'].includes(key)) {\n          node.rules[key] = userComponentConfig.rules[key];\n        }\n      });\n    }\n\n    if (userComponentConfig.related) {\n      const relatedNodeContext = {\n        id: node.id,\n        related: true,\n      };\n\n      Object.keys(userComponentConfig.related).forEach((comp) => {\n        node.related[comp] = (props) =>\n          React.createElement(\n            NodeProvider,\n            relatedNodeContext,\n            React.createElement(userComponentConfig.related[comp], props)\n          );\n      });\n    }\n\n    if (userComponentConfig.info) {\n      node.info = userComponentConfig.info;\n    }\n  }\n\n  return node;\n}\n", "import { ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER } from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { resolveComponent } from './resolveComponent';\n\nimport {\n  NodeData,\n  SerializedNode,\n  ReducedComp,\n  ReduceCompType,\n} from '../interfaces';\nimport { Resolver } from '../interfaces';\nimport { Canvas } from '../nodes/Canvas';\n\ntype DeserialisedType = React.JSX.Element & { name: string };\n\nconst restoreType = (type: ReduceCompType, resolver: Resolver) =>\n  typeof type === 'object' && type.resolvedName\n    ? type.resolvedName === 'Canvas'\n      ? Canvas\n      : resolver[type.resolvedName]\n    : typeof type === 'string'\n    ? type\n    : null;\n\nexport const deserializeComp = (\n  data: ReducedComp,\n  resolver: Resolver,\n  index?: number\n): DeserialisedType | void => {\n  let { type, props } = data;\n\n  const main = restoreType(type, resolver);\n\n  if (!main) {\n    return;\n  }\n\n  props = Object.keys(props).reduce((result: Record<string, any>, key) => {\n    const prop = props[key];\n    if (prop === null || prop === undefined) {\n      result[key] = null;\n    } else if (typeof prop === 'object' && prop.resolvedName) {\n      result[key] = deserializeComp(prop, resolver);\n    } else if (key === 'children' && Array.isArray(prop)) {\n      result[key] = prop.map((child) => {\n        if (typeof child === 'string') {\n          return child;\n        }\n        return deserializeComp(child, resolver);\n      });\n    } else {\n      result[key] = prop;\n    }\n    return result;\n  }, {});\n\n  if (index) {\n    props.key = index;\n  }\n\n  const jsx = {\n    ...React.createElement(main, {\n      ...props,\n    }),\n  };\n\n  return {\n    ...jsx,\n    name: resolveComponent(resolver, jsx.type),\n  };\n};\n\nexport const deserializeNode = (\n  data: SerializedNode,\n  resolver: Resolver\n): Omit<NodeData, 'event'> => {\n  const { type: Comp, props: Props, ...nodeData } = data;\n\n  const isCompAnHtmlElement = Comp !== undefined && typeof Comp === 'string';\n  const isCompAUserComponent =\n    Comp !== undefined &&\n    (Comp as { resolvedName?: string }).resolvedName !== undefined;\n\n  invariant(\n    isCompAnHtmlElement || isCompAUserComponent,\n    ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER.replace(\n      '%displayName%',\n      data.displayName\n    ).replace('%availableComponents%', Object.keys(resolver).join(', '))\n  );\n\n  const { type, name, props } = (deserializeComp(\n    data,\n    resolver\n  ) as unknown) as NodeData;\n\n  const { parent, custom, displayName, isCanvas, nodes, hidden } = nodeData;\n\n  const linkedNodes = nodeData.linkedNodes || nodeData._childCanvas;\n\n  return {\n    type,\n    name,\n    displayName: displayName || name,\n    props,\n    custom: custom || {},\n    isCanvas: !!isCanvas,\n    hidden: !!hidden,\n    parent,\n    linkedNodes: linkedNodes || {},\n    nodes: nodes || [],\n  };\n};\n", "import { Node, NodeTree } from '../interfaces';\n\nconst mergeNodes = (rootNode: Node, childrenNodes: NodeTree[]) => {\n  if (childrenNodes.length < 1) {\n    return { [rootNode.id]: rootNode };\n  }\n  const nodes = childrenNodes.map(({ rootNodeId }) => rootNodeId);\n  const nodeWithChildren = { ...rootNode, data: { ...rootNode.data, nodes } };\n  const rootNodes = { [rootNode.id]: nodeWithChildren };\n  return childrenNodes.reduce((accum, tree) => {\n    const currentNode = tree.nodes[tree.rootNodeId];\n    return {\n      ...accum,\n      ...tree.nodes,\n      // set the parent id for the current node\n      [currentNode.id]: {\n        ...currentNode,\n        data: {\n          ...currentNode.data,\n          parent: rootNode.id,\n        },\n      },\n    };\n  }, rootNodes);\n};\n\nexport const mergeTrees = (\n  rootNode: Node,\n  childrenNodes: NodeTree[]\n): NodeTree => ({\n  rootNodeId: rootNode.id,\n  nodes: mergeNodes(rootNode, childrenNodes),\n});\n", "import {\n  QueryCallbacksFor,\n  ERROR_NOT_IN_RESOLVER,\n  getDOMInfo,\n  deprecationWarning,\n  DEPRECATED_ROOT_NODE,\n  ROOT_NODE,\n} from '@craftjs/utils';\nimport React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EventHelpers } from './EventHelpers';\nimport { NodeHelpers } from './NodeHelpers';\n\nimport findPosition from '../events/findPosition';\nimport {\n  NodeId,\n  EditorState,\n  Indicator,\n  Node,\n  Options,\n  NodeEventTypes,\n  NodeInfo,\n  NodeSelector,\n  NodeTree,\n  SerializedNodes,\n  SerializedNode,\n  FreshNode,\n} from '../interfaces';\nimport { createNode } from '../utils/createNode';\nimport { deserializeNode } from '../utils/deserializeNode';\nimport { fromEntries } from '../utils/fromEntries';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { mergeTrees } from '../utils/mergeTrees';\nimport { parseNodeFromJSX } from '../utils/parseNodeFromJSX';\nimport { resolveComponent } from '../utils/resolveComponent';\n\nexport function QueryMethods(state: EditorState) {\n  const options = state && state.options;\n\n  const _: () => QueryCallbacksFor<typeof QueryMethods> = () =>\n    QueryMethods(state) as any;\n\n  return {\n    /**\n     * Determine the best possible location to drop the source Node relative to the target Node\n     *\n     * TODO: replace with Positioner.computeIndicator();\n     */\n    getDropPlaceholder: (\n      source: NodeSelector,\n      target: NodeId,\n      pos: { x: number; y: number },\n      nodesToDOM: (node: Node) => HTMLElement = (node) =>\n        state.nodes[node.id].dom\n    ) => {\n      const targetNode = state.nodes[target],\n        isTargetCanvas = _().node(targetNode.id).isCanvas();\n\n      const targetParent = isTargetCanvas\n        ? targetNode\n        : state.nodes[targetNode.data.parent];\n\n      if (!targetParent) return;\n\n      const targetParentNodes = targetParent.data.nodes || [];\n\n      const dimensionsInContainer = targetParentNodes\n        ? targetParentNodes.reduce((result, id: NodeId) => {\n            const dom = nodesToDOM(state.nodes[id]);\n            if (dom) {\n              const info: NodeInfo = {\n                id,\n                ...getDOMInfo(dom),\n              };\n\n              result.push(info);\n            }\n            return result;\n          }, [] as NodeInfo[])\n        : [];\n\n      const dropAction = findPosition(\n        targetParent,\n        dimensionsInContainer,\n        pos.x,\n        pos.y\n      );\n      const currentNode =\n        targetParentNodes.length &&\n        state.nodes[targetParentNodes[dropAction.index]];\n\n      const output: Indicator = {\n        placement: {\n          ...dropAction,\n          currentNode,\n        },\n        error: null,\n      };\n\n      const sourceNodes = getNodesFromSelector(state.nodes, source);\n\n      sourceNodes.forEach(({ node, exists }) => {\n        // If source Node is already in the editor, check if it's draggable\n        if (exists) {\n          _()\n            .node(node.id)\n            .isDraggable((err) => (output.error = err));\n        }\n      });\n\n      // Check if source Node is droppable in target\n      _()\n        .node(targetParent.id)\n        .isDroppable(source, (err) => (output.error = err));\n\n      return output;\n    },\n\n    /**\n     * Get the current Editor options\n     */\n    getOptions(): Options {\n      return options;\n    },\n\n    getNodes() {\n      return state.nodes;\n    },\n\n    /**\n     * Helper methods to describe the specified Node\n     * @param id\n     */\n    node(id: NodeId) {\n      return NodeHelpers(state, id);\n    },\n\n    /**\n     * Returns all the `nodes` in a serialized format\n     */\n    getSerializedNodes(): SerializedNodes {\n      const nodePairs = Object.keys(state.nodes).map((id: NodeId) => [\n        id,\n        this.node(id).toSerializedNode(),\n      ]);\n      return fromEntries(nodePairs);\n    },\n\n    getEvent(eventType: NodeEventTypes) {\n      return EventHelpers(state, eventType);\n    },\n\n    /**\n     * Retrieve the JSON representation of the editor's Nodes\n     */\n    serialize(): string {\n      return JSON.stringify(this.getSerializedNodes());\n    },\n\n    parseReactElement: (reactElement: React.ReactElement<any>) => ({\n      toNodeTree(\n        normalize?: (node: Node, jsx: React.ReactElement<any>) => void\n      ): NodeTree {\n        let node = parseNodeFromJSX(reactElement, (node, jsx) => {\n          const name = resolveComponent(state.options.resolver, node.data.type);\n\n          node.data.displayName = node.data.displayName || name;\n          node.data.name = name;\n\n          if (normalize) {\n            normalize(node, jsx);\n          }\n        });\n\n        let childrenNodes: NodeTree[] = [];\n\n        if (reactElement.props && reactElement.props.children) {\n          childrenNodes = React.Children.toArray(\n            reactElement.props.children\n          ).reduce<NodeTree[]>((accum, child: any) => {\n            if (React.isValidElement(child)) {\n              accum.push(_().parseReactElement(child).toNodeTree(normalize));\n            }\n            return accum;\n          }, []);\n        }\n\n        return mergeTrees(node, childrenNodes);\n      },\n    }),\n\n    parseSerializedNode: (serializedNode: SerializedNode) => ({\n      toNode(normalize?: (node: Node) => void): Node {\n        const data = deserializeNode(serializedNode, state.options.resolver);\n        invariant(data.type, ERROR_NOT_IN_RESOLVER);\n\n        const id = typeof normalize === 'string' && normalize;\n\n        if (id) {\n          deprecationWarning(`query.parseSerializedNode(...).toNode(id)`, {\n            suggest: `query.parseSerializedNode(...).toNode(node => node.id = id)`,\n          });\n        }\n\n        return _()\n          .parseFreshNode({\n            ...(id ? { id } : {}),\n            data,\n          })\n          .toNode(!id && normalize);\n      },\n    }),\n\n    parseFreshNode: (node: FreshNode) => ({\n      toNode(normalize?: (node: Node) => void): Node {\n        return createNode(node, (node) => {\n          if (node.data.parent === DEPRECATED_ROOT_NODE) {\n            node.data.parent = ROOT_NODE;\n          }\n\n          const name = resolveComponent(state.options.resolver, node.data.type);\n          invariant(name !== null, ERROR_NOT_IN_RESOLVER);\n          node.data.displayName = node.data.displayName || name;\n          node.data.name = name;\n\n          if (normalize) {\n            normalize(node);\n          }\n        });\n      },\n    }),\n\n    createNode(reactElement: React.ReactElement, extras?: any) {\n      deprecationWarning(`query.createNode(${reactElement})`, {\n        suggest: `query.parseReactElement(${reactElement}).toNodeTree()`,\n      });\n\n      const tree = this.parseReactElement(reactElement).toNodeTree();\n\n      const node = tree.nodes[tree.rootNodeId];\n\n      if (!extras) {\n        return node;\n      }\n\n      if (extras.id) {\n        node.id = extras.id;\n      }\n\n      if (extras.data) {\n        node.data = {\n          ...node.data,\n          ...extras.data,\n        };\n      }\n\n      return node;\n    },\n\n    getState() {\n      return state;\n    },\n  };\n}\n", "import { EditorState, NodeId, NodeEventTypes } from '../interfaces';\n\nexport function EventHelpers(state: EditorState, eventType: NodeEventTypes) {\n  const event = state.events[eventType];\n  return {\n    contains(id: NodeId) {\n      return event.has(id);\n    },\n    isEmpty() {\n      return this.all().length === 0;\n    },\n    first() {\n      const values = this.all();\n      return values[0];\n    },\n    last() {\n      const values = this.all();\n      return values[values.length - 1];\n    },\n    all() {\n      return Array.from(event);\n    },\n    size() {\n      return this.all().length;\n    },\n    at(i: number) {\n      return this.all()[i];\n    },\n    raw() {\n      return event;\n    },\n  };\n}\n", "import React, { Fragment } from 'react';\n\nimport { createNode } from './createNode';\n\nimport { Node } from '../interfaces';\n\nexport function parseNodeFromJSX(\n  jsx: React.ReactElement<any> | string,\n  normalize?: (node: Node, jsx: React.ReactElement<any>) => void\n) {\n  let element = jsx as React.ReactElement<any>;\n\n  if (typeof element === 'string') {\n    element = React.createElement(Fragment, {}, element) as React.ReactElement<\n      any\n    >;\n  }\n\n  let actualType = element.type as any;\n\n  return createNode(\n    {\n      data: {\n        type: actualType,\n        props: { ...element.props },\n      },\n    },\n    (node) => {\n      if (normalize) {\n        normalize(node, element);\n      }\n    }\n  );\n}\n", "import { DerivedEventHandlers, EventHandlers } from '@craftjs/utils';\n\nimport { EditorStore } from '../editor/store';\nimport { NodeId, NodeTree } from '../interfaces/nodes';\n\nexport interface CreateHandlerOptions {\n  onCreate: (nodeTree: NodeTree) => void;\n}\n\nexport class CoreEventHandlers<O = {}> extends EventHandlers<\n  { store: EditorStore; removeHoverOnMouseleave: boolean } & O\n> {\n  handlers() {\n    return {\n      connect: (el: HTMLElement, id: NodeId) => {},\n      select: (el: HTMLElement, id: NodeId) => {},\n      hover: (el: HTMLElement, id: NodeId) => {},\n      drag: (el: HTMLElement, id: NodeId) => {},\n      drop: (el: HTMLElement, id: NodeId) => {},\n      create: (\n        el: HTMLElement,\n        UserElement: React.ReactElement | (() => NodeTree | React.ReactElement),\n        options?: Partial<CreateHandlerOptions>\n      ) => {},\n    };\n  }\n}\n\nexport abstract class DerivedCoreEventHandlers<\n  O = {}\n> extends DerivedEventHandlers<CoreEventHandlers, O> {}\n", "import { getDOMInfo, ROOT_NODE } from '@craftjs/utils';\n\nimport findPosition from './findPosition';\n\nimport { EditorStore } from '../editor/store';\nimport {\n  DragTarget,\n  DropPosition,\n  Indicator,\n  Node,\n  NodeId,\n  NodeInfo,\n  NodeSelectorWrapper,\n} from '../interfaces';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\n\n// Hack: to trigger dragend event immediate\n// Otherwise we would have to wait until the native animation is completed before we can actually drop an block\nconst documentDragoverEventHandler = (e: DragEvent) => {\n  e.preventDefault();\n};\n\n/**\n * Positioner is responsible for computing the drop Indicator during a sequence of drag-n-drop events\n */\nexport class Positioner {\n  static BORDER_OFFSET = 10;\n\n  // Current Node being hovered on\n  private currentDropTargetId: NodeId | null;\n  // Current closest Canvas Node relative to the currentDropTarget\n  private currentDropTargetCanvasAncestorId: NodeId | null;\n\n  private currentIndicator: Indicator | null = null;\n\n  private currentTargetId: NodeId | null;\n  private currentTargetChildDimensions: NodeInfo[] | null;\n\n  private dragError: string | null;\n  private draggedNodes: NodeSelectorWrapper[];\n\n  private onScrollListener: (e: Event) => void;\n\n  constructor(readonly store: EditorStore, readonly dragTarget: DragTarget) {\n    this.currentDropTargetId = null;\n    this.currentDropTargetCanvasAncestorId = null;\n\n    this.currentTargetId = null;\n    this.currentTargetChildDimensions = null;\n\n    this.currentIndicator = null;\n\n    this.dragError = null;\n    this.draggedNodes = this.getDraggedNodes();\n\n    this.validateDraggedNodes();\n\n    this.onScrollListener = this.onScroll.bind(this);\n    window.addEventListener('scroll', this.onScrollListener, true);\n    window.addEventListener('dragover', documentDragoverEventHandler, false);\n  }\n\n  cleanup() {\n    window.removeEventListener('scroll', this.onScrollListener, true);\n    window.removeEventListener('dragover', documentDragoverEventHandler, false);\n  }\n\n  private onScroll(e: Event) {\n    const scrollBody = e.target;\n    const rootNode = this.store.query.node(ROOT_NODE).get();\n\n    // Clear the currentTargetChildDimensions if the user has scrolled\n    // Because we will have to recompute new dimensions relative to the new scroll pos\n    const shouldClearChildDimensionsCache =\n      scrollBody instanceof Element &&\n      rootNode &&\n      rootNode.dom &&\n      scrollBody.contains(rootNode.dom);\n\n    if (!shouldClearChildDimensionsCache) {\n      return;\n    }\n\n    this.currentTargetChildDimensions = null;\n  }\n\n  private getDraggedNodes() {\n    if (this.dragTarget.type === 'new') {\n      return getNodesFromSelector(\n        this.store.query.getNodes(),\n        this.dragTarget.tree.nodes[this.dragTarget.tree.rootNodeId]\n      );\n    }\n\n    return getNodesFromSelector(\n      this.store.query.getNodes(),\n      this.dragTarget.nodes\n    );\n  }\n\n  // Check if the elements being dragged are allowed to be dragged\n  private validateDraggedNodes() {\n    // We don't need to check for dragTarget.type = \"new\" because those nodes are not yet in the state (ie: via the .create() connector)\n    if (this.dragTarget.type === 'new') {\n      return;\n    }\n\n    this.draggedNodes.forEach(({ node, exists }) => {\n      if (!exists) {\n        return;\n      }\n\n      this.store.query.node(node.id).isDraggable((err) => {\n        this.dragError = err;\n      });\n    });\n  }\n\n  private isNearBorders(\n    domInfo: ReturnType<typeof getDOMInfo>,\n    x: number,\n    y: number\n  ) {\n    const { top, bottom, left, right } = domInfo;\n\n    if (\n      top + Positioner.BORDER_OFFSET > y ||\n      bottom - Positioner.BORDER_OFFSET < y ||\n      left + Positioner.BORDER_OFFSET > x ||\n      right - Positioner.BORDER_OFFSET < x\n    ) {\n      return true;\n    }\n\n    return false;\n  }\n\n  private isDiff(newPosition: DropPosition) {\n    if (\n      this.currentIndicator &&\n      this.currentIndicator.placement.parent.id === newPosition.parent.id &&\n      this.currentIndicator.placement.index === newPosition.index &&\n      this.currentIndicator.placement.where === newPosition.where\n    ) {\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Get dimensions of every child Node in the specified parent Node\n   */\n  private getChildDimensions(newParentNode: Node) {\n    // Use previously computed child dimensions if newParentNode is the same as the previous one\n    const existingTargetChildDimensions = this.currentTargetChildDimensions;\n    if (\n      this.currentTargetId === newParentNode.id &&\n      existingTargetChildDimensions\n    ) {\n      return existingTargetChildDimensions;\n    }\n\n    return newParentNode.data.nodes.reduce((result, id: NodeId) => {\n      const dom = this.store.query.node(id).get().dom;\n\n      if (dom) {\n        result.push({\n          id,\n          ...getDOMInfo(dom),\n        });\n      }\n\n      return result;\n    }, [] as NodeInfo[]);\n  }\n\n  /**\n   * Get closest Canvas node relative to the dropTargetId\n   * Return dropTargetId if it itself is a Canvas node\n   *\n   * In most cases it will be the dropTarget itself or its immediate parent.\n   * We typically only need to traverse 2 levels or more if the dropTarget is a linked node\n   *\n   * TODO: We should probably have some special rules to handle linked nodes\n   */\n  private getCanvasAncestor(dropTargetId: NodeId) {\n    // If the dropTargetId is the same as the previous one\n    // Return the canvas ancestor node that we found previuously\n    if (\n      dropTargetId === this.currentDropTargetId &&\n      this.currentDropTargetCanvasAncestorId\n    ) {\n      const node = this.store.query\n        .node(this.currentDropTargetCanvasAncestorId)\n        .get();\n\n      if (node) {\n        return node;\n      }\n    }\n\n    const getCanvas = (nodeId: NodeId): Node => {\n      const node = this.store.query.node(nodeId).get();\n\n      if (node && node.data.isCanvas) {\n        return node;\n      }\n\n      if (!node.data.parent) {\n        return null;\n      }\n\n      return getCanvas(node.data.parent);\n    };\n\n    return getCanvas(dropTargetId);\n  }\n\n  /**\n   * Compute a new Indicator object based on the dropTarget and x,y coords\n   * Returns null if theres no change from the previous Indicator\n   */\n  computeIndicator(dropTargetId: NodeId, x: number, y: number): Indicator {\n    let newParentNode = this.getCanvasAncestor(dropTargetId);\n\n    if (!newParentNode) {\n      return;\n    }\n\n    this.currentDropTargetId = dropTargetId;\n    this.currentDropTargetCanvasAncestorId = newParentNode.id;\n\n    // Get parent if we're hovering at the border of the current node\n    if (\n      newParentNode.data.parent &&\n      this.isNearBorders(getDOMInfo(newParentNode.dom), x, y) &&\n      // Ignore if linked node because there's won't be an adjacent sibling anyway\n      !this.store.query.node(newParentNode.id).isLinkedNode()\n    ) {\n      newParentNode = this.store.query.node(newParentNode.data.parent).get();\n    }\n\n    if (!newParentNode) {\n      return;\n    }\n\n    this.currentTargetChildDimensions = this.getChildDimensions(newParentNode);\n    this.currentTargetId = newParentNode.id;\n\n    const position = findPosition(\n      newParentNode,\n      this.currentTargetChildDimensions,\n      x,\n      y\n    );\n\n    // Ignore if the position is similar as the previous one\n    if (!this.isDiff(position)) {\n      return;\n    }\n\n    let error = this.dragError;\n\n    // Last thing to check for is if the dragged nodes can be dropped in the target area\n    if (!error) {\n      this.store.query.node(newParentNode.id).isDroppable(\n        this.draggedNodes.map((sourceNode) => sourceNode.node),\n        (dropError) => {\n          error = dropError;\n        }\n      );\n    }\n\n    const currentNodeId = newParentNode.data.nodes[position.index];\n    const currentNode =\n      currentNodeId && this.store.query.node(currentNodeId).get();\n\n    this.currentIndicator = {\n      placement: {\n        ...position,\n        currentNode,\n      },\n      error,\n    };\n\n    return this.currentIndicator;\n  }\n\n  getIndicator() {\n    return this.currentIndicator;\n  }\n}\n", "// Works partially with Linux (except on Chrome)\n// We'll need an alternate way to create drag shadows\nexport const createShadow = (\n  e: DragEvent,\n  shadowsToCreate: HTMLElement[],\n  forceSingleShadow: boolean = false\n) => {\n  if (shadowsToCreate.length === 1 || forceSingleShadow) {\n    const { width, height } = shadowsToCreate[0].getBoundingClientRect();\n    const shadow = shadowsToCreate[0].cloneNode(true) as HTMLElement;\n\n    shadow.style.position = `absolute`;\n    shadow.style.left = `-100%`;\n    shadow.style.top = `-100%`;\n    shadow.style.width = `${width}px`;\n    shadow.style.height = `${height}px`;\n    shadow.style.pointerEvents = 'none';\n    shadow.classList.add('drag-shadow');\n\n    document.body.appendChild(shadow);\n    e.dataTransfer.setDragImage(shadow, 0, 0);\n\n    return shadow;\n  }\n\n  /**\n   * If there's supposed to be multiple drag shadows, we will create a single container div to store them\n   * That container will be used as the drag shadow for the current drag event\n   */\n  const container = document.createElement('div');\n  container.style.position = 'absolute';\n  container.style.left = '-100%';\n  container.style.top = `-100%`;\n  container.style.width = '100%';\n  container.style.height = '100%';\n  container.style.pointerEvents = 'none';\n  container.classList.add('drag-shadow-container');\n\n  shadowsToCreate.forEach((dom) => {\n    const { width, height, top, left } = dom.getBoundingClientRect();\n    const shadow = dom.cloneNode(true) as HTMLElement;\n\n    shadow.style.position = `absolute`;\n    shadow.style.left = `${left}px`;\n    shadow.style.top = `${top}px`;\n    shadow.style.width = `${width}px`;\n    shadow.style.height = `${height}px`;\n    shadow.classList.add('drag-shadow');\n\n    container.appendChild(shadow);\n  });\n\n  document.body.appendChild(container);\n  e.dataTransfer.setDragImage(container, e.clientX, e.clientY);\n\n  return container;\n};\n", "import { isChromium, isLinux } from '@craftjs/utils';\nimport isFunction from 'lodash/isFunction';\nimport React from 'react';\n\nimport { CoreEventHandlers, CreateHandlerOptions } from './CoreEventHandlers';\nimport { Positioner } from './Positioner';\nimport { createShadow } from './createShadow';\n\nimport { Indicator, NodeId, DragTarget, NodeTree } from '../interfaces';\n\nexport type DefaultEventHandlersOptions = {\n  isMultiSelectEnabled: (e: MouseEvent) => boolean;\n  removeHoverOnMouseleave: boolean;\n};\n\n/**\n * Specifies Editor-wide event handlers and connectors\n */\nexport class DefaultEventHandlers<O = {}> extends CoreEventHandlers<\n  DefaultEventHandlersOptions & O\n> {\n  /**\n   * Note: Multiple drag shadows (ie: via multiselect in v0.2 and higher) do not look good on Linux Chromium due to way it renders drag shadows in general,\n   * so will have to fallback to the single shadow approach above for the time being\n   * see: https://bugs.chromium.org/p/chromium/issues/detail?id=550999\n   */\n  static forceSingleDragShadow = isChromium() && isLinux();\n\n  draggedElementShadow: HTMLElement;\n  dragTarget: DragTarget;\n  positioner: Positioner | null = null;\n  currentSelectedElementIds = [];\n\n  onDisable() {\n    this.options.store.actions.clearEvents();\n  }\n\n  handlers() {\n    const store = this.options.store;\n\n    return {\n      connect: (el: HTMLElement, id: NodeId) => {\n        store.actions.setDOM(id, el);\n\n        return this.reflect((connectors) => {\n          connectors.select(el, id);\n          connectors.hover(el, id);\n          connectors.drop(el, id);\n        });\n      },\n      select: (el: HTMLElement, id: NodeId) => {\n        const unbindOnMouseDown = this.addCraftEventListener(\n          el,\n          'mousedown',\n          (e) => {\n            e.craft.stopPropagation();\n\n            let newSelectedElementIds = [];\n\n            if (id) {\n              const { query } = store;\n              const selectedElementIds = query.getEvent('selected').all();\n              const isMultiSelect = this.options.isMultiSelectEnabled(e);\n\n              /**\n               * Retain the previously select elements if the multi-select condition is enabled\n               * or if the currentNode is already selected\n               *\n               * so users can just click to drag the selected elements around without holding the multi-select key\n               */\n\n              if (isMultiSelect || selectedElementIds.includes(id)) {\n                newSelectedElementIds = selectedElementIds.filter(\n                  (selectedId) => {\n                    const descendants = query\n                      .node(selectedId)\n                      .descendants(true);\n                    const ancestors = query.node(selectedId).ancestors(true);\n\n                    // Deselect ancestors/descendants\n                    if (descendants.includes(id) || ancestors.includes(id)) {\n                      return false;\n                    }\n\n                    return true;\n                  }\n                );\n              }\n\n              if (!newSelectedElementIds.includes(id)) {\n                newSelectedElementIds.push(id);\n              }\n            }\n\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          }\n        );\n\n        const unbindOnClick = this.addCraftEventListener(el, 'click', (e) => {\n          e.craft.stopPropagation();\n\n          const { query } = store;\n          const selectedElementIds = query.getEvent('selected').all();\n\n          const isMultiSelect = this.options.isMultiSelectEnabled(e);\n          const isNodeAlreadySelected = this.currentSelectedElementIds.includes(\n            id\n          );\n\n          let newSelectedElementIds = [...selectedElementIds];\n\n          if (isMultiSelect && isNodeAlreadySelected) {\n            newSelectedElementIds.splice(newSelectedElementIds.indexOf(id), 1);\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          } else if (!isMultiSelect && selectedElementIds.length > 1) {\n            newSelectedElementIds = [id];\n            store.actions.setNodeEvent('selected', newSelectedElementIds);\n          }\n\n          this.currentSelectedElementIds = newSelectedElementIds;\n        });\n\n        return () => {\n          unbindOnMouseDown();\n          unbindOnClick();\n        };\n      },\n      hover: (el: HTMLElement, id: NodeId) => {\n        const unbindMouseover = this.addCraftEventListener(\n          el,\n          'mouseover',\n          (e) => {\n            e.craft.stopPropagation();\n            store.actions.setNodeEvent('hovered', id);\n          }\n        );\n\n        let unbindMouseleave: (() => void) | null = null;\n\n        if (this.options.removeHoverOnMouseleave) {\n          unbindMouseleave = this.addCraftEventListener(\n            el,\n            'mouseleave',\n            (e) => {\n              e.craft.stopPropagation();\n              store.actions.setNodeEvent('hovered', null);\n            }\n          );\n        }\n\n        return () => {\n          unbindMouseover();\n\n          if (!unbindMouseleave) {\n            return;\n          }\n\n          unbindMouseleave();\n        };\n      },\n      drop: (el: HTMLElement, targetId: NodeId) => {\n        const unbindDragOver = this.addCraftEventListener(\n          el,\n          'dragover',\n          (e) => {\n            e.craft.stopPropagation();\n            e.preventDefault();\n\n            if (!this.positioner) {\n              return;\n            }\n\n            const indicator = this.positioner.computeIndicator(\n              targetId,\n              e.clientX,\n              e.clientY\n            );\n\n            if (!indicator) {\n              return;\n            }\n\n            store.actions.setIndicator(indicator);\n          }\n        );\n\n        const unbindDragEnter = this.addCraftEventListener(\n          el,\n          'dragenter',\n          (e) => {\n            e.craft.stopPropagation();\n            e.preventDefault();\n          }\n        );\n\n        return () => {\n          unbindDragEnter();\n          unbindDragOver();\n        };\n      },\n      drag: (el: HTMLElement, id: NodeId) => {\n        if (!store.query.node(id).isDraggable()) {\n          return () => {};\n        }\n\n        el.setAttribute('draggable', 'true');\n\n        const unbindDragStart = this.addCraftEventListener(\n          el,\n          'dragstart',\n          (e) => {\n            e.craft.stopPropagation();\n\n            const { query, actions } = store;\n\n            let selectedElementIds = query.getEvent('selected').all();\n\n            const isMultiSelect = this.options.isMultiSelectEnabled(e);\n            const isNodeAlreadySelected = this.currentSelectedElementIds.includes(\n              id\n            );\n\n            if (!isNodeAlreadySelected) {\n              if (isMultiSelect) {\n                selectedElementIds = [...selectedElementIds, id];\n              } else {\n                selectedElementIds = [id];\n              }\n              store.actions.setNodeEvent('selected', selectedElementIds);\n            }\n\n            actions.setNodeEvent('dragged', selectedElementIds);\n\n            const selectedDOMs = selectedElementIds.map(\n              (id) => query.node(id).get().dom\n            );\n\n            this.draggedElementShadow = createShadow(\n              e,\n              selectedDOMs,\n              DefaultEventHandlers.forceSingleDragShadow\n            );\n\n            this.dragTarget = {\n              type: 'existing',\n              nodes: selectedElementIds,\n            };\n\n            this.positioner = new Positioner(\n              this.options.store,\n              this.dragTarget\n            );\n          }\n        );\n\n        const unbindDragEnd = this.addCraftEventListener(el, 'dragend', (e) => {\n          e.craft.stopPropagation();\n\n          this.dropElement((dragTarget, indicator) => {\n            if (dragTarget.type === 'new') {\n              return;\n            }\n\n            const index =\n              indicator.placement.index +\n              (indicator.placement.where === 'after' ? 1 : 0);\n\n            store.actions.move(\n              dragTarget.nodes,\n              indicator.placement.parent.id,\n              index\n            );\n          });\n        });\n\n        return () => {\n          el.setAttribute('draggable', 'false');\n          unbindDragStart();\n          unbindDragEnd();\n        };\n      },\n      create: (\n        el: HTMLElement,\n        userElement: React.ReactElement | (() => NodeTree | React.ReactElement),\n        options?: Partial<CreateHandlerOptions>\n      ) => {\n        el.setAttribute('draggable', 'true');\n\n        const unbindDragStart = this.addCraftEventListener(\n          el,\n          'dragstart',\n          (e) => {\n            e.craft.stopPropagation();\n            let tree;\n            if (typeof userElement === 'function') {\n              const result = userElement();\n              if (React.isValidElement(result)) {\n                tree = store.query.parseReactElement(result).toNodeTree();\n              } else {\n                tree = result;\n              }\n            } else {\n              tree = store.query.parseReactElement(userElement).toNodeTree();\n            }\n\n            const dom = e.currentTarget as HTMLElement;\n            this.draggedElementShadow = createShadow(\n              e,\n              [dom],\n              DefaultEventHandlers.forceSingleDragShadow\n            );\n            this.dragTarget = {\n              type: 'new',\n              tree,\n            };\n\n            this.positioner = new Positioner(\n              this.options.store,\n              this.dragTarget\n            );\n          }\n        );\n\n        const unbindDragEnd = this.addCraftEventListener(el, 'dragend', (e) => {\n          e.craft.stopPropagation();\n          this.dropElement((dragTarget, indicator) => {\n            if (dragTarget.type === 'existing') {\n              return;\n            }\n\n            const index =\n              indicator.placement.index +\n              (indicator.placement.where === 'after' ? 1 : 0);\n            store.actions.addNodeTree(\n              dragTarget.tree,\n              indicator.placement.parent.id,\n              index\n            );\n\n            if (options && isFunction(options.onCreate)) {\n              options.onCreate(dragTarget.tree);\n            }\n          });\n        });\n\n        return () => {\n          el.removeAttribute('draggable');\n          unbindDragStart();\n          unbindDragEnd();\n        };\n      },\n    };\n  }\n\n  private dropElement(\n    onDropNode: (dragTarget: DragTarget, placement: Indicator) => void\n  ) {\n    const store = this.options.store;\n\n    if (!this.positioner) {\n      return;\n    }\n\n    const draggedElementShadow = this.draggedElementShadow;\n\n    const indicator = this.positioner.getIndicator();\n\n    if (this.dragTarget && indicator && !indicator.error) {\n      onDropNode(this.dragTarget, indicator);\n    }\n\n    if (draggedElementShadow) {\n      draggedElementShadow.parentNode.removeChild(draggedElementShadow);\n      this.draggedElementShadow = null;\n    }\n\n    this.dragTarget = null;\n\n    store.actions.setIndicator(null);\n    store.actions.setNodeEvent('dragged', null);\n    this.positioner.cleanup();\n\n    this.positioner = null;\n  }\n}\n", "import { DropPosition, DOMInfo } from '../interfaces';\n\nexport default function movePlaceholder(\n  pos: DropPosition,\n  canvasDOMInfo: DOMInfo, // which canvas is cursor at\n  bestTargetDomInfo: DOMInfo | null, // closest element in canvas (null if canvas is empty)\n  thickness: number = 2\n) {\n  let t = 0,\n    l = 0,\n    w = 0,\n    h = 0,\n    where = pos.where;\n\n  const elDim = bestTargetDomInfo;\n\n  if (elDim) {\n    // If it's not in flow (like 'float' element)\n    if (!elDim.inFlow) {\n      w = thickness;\n      h = elDim.outerHeight;\n      t = elDim.top;\n      l = where === 'before' ? elDim.left : elDim.left + elDim.outerWidth;\n    } else {\n      w = elDim.outerWidth;\n      h = thickness;\n      t = where === 'before' ? elDim.top : elDim.bottom;\n      l = elDim.left;\n    }\n  } else {\n    if (canvasDOMInfo) {\n      t = canvasDOMInfo.top + canvasDOMInfo.padding.top;\n      l = canvasDOMInfo.left + canvasDOMInfo.padding.left;\n      w =\n        canvasDOMInfo.outerWidth -\n        canvasDOMInfo.padding.right -\n        canvasDOMInfo.padding.left -\n        canvasDOMInfo.margin.left -\n        canvasDOMInfo.margin.right;\n      h = thickness;\n    }\n  }\n  return {\n    top: `${t}px`,\n    left: `${l}px`,\n    width: `${w}px`,\n    height: `${h}px`,\n  };\n}\n", "import { RenderIndicator, getDOMInfo } from '@craftjs/utils';\nimport React, { useEffect } from 'react';\n\nimport { useEventHandler } from './EventContext';\nimport movePlaceholder from './movePlaceholder';\n\nimport { useInternalEditor } from '../editor/useInternalEditor';\n\nexport const RenderEditorIndicator = () => {\n  const { indicator, indicatorOptions, enabled } = useInternalEditor(\n    (state) => ({\n      indicator: state.indicator,\n      indicatorOptions: state.options.indicator,\n      enabled: state.options.enabled,\n    })\n  );\n\n  const handler = useEventHandler();\n\n  useEffect(() => {\n    if (!handler) {\n      return;\n    }\n\n    if (!enabled) {\n      handler.disable();\n      return;\n    }\n\n    handler.enable();\n  }, [enabled, handler]);\n\n  if (!indicator) {\n    return null;\n  }\n\n  return React.createElement(RenderIndicator, {\n    className: indicatorOptions.className,\n    style: {\n      ...movePlaceholder(\n        indicator.placement,\n        getDOMInfo(indicator.placement.parent.dom),\n        indicator.placement.currentNode &&\n          getDOMInfo(indicator.placement.currentNode.dom),\n        indicatorOptions.thickness\n      ),\n      backgroundColor: indicator.error\n        ? indicatorOptions.error\n        : indicatorOptions.success,\n      transition: indicatorOptions.transition || '0.2s ease-in',\n      ...(indicatorOptions.style ?? {}),\n    },\n    parentDom: indicator.placement.parent.dom,\n  });\n};\n", "import React, { useContext, useMemo } from 'react';\n\nimport { EventHandlerContext } from './EventContext';\nimport { RenderEditorIndicator } from './RenderEditorIndicator';\n\nimport { EditorContext } from '../editor/EditorContext';\n\ntype EventsProps = {\n  children?: React.ReactNode;\n};\n\nexport const Events = ({ children }: EventsProps) => {\n  const store = useContext(EditorContext);\n\n  const handler = useMemo(() => store.query.getOptions().handlers(store), [\n    store,\n  ]);\n\n  if (!handler) {\n    return null;\n  }\n\n  return (\n    <EventHandlerContext.Provider value={handler}>\n      <RenderEditorIndicator />\n      {children}\n    </EventHandlerContext.Provider>\n  );\n};\n", "import {\n  useMethods,\n  SubscriberAndCallbacksFor,\n  PatchListener,\n} from '@craftjs/utils';\n\nimport { ActionMethods } from './actions';\nimport { QueryMethods } from './query';\n\nimport { DefaultEventHandlers } from '../events';\nimport { EditorState, Options, NodeEventTypes, NodeId } from '../interfaces';\n\nexport const editorInitialState: EditorState = {\n  nodes: {},\n  events: {\n    dragged: new Set<NodeId>(),\n    selected: new Set<NodeId>(),\n    hovered: new Set<NodeId>(),\n  },\n  indicator: null,\n  options: {\n    onNodesChange: () => null,\n    onRender: ({ render }) => render,\n    onBeforeMoveEnd: () => null,\n    resolver: {},\n    enabled: true,\n    indicator: {\n      error: 'red',\n      success: 'rgb(98, 196, 98)',\n    },\n    handlers: (store) =>\n      new DefaultEventHandlers({\n        store,\n        removeHoverOnMouseleave: false,\n        isMultiSelectEnabled: (e: MouseEvent) => !!e.meta<PERSON>ey,\n      }),\n    normalizeNodes: () => {},\n  },\n};\n\nexport const ActionMethodsWithConfig = {\n  methods: ActionMethods,\n  ignoreHistoryForActions: [\n    'setDOM',\n    'setNodeEvent',\n    'selectNode',\n    'clearEvents',\n    'setOptions',\n    'setIndicator',\n  ] as const,\n  normalizeHistory: (state: EditorState) => {\n    /**\n     * On every undo/redo, we remove events pointing to deleted Nodes\n     */\n    Object.keys(state.events).forEach((eventName: NodeEventTypes) => {\n      const nodeIds = Array.from(state.events[eventName] || []);\n\n      nodeIds.forEach((id) => {\n        if (!state.nodes[id]) {\n          state.events[eventName].delete(id);\n        }\n      });\n    });\n\n    // Remove any invalid node[nodeId].events\n    // TODO(prev): it's really cumbersome to have to ensure state.events and state.nodes[nodeId].events are in sync\n    // Find a way to make it so that once state.events is set, state.nodes[nodeId] automatically reflects that (maybe using proxies?)\n    Object.keys(state.nodes).forEach((id) => {\n      const node = state.nodes[id];\n\n      Object.keys(node.events).forEach((eventName: NodeEventTypes) => {\n        const isEventActive = !!node.events[eventName];\n\n        if (\n          isEventActive &&\n          state.events[eventName] &&\n          !state.events[eventName].has(node.id)\n        ) {\n          node.events[eventName] = false;\n        }\n      });\n    });\n  },\n};\n\nexport type EditorStore = SubscriberAndCallbacksFor<\n  typeof ActionMethodsWithConfig,\n  typeof QueryMethods\n>;\n\nexport const useEditorStore = (\n  options: Partial<Options>,\n  patchListener: PatchListener<\n    EditorState,\n    typeof ActionMethodsWithConfig,\n    typeof QueryMethods\n  >\n): EditorStore => {\n  // TODO: fix type\n  return useMethods(\n    ActionMethodsWithConfig,\n    {\n      ...editorInitialState,\n      options: {\n        ...editorInitialState.options,\n        ...options,\n      },\n    },\n    QueryMethods,\n    patchListener\n  ) as EditorStore;\n};\n", "import {\n  deprecationWarning,\n  ERROR_INVALID_NODEID,\n  ROOT_NODE,\n  DEPRECATED_ROOT_NODE,\n  QueryCallbacksFor,\n  ERROR_NOPARENT,\n  ERROR_DELETE_TOP_LEVEL_NODE,\n  CallbacksFor,\n  Delete,\n  ERROR_NOT_IN_RESOLVER,\n} from '@craftjs/utils';\nimport invariant from 'tiny-invariant';\n\nimport { QueryMethods } from './query';\n\nimport {\n  EditorState,\n  Indicator,\n  NodeId,\n  Node,\n  Nodes,\n  Options,\n  NodeEventTypes,\n  NodeTree,\n  SerializedNodes,\n  NodeSelector,\n  NodeSelectorType,\n} from '../interfaces';\nimport { fromEntries } from '../utils/fromEntries';\nimport { getNodesFromSelector } from '../utils/getNodesFromSelector';\nimport { removeNodeFromEvents } from '../utils/removeNodeFromEvents';\n\nconst Methods = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => {\n  /** Helper functions */\n  const addNodeTreeToParent = (\n    tree: NodeTree,\n    parentId?: NodeId,\n    addNodeType?:\n      | {\n          type: 'child';\n          index: number;\n        }\n      | {\n          type: 'linked';\n          id: string;\n        }\n  ) => {\n    const iterateChildren = (id: NodeId, parentId?: NodeId) => {\n      const node = tree.nodes[id];\n\n      if (typeof node.data.type !== 'string') {\n        invariant(\n          state.options.resolver[node.data.name],\n          ERROR_NOT_IN_RESOLVER.replace(\n            '%node_type%',\n            `${(node.data.type as any).name}`\n          )\n        );\n      }\n\n      state.nodes[id] = {\n        ...node,\n        data: {\n          ...node.data,\n          parent: parentId,\n        },\n      };\n\n      if (node.data.nodes.length > 0) {\n        delete state.nodes[id].data.props.children;\n        node.data.nodes.forEach((childNodeId) =>\n          iterateChildren(childNodeId, node.id)\n        );\n      }\n\n      Object.values(node.data.linkedNodes).forEach((linkedNodeId) =>\n        iterateChildren(linkedNodeId, node.id)\n      );\n    };\n\n    iterateChildren(tree.rootNodeId, parentId);\n\n    if (!parentId && tree.rootNodeId === ROOT_NODE) {\n      return;\n    }\n\n    const parent = getParentAndValidate(parentId);\n\n    if (addNodeType.type === 'child') {\n      const index = addNodeType.index;\n\n      if (index != null) {\n        parent.data.nodes.splice(index, 0, tree.rootNodeId);\n      } else {\n        parent.data.nodes.push(tree.rootNodeId);\n      }\n\n      return;\n    }\n\n    parent.data.linkedNodes[addNodeType.id] = tree.rootNodeId;\n  };\n\n  const getParentAndValidate = (parentId: NodeId): Node => {\n    invariant(parentId, ERROR_NOPARENT);\n    const parent = state.nodes[parentId];\n    invariant(parent, ERROR_INVALID_NODEID);\n    return parent;\n  };\n\n  const deleteNode = (id: NodeId) => {\n    const targetNode = state.nodes[id],\n      parentNode = state.nodes[targetNode.data.parent];\n\n    if (targetNode.data.nodes) {\n      // we deep clone here because otherwise immer will mutate the node\n      // object as we remove nodes\n      [...targetNode.data.nodes].forEach((childId) => deleteNode(childId));\n    }\n\n    if (targetNode.data.linkedNodes) {\n      Object.values(targetNode.data.linkedNodes).map((linkedNodeId) =>\n        deleteNode(linkedNodeId)\n      );\n    }\n\n    const isChildNode = parentNode.data.nodes.includes(id);\n\n    if (isChildNode) {\n      const parentChildren = parentNode.data.nodes;\n      parentChildren.splice(parentChildren.indexOf(id), 1);\n    } else {\n      const linkedId = Object.keys(parentNode.data.linkedNodes).find(\n        (id) => parentNode.data.linkedNodes[id] === id\n      );\n      if (linkedId) {\n        delete parentNode.data.linkedNodes[linkedId];\n      }\n    }\n\n    removeNodeFromEvents(state, id);\n    delete state.nodes[id];\n  };\n\n  return {\n    /**\n     * @private\n     * Add a new linked Node to the editor.\n     * Only used internally by the <Element /> component\n     *\n     * @param tree\n     * @param parentId\n     * @param id\n     */\n    addLinkedNodeFromTree(tree: NodeTree, parentId: NodeId, id: string) {\n      const parent = getParentAndValidate(parentId);\n\n      const existingLinkedNode = parent.data.linkedNodes[id];\n\n      if (existingLinkedNode) {\n        deleteNode(existingLinkedNode);\n      }\n\n      addNodeTreeToParent(tree, parentId, { type: 'linked', id });\n    },\n\n    /**\n     * Add a new Node to the editor.\n     *\n     * @param nodeToAdd\n     * @param parentId\n     * @param index\n     */\n    add(nodeToAdd: Node | Node[], parentId?: NodeId, index?: number) {\n      // TODO: Deprecate adding array of Nodes to keep implementation simpler\n      let nodes = [nodeToAdd];\n      if (Array.isArray(nodeToAdd)) {\n        deprecationWarning('actions.add(node: Node[])', {\n          suggest: 'actions.add(node: Node)',\n        });\n        nodes = nodeToAdd;\n      }\n      nodes.forEach((node: Node) => {\n        addNodeTreeToParent(\n          {\n            nodes: {\n              [node.id]: node,\n            },\n            rootNodeId: node.id,\n          },\n          parentId,\n          { type: 'child', index }\n        );\n      });\n    },\n\n    /**\n     * Add a NodeTree to the editor\n     *\n     * @param tree\n     * @param parentId\n     * @param index\n     */\n    addNodeTree(tree: NodeTree, parentId?: NodeId, index?: number) {\n      addNodeTreeToParent(tree, parentId, { type: 'child', index });\n    },\n\n    /**\n     * Delete a Node\n     * @param id\n     */\n    delete(selector: NodeSelector<NodeSelectorType.Id>) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        existOnly: true,\n        idOnly: true,\n      });\n\n      targets.forEach(({ node }) => {\n        invariant(\n          !query.node(node.id).isTopLevelNode(),\n          ERROR_DELETE_TOP_LEVEL_NODE\n        );\n        deleteNode(node.id);\n      });\n    },\n\n    deserialize(input: SerializedNodes | string) {\n      const dehydratedNodes =\n        typeof input == 'string' ? JSON.parse(input) : input;\n\n      const nodePairs = Object.keys(dehydratedNodes).map((id) => {\n        let nodeId = id;\n\n        if (id === DEPRECATED_ROOT_NODE) {\n          nodeId = ROOT_NODE;\n        }\n\n        return [\n          nodeId,\n          query\n            .parseSerializedNode(dehydratedNodes[id])\n            .toNode((node) => (node.id = nodeId)),\n        ];\n      });\n\n      this.replaceNodes(fromEntries(nodePairs));\n    },\n\n    /**\n     * Move a target Node to a new Parent at a given index\n     * @param targetId\n     * @param newParentId\n     * @param index\n     */\n    move(selector: NodeSelector, newParentId: NodeId, index: number) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        existOnly: true,\n      });\n\n      const newParent = state.nodes[newParentId];\n\n      const nodesArrToCleanup = new Set<string[]>();\n\n      targets.forEach(({ node: targetNode }, i) => {\n        const targetId = targetNode.id;\n        const currentParentId = targetNode.data.parent;\n\n        query.node(newParentId).isDroppable([targetId], (err) => {\n          throw new Error(err);\n        });\n\n        // modify node props\n        state.options.onBeforeMoveEnd(\n          targetNode,\n          newParent,\n          state.nodes[currentParentId]\n        );\n\n        const currentParent = state.nodes[currentParentId];\n        const currentParentNodes = currentParent.data.nodes;\n\n        nodesArrToCleanup.add(currentParentNodes);\n\n        const oldIndex = currentParentNodes.indexOf(targetId);\n        currentParentNodes[oldIndex] = '$$'; // mark for deletion\n\n        newParent.data.nodes.splice(index + i, 0, targetId);\n\n        state.nodes[targetId].data.parent = newParentId;\n      });\n\n      nodesArrToCleanup.forEach((nodes) => {\n        const length = nodes.length;\n\n        [...nodes].reverse().forEach((value, index) => {\n          if (value !== '$$') {\n            return;\n          }\n\n          nodes.splice(length - 1 - index, 1);\n        });\n      });\n    },\n\n    replaceNodes(nodes: Nodes) {\n      this.clearEvents();\n      state.nodes = nodes;\n    },\n\n    clearEvents() {\n      this.setNodeEvent('selected', null);\n      this.setNodeEvent('hovered', null);\n      this.setNodeEvent('dragged', null);\n      this.setIndicator(null);\n    },\n\n    /**\n     * Resets all the editor state.\n     */\n    reset() {\n      this.clearEvents();\n      this.replaceNodes({});\n    },\n\n    /**\n     * Set editor options via a callback function\n     *\n     * @param cb: function used to set the options.\n     */\n    setOptions(cb: (options: Partial<Options>) => void) {\n      cb(state.options);\n    },\n\n    setNodeEvent(\n      eventType: NodeEventTypes,\n      nodeIdSelector: NodeSelector<NodeSelectorType.Id>\n    ) {\n      state.events[eventType].forEach((id) => {\n        if (state.nodes[id]) {\n          state.nodes[id].events[eventType] = false;\n        }\n      });\n\n      state.events[eventType] = new Set();\n\n      if (!nodeIdSelector) {\n        return;\n      }\n\n      const targets = getNodesFromSelector(state.nodes, nodeIdSelector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      const nodeIds: Set<NodeId> = new Set(targets.map(({ node }) => node.id));\n      nodeIds.forEach((id) => {\n        state.nodes[id].events[eventType] = true;\n      });\n      state.events[eventType] = nodeIds;\n    },\n\n    /**\n     * Set custom values to a Node\n     * @param id\n     * @param cb\n     */\n    setCustom<T extends NodeId>(\n      selector: NodeSelector<NodeSelectorType.Id>,\n      cb: (data: EditorState['nodes'][T]['data']['custom']) => void\n    ) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      targets.forEach(({ node }) => cb(state.nodes[node.id].data.custom));\n    },\n\n    /**\n     * Given a `id`, it will set the `dom` porperty of that node.\n     *\n     * @param id of the node we want to set\n     * @param dom\n     */\n    setDOM(id: NodeId, dom: HTMLElement) {\n      if (!state.nodes[id]) {\n        return;\n      }\n\n      state.nodes[id].dom = dom;\n    },\n\n    setIndicator(indicator: Indicator | null) {\n      if (\n        indicator &&\n        (!indicator.placement.parent.dom ||\n          (indicator.placement.currentNode &&\n            !indicator.placement.currentNode.dom))\n      )\n        return;\n      state.indicator = indicator;\n    },\n\n    /**\n     * Hide a Node\n     * @param id\n     * @param bool\n     */\n    setHidden(id: NodeId, bool: boolean) {\n      state.nodes[id].data.hidden = bool;\n    },\n\n    /**\n     * Update the props of a Node\n     * @param id\n     * @param cb\n     */\n    setProp(\n      selector: NodeSelector<NodeSelectorType.Id>,\n      cb: (props: any) => void\n    ) {\n      const targets = getNodesFromSelector(state.nodes, selector, {\n        idOnly: true,\n        existOnly: true,\n      });\n\n      targets.forEach(({ node }) => cb(state.nodes[node.id].data.props));\n    },\n\n    selectNode(nodeIdSelector?: NodeSelector<NodeSelectorType.Id>) {\n      if (nodeIdSelector) {\n        const targets = getNodesFromSelector(state.nodes, nodeIdSelector, {\n          idOnly: true,\n          existOnly: true,\n        });\n\n        this.setNodeEvent(\n          'selected',\n          targets.map(({ node }) => node.id)\n        );\n      } else {\n        this.setNodeEvent('selected', null);\n      }\n\n      this.setNodeEvent('hovered', null);\n    },\n  };\n};\n\nexport const ActionMethods = (\n  state: EditorState,\n  query: QueryCallbacksFor<typeof QueryMethods>\n) => {\n  return {\n    ...Methods(state, query),\n    // Note: Beware: advanced method! You most likely don't need to use this\n    // TODO: fix parameter types and cleanup the method\n    setState(\n      cb: (\n        state: EditorState,\n        actions: Delete<CallbacksFor<typeof Methods>, 'history'>\n      ) => void\n    ) {\n      const { history, ...actions } = this;\n\n      // We pass the other actions as the second parameter, so that devs could still make use of the predefined actions\n      cb(state, actions);\n    },\n  };\n};\n", "import { EditorState, NodeId } from '../interfaces';\n\nexport const removeNodeFromEvents = (state: EditorState, nodeId: NodeId) =>\n  Object.keys(state.events).forEach((key) => {\n    const eventSet = state.events[key];\n    if (eventSet && eventSet.has && eventSet.has(nodeId)) {\n      state.events[key] = new Set(\n        Array.from(eventSet).filter((id) => nodeId !== id)\n      );\n    }\n  });\n", "import cloneDeep from 'lodash/cloneDeep';\n\nimport { createNode } from './createNode';\n\nimport { editorInitialState } from '../editor/store';\nimport { Nodes } from '../interfaces';\n\nconst getTestNode = (parentNode) => {\n  const {\n    events,\n    data: { nodes: childNodes, linkedNodes },\n    ...restParentNode\n  } = parentNode;\n  const validParentNode = createNode(cloneDeep(parentNode));\n  parentNode = {\n    ...validParentNode,\n    ...restParentNode,\n    events: {\n      ...validParentNode.events,\n      ...events,\n    },\n    dom: parentNode.dom || validParentNode.dom,\n  };\n\n  return {\n    node: parentNode,\n    childNodes,\n    linkedNodes,\n  };\n};\n\nexport const expectEditorState = (lhs, rhs) => {\n  const { nodes: nodesRhs, ...restRhs } = rhs;\n  const { nodes: nodesLhs, ...restLhs } = lhs;\n  expect(restLhs).toEqual(restRhs);\n\n  const nodesRhsSimplified = Object.keys(nodesRhs).reduce((accum, id) => {\n    const { _hydrationTimestamp, rules, ...node } = nodesRhs[id];\n    accum[id] = node;\n    return accum;\n  }, {});\n\n  const nodesLhsSimplified = Object.keys(nodesLhs).reduce((accum, id) => {\n    const { _hydrationTimestamp, rules, ...node } = nodesLhs[id];\n    accum[id] = node;\n    return accum;\n  }, {});\n\n  expect(nodesLhsSimplified).toEqual(nodesRhsSimplified);\n};\n\nexport const createTestNodes = (rootNode): Nodes => {\n  const nodes = {};\n  const iterateNodes = (testNode) => {\n    const { node: parentNode, childNodes, linkedNodes } = getTestNode(testNode);\n    nodes[parentNode.id] = parentNode;\n\n    if (childNodes) {\n      childNodes.forEach((childTestNode, i) => {\n        const {\n          node: childNode,\n          childNodes: grandChildNodes,\n          linkedNodes: grandChildLinkedNodes,\n        } = getTestNode(childTestNode);\n        childNode.data.parent = parentNode.id;\n        nodes[childNode.id] = childNode;\n        parentNode.data.nodes[i] = childNode.id;\n        iterateNodes({\n          ...childNode,\n          data: {\n            ...childNode.data,\n            nodes: grandChildNodes || [],\n            linkedNodes: grandChildLinkedNodes || {},\n          },\n        });\n      });\n    }\n\n    if (linkedNodes) {\n      Object.keys(linkedNodes).forEach((linkedId) => {\n        const {\n          node: childNode,\n          childNodes: grandChildNodes,\n          linkedNodes: grandChildLinkedNodes,\n        } = getTestNode(linkedNodes[linkedId]);\n        parentNode.data.linkedNodes[linkedId] = childNode.id;\n\n        childNode.data.parent = parentNode.id;\n        nodes[childNode.id] = childNode;\n        iterateNodes({\n          ...childNode,\n          data: {\n            ...childNode.data,\n            nodes: grandChildNodes || [],\n            linkedNodes: grandChildLinkedNodes || {},\n          },\n        });\n      });\n    }\n  };\n\n  iterateNodes(rootNode);\n\n  return nodes;\n};\n\nexport const createTestState = (state = {} as any) => {\n  const { nodes: rootNode, events } = state;\n\n  return {\n    ...editorInitialState,\n    ...state,\n    nodes: rootNode ? createTestNodes(rootNode) : {},\n    events: {\n      ...editorInitialState.events,\n      ...(events || {}),\n    },\n  };\n};\n", "import { ERROR_RESOLVER_NOT_AN_OBJECT, HISTORY_ACTIONS } from '@craftjs/utils';\nimport * as React from 'react';\nimport invariant from 'tiny-invariant';\n\nimport { EditorContext } from './EditorContext';\nimport { useEditorStore } from './store';\n\nimport { Events } from '../events';\nimport { Options } from '../interfaces';\n\ntype EditorProps = Partial<Options> & {\n  children?: React.ReactNode;\n};\n\n/**\n * A React Component that provides the Editor context\n */\nexport const Editor = ({ children, ...options }: EditorProps) => {\n  // we do not want to warn the user if no resolver was supplied\n  if (options.resolver !== undefined) {\n    invariant(\n      typeof options.resolver === 'object' &&\n        !Array.isArray(options.resolver) &&\n        options.resolver !== null,\n      ERROR_RESOLVER_NOT_AN_OBJECT\n    );\n  }\n\n  const optionsRef = React.useRef(options);\n\n  const context = useEditorStore(\n    optionsRef.current,\n    (state, previousState, actionPerformedWithPatches, query, normalizer) => {\n      if (!actionPerformedWithPatches) {\n        return;\n      }\n\n      const { patches, ...actionPerformed } = actionPerformedWithPatches;\n\n      for (let i = 0; i < patches.length; i++) {\n        const { path } = patches[i];\n        const isModifyingNodeData =\n          path.length > 2 && path[0] === 'nodes' && path[2] === 'data';\n\n        let actionType = actionPerformed.type;\n\n        if (\n          [HISTORY_ACTIONS.IGNORE, HISTORY_ACTIONS.THROTTLE].includes(\n            actionType\n          ) &&\n          actionPerformed.params\n        ) {\n          actionPerformed.type = actionPerformed.params[0];\n        }\n\n        if (\n          ['setState', 'deserialize'].includes(actionPerformed.type) ||\n          isModifyingNodeData\n        ) {\n          normalizer((draft) => {\n            if (state.options.normalizeNodes) {\n              state.options.normalizeNodes(\n                draft,\n                previousState,\n                actionPerformed,\n                query\n              );\n            }\n          });\n          break; // we exit the loop as soon as we find a change in node.data\n        }\n      }\n    }\n  );\n\n  // sync enabled prop with editor store options\n  React.useEffect(() => {\n    if (!context) {\n      return;\n    }\n\n    if (\n      options.enabled === undefined ||\n      context.query.getOptions().enabled === options.enabled\n    ) {\n      return;\n    }\n\n    context.actions.setOptions((editorOptions) => {\n      editorOptions.enabled = options.enabled;\n    });\n  }, [context, options.enabled]);\n\n  React.useEffect(() => {\n    context.subscribe(\n      (_) => ({\n        json: context.query.serialize(),\n      }),\n      () => {\n        context.query.getOptions().onNodesChange(context.query);\n      }\n    );\n  }, [context]);\n\n  if (!context) {\n    return null;\n  }\n\n  return (\n    <EditorContext.Provider value={context}>\n      <Events>{children}</Events>\n    </EditorContext.Provider>\n  );\n};\n", "import React from 'react';\n\nimport { EditorState } from '../../interfaces';\nimport { useEditor } from '../useEditor';\n\nexport function connectEditor<C>(collect?: (state: EditorState) => C) {\n  return (WrappedComponent: React.ElementType) => {\n    return (props: any) => {\n      const Editor = collect ? useEditor(collect) : useEditor();\n      return <WrappedComponent {...Editor} {...props} />;\n    };\n  };\n}\n", "import React from 'react';\n\nimport { Node } from '../../interfaces';\nimport { useNode } from '../useNode';\n\nexport function connectNode<C>(collect?: (state: Node) => C) {\n  return function (WrappedComponent: React.ElementType) {\n    return (props: any) => {\n      const node = useNode(collect);\n      return <WrappedComponent {...node} {...props} />;\n    };\n  };\n}\n"], "names": ["NodeContext", "React", "createContext", "NodeProvider", "id", "related", "children", "createElement", "Provider", "value", "EditorContext", "EventHandlerContext", "useEventHandler", "useContext", "useInternalEditor", "collector", "handler", "store", "invariant", "ERROR_USE_EDITOR_OUTSIDE_OF_EDITOR_CONTEXT", "collected", "useCollector", "connectorsUsage", "useMemo", "createConnectorsUsage", "useEffect", "register", "cleanup", "connectors", "wrapConnectorHooks", "_objectSpread", "inContext", "useInternalNode", "collect", "context", "ERROR_USE_NODE_OUTSIDE_OF_EDITOR_CONTEXT", "state", "nodes", "EditorActions", "actions", "editorConnectors", "_objectWithoutProperties", "_useInternalEditor", "_excluded", "connect", "dom", "drag", "setProp", "cb", "throttleRate", "history", "throttle", "setCustom", "setHidden", "bool", "inNodeContext", "useNode", "_useInternalNode", "deprecationWarning", "suggest", "SimpleElement", "render", "type", "cloneElement", "De<PERSON>ult<PERSON>ender", "props", "hydrationTimestamp", "node", "data", "_hydrationTimestamp", "length", "Fragment", "map", "NodeElement", "key", "RenderNodeToElement", "hidden", "onRender", "options", "defaultElementProps", "is", "canvas", "custom", "elementPropToNodeData", "Element", "elementProps", "query", "nodeId", "linkedNodeId", "useState", "ERROR_TOP_LEVEL_ELEMENT_NO_ID", "get", "existingNode", "linkedNodes", "linkedElement", "tree", "parseReactElement", "toNodeTree", "ignore", "addLinkedNodeFromTree", "rootNodeId", "deprecateCanvasComponent", "<PERSON><PERSON>", "RenderRootNode", "timestamp", "ROOT_NODE", "NodeSelectorType", "getPublicActions", "setDOM", "setNodeEvent", "replaceNodes", "reset", "useEditor", "internalActions", "args", "fromEntries", "pairs", "Object", "reduce", "accum", "_ref", "_ref2", "_slicedToArray", "_defineProperty", "getNodesFromSelector", "selector", "config", "items", "Array", "isArray", "mergedConfig", "existOnly", "idOnly", "nodeSelectors", "filter", "item", "exists", "_typeof", "ERROR_INVALID_NODEID", "CACHED_RESOLVER_DATA", "resolveComponent", "resolver", "comp", "component", "resolvedName", "name", "reversed", "Map", "_i", "_Object$entries", "entries", "_Object$entries$_i", "set", "getReversedResolver", "undefined", "searchComponentInResolver", "ERROR_NOT_IN_RESOLVER", "replace", "displayName", "reduceType", "serializeComp", "isCanvas", "keys", "result", "prop", "Children", "child", "serializeNode", "nodeData", "NodeHelpers", "ERROR_INVALID_NODE_ID", "nodeHelpers", "isRoot", "isLinkedNode", "parent", "includes", "isTopLevelNode", "this", "isDeletable", "isParentOfTopLevelNodes", "isParentOfTopLevelCanvas", "isSelected", "events", "selected", "has", "isHovered", "hovered", "isDragged", "dragged", "ancestors", "deep", "appendParentNode", "depth", "push", "descendants", "include<PERSON>nly", "arguments", "appendChildNode", "for<PERSON>ach", "childNodes", "values", "isDraggable", "onError", "targetNode", "ERROR_MOVE_TOP_LEVEL_NODE", "ERROR_MOVE_NONCANVAS_CHILD", "rules", "canDrag", "ERROR_CANNOT_DRAG", "err", "isDroppable", "targets", "newParentNode", "ERROR_MOVE_TO_NONCANVAS_PARENT", "canMoveIn", "ERROR_MOVE_INCOMING_PARENT", "parentNodes", "canDrop", "ERROR_MOVE_CANNOT_DROP", "targetDeepNodes", "ERROR_MOVE_TO_DESCENDANT", "currentParentNode", "ERROR_DUPLICATE_NODEID", "parentNodeId", "parentNode", "canMoveOut", "ERROR_MOVE_OUTGOING_PARENT", "toSerializedNode", "descendantId", "decendants", "isTopLevelCanvas", "findPosition", "dims", "posX", "posY", "index", "where", "leftLimit", "xLimit", "yLimit", "xCenter", "yCenter", "dimDown", "i", "len", "dim", "top", "outerHeight", "left", "outerWidth", "inFlow", "getNodeTypeName", "createNode", "newNode", "normalize", "actualType", "getRandomNodeId", "getRandomId", "Date", "now", "info", "mergedProps", "userComponentConfig", "craft", "defaultProps", "relatedNodeContext", "deserializeComp", "main", "restoreType", "jsx", "deserializeNode", "Comp", "Props", "ERROR_DESERIALIZE_COMPONENT_NOT_IN_RESOLVER", "join", "_child<PERSON><PERSON><PERSON>", "mergeNodes", "rootNode", "childrenNodes", "nodeWithChildren", "currentNode", "mergeTrees", "QueryMethods", "_", "getDropPlaceholder", "source", "target", "pos", "nodesToDOM", "targetParent", "targetParentNodes", "dropAction", "getDOMInfo", "x", "y", "output", "placement", "error", "getOptions", "getNodes", "getSerializedNodes", "nodePairs", "getEvent", "eventType", "event", "contains", "isEmpty", "all", "first", "last", "from", "size", "at", "raw", "EventHelpers", "serialize", "JSON", "stringify", "reactElement", "element", "parseNodeFromJSX", "toArray", "isValidElement", "parseSerializedNode", "serializedNode", "toNode", "parseFreshNode", "DEPRECATED_ROOT_NODE", "extras", "getState", "CoreEventHandlers", "_EventHandlers", "_inherits", "EventHandlers", "_super", "_createSuper", "_classCallCheck", "apply", "_createClass", "el", "select", "hover", "drop", "create", "UserElement", "DerivedCoreEventHandlers", "_DerivedEventHandlers", "DerivedEventHandlers", "_super2", "documentDragoverEventHandler", "e", "preventDefault", "Positioner", "dragTarget", "currentDropTargetId", "currentDropTargetCanvasAncestorId", "currentTargetId", "currentTargetChildDimensions", "currentIndicator", "dragError", "draggedNodes", "getDraggedNodes", "validateDraggedNodes", "onScrollListener", "onScroll", "bind", "window", "addEventListener", "removeEventListener", "scrollBody", "_this", "domInfo", "BORDER_OFFSET", "bottom", "right", "newPosition", "_this2", "existingTargetChildDimensions", "dropTargetId", "_this3", "get<PERSON>anvas", "getCanvasAncestor", "isNearBorders", "getChildDimensions", "position", "isDiff", "sourceNode", "dropError", "currentNodeId", "createShadow", "shadowsToCreate", "_shadowsToCreate$0$ge", "getBoundingClientRect", "width", "height", "shadow", "cloneNode", "style", "concat", "pointerEvents", "classList", "add", "document", "body", "append<PERSON><PERSON><PERSON>", "dataTransfer", "setDragImage", "container", "clientX", "clientY", "DefaultEventHandlers", "_CoreEventHandlers", "_len", "_key", "_assertThisInitialized", "call", "clearEvents", "reflect", "unbindOnMouseDown", "addCraftEventListener", "stopPropagation", "newSelectedElementIds", "selectedElementIds", "isMultiSelectEnabled", "selectedId", "unbindOnClick", "isMultiSelect", "isNodeAlreadySelected", "currentSelectedElementIds", "splice", "indexOf", "unbindMouseover", "unbindMouseleave", "removeHoverOnMouseleave", "targetId", "unbindDragOver", "positioner", "indicator", "computeIndicator", "setIndicator", "unbindDragEnter", "setAttribute", "unbindDragStart", "selectedDOMs", "draggedElementShadow", "forceSingleDragShadow", "unbindDragEnd", "dropElement", "move", "userElement", "currentTarget", "addNodeTree", "isFunction", "onCreate", "removeAttribute", "onDropNode", "getIndicator", "<PERSON><PERSON><PERSON><PERSON>", "movePlaceholder", "canvasDOMInfo", "bestTargetDomInfo", "thickness", "t", "l", "w", "h", "padding", "margin", "isChromium", "isLinux", "RenderEditorIndicator", "indicatorOptions", "enabled", "enable", "disable", "RenderIndicator", "className", "backgroundColor", "success", "transition", "parentDom", "Events", "handlers", "editorInitialState", "Set", "onNodesChange", "onBeforeMoveEnd", "metaKey", "normalizeNodes", "ActionMethodsWithConfig", "methods", "addNodeTreeToParent", "parentId", "addNodeType", "iterateChildren", "childNodeId", "getParentAndValidate", "ERROR_NOPARENT", "deleteNode", "_toConsumableArray", "childId", "parent<PERSON><PERSON><PERSON><PERSON>", "linkedId", "find", "eventSet", "removeNodeFromEvents", "existingLinkedNode", "nodeToAdd", "delete", "ERROR_DELETE_TOP_LEVEL_NODE", "deserialize", "input", "dehydratedNodes", "parse", "newParentId", "newParent", "nodesArrToCleanup", "currentParentId", "Error", "currentParentNodes", "oldIndex", "reverse", "setOptions", "nodeIdSelector", "nodeIds", "_ref3", "_ref4", "_ref5", "selectNode", "_ref6", "Methods", "setState", "ignoreHistoryForActions", "normalizeHistory", "eventName", "useEditorStore", "patchListener", "useMethods", "getTestNode", "_parentNode$data", "restParentNode", "validParentNode", "cloneDeep", "createTestNodes", "iterateNodes", "testNode", "childTestNode", "childNode", "grandChildNodes", "grandChildLinkedNodes", "_getTestNode3", "ERROR_RESOLVER_NOT_AN_OBJECT", "optionsRef", "useRef", "current", "previousState", "actionPerformedWithPatches", "normalizer", "patches", "actionPerformed", "path", "isModifyingNodeData", "HISTORY_ACTIONS", "IGNORE", "THROTTLE", "params", "draft", "editorOptions", "subscribe", "json", "isLoaded", "initialData", "only", "WrappedComponent", "Editor", "lhs", "rhs", "nodesRhs", "restRhs", "_excluded2", "nodesLhs", "restLhs", "_excluded3", "expect", "toEqual", "nodesRhsSimplified", "_excluded4", "nodesLhsSimplified", "_excluded5"], "mappings": "qtBASO,MAAMA,EAAcC,EAAK,QAACC,cAA+B,MAMnDC,EAAe,EAC1BC,KACAC,WAAU,EACVC,cAGEL,UAACM,cAAAP,EAAYQ,UAASC,MAAO,CAAEL,KAAIC,YAChCC,giJCjBA,MAAMI,EAAgBR,EAAaA,cAAoB,MCDvD,IAAMS,EAAsBT,EAAAA,cAAiC,MAEvDU,EAAkB,WAAH,OAASC,EAAAA,WAAWF,EAAoB,EC2B9D,SAAUG,EACdC,GAEA,IAAMC,EAAUJ,IACVK,EAAQJ,aAAWH,GACzBQ,UAAUD,EAAOE,EAAAA,4CAEjB,IAAMC,EAAYC,EAAAA,aAAaJ,EAAOF,GAEhCO,EAAkBC,EAAAA,SACtB,WAAA,OAAMP,GAAWA,EAAQQ,0BACzB,CAACR,IAGHS,EAAAA,WAAU,WAGR,OAFAH,EAAgBI,WAET,WACLJ,EAAgBK,UAEpB,GAAG,CAACL,IAEJ,IAAMM,EAAaL,EAAAA,SACjB,WAAA,OAAMD,GAAmBO,EAAAA,mBAAmBP,EAAgBM,cAC5D,CAACN,IAGH,OAAAQ,EAAAA,EAAA,CAAA,EACKV,GAAS,CAAA,EAAA,CACZQ,WAAAA,EACAG,YAAad,EACbA,MAAAA,GAEJ,wCCtDM,SAAUe,EAA0BC,GACxC,IAAMC,EAAUrB,aAAWb,GAC3BkB,UAAUgB,EAASC,EAAAA,0CAEnB,IAAQ/B,EAAgB8B,EAAhB9B,GAAIC,EAAY6B,EAAZ7B,QAORS,EAAAA,GACF,SAACsB,GAAK,OAAKhC,GAAMgC,EAAMC,MAAMjC,IAAO6B,GAAWA,EAAQG,EAAMC,MAAMjC,OAL1DkC,IAATC,QAEYC,IAAZZ,WACGR,EAASqB,EAAAC,EAAAC,GAKRf,EAAaL,EAAAA,SACjB,WAAA,OACEM,qBAAmB,CACjBe,QAAS,SAACC,GAAgB,OAAKL,EAAiBI,QAAQC,EAAKzC,EAAG,EAChE0C,KAAM,SAACD,GAAgB,OAAKL,EAAiBM,KAAKD,EAAKzC,EAAG,GAC1D,GACJ,CAACoC,EAAkBpC,IAGfmC,EAAUhB,EAAAA,SAAQ,WACtB,MAAO,CACLwB,QAAS,SAACC,EAASC,GACbA,EACFX,EAAcY,QAAQC,SAASF,GAAcF,QAAQ3C,EAAI4C,GAEzDV,EAAcS,QAAQ3C,EAAI4C,EAE7B,EACDI,UAAW,SAACJ,EAASC,GACfA,EACFX,EAAcY,QAAQC,SAASF,GAAcG,UAAUhD,EAAI4C,GAE3DV,EAAcc,UAAUhD,EAAI4C,EAE/B,EACDK,UAAW,SAACC,GAAa,OAAKhB,EAAce,UAAUjD,EAAIkD,EAAK,EAEnE,GAAG,CAAChB,EAAelC,IAEnB,OAAA0B,EAAAA,EAAA,CAAA,EACKV,GAAS,CAAA,EAAA,CACZhB,GAAAA,EACAC,QAAAA,EACAkD,gBAAiBrB,EACjBK,QAAAA,EACAX,WAAAA,GAEJ,+DCvDM,SAAU4B,EAAkBvB,GAChC,IAOID,EAAAA,EAAgBC,GANlB7B,IAAAA,GACAC,IAAAA,QACAkC,IAAAA,QACAgB,IAAAA,cACA3B,IAAAA,WAIF,OAAAE,EAAAA,EAAA,CAAA,EAHcW,EAAAgB,EAAAd,IAIA,CAAA,EAAA,CACZJ,QAAAA,EACAnC,GAAAA,EACAC,QAAAA,EACA0C,QAAS,SACPC,EACAC,GAKA,OAHAS,EAAAA,mBAAmB,sBAAuB,CACxCC,QAAS,gCAEJpB,EAAQQ,QAAQC,EAAIC,EAC5B,EACDM,cAAAA,EACA3B,WAAAA,GAEJ,CChCO,MAAMgC,EAAgB,EAAGC,aAC9B,MACEjC,YAAYgB,QAAEA,EAAOE,KAAEA,IACrBU,IAEJ,MAA8B,iBAAhBK,EAAOC,KACjBlB,EAAQE,EAAK7C,EAAAA,QAAM8D,aAAaF,KAChCA,CAAM,ECHCG,EAAgB,KAC3B,MAAMF,KAAEA,EAAIG,MAAEA,EAAK5B,MAAEA,EAAK6B,mBAAEA,GAAuBlC,GAChDmC,IAAU,CACTL,KAAMK,EAAKC,KAAKN,KAChBG,MAAOE,EAAKC,KAAKH,MACjB5B,MAAO8B,EAAKC,KAAK/B,MACjB6B,mBAAoBC,EAAKE,wBAI7B,OAAO9C,EAAOA,SAAC,KACb,IAAIjB,EAAW2D,EAAM3D,SAEjB+B,GAASA,EAAMiC,OAAS,IAC1BhE,EACEL,EAAAA,QAACM,cAAAN,EAAK,QAACsE,SAAQ,KACZlC,EAAMmC,KAAKpE,GACVH,EAAA,QAAAM,cAACkE,EAAW,CAACrE,GAAIA,EAAIsE,IAAKtE,QAMlC,MAAMyD,EAAS5D,EAAAA,QAAMM,cAAcuD,EAAMG,EAAO3D,GAEhD,MAAmB,iBAARwD,EACF7D,EAAAA,sBAAC2D,EAAa,CAACC,OAAQA,IAGzBA,CAAM,GAEZ,CAACC,EAAMG,EAAOC,EAAoB7B,GAAO,EC5BjCsC,EAAsB,EAAGd,aACpC,MAAMe,OAAEA,GAAW5C,GAAiBmC,IAAU,CAC5CS,OAAQT,EAAKC,KAAKQ,YAGdC,SAAEA,GAAa/D,GAAmBsB,IAAW,CACjDyC,SAAUzC,EAAM0C,QAAQD,aAI1B,OAAID,EACK,KAGF3E,UAAMM,cAAcsE,EAAU,CAAEhB,OAAQA,GAAU5D,EAAC,QAAAM,cAAAyD,EAAgB,OAAG,ECblES,EAAc,EAAGrE,KAAIyD,YAE9B5D,EAAC,QAAAM,cAAAJ,EAAa,CAAAC,GAAIA,GAChBH,EAAC,QAAAM,cAAAoE,GAAoBd,OAAQA,KCLtBkB,EAAsB,CACjCC,GAAI,MACJC,QAAQ,EACRC,OAAQ,CAAE,EACVN,QAAQ,GAGGO,EAAwB,CACnCH,GAAI,OACJC,OAAQ,YAYJ,SAAUG,GAAqChF,GACnDA,EAAEE,SACFA,KACG+E,IAEH,MAAML,GAAEA,GAAO,IACVD,KACAM,IAGCC,MAAEA,EAAK/C,QAAEA,GAAYzB,KACnBV,GAAImF,EAAMhC,cAAEA,GAAkBvB,KAE/BwD,GAAgBC,EAAAA,UAAwB,KAC7CvE,EAAAA,UAAYd,EAAIsF,EAAAA,+BAChB,MAAMvB,EAAOmB,EAAMnB,KAAKoB,GAAQI,MAEhC,GAAIpC,EAAe,CACjB,MAAMqC,EAAezB,EAAKC,KAAKyB,YAAYzF,GACvCkF,EAAMnB,KAAKA,EAAKC,KAAKyB,YAAYzF,IAAKuF,MACtC,KAGJ,GAAIC,GAAgBA,EAAaxB,KAAKN,OAASkB,EAC7C,OAAOY,EAAaxF,GAItB,MAAM0F,EAAgB7F,EAAAA,QAAMM,cAC1B6E,EACAC,EACA/E,GAGIyF,EAAOT,EAAMU,kBAAkBF,GAAeG,aAGpD,OADA1D,EAAQW,QAAQgD,SAASC,sBAAsBJ,EAAMR,EAAQnF,GACtD2F,EAAKK,UACb,CACD,OAAO,IAAI,IAGb,OAAOZ,EAAevF,wBAACwE,EAAW,CAACrE,GAAIoF,IAAmB,IAC5D,CCnEa,MAAAa,EAA2B,IACtC3C,EAAkBA,mBAAC,aAAc,CAC/BC,QAAS,uCAGG2C,WACXrC,IAIH,OAFAxC,EAAAA,WAAU,IAAM4E,KAA4B,IAErCpG,EAAA,QAAAM,cAAC6E,EAAY,IAAAnB,EAAOgB,QAAQ,GACrC,CCLA,MAAMsB,EAAiB,KACrB,MAAMC,UAAEA,GAAc1F,GAAmBsB,IAAW,CAClDoE,UACEpE,EAAMC,MAAMoE,EAASA,YAAKrE,EAAMC,MAAMoE,EAASA,WAAEpC,wBAGrD,OAAKmC,EAIEvG,EAAA,QAAAM,cAACkE,EAAW,CAACrE,GAAIqG,YAAW/B,IAAK8B,IAH/B,IAG4C,ECkFvD,IAAYE,EAAAA,QAIXA,sBAAA,GAJWA,EAAAA,QAAgBA,mBAAhBA,yBAIX,CAAA,IAHCA,EAAA,IAAA,GAAA,MACAA,EAAAA,EAAA,GAAA,GAAA,KACAA,EAAAA,EAAA,IAAA,GAAA,MC5FF,MAAMC,EAAoBpE,IACxB,MAAM4D,sBACJA,EAAqBS,OACrBA,EAAMC,aACNA,EAAYC,aACZA,EAAYC,MACZA,KACGzE,GACDC,EAEJ,OAAOD,CAAa,EAuChB,SAAU0E,EAAa/E,GAC3B,MAAML,WACJA,EACAW,QAAS0E,EAAe3B,MACxBA,EAAKrE,MACLA,KACGG,GACDN,EAAkBmB,GAEhBK,EAAgBqE,EAAiBM,GAevC,MAAO,CACLrF,aACAW,QAfchB,EAAAA,SAAQ,KACf,IACFe,EACHY,QAAS,IACJZ,EAAcY,QACjBgD,OAAQ,IAAIgB,IACVP,EAAiBrE,EAAcY,QAAQgD,UAAUgB,IACnD/D,SAAU,IAAI+D,IACZP,EAAiBrE,EAAcY,QAAQC,YAAY+D,QAGxD,CAAC5E,IAKFgD,QACArE,WACIG,EAER,CChGO,IAAM+F,EAAc,SAACC,GAC1B,OAAIC,OAAOF,YACFE,OAAOF,YAAYC,GAErBA,EAAME,QACX,SAACC,EAAKC,GAAA,IAAAC,EAAAC,EAAAF,EAAA,GAAGpH,EAAEqH,EAAA,GAAEhH,EAAKgH,EAAA,GAAA,OAAA3F,EAAAA,EAAA,CAAA,EACbyF,GAAK,GAAAI,EAAA,CAAA,EACPvH,EAAKK,GACN,GACF,CAAE,EAEN,ECLamH,EAAuB,SAClCvF,EACAwF,EACAC,GAEA,IAAMC,EAAQC,MAAMC,QAAQJ,GAAYA,EAAW,CAACA,GAE9CK,EAAYpG,EAAA,CAChBqG,WAAW,EACXC,QAAQ,GACJN,GAAU,CAAA,GAGVO,EAAgBN,EACnBO,QAAO,SAACC,GAAI,QAAOA,CAAI,IACvB/D,KAAI,SAAC+D,GACJ,MAAoB,iBAATA,EACF,CACLpE,KAAM9B,EAAMkG,GACZC,SAAUnG,EAAMkG,IAIA,WAAhBE,EAAOF,IAAsBL,EAAaE,OAQvC,CACLjE,KAAM,KACNqE,QAAQ,GARD,CACLrE,KAFWoE,EAGXC,SAAUnG,EAHCkG,EAGUnI,IAQ3B,IASF,OAPI8H,EAAaC,WACfjH,EAAAA,QACkE,IAAhEmH,EAAcC,QAAO,SAACT,GAAQ,OAAMA,EAASW,MAAM,IAAElE,OACrDoE,EAAAA,sBAIGL,CACT,iBCtCIM,GAAkD,KA+BzCC,GAAmB,SAC9BC,EACAC,GAEA,GAAoB,iBAATA,EACT,OAAOA,EAGT,IApBwBC,EAoBlBC,EAhB0B,SAChCH,EACAC,GAEA,IAAMG,EAzBoB,SAACJ,GAC3B,GAAIF,IAAwBA,GAAqBE,WAAaA,EAC5D,OAAOF,GAAqBO,SAG9BP,GAAuB,CACrBE,SAAAA,EACAK,SAAU,IAAIC,KAGhB,IAAA,IAAAC,EAAA,EAAAC,EAA2BhC,OAAOiC,QAAQT,GAAWO,EAAAC,EAAA/E,OAAA8E,IAAA,CAAhD,IAAAG,EAAA7B,EAAA2B,EAAAD,GAAA,GACHT,GAAqBO,SAASM,IADVD,EAAA,GAANA,EAAA,GAEhB,CAEA,OAAOZ,GAAqBO,QAC9B,CAUeO,CAAoBZ,GAAUlD,IAAImD,GAC/C,YAAgBY,IAATT,EAAqBA,EAAO,IACrC,CAUuBU,CAA0Bd,EAAUC,GAOzD,OALA5H,UACE8H,EACAY,EAAAA,sBAAsBC,QAAQ,eAxBRd,EAwBwCD,GAvBtCG,MAASF,EAAkBe,cA0B9Cd,CACT,ECrDA,MAAMe,GAAa,CAACjG,EAAkC+E,IAChC,iBAAT/E,EACFA,EAEF,CAAEkF,aAAcJ,GAAiBC,EAAU/E,IAGvCkG,GAAgB,CAC3B5F,EACAyE,KAEA,IAAI/E,KAAEA,EAAImG,SAAEA,EAAQhG,MAAEA,GAAUG,EAuBhC,OAtBAH,EAAQoD,OAAO6C,KAAKjG,GAAOqD,QAAO,CAAC6C,EAA6BzF,KAC9D,MAAM0F,EAAOnG,EAAMS,GAEnB,OAAI0F,SAAuD,mBAATA,IAKhDD,EAAOzF,GADG,aAARA,GAAsC,iBAAT0F,EACjBC,EAAAA,SAAS7F,IAAI4F,GAAOE,GACX,iBAAVA,EACFA,EAEFN,GAAcM,EAAOzB,KAEA,mBAAduB,EAAKtG,KACPkG,GAAcI,EAAMvB,GAEpBuB,GAbPD,CAeI,GACZ,CAAE,GAEE,CACLrG,KAAMiG,GAAWjG,EAAM+E,GACvBoB,WAAYA,EACZhG,QACD,EAGUsG,GAAgB,CAC3BnG,EACAyE,KAEA,MAAM/E,KAAEA,EAAIG,MAAEA,EAAKgG,SAAEA,EAAQhB,KAAEA,KAASuB,GAAapG,EAIrD,MAAO,IAFa4F,GAAc,CAAElG,OAAMmG,WAAUhG,SAAS4E,MAIxD2B,EACJ,ECvCa,SAAAC,GAAYrI,EAAoBhC,GAC9Cc,EAAAA,QAAuB,iBAANd,EAAgBsK,EAAqBA,uBAEtD,IAAMvG,EAAO/B,EAAMC,MAAMjC,GAEnBuK,EAAc,SAACvK,GAAE,OAAKqK,GAAYrI,EAAOhC,EAAG,EAElD,MAAO,CACL6J,SAAQ,WACN,QAAS9F,EAAKC,KAAK6F,QACpB,EACDW,OAAM,WACJ,OAAOzG,EAAK/D,KAAOqG,WACpB,EACDoE,aAAY,WACV,OACE1G,EAAKC,KAAK0G,QACVH,EAAYxG,EAAKC,KAAK0G,QAAQjF,cAAckF,SAAS5G,EAAK/D,GAE7D,EACD4K,eAAc,WACZ,OAAOC,KAAKL,UAAYK,KAAKJ,cAC9B,EACDK,YAAW,WACT,OAAQD,KAAKD,gBACd,EACDG,wBAAyB,WAAA,OACvBhH,EAAKC,KAAKyB,aAAewB,OAAO6C,KAAK/F,EAAKC,KAAKyB,aAAavB,OAAS,CAAC,EACxE8G,yBAAwB,WAItB,OAHA1H,EAAAA,mBAAmB,0CAA2C,CAC5DC,QAAS,2CAEJsH,KAAKE,yBACb,EACDE,WAAU,WACR,OAAOjJ,EAAMkJ,OAAOC,SAASC,IAAIpL,EAClC,EACDqL,UAAS,WACP,OAAOrJ,EAAMkJ,OAAOI,QAAQF,IAAIpL,EACjC,EACDuL,UAAS,WACP,OAAOvJ,EAAMkJ,OAAOM,QAAQJ,IAAIpL,EACjC,EACDuF,IAAG,WACD,OAAOxB,CACR,EACD0H,UAAsB,WAAA,IAAZC,0DAsBR,OArBA,SAASC,EACP3L,GAEiB,IADjByL,yDAAsB,GACtBG,yDAAgB,EAEV7H,EAAO/B,EAAMC,MAAMjC,GACzB,OAAK+D,GAIL0H,EAAUI,KAAK7L,GAEV+D,EAAKC,KAAK0G,SAIXgB,IAAUA,GAAkB,IAAVE,KACpBH,EAAYE,EAAiB5H,EAAKC,KAAK0G,OAAQe,EAAWG,EAAQ,IAE7DH,GANEA,GANAA,CAaX,CACOE,CAAiB5H,EAAKC,KAAK0G,OACnC,EACDoB,YAE4C,WAAA,IAD1CJ,0DACAK,EAA0CC,UAAA9H,OAAA,EAAA8H,UAAA,QAAA1C,EAqC1C,OAnCA,SAAS2C,EACPjM,GAEiB,IADjB8L,yDAAwB,GACxBF,yDAAgB,EAEhB,OAAIF,IAAUA,GAAkB,IAAVE,IACP5J,EAAMC,MAAMjC,IAML,eAAhB+L,GAEkBxB,EAAYvK,GAAIyF,cAExByG,SAAQ,SAAC/G,GACnB2G,EAAYD,KAAK1G,GACjB2G,EAAcG,EAAgB9G,EAAQ2G,EAAaF,EAAQ,EAC7D,IAGkB,gBAAhBG,GACiBxB,EAAYvK,GAAImM,aAExBD,SAAQ,SAAC/G,GAClB2G,EAAYD,KAAK1G,GACjB2G,EAAcG,EAAgB9G,EAAQ2G,EAAaF,EAAQ,EAC7D,IAGKE,GAEFA,CACT,CACOG,CAAgBjM,EACxB,EACDyF,YAAW,WACT,OAAOwB,OAAOmF,OAAOrI,EAAKC,KAAKyB,aAAe,CAAA,EAC/C,EACD0G,WAAU,WACR,OAAOpI,EAAKC,KAAK/B,OAAS,EAC3B,EACDoK,YAAW,SAACC,GACV,IACE,IAAMC,EAAaxI,EAUnB,OATAjD,EAAAA,SAAW+J,KAAKD,iBAAkB4B,EAAyBA,2BAC3D1L,UACEuJ,GAAYrI,EAAOuK,EAAWvI,KAAK0G,QAAQb,WAC3C4C,EAAAA,4BAEF3L,EAAS,QACPyL,EAAWG,MAAMC,QAAQJ,EAAYhC,GACrCqC,EAAAA,oBAEK,CAMT,CALE,MAAOC,GAIP,OAHIP,GACFA,EAAQO,IAEH,CACT,CACD,EACDC,YAAYrF,SAAAA,EAAwB6E,GAClC,IAAMS,EAAUvF,EAAqBxF,EAAMC,MAAOwF,GAE5CuF,EAAgBjJ,EAEtB,IACEjD,EAAAA,QAAU+J,KAAKhB,WAAYoD,EAA8BA,gCACzDnM,EAAS,QACPkM,EAAcN,MAAMQ,UAClBH,EAAQ3I,KAAI,SAACqD,GAAQ,OAAKA,EAAS1D,IAAI,IACvCiJ,EACAzC,GAEF4C,EAA0BA,4BAG5B,IAAMC,EAAc,CAAA,EA2DpB,OAzDAL,EAAQb,SAAQ,SAAiC9E,GAAA,IAAxBmF,IAANxI,KAAkBqE,IAAAA,OAOnC,GANAtH,UACEyL,EAAWG,MAAMW,QAAQL,EAAeT,EAAYhC,GACpD+C,EAAAA,wBAIGlF,EAAL,CAIAtH,EAAS,SACNyJ,EAAYgC,EAAWvM,IAAI4K,iBAC5B4B,EAAAA,2BAGF,IAAMe,EAAkBhD,EAAYgC,EAAWvM,IAAI8L,aAAY,GAE/DhL,EAAAA,SACGyM,EAAgB5C,SAASqC,EAAchN,KACtCgN,EAAchN,KAAOuM,EAAWvM,GAClCwN,EAAwBA,0BAG1B,IAAMC,EACJlB,EAAWvI,KAAK0G,QAAU1I,EAAMC,MAAMsK,EAAWvI,KAAK0G,QAExD5J,EAAAA,QACE2M,EAAkBzJ,KAAK6F,SACvB4C,EAA0BA,4BAG5B3L,UACE2M,IACIA,IAAsBzL,EAAMC,MAAMsK,EAAWvM,IACjD0N,EAAAA,wBAGED,EAAkBzN,KAAOgN,EAAchN,KACpCoN,EAAYK,EAAkBzN,MACjCoN,EAAYK,EAAkBzN,IAAM,IAGtCoN,EAAYK,EAAkBzN,IAAI6L,KAAKU,GAlCzC,CAoCF,IAEAtF,OAAO6C,KAAKsD,GAAalB,SAAQ,SAACyB,GAChC,IACMC,EAAa5L,EAAMC,MAAM0L,GAE/B7M,UACE8M,EAAWlB,MAAMmB,WAJAT,EAAYO,GAIWC,EAAYrD,GACpDuD,EAAAA,2BAEJ,KAEO,CAMT,CALE,MAAOjB,GAIP,OAHIP,GACFA,EAAQO,IAEH,CACT,CACD,EACDkB,iBAAgB,WACd,OAAO5D,GAAcpG,EAAKC,KAAMhC,EAAM0C,QAAQ+D,SAC/C,EACD5C,WAAU,SAACkG,GACT,IAAM9J,EAAQ,CAACjC,YAAO6K,KAAKiB,aAAY,EAAMC,KAAc7E,QACzD,SAACC,EAAO6G,GAEN,OADA7G,EAAM6G,GAAgBzD,EAAYyD,GAAczI,MACzC4B,CACR,GACD,CAAE,GAGJ,MAAO,CACLnB,WAAYhG,EACZiC,MAAAA,EAEH,EAMDgM,WAAuB,WAAA,IAAZvC,0DAIT,OAHApI,EAAAA,mBAAmB,4BAA6B,CAC9CC,QAAS,+BAEJsH,KAAKiB,YAAYJ,EACzB,EACDwC,iBAAgB,WACd,OAAQrD,KAAKL,WAAazG,EAAKC,KAAK0G,MACtC,EAEJ,CC9Qc,SAAUyD,GACtBzD,EACA0D,EACAC,EACAC,GAiBA,IAfA,IAAIvE,EAAuB,CACzBW,OAAAA,EACA6D,MAAO,EACPC,MAAO,UAGLC,EAAY,EACdC,EAAS,EAETC,EAAS,EACTC,EAAU,EACVC,EAAU,EACVC,EAAU,EAGHC,EAAI,EAAGC,EAAMZ,EAAKlK,OAAQ6K,EAAIC,EAAKD,IAAK,CAC/C,IAAME,EAAMb,EAAKW,GAWjB,GANAD,EAAUG,EAAIC,IAAMD,EAAIE,YAExBP,EAAUK,EAAIG,KAAOH,EAAII,WAAa,EAEtCR,EAAUI,EAAIC,IAAMD,EAAIE,YAAc,IAGnCT,GAAUO,EAAIG,KAAOV,GACrBC,GAAUE,GAAWF,GACrBF,GAXQQ,EAAIG,KAAOH,EAAII,WAWCZ,GAM3B,GAFA1E,EAAOwE,MAAQQ,EAEVE,EAAIK,OAUF,CAEL,GAAIhB,EAAOO,EAAS,CAClB9E,EAAOyE,MAAQ,SACf,KACF,CAAOzE,EAAOyE,MAAQ,OACxB,MAfMF,EAAOQ,IAASH,EAASG,GAEzBT,EAAOO,GACTF,EAASE,EACT7E,EAAOyE,MAAQ,WAEfC,EAAYG,EACZ7E,EAAOyE,MAAQ,QASrB,CAEA,OAAOzE,CACT,CCnDA,IAAMwF,GAAkB,SAAC7L,GAA+B,MACvC,iBAARA,EAAmBA,EAAOA,EAAKmF,IAAI,EAE5B,SAAA2G,GACdC,EACAC,GAEA,IAAIC,EAAaF,EAAQzL,KAAKN,KAGxBK,EAAa,CACjB/D,GAHOyP,EAAQzP,IAAM4P,EAAeC,cAIpC5L,oBAAqB6L,KAAKC,MAC1B/L,KAAItC,EAAA,CACFgC,KAAMiM,EACN9G,KAAM0G,GAAgBI,GACtBjG,YAAa6F,GAAgBI,GAC7B9L,MAAO,CAAE,EACTiB,OAAQ,CAAE,EACV4F,OAAQ,KACRb,UAAU,EACVrF,QAAQ,EACRvC,MAAO,GACPwD,YAAa,CAAE,GACZgK,EAAQzL,MAEbgM,KAAM,CAAE,EACR/P,QAAS,CAAE,EACXiL,OAAQ,CACNC,UAAU,EACVK,SAAS,EACTF,SAAS,GAEXoB,MAAO,CACLC,QAAS,WAAA,OAAM,CAAI,EACnBU,QAAS,WAAA,OAAM,CAAI,EACnBH,UAAW,WAAA,OAAM,CAAI,EACrBW,WAAY,WAAA,OAAM,CAAI,GAExBpL,IAAK,MAIP,GAAIsB,EAAKC,KAAKN,OAASsB,GAAWjB,EAAKC,KAAKN,OAASwC,OAAQ,CAC3D,IAAM+J,SACDtL,GACAZ,EAAKC,KAAKH,OAGfE,EAAKC,KAAKH,MAAQoD,OAAO6C,KAAK/F,EAAKC,KAAKH,OAAOqD,QAAO,SAACrD,EAAOS,GAU5D,OATI2C,OAAO6C,KAAKnF,GAAqBgG,SAASrG,GAG5CP,EAAKC,KAAKe,EAAsBT,IAAQA,GAAO2L,EAAY3L,GAG3DT,EAAMS,GAAOP,EAAKC,KAAKH,MAAMS,GAGxBT,CACR,GAAE,CAAE,GAGLE,EAAKC,KAAK6E,KAAO0G,GADjBI,EAAa5L,EAAKC,KAAKN,MAEvBK,EAAKC,KAAK0F,YAAc6F,GAAgBI,GAEV5L,EAAKC,KAAKN,OAASwC,SAE/CnC,EAAKC,KAAK6F,UAAW,EACrB5D,IAEJ,CAEIyJ,GACFA,EAAU3L,GAIZ,IAAMmM,EAAsBP,EAAWQ,MAEvC,GAAID,EAAqB,CA+BvB,GA9BAnM,EAAKC,KAAK0F,YACRwG,EAAoBxG,aACpBwG,EAAoBrH,MACpB9E,EAAKC,KAAK0F,YAEZ3F,EAAKC,KAAKH,aACJqM,EAAoBrM,OAASqM,EAAoBE,cAAgB,CAAE,GACpErM,EAAKC,KAAKH,OAGfE,EAAKC,KAAKc,OAAMpD,EAAAA,EAAA,CAAA,EACVwO,EAAoBpL,QAAU,CAAA,GAC/Bf,EAAKC,KAAKc,QAIboL,QAAoBrG,WAGpB9F,EAAKC,KAAK6F,SAAWqG,EAAoBrG,UAGvCqG,EAAoBxD,OACtBzF,OAAO6C,KAAKoG,EAAoBxD,OAAOR,SAAQ,SAAC5H,GAC1C,CAAC,UAAW,UAAW,YAAa,cAAcqG,SAASrG,KAC7DP,EAAK2I,MAAMpI,GAAO4L,EAAoBxD,MAAMpI,GAEhD,IAGE4L,EAAoBjQ,QAAS,CAC/B,IAAMoQ,EAAqB,CACzBrQ,GAAI+D,EAAK/D,GACTC,SAAS,GAGXgH,OAAO6C,KAAKoG,EAAoBjQ,SAASiM,SAAQ,SAACxD,GAChD3E,EAAK9D,QAAQyI,GAAQ,SAAC7E,GAAK,OACzBhE,UAAMM,cACJJ,EACAsQ,EACAxQ,EAAK,QAACM,cAAc+P,EAAoBjQ,QAAQyI,GAAO7E,GACxD,CACL,GACF,CAEIqM,EAAoBF,OACtBjM,EAAKiM,KAAOE,EAAoBF,KAEpC,CAEA,OAAOjM,CACT,CCjIA,MASauM,GAAkB,CAC7BtM,EACAyE,EACA8F,KAEA,IAAI7K,KAAEA,EAAIG,MAAEA,GAAUG,EAEtB,MAAMuM,EAhBY,EAAC7M,EAAsB+E,IACzB,iBAAT/E,GAAqBA,EAAKkF,aACP,WAAtBlF,EAAKkF,aACH1C,OACAuC,EAAS/E,EAAKkF,cACA,iBAATlF,EACPA,EACA,KASS8M,CAAY9M,EAAM+E,GAE/B,IAAK8H,EACH,OAGF1M,EAAQoD,OAAO6C,KAAKjG,GAAOqD,QAAO,CAAC6C,EAA6BzF,KAC9D,MAAM0F,EAAOnG,EAAMS,GAenB,OAbEyF,EAAOzF,GADL0F,QACY,KACW,iBAATA,GAAqBA,EAAKpB,aAC5B0H,GAAgBtG,EAAMvB,GACnB,aAARnE,GAAsBsD,MAAMC,QAAQmC,GAC/BA,EAAK5F,KAAK8F,GACD,iBAAVA,EACFA,EAEFoG,GAAgBpG,EAAOzB,KAGlBuB,EAETD,CAAM,GACZ,CAAE,GAEDwE,IACF1K,EAAMS,IAAMiK,GAGd,MAAMkC,EAAM,IACP5Q,EAAK,QAACM,cAAcoQ,EAAM,IACxB1M,KAIP,MAAO,IACF4M,EACH5H,KAAML,GAAiBC,EAAUgI,EAAI/M,MACtC,EAGUgN,GAAkB,CAC7B1M,EACAyE,KAEA,MAAQ/E,KAAMiN,EAAM9M,MAAO+M,KAAUxG,GAAapG,EAOlDlD,EAAS,aAL4BwI,IAATqH,GAAsC,iBAATA,QAE9CrH,IAATqH,QACqDrH,IAApDqH,EAAmC/H,aAIpCiI,EAAAA,4CAA4CpH,QAC1C,gBACAzF,EAAK0F,aACLD,QAAQ,wBAAyBxC,OAAO6C,KAAKrB,GAAUqI,KAAK,QAGhE,MAAMpN,KAAEA,EAAImF,KAAEA,EAAIhF,MAAEA,GAAWyM,GAC7BtM,EACAyE,IAGIiC,OAAEA,EAAM5F,OAAEA,EAAM4E,YAAEA,EAAWG,SAAEA,EAAQ5H,MAAEA,EAAKuC,OAAEA,GAAW4F,EAIjE,MAAO,CACL1G,OACAmF,OACAa,YAAaA,GAAeb,EAC5BhF,QACAiB,OAAQA,GAAU,CAAE,EACpB+E,WAAYA,EACZrF,SAAUA,EACVkG,SACAjF,YAXkB2E,EAAS3E,aAAe2E,EAAS2G,cAWvB,CAAE,EAC9B9O,MAAOA,GAAS,GACjB,EC/GG+O,GAAa,CAACC,EAAgBC,KAClC,GAAIA,EAAchN,OAAS,EACzB,MAAO,CAAE,CAAC+M,EAASjR,IAAKiR,GAE1B,MAAMhP,EAAQiP,EAAc9M,KAAI,EAAG4B,gBAAiBA,IAC9CmL,EAAmB,IAAKF,EAAUjN,KAAM,IAAKiN,EAASjN,KAAM/B,UAElE,OAAOiP,EAAchK,QAAO,CAACC,EAAOxB,KAClC,MAAMyL,EAAczL,EAAK1D,MAAM0D,EAAKK,YACpC,MAAO,IACFmB,KACAxB,EAAK1D,MAER,CAACmP,EAAYpR,IAAK,IACboR,EACHpN,KAAM,IACDoN,EAAYpN,KACf0G,OAAQuG,EAASjR,KAGtB,GAde,CAAE,CAACiR,EAASjR,IAAKmR,GAetB,EAGFE,GAAa,CACxBJ,EACAC,KACc,CACdlL,WAAYiL,EAASjR,GACrBiC,MAAO+O,GAAWC,EAAUC,KCMxB,SAAUI,GAAatP,GAC3B,MAAM0C,EAAU1C,GAASA,EAAM0C,QAEzB6M,EAAkD,IACtDD,GAAatP,GAEf,MAAO,CAMLwP,mBAAoB,CAClBC,EACAC,EACAC,EACAC,EAA0C,CAAC7N,GACzC/B,EAAMC,MAAM8B,EAAK/D,IAAIyC,QAEvB,MAAM8J,EAAavK,EAAMC,MAAMyP,GAGzBG,EAFaN,IAAIxN,KAAKwI,EAAWvM,IAAI6J,WAGvC0C,EACAvK,EAAMC,MAAMsK,EAAWvI,KAAK0G,QAEhC,IAAKmH,EAAc,OAEnB,MAAMC,EAAoBD,EAAa7N,KAAK/B,OAAS,GAiB/C8P,EAAa5D,GACjB0D,EAhB4BC,EAC1BA,EAAkB5K,QAAO,CAAC6C,EAAQ/J,KAChC,MAAMyC,EAAMmP,EAAW5P,EAAMC,MAAMjC,IACnC,GAAIyC,EAAK,CACP,MAAMuN,EAAiB,CACrBhQ,QACGgS,EAAAA,WAAWvP,IAGhBsH,EAAO8B,KAAKmE,EACb,CACD,OAAOjG,CAAM,GACZ,IACH,GAKF4H,EAAIM,EACJN,EAAIO,GAEAd,EACJU,EAAkB5N,QAClBlC,EAAMC,MAAM6P,EAAkBC,EAAWxD,QAErC4D,EAAoB,CACxBC,UAAW,IACNL,EACHX,eAEFiB,MAAO,MAmBT,OAhBoB7K,EAAqBxF,EAAMC,MAAOwP,GAE1CvF,SAAQ,EAAGnI,OAAMqE,aAEvBA,GACFmJ,IACGxN,KAAKA,EAAK/D,IACVqM,aAAaQ,GAASsF,EAAOE,MAAQxF,GACzC,IAIH0E,IACGxN,KAAK8N,EAAa7R,IAClB8M,YAAY2E,GAAS5E,GAASsF,EAAOE,MAAQxF,IAEzCsF,CAAM,EAMfG,WAAU,IACD5N,EAGT6N,SAAQ,IACCvQ,EAAMC,MAOf8B,KAAK/D,GACIqK,GAAYrI,EAAOhC,GAM5BwS,qBACE,MAAMC,EAAYxL,OAAO6C,KAAK9H,EAAMC,OAAOmC,KAAKpE,GAAe,CAC7DA,EACA6K,KAAK9G,KAAK/D,GAAI+N,sBAEhB,OAAOhH,EAAY0L,EACpB,EAEDC,SAASC,GCnJG,SAAa3Q,EAAoB2Q,GAC/C,IAAMC,EAAQ5Q,EAAMkJ,OAAOyH,GAC3B,MAAO,CACLE,SAAQ,SAAC7S,GACP,OAAO4S,EAAMxH,IAAIpL,EAClB,EACD8S,QAAO,WACL,OAA6B,IAAtBjI,KAAKkI,MAAM7O,MACnB,EACD8O,MAAK,WAEH,OADenI,KAAKkI,MACN,EACf,EACDE,KAAI,WACF,IAAM7G,EAASvB,KAAKkI,MACpB,OAAO3G,EAAOA,EAAOlI,OAAS,EAC/B,EACD6O,IAAG,WACD,OAAOnL,MAAMsL,KAAKN,EACnB,EACDO,KAAI,WACF,OAAOtI,KAAKkI,MAAM7O,MACnB,EACDkP,GAAE,SAACrE,GACD,OAAOlE,KAAKkI,MAAMhE,EACnB,EACDsE,IAAG,WACD,OAAOT,CACT,EAEJ,CDsHaU,CAAatR,EAAO2Q,GAM7BY,YACE,OAAOC,KAAKC,UAAU5I,KAAK2H,qBAC5B,EAED5M,kBAAoB8N,IAA2C,CAC7D7N,WACE6J,GAEA,IAAI3L,EE9JI,SACd0M,EACAf,GAEA,IAAIiE,EAAUlD,EAUd,MARuB,iBAAZkD,IACTA,EAAU9T,EAAK,QAACM,cAAcgE,EAAQA,SAAE,CAAE,EAAEwP,IAOvCnE,GACL,CACExL,KAAM,CACJN,KALWiQ,EAAQjQ,KAMnBG,MAAO,IAAK8P,EAAQ9P,UAGvBE,IACK2L,GACFA,EAAU3L,EAAM4P,EACjB,GAGP,CFmImBC,CAAiBF,GAAc,CAAC3P,EAAM0M,KAC/C,MAAM5H,EAAOL,GAAiBxG,EAAM0C,QAAQ+D,SAAU1E,EAAKC,KAAKN,MAEhEK,EAAKC,KAAK0F,YAAc3F,EAAKC,KAAK0F,aAAeb,EACjD9E,EAAKC,KAAK6E,KAAOA,EAEb6G,GACFA,EAAU3L,EAAM0M,EACjB,IAGCS,EAA4B,GAahC,OAXIwC,EAAa7P,OAAS6P,EAAa7P,MAAM3D,WAC3CgR,EAAgBrR,EAAK,QAACoK,SAAS4J,QAC7BH,EAAa7P,MAAM3D,UACnBgH,QAAmB,CAACC,EAAO+C,KACvBrK,EAAK,QAACiU,eAAe5J,IACvB/C,EAAM0E,KAAK0F,IAAI3L,kBAAkBsE,GAAOrE,WAAW6J,IAE9CvI,IACN,KAGEkK,GAAWtN,EAAMmN,EACzB,IAGH6C,oBAAsBC,IAAoC,CACxDC,OAAOvE,GACL,MAAM1L,EAAO0M,GAAgBsD,EAAgBhS,EAAM0C,QAAQ+D,UAC3D3H,EAAAA,QAAUkD,EAAKN,KAAM8F,EAAAA,uBAErB,MAAMxJ,EAA0B,iBAAd0P,GAA0BA,EAQ5C,OANI1P,GACFsD,EAAkBA,mBAAC,4CAA6C,CAC9DC,QAAS,gEAINgO,IACJ2C,eAAe,IACVlU,EAAK,CAAEA,MAAO,GAClBgE,SAEDiQ,QAAQjU,GAAM0P,EAClB,IAGHwE,eAAiBnQ,IAAqB,CACpCkQ,OAAOvE,GACEF,GAAWzL,GAAOA,IACnBA,EAAKC,KAAK0G,SAAWyJ,yBACvBpQ,EAAKC,KAAK0G,OAASrE,aAGrB,MAAMwC,EAAOL,GAAiBxG,EAAM0C,QAAQ+D,SAAU1E,EAAKC,KAAKN,MAChE5C,EAAAA,QAAmB,OAAT+H,EAAeW,EAAAA,uBACzBzF,EAAKC,KAAK0F,YAAc3F,EAAKC,KAAK0F,aAAeb,EACjD9E,EAAKC,KAAK6E,KAAOA,EAEb6G,GACFA,EAAU3L,EACX,MAKPyL,WAAWkE,EAAkCU,GAC3C9Q,EAAAA,mBAAmB,oBAAoBoQ,KAAiB,CACtDnQ,QAAS,2BAA2BmQ,oBAGtC,MAAM/N,EAAOkF,KAAKjF,kBAAkB8N,GAAc7N,aAE5C9B,EAAO4B,EAAK1D,MAAM0D,EAAKK,YAE7B,OAAKoO,GAIDA,EAAOpU,KACT+D,EAAK/D,GAAKoU,EAAOpU,IAGfoU,EAAOpQ,OACTD,EAAKC,KAAO,IACPD,EAAKC,QACLoQ,EAAOpQ,OAIPD,GAdEA,CAeV,EAEDsQ,SAAQ,IACCrS,EAGb,CG/PA,IAAasS,GAA0B,SAAAC,GAAAC,EAAAF,EAAQG,iBAAR,IAAAC,EAAAC,EAAAL,GAAA,SAAAA,IAAA,OAAAM,EAAA/J,KAAAyJ,GAAAI,EAAAG,MAAAhK,KAAAmB,UAAA,CAgBpC,OAhBoC8I,EAAAR,EAAA,CAAA,CAAAhQ,IAAA,WAAAjE,MAGrC,WACE,MAAO,CACLmC,QAAS,SAACuS,EAAiB/U,GAAiB,EAC5CgV,OAAQ,SAACD,EAAiB/U,GAAiB,EAC3CiV,MAAO,SAACF,EAAiB/U,GAAiB,EAC1C0C,KAAM,SAACqS,EAAiB/U,GAAiB,EACzCkV,KAAM,SAACH,EAAiB/U,GAAiB,EACzCmV,OAAQ,SACNJ,EACAK,EACA1Q,GACI,EAEV,KAAC4P,CAAA,CAhBoC,GAmBjBe,GAEpB,SAAAC,GAAAd,EAAAa,EAAQE,EAA0CA,sBAAlD,IAAAC,EAAAb,EAAAU,GAAA,SAAAA,IAAA,OAAAT,EAAA/J,KAAAwK,GAAAG,EAAAX,MAAAhK,KAAAmB,UAAA,CAAA,OAAA8I,EAAAO,EAAA,CAAA,GCZII,GAA+B,SAACC,GACpCA,EAAEC,gBACJ,EAKaC,GAAU,WAkBrB,SAAqB/U,EAAAA,EAA6BgV,GAAsBjB,EAAA/J,KAAA+K,GAAArO,EAAAsD,KAAA,aAAA,GAAAtD,EAAAsD,KAAA,kBAAA,GAAAtD,EAAAsD,KAAA,2BAAA,GAAAtD,EAAAsD,KAAA,yCAAA,GAAAtD,EAAAsD,KAAA,mBAV3B,MAAItD,EAAAsD,KAAA,uBAAA,GAAAtD,EAAAsD,KAAA,oCAAA,GAAAtD,EAAAsD,KAAA,iBAAA,GAAAtD,EAAAsD,KAAA,oBAAA,GAAAtD,EAAAsD,KAAA,wBAAA,GAU5BA,KAAKhK,MAALA,EAA6BgK,KAAUgL,WAAVA,EAChDhL,KAAKiL,oBAAsB,KAC3BjL,KAAKkL,kCAAoC,KAEzClL,KAAKmL,gBAAkB,KACvBnL,KAAKoL,6BAA+B,KAEpCpL,KAAKqL,iBAAmB,KAExBrL,KAAKsL,UAAY,KACjBtL,KAAKuL,aAAevL,KAAKwL,kBAEzBxL,KAAKyL,uBAELzL,KAAK0L,iBAAmB1L,KAAK2L,SAASC,KAAK5L,MAC3C6L,OAAOC,iBAAiB,SAAU9L,KAAK0L,kBAAkB,GACzDG,OAAOC,iBAAiB,WAAYlB,IAA8B,EACpE,CAuOC,OAvOAX,EAAAc,EAAA,CAAA,CAAAtR,IAAA,UAAAjE,MAED,WACEqW,OAAOE,oBAAoB,SAAU/L,KAAK0L,kBAAkB,GAC5DG,OAAOE,oBAAoB,WAAYnB,IAA8B,EACvE,GAAC,CAAAnR,IAAA,WAAAjE,MAEO,SAASqV,GACf,IAAMmB,EAAanB,EAAEhE,OACfT,EAAWpG,KAAKhK,MAAMqE,MAAMnB,KAAKsC,EAASA,WAAEd,MAKhDsR,aAAsB7R,SACtBiM,GACAA,EAASxO,KACToU,EAAWhE,SAAS5B,EAASxO,OAM/BoI,KAAKoL,6BAA+B,KACtC,GAAC,CAAA3R,IAAA,kBAAAjE,MAEO,WACN,OACSmH,EACLqD,KAAKhK,MAAMqE,MAAMqN,WAFQ,QAAzB1H,KAAKgL,WAAWnS,KAGhBmH,KAAKgL,WAAWlQ,KAAK1D,MAAM4I,KAAKgL,WAAWlQ,KAAKK,YAMlD6E,KAAKgL,WAAW5T,MAEpB,GAEA,CAAAqC,IAAA,uBAAAjE,MACQ,WAAoB,IAAAyW,EAAAjM,KAEG,QAAzBA,KAAKgL,WAAWnS,MAIpBmH,KAAKuL,aAAalK,SAAQ,SAAqB9E,KAAZgB,QAKjC0O,EAAKjW,MAAMqE,MAAMnB,OALUA,KAKA/D,IAAIqM,aAAY,SAACQ,GAC1CiK,EAAKX,UAAYtJ,CACnB,GACF,GACF,GAAC,CAAAvI,IAAA,gBAAAjE,MAEO,SACN0W,EACA9E,EACAC,GAIA,OAFqC6E,EAA7B7H,IAGA0G,EAAWoB,cAAgB9E,GAHE6E,EAAxBE,OAIFrB,EAAWoB,cAAgB9E,GAJD6E,EAAhB3H,KAKZwG,EAAWoB,cAAgB/E,GALC8E,EAAVG,MAMjBtB,EAAWoB,cAAgB/E,CAMvC,GAAC,CAAA3N,IAAA,SAAAjE,MAEO,SAAO8W,GACb,OACEtM,KAAKqL,kBACLrL,KAAKqL,iBAAiB9D,UAAU1H,OAAO1K,KAAOmX,EAAYzM,OAAO1K,IACjE6K,KAAKqL,iBAAiB9D,UAAU7D,QAAU4I,EAAY5I,OACtD1D,KAAKqL,iBAAiB9D,UAAU5D,QAAU2I,EAAY3I,KAM1D,GAEA,CAAAlK,IAAA,qBAAAjE,MAGQ,SAAmB2M,GAAmB,IAAAoK,EAAAvM,KAEtCwM,EAAgCxM,KAAKoL,6BAC3C,OACEpL,KAAKmL,kBAAoBhJ,EAAchN,IACvCqX,EAEOA,EAGFrK,EAAchJ,KAAK/B,MAAMiF,QAAO,SAAC6C,EAAQ/J,GAC9C,IAAMyC,EAAM2U,EAAKvW,MAAMqE,MAAMnB,KAAK/D,GAAIuF,MAAM9C,IAS5C,OAPIA,GACFsH,EAAO8B,KAAInK,EAAA,CACT1B,GAAAA,GACGgS,EAAUA,WAACvP,KAIXsH,CACR,GAAE,GACL,GAEA,CAAAzF,IAAA,oBAAAjE,MASQ,SAAkBiX,GAAoB,IAAAC,EAAA1M,KAG5C,GACEyM,IAAiBzM,KAAKiL,qBACtBjL,KAAKkL,kCACL,CACA,IAAMhS,EAAO8G,KAAKhK,MAAMqE,MACrBnB,KAAK8G,KAAKkL,mCACVxQ,MAEH,GAAIxB,EACF,OAAOA,CAEX,CAgBA,OAdkB,SAAZyT,EAAarS,GACjB,IAAMpB,EAAOwT,EAAK1W,MAAMqE,MAAMnB,KAAKoB,GAAQI,MAE3C,OAAIxB,GAAQA,EAAKC,KAAK6F,SACb9F,EAGJA,EAAKC,KAAK0G,OAIR8M,EAAUzT,EAAKC,KAAK0G,QAHlB,KAMJ8M,CAAUF,EACnB,GAEA,CAAAhT,IAAA,mBAAAjE,MAIA,SAAiBiX,EAAsBrF,EAAWC,GAChD,IAAIlF,EAAgBnC,KAAK4M,kBAAkBH,GAE3C,GAAKtK,IAILnC,KAAKiL,oBAAsBwB,EAC3BzM,KAAKkL,kCAAoC/I,EAAchN,GAIrDgN,EAAchJ,KAAK0G,QACnBG,KAAK6M,cAAc1F,EAAUA,WAAChF,EAAcvK,KAAMwP,EAAGC,KAEpDrH,KAAKhK,MAAMqE,MAAMnB,KAAKiJ,EAAchN,IAAIyK,iBAEzCuC,EAAgBnC,KAAKhK,MAAMqE,MAAMnB,KAAKiJ,EAAchJ,KAAK0G,QAAQnF,OAG9DyH,GAAL,CAIAnC,KAAKoL,6BAA+BpL,KAAK8M,mBAAmB3K,GAC5DnC,KAAKmL,gBAAkBhJ,EAAchN,GAErC,IAAM4X,EAAWzJ,GACfnB,EACAnC,KAAKoL,6BACLhE,EACAC,GAIF,GAAKrH,KAAKgN,OAAOD,GAAjB,CAIA,IAAIvF,EAAQxH,KAAKsL,UAGZ9D,GACHxH,KAAKhK,MAAMqE,MAAMnB,KAAKiJ,EAAchN,IAAI8M,YACtCjC,KAAKuL,aAAahS,KAAI,SAAC0T,GAAU,OAAKA,EAAW/T,SACjD,SAACgU,GACC1F,EAAQ0F,CACV,IAIJ,IAAMC,EAAgBhL,EAAchJ,KAAK/B,MAAM2V,EAASrJ,OAClD6C,EACJ4G,GAAiBnN,KAAKhK,MAAMqE,MAAMnB,KAAKiU,GAAezS,MAUxD,OARAsF,KAAKqL,iBAAmB,CACtB9D,iBACKwF,GAAQ,CAAA,EAAA,CACXxG,YAAAA,IAEFiB,MAAAA,GAGKxH,KAAKqL,gBA1BZ,CAfA,CA0CF,GAAC,CAAA5R,IAAA,eAAAjE,MAED,WACE,OAAOwK,KAAKqL,gBACd,KAACN,CAAA,CA1QoB,KAAVA,mBACY,ICxBlB,IAAMqC,GAAe,SAC1BvC,EACAwC,GAGA,GAA+B,IAA3BA,EAAgBhU,gEAAmC,CACrD,IAAAiU,EAA0BD,EAAgB,GAAGE,wBAArCC,IAAAA,MAAOC,IAAAA,OACTC,EAASL,EAAgB,GAAGM,WAAU,GAa5C,OAXAD,EAAOE,MAAMb,SAAqB,WAClCW,EAAOE,MAAMrJ,KAAc,QAC3BmJ,EAAOE,MAAMvJ,IAAa,QAC1BqJ,EAAOE,MAAMJ,MAAK,GAAAK,OAAML,EAAS,MACjCE,EAAOE,MAAMH,OAAM,GAAAI,OAAMJ,EAAU,MACnCC,EAAOE,MAAME,cAAgB,OAC7BJ,EAAOK,UAAUC,IAAI,eAErBC,SAASC,KAAKC,YAAYT,GAC1B7C,EAAEuD,aAAaC,aAAaX,EAAQ,EAAG,GAEhCA,CACT,CAMA,IAAMY,EAAYL,SAAS3Y,cAAc,OA0BzC,OAzBAgZ,EAAUV,MAAMb,SAAW,WAC3BuB,EAAUV,MAAMrJ,KAAO,QACvB+J,EAAUV,MAAMvJ,IAAa,QAC7BiK,EAAUV,MAAMJ,MAAQ,OACxBc,EAAUV,MAAMH,OAAS,OACzBa,EAAUV,MAAME,cAAgB,OAChCQ,EAAUP,UAAUC,IAAI,yBAExBX,EAAgBhM,SAAQ,SAACzJ,GACvB,IAAqCA,EAAAA,EAAI2V,wBAAjCC,IAAAA,MAAOC,IAAAA,OAAQpJ,IAAAA,IAAKE,IAAAA,KACtBmJ,EAAS9V,EAAI+V,WAAU,GAE7BD,EAAOE,MAAMb,SAAqB,WAClCW,EAAOE,MAAMrJ,KAAI,GAAAsJ,OAAMtJ,EAAQ,MAC/BmJ,EAAOE,MAAMvJ,IAAG,GAAAwJ,OAAMxJ,EAAO,MAC7BqJ,EAAOE,MAAMJ,MAAK,GAAAK,OAAML,EAAS,MACjCE,EAAOE,MAAMH,OAAM,GAAAI,OAAMJ,EAAU,MACnCC,EAAOK,UAAUC,IAAI,eAErBM,EAAUH,YAAYT,EACxB,IAEAO,SAASC,KAAKC,YAAYG,GAC1BzD,EAAEuD,aAAaC,aAAaC,EAAWzD,EAAE0D,QAAS1D,EAAE2D,SAE7CF,CACT,ECtCaG,GAA6B,SAAAC,GAAA/E,EAAA8E,EAAQhF,IAAR,IAAAI,EAAAC,EAAA2E,GAAA,SAAAA,IAAA,IAAAxC,EAAAlC,EAAA/J,KAAAyO,GAAA,IAAA,IAAAE,EAAAxN,UAAA9H,OAAA4C,EAAA,IAAAc,MAAA4R,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAA3S,EAAA2S,GAAAzN,UAAAyN,GAaV,OAbUlS,EAAAmS,EAAA5C,EAAApC,EAAAiF,KAAA9E,MAAAH,EAAA,CAAA7J,MAAA6N,OAAA5R,KAAA,4BAAA,GAAAS,EAAAmS,EAAA5C,GAAA,kBAAA,GAAAvP,EAAAmS,EAAA5C,GAAA,aAYR,MAAIvP,EAAAmS,EAAA5C,GAAA,4BACR,IAAEA,CAAA,CAgW7B,OAhW6BhC,EAAAwE,EAAA,CAAA,CAAAhV,IAAA,YAAAjE,MAE9B,WACEwK,KAAKnG,QAAQ7D,MAAMsB,QAAQyX,aAC7B,GAAC,CAAAtV,IAAA,WAAAjE,MAED,WAAQ,IAAA+W,EAAAvM,KACAhK,EAAQgK,KAAKnG,QAAQ7D,MAE3B,MAAO,CACL2B,QAAS,SAACuS,EAAiB/U,GAGzB,OAFAa,EAAMsB,QAAQqE,OAAOxG,EAAI+U,GAElBqC,EAAKyC,SAAQ,SAACrY,GACnBA,EAAWwT,OAAOD,EAAI/U,GACtBwB,EAAWyT,MAAMF,EAAI/U,GACrBwB,EAAW0T,KAAKH,EAAI/U,EACtB,GACD,EACDgV,OAAQ,SAACD,EAAiB/U,GACxB,IAAM8Z,EAAoB1C,EAAK2C,sBAC7BhF,EACA,aACA,SAACW,GACCA,EAAEvF,MAAM6J,kBAER,IAAIC,EAAwB,GAE5B,GAAIja,EAAI,CACN,IAAQkF,EAAUrE,EAAVqE,MACFgV,EAAqBhV,EAAMwN,SAAS,YAAYK,OAChCqE,EAAK1S,QAAQyV,qBAAqBzE,IASnCwE,EAAmBvP,SAAS3K,MAC/Cia,EAAwBC,EAAmBhS,QACzC,SAACkS,GACC,IAAMtO,EAAc5G,EACjBnB,KAAKqW,GACLtO,aAAY,GACTL,EAAYvG,EAAMnB,KAAKqW,GAAY3O,WAAU,GAGnD,OAAIK,EAAYnB,SAAS3K,KAAOyL,EAAUd,SAAS3K,EAKrD,KAICia,EAAsBtP,SAAS3K,IAClCia,EAAsBpO,KAAK7L,EAE/B,CAEAa,EAAMsB,QAAQsE,aAAa,WAAYwT,EACzC,IAGII,EAAgBjD,EAAK2C,sBAAsBhF,EAAI,SAAS,SAACW,GAC7DA,EAAEvF,MAAM6J,kBAER,IACME,EADYrZ,EAAVqE,MACyBwN,SAAS,YAAYK,MAEhDuH,EAAgBlD,EAAK1S,QAAQyV,qBAAqBzE,GAClD6E,EAAwBnD,EAAKoD,0BAA0B7P,SAC3D3K,GAGEia,EAA4BC,EAAAA,GAE5BI,GAAiBC,GACnBN,EAAsBQ,OAAOR,EAAsBS,QAAQ1a,GAAK,GAChEa,EAAMsB,QAAQsE,aAAa,WAAYwT,KAC7BK,GAAiBJ,EAAmBhW,OAAS,GAEvDrD,EAAMsB,QAAQsE,aAAa,WAD3BwT,EAAwB,CAACja,IAI3BoX,EAAKoD,0BAA4BP,CACnC,IAEA,OAAO,WACLH,IACAO,IAEH,EACDpF,MAAO,SAACF,EAAiB/U,GACvB,IAAM2a,EAAkBvD,EAAK2C,sBAC3BhF,EACA,aACA,SAACW,GACCA,EAAEvF,MAAM6J,kBACRnZ,EAAMsB,QAAQsE,aAAa,UAAWzG,EACxC,IAGE4a,EAAwC,KAa5C,OAXIxD,EAAK1S,QAAQmW,0BACfD,EAAmBxD,EAAK2C,sBACtBhF,EACA,cACA,SAACW,GACCA,EAAEvF,MAAM6J,kBACRnZ,EAAMsB,QAAQsE,aAAa,UAAW,KACxC,KAIG,WACLkU,IAEKC,GAILA,IAEH,EACD1F,KAAM,SAACH,EAAiB+F,GACtB,IAAMC,EAAiB3D,EAAK2C,sBAC1BhF,EACA,YACA,SAACW,GAIC,GAHAA,EAAEvF,MAAM6J,kBACRtE,EAAEC,iBAEGyB,EAAK4D,WAAV,CAIA,IAAMC,EAAY7D,EAAK4D,WAAWE,iBAChCJ,EACApF,EAAE0D,QACF1D,EAAE2D,SAGC4B,GAILpa,EAAMsB,QAAQgZ,aAAaF,EAZ3B,CAaF,IAGIG,EAAkBhE,EAAK2C,sBAC3BhF,EACA,aACA,SAACW,GACCA,EAAEvF,MAAM6J,kBACRtE,EAAEC,gBACJ,IAGF,OAAO,WACLyF,IACAL,IAEH,EACDrY,KAAM,SAACqS,EAAiB/U,GACtB,IAAKa,EAAMqE,MAAMnB,KAAK/D,GAAIqM,cACxB,OAAO,WAAO,EAGhB0I,EAAGsG,aAAa,YAAa,QAE7B,IAAMC,EAAkBlE,EAAK2C,sBAC3BhF,EACA,aACA,SAACW,GACCA,EAAEvF,MAAM6J,kBAER,IAAQ9U,EAAmBrE,EAAnBqE,MAAO/C,EAAYtB,EAAZsB,QAEX+X,EAAqBhV,EAAMwN,SAAS,YAAYK,MAE9CuH,EAAgBlD,EAAK1S,QAAQyV,qBAAqBzE,GAC1B0B,EAAKoD,0BAA0B7P,SAC3D3K,KAKEka,EADEI,EACuBJ,GAAAA,OAAAA,EAAAA,GAAoBla,CAAAA,IAExB,CAACA,GAExBa,EAAMsB,QAAQsE,aAAa,WAAYyT,IAGzC/X,EAAQsE,aAAa,UAAWyT,GAEhC,IAAMqB,EAAerB,EAAmB9V,KACtC,SAACpE,GAAE,OAAKkF,EAAMnB,KAAK/D,GAAIuF,MAAM9C,OAG/B2U,EAAKoE,qBAAuBvD,GAC1BvC,EACA6F,EACAjC,EAAqBmC,uBAGvBrE,EAAKvB,WAAa,CAChBnS,KAAM,WACNzB,MAAOiY,GAGT9C,EAAK4D,WAAa,IAAIpF,GACpBwB,EAAK1S,QAAQ7D,MACbuW,EAAKvB,WAET,IAGI6F,EAAgBtE,EAAK2C,sBAAsBhF,EAAI,WAAW,SAACW,GAC/DA,EAAEvF,MAAM6J,kBAER5C,EAAKuE,aAAY,SAAC9F,EAAYoF,GACJ,QAApBpF,EAAWnS,MAQf7C,EAAMsB,QAAQyZ,KACZ/F,EAAW5T,MACXgZ,EAAU7I,UAAU1H,OAAO1K,GAL3Bib,EAAU7I,UAAU7D,OACW,UAA9B0M,EAAU7I,UAAU5D,MAAoB,EAAI,GAOjD,GACF,IAEA,OAAO,WACLuG,EAAGsG,aAAa,YAAa,SAC7BC,IACAI,IAEH,EACDvG,OAAQ,SACNJ,EACA8G,EACAnX,GAEAqQ,EAAGsG,aAAa,YAAa,QAE7B,IAAMC,EAAkBlE,EAAK2C,sBAC3BhF,EACA,aACA,SAACW,GAEC,IAAI/P,EACJ,GAFA+P,EAAEvF,MAAM6J,kBAEmB,mBAAhB6B,EAA4B,CACrC,IAAM9R,EAAS8R,IAEblW,EADE9F,EAAK,QAACiU,eAAe/J,GAChBlJ,EAAMqE,MAAMU,kBAAkBmE,GAAQlE,aAEtCkE,CAEX,MACEpE,EAAO9E,EAAMqE,MAAMU,kBAAkBiW,GAAahW,aAIpDuR,EAAKoE,qBAAuBvD,GAC1BvC,EACA,CAHUA,EAAEoG,eAIZxC,EAAqBmC,uBAEvBrE,EAAKvB,WAAa,CAChBnS,KAAM,MACNiC,KAAAA,GAGFyR,EAAK4D,WAAa,IAAIpF,GACpBwB,EAAK1S,QAAQ7D,MACbuW,EAAKvB,WAET,IAGI6F,EAAgBtE,EAAK2C,sBAAsBhF,EAAI,WAAW,SAACW,GAC/DA,EAAEvF,MAAM6J,kBACR5C,EAAKuE,aAAY,SAAC9F,EAAYoF,GACJ,aAApBpF,EAAWnS,OAOf7C,EAAMsB,QAAQ4Z,YACZlG,EAAWlQ,KACXsV,EAAU7I,UAAU1H,OAAO1K,GAJ3Bib,EAAU7I,UAAU7D,OACW,UAA9B0M,EAAU7I,UAAU5D,MAAoB,EAAI,IAO3C9J,GAAWsX,EAAAA,QAAWtX,EAAQuX,WAChCvX,EAAQuX,SAASpG,EAAWlQ,MAEhC,GACF,IAEA,OAAO,WACLoP,EAAGmH,gBAAgB,aACnBZ,IACAI,IAEJ,EAEJ,GAAC,CAAApX,IAAA,cAAAjE,MAEO,SACN8b,GAEA,IAAMtb,EAAQgK,KAAKnG,QAAQ7D,MAE3B,GAAKgK,KAAKmQ,WAAV,CAIA,IAAMQ,EAAuB3Q,KAAK2Q,qBAE5BP,EAAYpQ,KAAKmQ,WAAWoB,eAE9BvR,KAAKgL,YAAcoF,IAAcA,EAAU5I,OAC7C8J,EAAWtR,KAAKgL,WAAYoF,GAG1BO,IACFA,EAAqB5N,WAAWyO,YAAYb,GAC5C3Q,KAAK2Q,qBAAuB,MAG9B3Q,KAAKgL,WAAa,KAElBhV,EAAMsB,QAAQgZ,aAAa,MAC3Bta,EAAMsB,QAAQsE,aAAa,UAAW,MACtCoE,KAAKmQ,WAAWzZ,UAEhBsJ,KAAKmQ,WAAa,IArBlB,CAsBF,KAAC1B,CAAA,CA7WuC,GChBlB,SAAAgD,GACtB3K,EACA4K,EACAC,GACqB,IAArBC,yDAAoB,EAEhBC,EAAI,EACNC,EAAI,EACJC,EAAI,EACJC,EAAI,EACJrO,EAAQmD,EAAInD,MA8Bd,OA5BcgO,IAIDlN,QAMTsN,EAVUJ,EAUAnN,WACVwN,EAAIJ,EACJC,EAAc,WAAVlO,EAZMgO,EAYqBtN,IAZrBsN,EAYiCvF,OAC3C0F,EAbUH,EAaApN,OARVwN,EAAIH,EACJI,EANUL,EAMArN,YACVuN,EAPUF,EAOAtN,IACVyN,EAAc,WAAVnO,EARMgO,EAQqBpN,KARrBoN,EAQkCpN,KARlCoN,EAQ+CnN,YAQvDkN,IACFG,EAAIH,EAAcrN,IAAMqN,EAAcO,QAAQ5N,IAC9CyN,EAAIJ,EAAcnN,KAAOmN,EAAcO,QAAQ1N,KAC/CwN,EACEL,EAAclN,WACdkN,EAAcO,QAAQ5F,MACtBqF,EAAcO,QAAQ1N,KACtBmN,EAAcQ,OAAO3N,KACrBmN,EAAcQ,OAAO7F,MACvB2F,EAAIJ,GAGD,CACLvN,IAAG,GAAAwJ,OAAKgE,EAAK,MACbtN,KAAI,GAAAsJ,OAAKiE,EAAK,MACdtE,MAAK,GAAAK,OAAKkE,EAAK,MACftE,iBAAWuE,EAAC,MAEhB,GD9BavD,GAQoB0D,wBAAAA,gBAAgBC,EAAOA,WElBjD,MAAMC,GAAwB,KACnC,MAAMjC,UAAEA,EAASkC,iBAAEA,EAAgBC,QAAEA,GAAY1c,GAC9CsB,IAAW,CACViZ,UAAWjZ,EAAMiZ,UACjBkC,iBAAkBnb,EAAM0C,QAAQuW,UAChCmC,QAASpb,EAAM0C,QAAQ0Y,YAIrBxc,EAAUJ,IAehB,OAbAa,EAAAA,WAAU,KACHT,IAIAwc,EAKLxc,EAAQyc,SAJNzc,EAAQ0c,UAIM,GACf,CAACF,EAASxc,IAERqa,EAIEpb,EAAK,QAACM,cAAcod,kBAAiB,CAC1CC,UAAWL,EAAiBK,UAC5B/E,MAAO,IACF6D,GACDrB,EAAU7I,UACVJ,EAAUA,WAACiJ,EAAU7I,UAAU1H,OAAOjI,KACtCwY,EAAU7I,UAAUhB,aAClBY,EAAUA,WAACiJ,EAAU7I,UAAUhB,YAAY3O,KAC7C0a,EAAiBV,WAEnBgB,gBAAiBxC,EAAU5I,MACvB8K,EAAiB9K,MACjB8K,EAAiBO,QACrBC,WAAYR,EAAiBQ,YAAc,kBACvCR,EAAiB1E,OAAS,IAEhCmF,UAAW3C,EAAU7I,UAAU1H,OAAOjI,MAnB/B,IAoBP,EC1CSob,GAAS,EAAG3d,eACvB,MAAMW,EAAQJ,aAAWH,GAEnBM,EAAUO,EAAOA,SAAC,IAAMN,EAAMqE,MAAMoN,aAAawL,SAASjd,IAAQ,CACtEA,IAGF,OAAKD,EAKHf,EAAAA,sBAACU,EAAoBH,SAAS,CAAAC,MAAOO,GACnCf,UAAAM,cAAC+c,GAAwB,MACxBhd,GANI,IAQP,ECfS6d,GAAkC,CAC7C9b,MAAO,CAAE,EACTiJ,OAAQ,CACNM,QAAS,IAAIwS,IACb7S,SAAU,IAAI6S,IACd1S,QAAS,IAAI0S,KAEf/C,UAAW,KACXvW,QAAS,CACPuZ,cAAe,IAAM,KACrBxZ,SAAU,EAAGhB,YAAaA,EAC1Bya,gBAAiB,IAAM,KACvBzV,SAAU,CAAE,EACZ2U,SAAS,EACTnC,UAAW,CACT5I,MAAO,MACPqL,QAAS,oBAEXI,SAAWjd,GACT,IAAIyY,GAAqB,CACvBzY,QACAga,yBAAyB,EACzBV,qBAAuBzE,KAAoBA,EAAEyI,UAEjDC,eAAgB,SAIPC,GAA0B,CACrCC,QC4Z2B,SAC3Btc,EACAkD,GAEA,OAAAxD,EAAAA,EAAA,GAxac,SACdM,EACAkD,GAGA,IAAMqZ,EAAsB,SAC1B5Y,EACA6Y,EACAC,GA6CA,GAnCwB,SAAlBC,EAAmB1e,EAAYwe,GACnC,IAAMza,EAAO4B,EAAK1D,MAAMjC,GAEM,iBAAnB+D,EAAKC,KAAKN,MACnB5C,EAAS,QACPkB,EAAM0C,QAAQ+D,SAAS1E,EAAKC,KAAK6E,MACjCW,EAAqBA,sBAACC,QACpB,cAAa,GAAAiP,OACT3U,EAAKC,KAAKN,KAAamF,QAKjC7G,EAAMC,MAAMjC,UACP+D,GAAI,GAAA,CACPC,KAAItC,EAAAA,EAAA,CAAA,EACCqC,EAAKC,MAAI,GAAA,CACZ0G,OAAQ8T,MAIRza,EAAKC,KAAK/B,MAAMiC,OAAS,WACpBlC,EAAMC,MAAMjC,GAAIgE,KAAKH,MAAM3D,SAClC6D,EAAKC,KAAK/B,MAAMiK,SAAQ,SAACyS,GAAW,OAClCD,EAAgBC,EAAa5a,EAAK/D,QAItCiH,OAAOmF,OAAOrI,EAAKC,KAAKyB,aAAayG,SAAQ,SAAC9G,GAAY,OACxDsZ,EAAgBtZ,EAAcrB,EAAK/D,OAIvC0e,CAAgB/Y,EAAKK,WAAYwY,GAE5BA,GAAY7Y,EAAKK,aAAeK,EAAAA,UAArC,CAIA,IAAMqE,EAASkU,EAAqBJ,GAEpC,GAAyB,UAArBC,EAAY/a,KAYhBgH,EAAO1G,KAAKyB,YAAYgZ,EAAYze,IAAM2F,EAAKK,eAZ/C,CACE,IAAMuI,EAAQkQ,EAAYlQ,MAEb,MAATA,EACF7D,EAAO1G,KAAK/B,MAAMwY,OAAOlM,EAAO,EAAG5I,EAAKK,YAExC0E,EAAO1G,KAAK/B,MAAM4J,KAAKlG,EAAKK,WAIhC,CAdA,GAmBI4Y,EAAuB,SAACJ,GAC5B1d,UAAU0d,EAAUK,EAAAA,gBACpB,IAAMnU,EAAS1I,EAAMC,MAAMuc,GAE3B,OADA1d,UAAU4J,EAAQpC,EAAAA,sBACXoC,GAGHoU,EAAa,SAAbA,EAAc9e,GAClB,IAAMuM,EAAavK,EAAMC,MAAMjC,GAC7B4N,EAAa5L,EAAMC,MAAMsK,EAAWvI,KAAK0G,QAgB3C,GAdI6B,EAAWvI,KAAK/B,OAGlB8c,EAAIxS,EAAWvI,KAAK/B,OAAOiK,SAAQ,SAAC8S,GAAO,OAAKF,EAAWE,MAGzDzS,EAAWvI,KAAKyB,aAClBwB,OAAOmF,OAAOG,EAAWvI,KAAKyB,aAAarB,KAAI,SAACgB,GAAY,OAC1D0Z,EAAW1Z,MAIKwI,EAAW5J,KAAK/B,MAAM0I,SAAS3K,GAElC,CACf,IAAMif,EAAiBrR,EAAW5J,KAAK/B,MACvCgd,EAAexE,OAAOwE,EAAevE,QAAQ1a,GAAK,EACpD,KAAO,CACL,IAAMkf,EAAWjY,OAAO6C,KAAK8D,EAAW5J,KAAKyB,aAAa0Z,MACxD,SAACnf,GAAE,OAAK4N,EAAW5J,KAAKyB,YAAYzF,KAAQA,KAE1Ckf,UACKtR,EAAW5J,KAAKyB,YAAYyZ,EAEvC,EC5IgC,SAACld,EAAoBmD,GACvD8B,OAAO6C,KAAK9H,EAAMkJ,QAAQgB,SAAQ,SAAC5H,GACjC,IAAM8a,EAAWpd,EAAMkJ,OAAO5G,GAC1B8a,GAAYA,EAAShU,KAAOgU,EAAShU,IAAIjG,KAC3CnD,EAAMkJ,OAAO5G,GAAO,IAAI0Z,IACtBpW,MAAMsL,KAAKkM,GAAUlX,QAAO,SAAClI,GAAE,OAAKmF,IAAWnF,CAAE,KAGvD,GAAE,CDsIAqf,CAAqBrd,EAAOhC,UACrBgC,EAAMC,MAAMjC,IAGrB,MAAO,CAUL+F,+BAAsBJ,EAAgB6Y,EAAkBxe,GACtD,IAEMsf,EAFSV,EAAqBJ,GAEFxa,KAAKyB,YAAYzF,GAE/Csf,GACFR,EAAWQ,GAGbf,EAAoB5Y,EAAM6Y,EAAU,CAAE9a,KAAM,SAAU1D,GAAAA,GACvD,EASD6Y,aAAI0G,EAA0Bf,EAAmBjQ,GAE/C,IAAItM,EAAQ,CAACsd,GACT3X,MAAMC,QAAQ0X,KAChBjc,EAAAA,mBAAmB,4BAA6B,CAC9CC,QAAS,4BAEXtB,EAAQsd,GAEVtd,EAAMiK,SAAQ,SAACnI,GACbwa,EACE,CACEtc,WACG8B,EAAK/D,GAAK+D,GAEbiC,WAAYjC,EAAK/D,IAEnBwe,EACA,CAAE9a,KAAM,QAAS6K,MAAAA,GAErB,GACD,EASDwN,qBAAYpW,EAAgB6Y,EAAmBjQ,GAC7CgQ,EAAoB5Y,EAAM6Y,EAAU,CAAE9a,KAAM,QAAS6K,MAAAA,GACtD,EAMDiR,OAAM,SAAC/X,GACWD,EAAqBxF,EAAMC,MAAOwF,EAAU,CAC1DM,WAAW,EACXC,QAAQ,IAGFkE,SAAQ,SAAa9E,GAAA,IAAVrD,IAAAA,KACjBjD,WACGoE,EAAMnB,KAAKA,EAAK/D,IAAI4K,iBACrB6U,EAAAA,6BAEFX,EAAW/a,EAAK/D,GAClB,GACD,EAED0f,YAAW,SAACC,GACV,IAAMC,EACY,iBAATD,EAAoBnM,KAAKqM,MAAMF,GAASA,EAE3ClN,EAAYxL,OAAO6C,KAAK8V,GAAiBxb,KAAI,SAACpE,GAClD,IAAImF,EAASnF,EAMb,OAJIA,IAAOmU,EAAAA,uBACThP,EAASkB,EAAAA,WAGJ,CACLlB,EACAD,EACG6O,oBAAoB6L,EAAgB5f,IACpCiU,QAAO,SAAClQ,GAAI,OAAMA,EAAK/D,GAAKmF,CAAO,IAE1C,IAEA0F,KAAKnE,aAAaK,EAAY0L,GAC/B,EAQDmJ,cAAKnU,EAAwBqY,EAAqBvR,GAChD,IAAMxB,EAAUvF,EAAqBxF,EAAMC,MAAOwF,EAAU,CAC1DM,WAAW,IAGPgY,EAAY/d,EAAMC,MAAM6d,GAExBE,EAAoB,IAAIhC,IAE9BjR,EAAQb,SAAQ,SAAA7E,EAAuB0H,GAAK,IAAnBxC,IAANxI,KACX+W,EAAWvO,EAAWvM,GACtBigB,EAAkB1T,EAAWvI,KAAK0G,OAExCxF,EAAMnB,KAAK+b,GAAahT,YAAY,CAACgO,IAAW,SAACjO,GAC/C,MAAM,IAAIqT,MAAMrT,EAClB,IAGA7K,EAAM0C,QAAQwZ,gBACZ3R,EACAwT,EACA/d,EAAMC,MAAMge,IAGd,IACME,EADgBne,EAAMC,MAAMge,GACOjc,KAAK/B,MAE9C+d,EAAkBnH,IAAIsH,GAEtB,IAAMC,EAAWD,EAAmBzF,QAAQI,GAC5CqF,EAAmBC,GAAY,KAE/BL,EAAU/b,KAAK/B,MAAMwY,OAAOlM,EAAQQ,EAAG,EAAG+L,GAE1C9Y,EAAMC,MAAM6Y,GAAU9W,KAAK0G,OAASoV,CACtC,IAEAE,EAAkB9T,SAAQ,SAACjK,GACzB,IAAMiC,EAASjC,EAAMiC,OAErB6a,EAAI9c,GAAOoe,UAAUnU,SAAQ,SAAC7L,EAAOkO,GACrB,OAAVlO,GAIJ4B,EAAMwY,OAAOvW,EAAS,EAAIqK,EAAO,EACnC,GACF,GACD,EAED7H,aAAY,SAACzE,GACX4I,KAAK+O,cACL5X,EAAMC,MAAQA,CACf,EAED2X,YAAW,WACT/O,KAAKpE,aAAa,WAAY,MAC9BoE,KAAKpE,aAAa,UAAW,MAC7BoE,KAAKpE,aAAa,UAAW,MAC7BoE,KAAKsQ,aAAa,KACnB,EAKDxU,MAAK,WACHkE,KAAK+O,cACL/O,KAAKnE,aAAa,CAAA,EACnB,EAOD4Z,WAAU,SAAC1d,GACTA,EAAGZ,EAAM0C,QACV,EAED+B,aACEkM,SAAAA,EACA4N,GAUA,GARAve,EAAMkJ,OAAOyH,GAAWzG,SAAQ,SAAClM,GAC3BgC,EAAMC,MAAMjC,KACdgC,EAAMC,MAAMjC,GAAIkL,OAAOyH,IAAa,EAExC,IAEA3Q,EAAMkJ,OAAOyH,GAAa,IAAIqL,IAEzBuC,EAAL,CAIA,IAAMxT,EAAUvF,EAAqBxF,EAAMC,MAAOse,EAAgB,CAChEvY,QAAQ,EACRD,WAAW,IAGPyY,EAAuB,IAAIxC,IAAIjR,EAAQ3I,KAAI,SAAAqc,GAAO,SAAJ1c,KAAgB/D,EAAE,KACtEwgB,EAAQtU,SAAQ,SAAClM,GACfgC,EAAMC,MAAMjC,GAAIkL,OAAOyH,IAAa,CACtC,IACA3Q,EAAMkJ,OAAOyH,GAAa6N,CAX1B,CAYD,EAODxd,UACEyE,SAAAA,EACA7E,GAEgB4E,EAAqBxF,EAAMC,MAAOwF,EAAU,CAC1DO,QAAQ,EACRD,WAAW,IAGLmE,SAAQ,SAAAwU,GAAO,OAAO9d,EAAGZ,EAAMC,QAApB8B,KAA+B/D,IAAIgE,KAAKc,UAC5D,EAQD0B,OAAOxG,SAAAA,EAAYyC,GACZT,EAAMC,MAAMjC,KAIjBgC,EAAMC,MAAMjC,GAAIyC,IAAMA,EACvB,EAED0Y,aAAY,SAACF,GAETA,KACEA,EAAU7I,UAAU1H,OAAOjI,KAC1BwY,EAAU7I,UAAUhB,cAClB6J,EAAU7I,UAAUhB,YAAY3O,OAGvCT,EAAMiZ,UAAYA,EACnB,EAODhY,UAAUjD,SAAAA,EAAYkD,GACpBlB,EAAMC,MAAMjC,GAAIgE,KAAKQ,OAAStB,CAC/B,EAODP,QACE8E,SAAAA,EACA7E,GAEgB4E,EAAqBxF,EAAMC,MAAOwF,EAAU,CAC1DO,QAAQ,EACRD,WAAW,IAGLmE,SAAQ,SAAAyU,GAAO,OAAO/d,EAAGZ,EAAMC,QAApB8B,KAA+B/D,IAAIgE,KAAKH,SAC5D,EAED+c,WAAU,SAACL,GACT,GAAIA,EAAgB,CAClB,IAAMxT,EAAUvF,EAAqBxF,EAAMC,MAAOse,EAAgB,CAChEvY,QAAQ,EACRD,WAAW,IAGb8C,KAAKpE,aACH,WACAsG,EAAQ3I,KAAI,SAAAyc,GAAO,SAAJ9c,KAAgB/D,EAAE,IAErC,MACE6K,KAAKpE,aAAa,WAAY,MAGhCoE,KAAKpE,aAAa,UAAW,KAC/B,EAEJ,CAOOqa,CAAQ9e,EAAOkD,IAAM,GAAA,CAGxB6b,SAAQ,SACNne,GAKoBT,IAAAA,IAAY0I,KAAItI,IAGpCK,EAAGZ,EAAOG,EACZ,GAEJ,ED/aE6e,wBAAyB,CACvB,SACA,eACA,aACA,cACA,aACA,gBAEFC,iBAAmBjf,IAIjBiF,OAAO6C,KAAK9H,EAAMkJ,QAAQgB,SAASgV,IACjBtZ,MAAMsL,KAAKlR,EAAMkJ,OAAOgW,IAAc,IAE9ChV,SAASlM,IACVgC,EAAMC,MAAMjC,IACfgC,EAAMkJ,OAAOgW,GAAW1B,OAAOxf,EAChC,GACD,IAMJiH,OAAO6C,KAAK9H,EAAMC,OAAOiK,SAASlM,IAChC,MAAM+D,EAAO/B,EAAMC,MAAMjC,GAEzBiH,OAAO6C,KAAK/F,EAAKmH,QAAQgB,SAASgV,IACRnd,EAAKmH,OAAOgW,IAIlClf,EAAMkJ,OAAOgW,KACZlf,EAAMkJ,OAAOgW,GAAW9V,IAAIrH,EAAK/D,MAElC+D,EAAKmH,OAAOgW,IAAa,EAC1B,GACD,GACF,GASOC,GAAiB,CAC5Bzc,EACA0c,IAOOC,EAAAA,WACLhD,GACA,IACKN,GACHrZ,QAAS,IACJqZ,GAAmBrZ,WACnBA,IAGP4M,GACA8P,4HGtGEE,GAAc,SAAC1T,GACnB,IACE1C,EAGE0C,EAHF1C,OAAMqW,EAGJ3T,EAFF5J,KAAemI,IAAPlK,MAAmBwD,IAAAA,YACxB+b,EAAcnf,EACfuL,EADerL,IAEbkf,EAAkBjS,GAAWkS,UAAU9T,IAW7C,MAAO,CACL7J,KAXF6J,EAAUlM,EAAAA,EAAAA,EAAA,CAAA,EACL+f,GACAD,GAAc,GAAA,CACjBtW,cACKuW,EAAgBvW,QAChBA,GAELzI,IAAKmL,EAAWnL,KAAOgf,EAAgBhf,MAKvC0J,WAAAA,EACA1G,YAAAA,EAEJ,EAsBakc,GAAkB,SAAC1Q,GAC9B,IAAMhP,EAAQ,CAAA,EAmDd,OAlDqB,SAAf2f,EAAgBC,GACpB,IAAsDP,EAAAA,GAAYO,GAApDjU,IAAN7J,KAAkBoI,IAAAA,WAAY1G,IAAAA,YACtCxD,EAAM2L,EAAW5N,IAAM4N,EAEnBzB,GACFA,EAAWD,SAAQ,SAAC4V,EAAe/S,GACjC,IAIIuS,EAAAA,GAAYQ,GAHRC,IAANhe,KACYie,IAAZ7V,WACa8V,IAAbxc,YAEFsc,EAAU/d,KAAK0G,OAASkD,EAAW5N,GACnCiC,EAAM8f,EAAU/hB,IAAM+hB,EACtBnU,EAAW5J,KAAK/B,MAAM8M,GAAKgT,EAAU/hB,GACrC4hB,SACKG,GAAS,CAAA,EAAA,CACZ/d,KAAItC,EAAAA,EAAA,CAAA,EACCqgB,EAAU/d,MAAI,GAAA,CACjB/B,MAAO+f,GAAmB,GAC1Bvc,YAAawc,GAAyB,CAAE,MAG9C,IAGExc,GACFwB,OAAO6C,KAAKrE,GAAayG,SAAQ,SAACgT,GAChC,IAAAgD,EAIIZ,GAAY7b,EAAYyZ,IAHpB6C,IAANhe,KACYie,IAAZ7V,WACa8V,IAAbxc,YAEFmI,EAAW5J,KAAKyB,YAAYyZ,GAAY6C,EAAU/hB,GAElD+hB,EAAU/d,KAAK0G,OAASkD,EAAW5N,GACnCiC,EAAM8f,EAAU/hB,IAAM+hB,EACtBH,SACKG,GAAS,CAAA,EAAA,CACZ/d,KAAItC,EAAAA,EAAA,CAAA,EACCqgB,EAAU/d,MAAI,GAAA,CACjB/B,MAAO+f,GAAmB,GAC1Bvc,YAAawc,GAAyB,CAAE,MAG9C,IAIJL,CAAa3Q,GAENhP,CACT,yQCvFsB,EAAG/B,cAAawE,WAEX4E,IAArB5E,EAAQ+D,UACV3H,UAC8B,iBAArB4D,EAAQ+D,WACZb,MAAMC,QAAQnD,EAAQ+D,WACF,OAArB/D,EAAQ+D,SACV0Z,EAAAA,8BAIJ,MAAMC,EAAaviB,EAAMwiB,OAAO3d,GAE1B5C,EAAUqf,GACdiB,EAAWE,SACX,CAACtgB,EAAOugB,EAAeC,EAA4Btd,EAAOud,KACxD,IAAKD,EACH,OAGF,MAAME,QAAEA,KAAYC,GAAoBH,EAExC,IAAK,IAAIzT,EAAI,EAAGA,EAAI2T,EAAQxe,OAAQ6K,IAAK,CACvC,MAAM6T,KAAEA,GAASF,EAAQ3T,GACnB8T,EACJD,EAAK1e,OAAS,GAAiB,UAAZ0e,EAAK,IAA8B,SAAZA,EAAK,GAajD,GARE,CAACE,EAAeA,gBAACC,OAAQD,EAAAA,gBAAgBE,UAAUrY,SAHpCgY,EAAgBjf,OAM/Bif,EAAgBM,SAEhBN,EAAgBjf,KAAOif,EAAgBM,OAAO,IAI9C,CAAC,WAAY,eAAetY,SAASgY,EAAgBjf,OACrDmf,EACA,CACAJ,GAAYS,IACNlhB,EAAM0C,QAAQ0Z,gBAChBpc,EAAM0C,QAAQ0Z,eACZ8E,EACAX,EACAI,EACAzd,EAEH,IAEH,KACD,CACF,KAiCL,OA5BArF,EAAMwB,WAAU,KACTS,QAKiBwH,IAApB5E,EAAQ0Y,SACRtb,EAAQoD,MAAMoN,aAAa8K,UAAY1Y,EAAQ0Y,SAKjDtb,EAAQK,QAAQme,YAAY6C,IAC1BA,EAAc/F,QAAU1Y,EAAQ0Y,OAAO,GACvC,GACD,CAACtb,EAAS4C,EAAQ0Y,UAErBvd,EAAMwB,WAAU,KACdS,EAAQshB,WACL7R,IAAO,CACN8R,KAAMvhB,EAAQoD,MAAMqO,gBAEtB,KACEzR,EAAQoD,MAAMoN,aAAa2L,cAAcnc,EAAQoD,MAAM,GAE1D,GACA,CAACpD,IAECA,EAKHjC,gBAACS,EAAcF,SAAS,CAAAC,MAAOyB,GAC7BjC,EAAAM,cAAC0d,GAAQ,KAAA3d,IALJ,IAOP,oD1BnFiB,EAAGA,WAAUmjB,OAAMrf,WACtC,MAAM7B,QAAEA,EAAO+C,MAAEA,GAAUxE,IAErB2iB,GACJ/f,EAAAA,mBAAmB,uBAAwB,CACzCC,QAAS,yBAIb,MAAM+f,EAAWjB,UAAO,GAExB,IAAKiB,EAAShB,QAAS,CACrB,MAAMiB,EAAcvf,GAAQqf,EAE5B,GAAIE,EACFphB,EAAQW,QAAQgD,SAAS4Z,YAAY6D,QAChC,GAAIrjB,EAAU,CACnB,MAAM+Q,EAAWpR,EAAK,QAACoK,SAASuZ,KAAKtjB,GAE/B6D,EAAOmB,EAAMU,kBAAkBqL,GAAUpL,YAAW,CAAC9B,EAAM0M,KAC3DA,IAAQQ,IACVlN,EAAK/D,GAAKqG,aAELtC,KAGT5B,EAAQW,QAAQgD,SAASiW,YAAYhY,EACtC,CAEDuf,EAAShB,SAAU,CACpB,CAED,OAAOziB,UAAAM,cAACgG,EAAc,KAAG,0I2BxDrB,SAA2BtE,GAC/B,OAAQ4hB,GACE5f,IACN,MAAM6f,EAAS7hB,EAAU+E,EAAU/E,GAAW+E,IAC9C,OAAO/G,EAAAA,sBAAC4jB,EAAgB,IAAKC,KAAY7f,GAAS,CAGxD,sBCPM,SAAyBhC,GAC7B,OAAO,SAAU4hB,GACf,OAAQ5f,IACN,MAAME,EAAOX,EAAQvB,GACrB,OAAOhC,EAAAA,sBAAC4jB,EAAgB,IAAK1f,KAAUF,GAAS,CAEpD,CACF,6EH8F+B,WAAsB,IAArB7B,EAAQgK,UAAA9H,OAAA,QAAAoF,IAAA0C,UAAA,GAAAA,UAAA,GAAA,CAAA,EACvBiF,EAAqBjP,EAA5BC,MAAiBiJ,EAAWlJ,EAAXkJ,OAEzB,OACK6S,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,IACA/b,GAAK,GAAA,CACRC,MAAOgP,EAAW0Q,GAAgB1Q,GAAY,CAAE,EAChD/F,OAAMxJ,EAAAA,EAAA,CAAA,EACDqc,GAAmB7S,QAClBA,GAAU,KAGpB,2JAvFiC,SAACyY,EAAKC,GACrC,IAAeC,EAAyBD,EAAhC3hB,MAAoB6hB,IAAYF,EAAGG,IAC5BC,EAAyBL,EAAhC1hB,MAAoBgiB,IAAYN,EAAGO,IAC3CC,OAAOF,GAASG,QAAQN,GAExB,IAAMO,EAAqBpd,OAAO6C,KAAK+Z,GAAU3c,QAAO,SAACC,EAAOnH,GACd6jB,IAAT9f,EAAI1B,EAAKwhB,EAAS7jB,GAAdskB,IAE3C,OADAnd,EAAMnH,GAAM+D,EACLoD,CACR,GAAE,CAAE,GAECod,EAAqBtd,OAAO6C,KAAKka,GAAU9c,QAAO,SAACC,EAAOnH,GACdgkB,IAATjgB,EAAI1B,EAAK2hB,EAAShkB,GAAdwkB,IAE3C,OADArd,EAAMnH,GAAM+D,EACLoD,CACR,GAAE,CAAE,GAELgd,OAAOI,GAAoBH,QAAQC,EACrC"}