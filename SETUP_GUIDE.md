# Student Management System - Setup Guide

## Overview

This is a comprehensive student management system with ID card generation capabilities. The system uses **Fabric.js** as a free alternative to Builder.io for creating interactive, customizable ID card templates.

## Architecture

- **Frontend**: React 18 + Vite + Tailwind CSS + Fabric.js
- **Backend**: Node.js + Express.js + SQLite
- **Template Engine**: Fabric.js (free alternative to Builder.io)
- **Database**: SQLite (can be easily switched to MySQL/PostgreSQL)

## Features

### ✅ Student Management (CRUD)
- Add, edit, delete, and view students
- Photo upload and management
- Search and filter functionality
- Pagination support

### ✅ ID Card Template System
- **Fabric.js-based template editor** (free alternative to Builder.io)
- Drag-and-drop interface for designing templates
- Support for text, images, and shapes
- Dynamic placeholders for student data
- Real-time preview functionality
- Template management (create, edit, delete)

### ✅ ID Card Generation
- Generate cards using any template
- Automatic data population from student records
- Download generated cards as PNG images
- Card generation history tracking

## Quick Start

### 1. Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### 2. Installation

```bash
# Navigate to project directory
cd "Student Management"

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 3. Environment Setup

The `.env` files are already configured for development:

**Backend (.env)**:
```
PORT=5001
NODE_ENV=development
DB_TYPE=sqlite
DB_PATH=./database/students.db
FRONTEND_URL=http://localhost:5173
```

**Frontend (.env)**:
```
VITE_API_BASE_URL=http://localhost:5001/api
```

### 4. Database Setup

```bash
cd backend
npm run setup-db
```

This will:
- Create the SQLite database
- Set up all required tables
- Insert sample data (3 students)
- Create a default ID card template

### 5. Start the Application

**Terminal 1 - Backend:**
```bash
cd backend
npm run dev
# or
node server.js
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```

### 6. Access the Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5001/api/health

## Usage Guide

### Student Management

1. **View Students**: Navigate to the Students page to see all registered students
2. **Add Student**: Click "Add Student" to register a new student with photo upload
3. **Edit Student**: Click the edit icon to modify student information
4. **Search/Filter**: Use the search bar or filters to find specific students

### Template Management

1. **View Templates**: Go to Templates page to see all available templates
2. **Create Template**: 
   - Click "Create Template"
   - Use the Fabric.js-based editor to design your template
   - Add text elements with placeholders like `{first_name}`, `{student_id}`
   - Add image placeholders for student photos
   - Add shapes and styling elements
   - Save your template

3. **Edit Template**: Click edit icon on any template to modify it

### ID Card Generation

1. **Generate Cards**: 
   - Go to Card Generator page
   - Select a student from the dropdown
   - Choose a template
   - Click "Preview Card" to see how it will look
   - Click "Generate Card" to create and save the card

2. **View Generated Cards**: Each student's detail page shows their card generation history

## Template System (Fabric.js Alternative to Builder.io)

### Why Fabric.js?

Fabric.js is a powerful, free, open-source HTML5 canvas library that provides:
- Interactive object model on top of canvas element
- SVG-to-canvas parser
- Rich text support
- Animation support
- Free to use (no licensing costs like Builder.io)

### Template Features

- **Drag & Drop**: Move elements around the canvas
- **Text Elements**: Add dynamic text with student data placeholders
- **Image Placeholders**: Define areas for student photos
- **Shapes**: Add rectangles, circles, lines for design
- **Styling**: Colors, fonts, borders, etc.
- **Real-time Preview**: See changes immediately

### Available Placeholders

Use these in text elements to automatically populate student data:
- `{first_name}` - Student's first name
- `{last_name}` - Student's last name
- `{student_id}` - Student ID number
- `{email}` - Student email
- `{course}` - Course/program name
- `{year_of_study}` - Year of study
- `{phone}` - Phone number
- `{address}` - Address

## API Documentation

### Students API
- `GET /api/students` - Get all students (paginated)
- `POST /api/students` - Create student (with file upload)
- `GET /api/students/:id` - Get student by ID
- `PUT /api/students/:id` - Update student
- `DELETE /api/students/:id` - Delete student
- `GET /api/students/search/:query` - Search students

### Templates API
- `GET /api/cards/templates` - Get all templates
- `POST /api/cards/templates` - Create template
- `PUT /api/cards/templates/:id` - Update template
- `DELETE /api/cards/templates/:id` - Delete template

### Card Generation API
- `POST /api/cards/generate/:studentId` - Generate card
- `POST /api/cards/preview` - Preview card
- `GET /api/cards/generated/:studentId` - Get generated cards

## Troubleshooting

### Common Issues

1. **Port conflicts**: If port 5001 is in use, update the PORT in backend/.env
2. **Database issues**: Delete the database folder and run `npm run setup-db` again
3. **CSS issues**: Make sure Tailwind CSS is properly configured
4. **Fabric.js issues**: Ensure the canvas element is properly initialized

### Development Tips

1. **Hot Reload**: Both frontend and backend support hot reload
2. **Database Reset**: Delete `backend/database/` folder to reset database
3. **Sample Data**: The setup script includes sample students and templates
4. **File Uploads**: Photos are stored in `backend/uploads/photos/`
5. **Generated Cards**: Cards are saved in `backend/uploads/cards/`

## Production Deployment

1. **Environment Variables**: Update .env files for production
2. **Database**: Consider switching to PostgreSQL or MySQL for production
3. **File Storage**: Consider using cloud storage (AWS S3, etc.) for files
4. **Security**: Add authentication and authorization
5. **Build**: Run `npm run build` in frontend for production build

## License

MIT License - Free to use and modify.
