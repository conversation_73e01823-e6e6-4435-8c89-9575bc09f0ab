const express = require('express');
const cardController = require('../controllers/cardController');
const { validateCardTemplate } = require('../middleware/validation');

const router = express.Router();

// Template routes
router.get('/templates', cardController.getAllTemplates);
router.get('/templates/:id', cardController.getTemplateById);
router.post('/templates', validateCardTemplate, cardController.createTemplate);
router.put('/templates/:id', validateCardTemplate, cardController.updateTemplate);
router.delete('/templates/:id', cardController.deleteTemplate);

// Card generation routes
router.post('/generate/:studentId', cardController.generateCard);
router.post('/generate/:studentId/template/:templateId', cardController.generateCardWithTemplate);
router.get('/generated/:studentId', cardController.getGeneratedCards);
router.delete('/generated/:id', cardController.deleteGeneratedCard);

// Preview routes
router.post('/preview', cardController.previewCard);

// Bulk generation routes
router.post('/bulk-generate/:templateId', cardController.bulkGenerateCards);
router.get('/bulk-jobs', cardController.getBulkJobs);
router.get('/bulk-jobs/:jobId', cardController.getBulkJobStatus);

module.exports = router;
