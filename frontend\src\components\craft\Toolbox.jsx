import React from 'react'
import { useEditor } from '@craftjs/core'
import { Type, Image, Square } from 'lucide-react'
import { Text } from './Text'
import { ImagePlaceholder } from './ImagePlaceholder'
import { Container } from './Container'

export const Toolbox = () => {
  const { connectors } = useEditor()

  const placeholderTexts = [
    { label: 'Student Name', value: '{name}' },
    { label: 'Student ID', value: 'ID: {student_id}' },
    { label: 'Course', value: 'Course: {course}' },
    { label: 'Email', value: 'Email: {email}' },
    { label: 'Address', value: 'Address: {address}' }
  ]

  return (
    <div className="space-y-6">
      {/* Basic Elements */}
      <div className="card p-4">
        <h3 className="font-semibold text-gray-900 mb-4">Elements</h3>
        <div className="space-y-2">
          <div
            ref={(ref) => connectors.create(ref, <Text text="Sample Text" />)}
            className="toolbox-btn"
          >
            <Type className="h-4 w-4" />
            Text
          </div>
          
          <div
            ref={(ref) => connectors.create(ref, <ImagePlaceholder />)}
            className="toolbox-btn"
          >
            <Image className="h-4 w-4" />
            Photo Placeholder
          </div>
          
          <div
            ref={(ref) => connectors.create(ref, 
              <Container>
                <Text text="Container" />
              </Container>
            )}
            className="toolbox-btn"
          >
            <Square className="h-4 w-4" />
            Container
          </div>
        </div>
      </div>

      {/* Quick Placeholders */}
      <div className="card p-4">
        <h3 className="font-semibold text-gray-900 mb-4">Student Data</h3>
        <div className="space-y-1">
          {placeholderTexts.map((placeholder, index) => (
            <div
              key={index}
              ref={(ref) => connectors.create(ref, 
                <Text 
                  text={placeholder.value} 
                  fontSize={14}
                  color="#666666"
                />
              )}
              className="toolbox-btn text-xs py-1"
            >
              {placeholder.label}
            </div>
          ))}
        </div>
      </div>

      {/* Pre-made Components */}
      <div className="card p-4">
        <h3 className="font-semibold text-gray-900 mb-4">Templates</h3>
        <div className="space-y-2">
          <div
            ref={(ref) => connectors.create(ref,
              <Container backgroundColor="#ffffff" padding={15}>
                <Text text="STUDENT ID CARD" fontSize={18} fontWeight="bold" textAlign="center" />
                <ImagePlaceholder width={80} height={100} />
                <Text text="{name}" fontSize={16} fontWeight="bold" />
                <Text text="ID: {student_id}" fontSize={12} color="#666666" />
                <Text text="Course: {course}" fontSize={12} color="#666666" />
              </Container>
            )}
            className="toolbox-btn text-xs py-2"
          >
            Basic ID Card
          </div>
        </div>
      </div>
    </div>
  )
}
