import React from 'react'
import { useEditor } from '@craftjs/core'

export const SettingsPanel = () => {
  const { selected, actions, query } = useEditor((state, query) => {
    const currentNodeId = query.getEvent('selected').last()
    let selected

    if (currentNodeId) {
      selected = {
        id: currentNodeId,
        name: state.nodes[currentNodeId].data.name,
        settings: state.nodes[currentNodeId].related && state.nodes[currentNodeId].related.settings,
        isDeletable: query.node(currentNodeId).isDeletable()
      }
    }

    return {
      selected
    }
  })

  return (
    <div className="card p-4">
      <h3 className="font-semibold text-gray-900 mb-4">Properties</h3>
      
      {selected ? (
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">
              {selected.name}
            </h4>
            
            {selected.settings && React.createElement(selected.settings)}
            
            {selected.isDeletable && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => actions.delete(selected.id)}
                  className="btn-danger w-full"
                >
                  Delete Element
                </button>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="text-gray-500 text-sm">
          Select an element to edit its properties
        </div>
      )}
    </div>
  )
}
