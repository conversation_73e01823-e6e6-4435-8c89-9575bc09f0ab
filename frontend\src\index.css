@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #f9fafb;
    color: #111827;
  }
}

@layer components {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    border: none;
    cursor: pointer;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    line-height: 1;
    min-height: 2.5rem;
    padding: 0.625rem 1rem;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: #2563eb;
    color: white;
    height: 2.5rem;
    padding: 0.5rem 1rem;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #1d4ed8;
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #111827;
    height: 2.5rem;
    padding: 0.5rem 1rem;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: #e5e7eb;
  }

  .btn-danger {
    background-color: #dc2626;
    color: white;
    height: 2.5rem;
    padding: 0.5rem 1rem;
  }

  .btn-danger:hover:not(:disabled) {
    background-color: #b91c1c;
  }

  .btn-sm {
    height: 2rem;
    padding: 0 0.75rem;
    font-size: 0.75rem;
  }

  /* Ensure buttons with icons are properly aligned */
  .btn svg {
    flex-shrink: 0;
  }

  /* Toolbox specific styles */
  .toolbox-btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    padding: 0.5rem;
    text-align: left;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s;
    border: 1px solid #e5e7eb;
    background-color: #ffffff;
    color: #374151;
  }

  .toolbox-btn:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
  }

  .toolbox-btn svg {
    margin-right: 0.5rem;
    flex-shrink: 0;
  }

  .input {
    display: flex;
    height: 2.5rem;
    width: 100%;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background-color: white;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s;
  }

  .input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .input:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .input::placeholder {
    color: #6b7280;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .table {
    @apply w-full caption-bottom text-sm;
  }
  
  .table-header {
    @apply border-b;
  }
  
  .table-row {
    @apply border-b transition-colors hover:bg-gray-50;
  }
  
  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-500;
  }
  
  .table-cell {
    @apply p-4 align-middle;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
