import React from 'react'
import { useNode } from '@craftjs/core'

export const Text = ({ text, fontSize, fontWeight, color, textAlign }) => {
  const { connectors: { connect, drag } } = useNode()
  
  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        fontSize: `${fontSize}px`,
        fontWeight,
        color,
        textAlign,
        padding: '5px',
        margin: '5px 0',
        cursor: 'move',
        border: '1px dashed transparent'
      }}
      className="hover:border-blue-400"
    >
      {text}
    </div>
  )
}

Text.craft = {
  props: {
    text: 'Sample Text',
    fontSize: 16,
    fontWeight: 'normal',
    color: '#333333',
    textAlign: 'left'
  },
  related: {
    settings: TextSettings
  }
}

export const TextSettings = () => {
  const { actions: { setProp }, props } = useNode((node) => ({
    props: node.data.props
  }))

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Text Content
        </label>
        <input
          type="text"
          value={props.text}
          onChange={(e) => setProp((props) => props.text = e.target.value)}
          className="input"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Font Size
        </label>
        <input
          type="number"
          value={props.fontSize}
          onChange={(e) => setProp((props) => props.fontSize = parseInt(e.target.value))}
          className="input"
          min="8"
          max="72"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Font Weight
        </label>
        <select
          value={props.fontWeight}
          onChange={(e) => setProp((props) => props.fontWeight = e.target.value)}
          className="input"
        >
          <option value="normal">Normal</option>
          <option value="bold">Bold</option>
          <option value="lighter">Light</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Color
        </label>
        <input
          type="color"
          value={props.color}
          onChange={(e) => setProp((props) => props.color = e.target.value)}
          className="input"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Text Align
        </label>
        <select
          value={props.textAlign}
          onChange={(e) => setProp((props) => props.textAlign = e.target.value)}
          className="input"
        >
          <option value="left">Left</option>
          <option value="center">Center</option>
          <option value="right">Right</option>
        </select>
      </div>
    </div>
  )
}
