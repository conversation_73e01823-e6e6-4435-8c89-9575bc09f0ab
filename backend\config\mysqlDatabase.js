const mysql = require('mysql2/promise');

let connection;

// Initialize MySQL database connection
async function initializeDatabase() {
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3305,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'aaaaa',
      database: process.env.DB_NAME || 'student_management'
    });

    console.log('Connected to MySQL database');
    
    // Create tables
    await createTables();
    console.log('Database tables created successfully');
    
  } catch (error) {
    console.error('Error connecting to MySQL:', error);
    throw error;
  }
}

// Create database tables
async function createTables() {
  try {
    // Students table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS students (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id VARCHAR(20) UNIQUE NOT NULL,
        name VARCHA<PERSON>(100) NOT NULL,
        course VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        address TEXT,
        photo_url VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Card templates table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS card_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        template_data TEXT NOT NULL,
        is_default BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Generated cards table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS generated_cards (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        template_id INT NOT NULL,
        card_url VARCHAR(255),
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
        FOREIGN KEY (template_id) REFERENCES card_templates (id) ON DELETE CASCADE
      )
    `);

    // Bulk card generation jobs table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS bulk_card_jobs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        template_id INT NOT NULL,
        total_students INT NOT NULL,
        completed_students INT DEFAULT 0,
        status VARCHAR(20) DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        FOREIGN KEY (template_id) REFERENCES card_templates (id) ON DELETE CASCADE
      )
    `);

    // Insert default template if not exists
    await insertDefaultTemplate();
    
  } catch (error) {
    console.error('Error creating tables:', error);
    throw error;
  }
}

// Insert default card template
async function insertDefaultTemplate() {
  try {
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM card_templates WHERE is_default = 1');
    
    if (rows[0].count === 0) {
      const defaultTemplate = {
        width: 400,
        height: 250,
        backgroundColor: '#ffffff',
        elements: [
          {
            type: 'text',
            content: 'STUDENT ID CARD',
            x: 200,
            y: 30,
            fontSize: 18,
            fontWeight: 'bold',
            textAlign: 'center',
            color: '#333333'
          },
          {
            type: 'image',
            x: 30,
            y: 60,
            width: 80,
            height: 100,
            placeholder: 'student_photo'
          },
          {
            type: 'text',
            content: '{name}',
            x: 130,
            y: 80,
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333333'
          },
          {
            type: 'text',
            content: 'ID: {student_id}',
            x: 130,
            y: 105,
            fontSize: 12,
            color: '#666666'
          },
          {
            type: 'text',
            content: 'Course: {course}',
            x: 130,
            y: 125,
            fontSize: 12,
            color: '#666666'
          },
          {
            type: 'text',
            content: 'Email: {email}',
            x: 130,
            y: 145,
            fontSize: 10,
            color: '#666666'
          },
          {
            type: 'text',
            content: 'Address: {address}',
            x: 130,
            y: 165,
            fontSize: 10,
            color: '#666666'
          }
        ]
      };

      await connection.execute(
        'INSERT INTO card_templates (name, template_data, is_default) VALUES (?, ?, ?)',
        ['Default Template', JSON.stringify(defaultTemplate), 1]
      );
      
      console.log('Default template inserted');
    }
  } catch (error) {
    console.error('Error inserting default template:', error);
    throw error;
  }
}

// Get database connection
function getDatabase() {
  return connection;
}

// Close database connection
async function closeDatabase() {
  if (connection) {
    await connection.end();
    console.log('Database connection closed');
  }
}

module.exports = {
  initializeDatabase,
  getDatabase,
  closeDatabase
};
