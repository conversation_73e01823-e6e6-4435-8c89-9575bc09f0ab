const mysql = require('mysql2/promise');

let connection;

// Initialize MySQL database connection
async function initializeDatabase() {
  try {
    // First connect without database to create it
    const tempConnection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3305,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'aaaaa'
    });

    // Check if database exists and create if needed
    const dbName = process.env.DB_NAME || 'student_management';
    const [databases] = await tempConnection.execute(`SHOW DATABASES LIKE '${dbName}'`);

    if (databases.length === 0) {
      await tempConnection.execute(`CREATE DATABASE \`${dbName}\``);
      console.log(`✓ Database '${dbName}' created`);
    } else {
      console.log(`✓ Database '${dbName}' already exists`);
    }

    // Close temporary connection
    await tempConnection.end();

    // Now connect to the specific database
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3305,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'aaaaa',
      database: dbName
    });

    console.log('Connected to MySQL database');

    // Create tables
    await createTables();
    console.log('Database tables created successfully');

  } catch (error) {
    console.error('Error connecting to MySQL:', error);
    throw error;
  }
}

// Create database tables
async function createTables() {
  try {
    // Check and create students table
    const [studentsExists] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = ? AND table_name = 'students'
    `, [process.env.DB_NAME || 'student_management']);

    if (studentsExists[0].count === 0) {
      await connection.execute(`
        CREATE TABLE students (
          id INT AUTO_INCREMENT PRIMARY KEY,
          student_id VARCHAR(20) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          course VARCHAR(100) NOT NULL,
          email VARCHAR(100) UNIQUE NOT NULL,
          address TEXT,
          photo_url VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
      console.log('✓ Created students table');
    } else {
      console.log('✓ Students table already exists');
    }

    // Check and create card templates table
    const [templatesExists] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = ? AND table_name = 'card_templates'
    `, [process.env.DB_NAME || 'student_management']);

    if (templatesExists[0].count === 0) {
      await connection.execute(`
        CREATE TABLE card_templates (
          id INT AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          template_data TEXT NOT NULL,
          is_default BOOLEAN DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
      console.log('✓ Created card_templates table');
    } else {
      console.log('✓ Card templates table already exists');
    }

    // Check and create generated cards table
    const [cardsExists] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = ? AND table_name = 'generated_cards'
    `, [process.env.DB_NAME || 'student_management']);

    if (cardsExists[0].count === 0) {
      await connection.execute(`
        CREATE TABLE generated_cards (
          id INT AUTO_INCREMENT PRIMARY KEY,
          student_id INT NOT NULL,
          template_id INT NOT NULL,
          card_url VARCHAR(255),
          generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
          FOREIGN KEY (template_id) REFERENCES card_templates (id) ON DELETE CASCADE
        )
      `);
      console.log('✓ Created generated_cards table');
    } else {
      console.log('✓ Generated cards table already exists');
    }

    // Check and create bulk jobs table
    const [jobsExists] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = ? AND table_name = 'bulk_card_jobs'
    `, [process.env.DB_NAME || 'student_management']);

    if (jobsExists[0].count === 0) {
      await connection.execute(`
        CREATE TABLE bulk_card_jobs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          template_id INT NOT NULL,
          total_students INT NOT NULL,
          completed_students INT DEFAULT 0,
          status VARCHAR(20) DEFAULT 'pending',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          completed_at TIMESTAMP NULL,
          FOREIGN KEY (template_id) REFERENCES card_templates (id) ON DELETE CASCADE
        )
      `);
      console.log('✓ Created bulk_card_jobs table');
    } else {
      console.log('✓ Bulk card jobs table already exists');
    }

    // Insert default template if not exists
    await insertDefaultTemplate();

  } catch (error) {
    console.error('Error creating tables:', error);
    throw error;
  }
}

// Insert default card template
async function insertDefaultTemplate() {
  try {
    // Check if default template already exists
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM card_templates WHERE is_default = 1');

    if (rows[0].count === 0) {
      console.log('Creating default template...');
      const defaultTemplate = {
        width: 400,
        height: 250,
        backgroundColor: '#ffffff',
        elements: [
          {
            type: 'text',
            content: 'STUDENT ID CARD',
            x: 200,
            y: 30,
            fontSize: 18,
            fontWeight: 'bold',
            textAlign: 'center',
            color: '#333333'
          },
          {
            type: 'image',
            x: 30,
            y: 60,
            width: 80,
            height: 100,
            placeholder: 'student_photo'
          },
          {
            type: 'text',
            content: '{name}',
            x: 130,
            y: 80,
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333333'
          },
          {
            type: 'text',
            content: 'ID: {student_id}',
            x: 130,
            y: 105,
            fontSize: 12,
            color: '#666666'
          },
          {
            type: 'text',
            content: 'Course: {course}',
            x: 130,
            y: 125,
            fontSize: 12,
            color: '#666666'
          },
          {
            type: 'text',
            content: 'Email: {email}',
            x: 130,
            y: 145,
            fontSize: 10,
            color: '#666666'
          },
          {
            type: 'text',
            content: 'Address: {address}',
            x: 130,
            y: 165,
            fontSize: 10,
            color: '#666666'
          }
        ]
      };

      await connection.execute(
        'INSERT INTO card_templates (name, template_data, is_default) VALUES (?, ?, ?)',
        ['Default Template', JSON.stringify(defaultTemplate), 1]
      );

      console.log('✓ Default template created');
    } else {
      console.log('✓ Default template already exists');
    }
  } catch (error) {
    console.error('Error inserting default template:', error);
    throw error;
  }
}

// Get database connection
function getDatabase() {
  return connection;
}

// Close database connection
async function closeDatabase() {
  if (connection) {
    await connection.end();
    console.log('Database connection closed');
  }
}

module.exports = {
  initializeDatabase,
  getDatabase,
  closeDatabase
};
