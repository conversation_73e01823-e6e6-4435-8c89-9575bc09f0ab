import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Plus, Edit, Trash2, Eye, Star } from 'lucide-react'
import { templateAPI } from '../services/api'
import toast from 'react-hot-toast'

export default function Templates() {
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      const response = await templateAPI.getAll()
      setTemplates(response.data)
    } catch (error) {
      console.error('Error fetching templates:', error)
      toast.error('Failed to load templates')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id, templateName) => {
    if (!window.confirm(`Are you sure you want to delete "${templateName}"?`)) {
      return
    }

    try {
      await templateAPI.delete(id)
      toast.success('Template deleted successfully')
      fetchTemplates()
    } catch (error) {
      console.error('Error deleting template:', error)
      const errorMessage = error.response?.data?.error || 'Failed to delete template'
      toast.error(errorMessage)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="card p-6">
                <div className="h-32 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">ID Card Templates</h1>
          <p className="text-gray-600">Manage and customize ID card templates</p>
        </div>
        <Link to="/templates/new" className="btn-primary btn-extra-wide">
          <Plus className="h-4 w-4" />
          Create Template
        </Link>
      </div>

      {/* Templates Grid */}
      {templates.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <div key={template.id} className="card overflow-hidden">
              {/* Template Preview */}
              <div className="h-48 bg-gray-50 flex items-center justify-center border-b border-gray-200">
                <div 
                  className="relative border border-gray-300 shadow-sm"
                  style={{
                    width: `${Math.min(template.template_data.width * 0.4, 160)}px`,
                    height: `${Math.min(template.template_data.height * 0.4, 100)}px`,
                    backgroundColor: template.template_data.backgroundColor || '#ffffff'
                  }}
                >
                  {/* Simplified preview of template elements */}
                  {template.template_data.elements?.slice(0, 3).map((element, index) => {
                    if (element.type === 'text') {
                      return (
                        <div
                          key={index}
                          className="absolute text-xs overflow-hidden"
                          style={{
                            left: `${(element.x / template.template_data.width) * 100}%`,
                            top: `${(element.y / template.template_data.height) * 100}%`,
                            fontSize: `${Math.max((element.fontSize || 14) * 0.3, 6)}px`,
                            color: element.color || '#000000',
                            fontWeight: element.fontWeight || 'normal'
                          }}
                        >
                          {element.content?.replace(/\{[^}]+\}/g, 'Sample') || 'Text'}
                        </div>
                      )
                    } else if (element.type === 'image') {
                      return (
                        <div
                          key={index}
                          className="absolute bg-gray-200 border border-gray-300"
                          style={{
                            left: `${(element.x / template.template_data.width) * 100}%`,
                            top: `${(element.y / template.template_data.height) * 100}%`,
                            width: `${(element.width / template.template_data.width) * 100}%`,
                            height: `${(element.height / template.template_data.height) * 100}%`
                          }}
                        />
                      )
                    }
                    return null
                  })}
                </div>
              </div>

              {/* Template Info */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                    {template.name}
                    {template.is_default && (
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    )}
                  </h3>
                </div>
                
                <div className="text-sm text-gray-600 mb-4">
                  <p>Size: {template.template_data.width} × {template.template_data.height}px</p>
                  <p>Elements: {template.template_data.elements?.length || 0}</p>
                  <p>Created: {new Date(template.created_at).toLocaleDateString()}</p>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Link
                      to={`/templates/${template.id}/edit`}
                      className="text-blue-600 hover:text-blue-900"
                      title="Edit Template"
                    >
                      <Edit className="h-4 w-4" />
                    </Link>
                    {!template.is_default && (
                      <button
                        onClick={() => handleDelete(template.id, template.name)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete Template"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                  
                  <Link
                    to={`/card-generator?template=${template.id}`}
                    className="btn-secondary btn-sm"
                  >
                    Use Template
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first ID card template.
          </p>
          <div className="mt-6">
            <Link to="/templates/new" className="btn-primary">
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Link>
          </div>
        </div>
      )}

      {/* Template Features Info */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Template Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-blue-100 rounded-lg p-3 w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Edit className="h-6 w-6 text-blue-600" />
            </div>
            <h4 className="font-medium text-gray-900">Drag & Drop Editor</h4>
            <p className="text-sm text-gray-600 mt-1">
              Easy-to-use visual editor with drag and drop functionality
            </p>
          </div>
          
          <div className="text-center">
            <div className="bg-green-100 rounded-lg p-3 w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Eye className="h-6 w-6 text-green-600" />
            </div>
            <h4 className="font-medium text-gray-900">Live Preview</h4>
            <p className="text-sm text-gray-600 mt-1">
              See your changes in real-time as you design your template
            </p>
          </div>
          
          <div className="text-center">
            <div className="bg-purple-100 rounded-lg p-3 w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Star className="h-6 w-6 text-purple-600" />
            </div>
            <h4 className="font-medium text-gray-900">Dynamic Content</h4>
            <p className="text-sm text-gray-600 mt-1">
              Use placeholders that automatically fill with student data
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
