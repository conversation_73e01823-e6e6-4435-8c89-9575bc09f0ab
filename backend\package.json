{"name": "student-management-backend", "version": "1.0.0", "description": "Backend API for Student Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup-db": "node scripts/setupDatabase.js", "populate-templates": "node scripts/populateTemplates.js", "test": "jest"}, "keywords": ["student", "management", "api", "express", "sql"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "multer": "^1.4.5-lts.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.9.2", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "uuid": "^9.0.0", "sharp": "^0.32.4", "canvas": "^2.11.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}