{"name": "student-management-backend", "version": "1.0.0", "description": "Backend API for Student Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup-db": "node scripts/setupDatabase.js", "populate-templates": "node scripts/populateTemplates.js", "test": "jest"}, "keywords": ["student", "management", "api", "express", "sql"], "author": "Your Name", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "canvas": "^2.11.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "sharp": "^0.32.4", "sqlite3": "^5.1.6", "uuid": "^9.0.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}