import React from 'react'
import { useNode } from '@craftjs/core'
import { Image } from 'lucide-react'

export const ImagePlaceholder = ({ width, height, borderStyle }) => {
  const { connectors: { connect, drag }, selected, actions: { setProp } } = useNode((state) => ({
    selected: state.events.selected
  }))

  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        border: selected
          ? '2px solid #3b82f6'
          : borderStyle === 'dashed' ? '2px dashed #cccccc' : '2px solid #cccccc',
        backgroundColor: '#f0f0f0',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'move',
        position: 'relative'
      }}
      className="hover:border-blue-400"
    >
      <div className="text-center text-gray-500">
        <Image className="h-8 w-8 mx-auto mb-2" />
        <span className="text-sm">Student Photo</span>
      </div>

      {/* Resize handles when selected */}
      {selected && (
        <>
          {/* Corner resize handle */}
          <div
            style={{
              position: 'absolute',
              bottom: '-4px',
              right: '-4px',
              width: '8px',
              height: '8px',
              backgroundColor: '#3b82f6',
              cursor: 'se-resize',
              border: '1px solid white'
            }}
            onMouseDown={(e) => {
              e.stopPropagation()
              const startX = e.clientX
              const startY = e.clientY
              const startWidth = width
              const startHeight = height

              const handleMouseMove = (e) => {
                const newWidth = Math.max(30, startWidth + (e.clientX - startX))
                const newHeight = Math.max(30, startHeight + (e.clientY - startY))
                setProp((props) => {
                  props.width = newWidth
                  props.height = newHeight
                })
              }

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove)
                document.removeEventListener('mouseup', handleMouseUp)
              }

              document.addEventListener('mousemove', handleMouseMove)
              document.addEventListener('mouseup', handleMouseUp)
            }}
          />

          {/* Right edge resize handle */}
          <div
            style={{
              position: 'absolute',
              top: '50%',
              right: '-4px',
              width: '8px',
              height: '20px',
              backgroundColor: '#3b82f6',
              cursor: 'e-resize',
              transform: 'translateY(-50%)',
              border: '1px solid white'
            }}
            onMouseDown={(e) => {
              e.stopPropagation()
              const startX = e.clientX
              const startWidth = width

              const handleMouseMove = (e) => {
                const newWidth = Math.max(30, startWidth + (e.clientX - startX))
                setProp((props) => props.width = newWidth)
              }

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove)
                document.removeEventListener('mouseup', handleMouseUp)
              }

              document.addEventListener('mousemove', handleMouseMove)
              document.addEventListener('mouseup', handleMouseUp)
            }}
          />

          {/* Bottom edge resize handle */}
          <div
            style={{
              position: 'absolute',
              bottom: '-4px',
              left: '50%',
              width: '20px',
              height: '8px',
              backgroundColor: '#3b82f6',
              cursor: 's-resize',
              transform: 'translateX(-50%)',
              border: '1px solid white'
            }}
            onMouseDown={(e) => {
              e.stopPropagation()
              const startY = e.clientY
              const startHeight = height

              const handleMouseMove = (e) => {
                const newHeight = Math.max(30, startHeight + (e.clientY - startY))
                setProp((props) => props.height = newHeight)
              }

              const handleMouseUp = () => {
                document.removeEventListener('mousemove', handleMouseMove)
                document.removeEventListener('mouseup', handleMouseUp)
              }

              document.addEventListener('mousemove', handleMouseMove)
              document.addEventListener('mouseup', handleMouseUp)
            }}
          />
        </>
      )}
    </div>
  )
}

export const ImagePlaceholderSettings = () => {
  const { actions: { setProp }, props } = useNode((node) => ({
    props: node.data.props
  }))

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-2">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Width (px)
          </label>
          <input
            type="number"
            value={props.width}
            onChange={(e) => setProp((props) => props.width = parseInt(e.target.value))}
            className="input"
            min="30"
            max="300"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Height (px)
          </label>
          <input
            type="number"
            value={props.height}
            onChange={(e) => setProp((props) => props.height = parseInt(e.target.value))}
            className="input"
            min="30"
            max="400"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Border Style
        </label>
        <select
          value={props.borderStyle}
          onChange={(e) => setProp((props) => props.borderStyle = e.target.value)}
          className="input"
        >
          <option value="dashed">Dashed</option>
          <option value="solid">Solid</option>
        </select>
      </div>
    </div>
  )
}

ImagePlaceholder.craft = {
  props: {
    width: 80,
    height: 100,
    borderStyle: 'dashed'
  },
  related: {
    settings: ImagePlaceholderSettings
  }
}
