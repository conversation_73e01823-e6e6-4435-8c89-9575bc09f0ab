import React from 'react'
import { useNode } from '@craftjs/core'
import { Image } from 'lucide-react'

export const ImagePlaceholder = ({ width, height, borderStyle }) => {
  const { connectors: { connect, drag } } = useNode()
  
  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        border: borderStyle === 'dashed' ? '2px dashed #cccccc' : '2px solid #cccccc',
        backgroundColor: '#f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'move',
        margin: '5px'
      }}
      className="hover:border-blue-400"
    >
      <div className="text-center text-gray-500">
        <Image className="h-8 w-8 mx-auto mb-2" />
        <span className="text-sm">Student Photo</span>
      </div>
    </div>
  )
}

ImagePlaceholder.craft = {
  props: {
    width: 80,
    height: 100,
    borderStyle: 'dashed'
  },
  related: {
    settings: ImagePlaceholderSettings
  }
}

export const ImagePlaceholderSettings = () => {
  const { actions: { setProp }, props } = useNode((node) => ({
    props: node.data.props
  }))

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Width (px)
        </label>
        <input
          type="number"
          value={props.width}
          onChange={(e) => setProp((props) => props.width = parseInt(e.target.value))}
          className="input"
          min="50"
          max="300"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Height (px)
        </label>
        <input
          type="number"
          value={props.height}
          onChange={(e) => setProp((props) => props.height = parseInt(e.target.value))}
          className="input"
          min="50"
          max="400"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Border Style
        </label>
        <select
          value={props.borderStyle}
          onChange={(e) => setProp((props) => props.borderStyle = e.target.value)}
          className="input"
        >
          <option value="dashed">Dashed</option>
          <option value="solid">Solid</option>
        </select>
      </div>
    </div>
  )
}
