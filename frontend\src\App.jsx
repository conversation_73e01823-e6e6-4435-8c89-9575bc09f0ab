import { Routes, Route } from 'react-router-dom'
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Students from './pages/Students'
import StudentForm from './pages/StudentForm'
import StudentDetail from './pages/StudentDetail'
import Templates from './pages/Templates'
import TemplateEditor from './pages/TemplateEditor'
import CardGenerator from './pages/CardGenerator'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/students" element={<Students />} />
        <Route path="/students/new" element={<StudentForm />} />
        <Route path="/students/:id" element={<StudentDetail />} />
        <Route path="/students/:id/edit" element={<StudentForm />} />
        <Route path="/templates" element={<Templates />} />
        <Route path="/templates/new" element={<TemplateEditor />} />
        <Route path="/templates/:id/edit" element={<TemplateEditor />} />
        <Route path="/card-generator" element={<CardGenerator />} />
        <Route path="/card-generator/:studentId" element={<CardGenerator />} />
      </Routes>
    </Layout>
  )
}

export default App
